## 模块说明
领域Core库。


## 组件更新日志

## 3.1.2
1. 领域跳商详支持新商品模型
2. 支持新的沉浸式
3. 支持新的消息通知实体

## 3.1.1
1. 领域跳商详方法收口
2. 支持外卖字段传递

## 3.1.0
1. 领域核心增加围栏维度

## 3.0.0
1. 首页架构调整


## 2.0.4
1. 增加获取消息未读数量的接口getMessageUnReadCountId()
2. 增加跳转到消息中心的类型和方法


## 2.0.3
1. 跳转直播详情页 新增跳转类型

## 2.0.2 
1. 跳转商详 新增榜单排名字段

## 2.0.1
1. 轮播Event新增backHeight字段传递

## 2.0.0
1. api能力调整
【新增】支持回调下拉刷新
【新增】支持判定真实触顶
【删除】删除气泡相关api

【影响范围】
非兼容性更改，注意api的移除，注意是否可以编译通过。关注吸顶效果的影响

【回归范围】
吸顶效果，气泡效果，上拉加载更多

2. 


### com.xstore.floorsdk:SFFloorCore:1.0.10
1. 搜索结果页跳转方法参数扩展
2. 支持跳转常购清单页
3. 增加记录可以使用沉浸式的新人三单礼进度小组件的楼层位置和楼层高度


### com.xstore.floorsdk:SFFloorCore:1.1.0
1.小米折叠屏适配
 
###  com.xstore.floorsdk:SFFloorCore:1.0.9
1.七鲜价适配 修改新老架构取值逻辑

### com.xstore.floorsdk:SFFloorCore:1.0.8
1. 请求方式新增缓存请求

### com.xstore.floorsdk:SFFloorCore:1.0.7
1.首页头部优化
2.地址门店服务包路径修改，取数逻辑修改

### com.xstore.floorsdk:SFFloorCore:1.0.6
1. 新增榜单卡片和会员卡片相关字段

### com.xstore.floorsdk:SFFloorCore:1.0.0-1.0.5
1. 创建仓库。
