<?xml version="1.0" encoding="utf-8"?>

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="5dip" />
            <gradient
                android:startColor="@color/sf_floor_core_color_F5F5F5"
                android:centerColor="@color/sf_floor_core_color_F5F5F5"
                android:centerY="0.75"
                android:endColor="@color/sf_floor_core_color_F5F5F5"
                android:angle="270"
                />
        </shape>
    </item>

    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape>
                <corners android:radius="5dip" />
                <gradient
                    android:startColor="@color/sf_floor_core_color_00A2C0"
                    android:centerColor="@color/sf_floor_core_color_00A2C0"
                    android:centerY="0.75"
                    android:endColor="@color/sf_floor_core_color_00A2C0"
                    android:angle="270"
                    />
            </shape>
        </clip>
    </item>

    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners android:radius="5dip" />
                <gradient
                    android:startColor="@color/sf_floor_core_color_00698C"
                    android:centerColor="@color/sf_floor_core_color_00698C"
                    android:centerY="0.75"
                    android:endColor="@color/sf_floor_core_color_00698C"
                    android:angle="270"
                    />
            </shape>
        </clip>
    </item>

</layer-list>