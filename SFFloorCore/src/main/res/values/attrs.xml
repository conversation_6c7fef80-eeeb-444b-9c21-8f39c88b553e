<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="sf_floor_core_LimitHeightListView">
        <attr name="sf_floor_core_max_height" format="dimension" />
    </declare-styleable>

    <declare-styleable name="sf_floor_core_HeightScrollView">
        <attr name="sf_floor_core_maxHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="sf_floor_core_CircleImageView">
        <attr name="sf_floor_core_border_width" format="dimension" />
        <attr name="sf_floor_core_outside_border_width" format="dimension" />
        <attr name="sf_floor_core_border_color" format="color" />
        <attr name="sf_floor_core_outside_border_color" format="color" />
        <attr name="sf_floor_core_border_overlay" format="boolean" />
    </declare-styleable>

    <declare-styleable name="sf_floor_core_ImageCodeView">
        <attr name="sf_floor_core_rate" format="float" />
    </declare-styleable>

    <!-- 圆角图片 -->
    <declare-styleable name="sf_floor_core_YLCircleImageView">
        <attr name="sf_floor_core_radiusYL" format="dimension">圆角半径</attr>
        <attr name="sf_floor_core_topLeftRadiusYL" format="dimension">左上</attr>
        <attr name="sf_floor_core_topRightRadiusYL" format="dimension">右上</attr>
        <attr name="sf_floor_core_bottomLeftRadiusYL" format="dimension">左下</attr>
        <attr name="sf_floor_core_bottomRightRadiusYL" format="dimension">右下</attr>

        <attr name="sf_floor_core_topLeftRadius_xYL" format="dimension">左上X轴</attr>
        <attr name="sf_floor_core_topLeftRadius_yYL" format="dimension">左上Y轴</attr>

        <attr name="sf_floor_core_topRightRadius_xYL" format="dimension">右上X轴</attr>
        <attr name="sf_floor_core_topRightRadius_yYL" format="dimension">右上Y轴</attr>

        <attr name="sf_floor_core_bottomLeftRadius_xYL" format="dimension">左下X轴</attr>
        <attr name="sf_floor_core_bottomLeftRadius_yYL" format="dimension">左下Y轴</attr>

        <attr name="sf_floor_core_bottomRightRadius_xYL" format="dimension">右下X轴</attr>
        <attr name="sf_floor_core_bottomRightRadius_yYL" format="dimension">右下Y轴</attr>

        <attr name="sf_floor_core_borderWidthYL" format="dimension">描边宽度</attr>
        <attr name="sf_floor_core_borderColorYL" format="color">描边颜色</attr>
        <attr name="sf_floor_core_borderSpaceYL" format="dimension">描边与图片间距</attr>
        <!-- 无论是什么展示形式，都会填充满整个控件，如果不想填充满，自己去改onDraw里面的 dst -->
        <attr name="sf_floor_core_scaleTypeYL" format="enum">
            <enum name="top" value="0">从图片顶部开始绘制，即肯定会展示图片顶部，默认</enum>
            <enum name="center" value="1">展示图片中心</enum>
            <enum name="bottom" value="2">从图片底部开始绘制，即肯定会展示图片底部</enum>
            <enum name="fitXY" value="3">完全展示，有可能变形</enum>
        </attr>
    </declare-styleable>

</resources>