package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/5/28
 * 动态设置 价格module
 */
public class DynamicLabelsBean implements Serializable {
    /**
     * 开始横坐标
     */
    private float startX;
    /**
     * 开始纵坐标
     */
    private float startY;
    /**
     * 结束横坐标
     */
    private float endX;
    /**
     * 结束纵坐标
     */
    private float endY;
    /**
     * 字体大小
     */
    private int font;
    /**
     * 价格文案
     */
    private String text;
    /**
     * 价格文案颜色
     */
    private String textColor;
    /**
     * 价格背景颜色
     */
    private String backgroundColor;

    public float getStartX() {
        return startX;
    }

    public void setStartX(float startX) {
        this.startX = startX;
    }

    public float getStartY() {
        return startY;
    }

    public void setStartY(float startY) {
        this.startY = startY;
    }

    public float getEndX() {
        return endX;
    }

    public void setEndX(float endX) {
        this.endX = endX;
    }

    public float getEndY() {
        return endY;
    }

    public void setEndY(float endY) {
        this.endY = endY;
    }

    public int getFont() {
        return font;
    }

    public void setFont(int font) {
        this.font = font;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }
}
