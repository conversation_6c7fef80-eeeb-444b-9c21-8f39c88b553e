package com.xstore.sdk.floor.floorcore.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ScrollView;

import com.xstore.floorsdk.floorcore.R;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;


/**
 * <AUTHOR>
 * @Description:
 * @date 2019/11/25
 */
public class HeightScrollView extends ScrollView {

    private int maxHeight;

    public HeightScrollView(Context context) {
        this(context, null);
    }

    public HeightScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HeightScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.sf_floor_core_HeightScrollView);
        maxHeight = typedArray.getDimensionPixelSize(R.styleable.sf_floor_core_HeightScrollView_sf_floor_core_maxHeight, ScreenUtils.dip2px(context, 120));
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST));
    }

    public void setMaxHeight(int maxHeight) {
        this.maxHeight = maxHeight;
        this.requestLayout();
    }
}

