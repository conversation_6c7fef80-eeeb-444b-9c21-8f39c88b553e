package com.xstore.sdk.floor.floorcore.interfaces;

import android.content.Intent;

import androidx.annotation.NonNull;

/**
 * 楼层生命周期感知
 */
public interface FloorLifecycle {

    /**
     * 生命周期-onResume，需要外部手动调用下
     *
     * @param hidden 当前是否是隐藏状态
     */
    void onResume(boolean hidden);

    /**
     * 生命周期-onPause，需要外部手动调用下
     */
    void onPause();

    /**
     * 生命周期-onHiddenChange，需要外部手动调用下
     *
     * @param hidden 当前是否是隐藏状态
     */
    void onHiddenChange(boolean hidden);

    /**
     * 生命周期-onDestroy，需要外部手动调用下
     */
    void onDestroy();

    /**
     * 接收启动时的结果
     * 虽然可以代理，但暂时没必要复杂实现
     *
     * @param requestCode 请求code
     * @param resultCode  结果code
     * @param data        数据
     */
    void onActivityResult(int requestCode, int resultCode, Intent data);

    /**
     * 权限申请相关的回调 用于内部直接处理权限申请结果
     * 虽然可以代理，但暂时没必要复杂实现
     *
     * @param requestCode
     * @param permissions
     * @param grantResults
     */
    void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults);

}
