package com.xstore.sdk.floor.floorcore.bean;


import android.content.Context;

import com.xstore.floorsdk.floorcore.R;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;

/**
 * 由轮播楼层触发的事件，用来传递当前轮播图的沉浸式效果
 */
public class BannerChangeEvent {
    /**
     * 当前沉浸式背景图
     */
    public String bgImage;
    /**
     * 当前banner的高度
     */
    public float bannerHeight;
    /**
     * 当前主题色
     */
    private String backgroundMainColor;

    private int backHeight;

    /**
     * @param bgImage             当前沉浸式背景图
     * @param backgroundMainColor 当前主题色
     * @param bannerHeight        当前banner的高度
     */
    public BannerChangeEvent(String bgImage, String backgroundMainColor, float bannerHeight) {
        this.bgImage = bgImage;
        this.bannerHeight = bannerHeight;
        this.backgroundMainColor = backgroundMainColor;
    }

    /**
     * @return 获取当前的主题色
     */
    public String getBackgroundMainColor() {
        return StringUtil.isEmpty(backgroundMainColor) ? "#F6F7F8" : backgroundMainColor;
    }

    /**
     * @return 获取搜索按钮背景色
     */
    public String getSearchBgColor(Context context) {
        return StringUtil.isEmpty(backgroundMainColor) ? context.getResources().getString(R.string.sf_theme_color_level_1) : backgroundMainColor;
    }

    public void setBackHeight(int backHeight) {
        this.backHeight = backHeight;
    }

    public int getBackHeight() {
        return backHeight;
    }
}
