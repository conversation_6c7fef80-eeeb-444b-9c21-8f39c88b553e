package com.xstore.sdk.floor.floorcore.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.xstore.floorsdk.floorcore.R;


/**
 * Created by lishupeng1 on 2018/1/12.
 */

public class ImageCodeView extends androidx.appcompat.widget.AppCompatImageView {
    private float rate;

    public ImageCodeView(Context context) {
        super(context);
    }

    public ImageCodeView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public ImageCodeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attr) {
        TypedArray array = getContext().obtainStyledAttributes(attr, R.styleable.sf_floor_core_ImageCodeView);
        rate = array.getFloat(R.styleable.sf_floor_core_ImageCodeView_sf_floor_core_rate, -1);
        array.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if(rate < 0) {
            return;
        }
        int width = getMeasuredWidth();
        int height = (int) (width * rate);
        setMeasuredDimension(width, height);
    }
}
