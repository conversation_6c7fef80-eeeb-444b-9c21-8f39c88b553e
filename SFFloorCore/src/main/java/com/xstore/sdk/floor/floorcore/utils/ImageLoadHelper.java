package com.xstore.sdk.floor.floorcore.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.GlideDrawableImageViewTarget;
import com.bumptech.glide.request.target.SimpleTarget;
import com.xstore.sevenfresh.image.ImageloadUtils;

public class ImageLoadHelper {

    public static void loadGif(Context context, ImageView view, String url, ImageLoadCallback callback) {
        if (context == null || Build.VERSION.SDK_INT < 17 || (context instanceof Activity && !((Activity) context).isDestroyed())) {
            url = ImageloadUtils.reformUrl(url);
            if (null != callback && null != view) {
                if (isGif(url)) {
                    Glide.with(context)
                            .load(url)
                            .asGif()
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE).into(new SimpleTarget<GifDrawable>() {
                                @Override
                                public void onResourceReady(GifDrawable resource, GlideAnimation<? super GifDrawable> glideAnimation) {
                                    if (resource == null) {
                                        if (callback != null) {
                                            callback.loadResult(false);
                                        }
                                        return;
                                    }

                                    GlideDrawableImageViewTarget target = new GlideDrawableImageViewTarget(view, 0);
                                    target.onResourceReady(resource, (GlideAnimation) glideAnimation);
                                    if (callback != null) {
                                        callback.loadResult(true);
                                    }
                                }

                                @Override
                                public void onLoadFailed(Exception e, Drawable errorDrawable) {
                                    super.onLoadFailed(e, errorDrawable);
                                    if (callback != null) {
                                        callback.loadResult(false);
                                    }
                                }
                            });
                } else {
                    Glide.with(context).load(url).dontAnimate().diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().into(new GlideDrawableImageViewTarget(view) {
                        public void onResourceReady(GlideDrawable drawable, GlideAnimation anim) {
                            super.onResourceReady(drawable, anim);
                            if (callback != null) {
                                callback.loadResult(true);
                            }
                        }

                        @Override
                        public void onLoadFailed(Exception e, Drawable errorDrawable) {
                            super.onLoadFailed(e, errorDrawable);
                            if (callback != null) {
                                callback.loadResult(false);
                            }
                        }
                    });

                }
            }
        }
    }

    public interface ImageLoadCallback {
        void loadResult(boolean success);
    }

    private static boolean isGif(String url) {
        return !TextUtils.isEmpty(url) && url.endsWith(".gif");
    }
}
