package com.xstore.sdk.floor.floorcore.interfaces;

import android.widget.FrameLayout;

/**
 * 列表播放视频接口
 */
public interface VideoHolderInterface {

    /**
     * @return 返回视频播放容器
     */
    public FrameLayout getVideoContainer();

    /**
     * @return 获取视频地址
     */
    public String getVideoPath();

    /**
     * @param isPlaying 正在播放
     * @param rendingStart 开始渲染
     */
    public void setPlayUI(boolean isPlaying, boolean rendingStart);

    /**
     * @return 获取自定义的itemId 唯一视频item数据源
     */
    public long getFakeItemId();


}
