package com.xstore.sdk.floor.floorcore.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ViewFlipper;


/**
 * FixViewFlipper简介
 * 捕获异常问题
 *
 * <AUTHOR>
 * @date 2021-01-15 10:14
 */
public class FixViewFlipper extends ViewFlipper {

    /**
     * callback 回调
     */
    private Callback callback;


    public FixViewFlipper(Context context) {
        super(context);
    }

    public FixViewFlipper(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onDetachedFromWindow() {
        try {
            super.onDetachedFromWindow();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void setDisplayedChild(int whichChild) {
        super.setDisplayedChild(whichChild);
        if (callback != null) {
            callback.onSetDisplayedChild(whichChild);
        }
    }

    public Callback getCallback() {
        return callback;
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        /**
         * 设置展示第i个
         * @param which
         */
        void onSetDisplayedChild(int which);
    }
}
