package com.xstore.sdk.floor.floorcore;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

import java.util.HashMap;

/**
 * 领域全局内存数据存储
 */
public class FloorMemoryStorageManager {
    private static final String TAG = "FloorMemoryStorageManager";

    /**
     * 所有领域变量集合
     */
    private static HashMap<String, HashMap<String, String>> fieldMaps = new HashMap<>();

    /**
     * 内部方法 不对外提供，非线程安全！
     *
     * @param fieldName 获取某一领域的变量存储map
     * @return k-v string
     */
    private static HashMap<String, String> getFieldDataMap(String fieldName) {
        HashMap<String, String> map = fieldMaps.get(fieldName);
        if (map == null) {
            map = new HashMap<>();
            fieldMaps.put(fieldName, map);
        }
        return map;
    }

    public static synchronized String getValueOfField(String field, String key) {
        String value = null;
        try {
            HashMap<String, String> map = getFieldDataMap(field);
            if (map == null) {
                JdCrashReport.postCaughtException(new Exception("getValueOfField异常，领域" + field + " map为null"));
                return null;
            }
            value = map.get(key);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        SFLogCollector.d(TAG, "removeValueOfField:" + field, " key:" + key + " value:" + value);
        return value;
    }

    public static synchronized void setValueOfField(String field, String key, String value) {
        SFLogCollector.d(TAG, "setValueOfField:" + field + " key:" + key + " value:" + value);
        try {
            HashMap<String, String> map = getFieldDataMap(field);
            if (map == null) {
                JdCrashReport.postCaughtException(new Exception("setValueOfField异常，领域" + field + " map为null"));
                return;
            }
            map.put(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    public static synchronized void clearValuesOfField(String field) {
        SFLogCollector.d(TAG, "clearValuesOfField:" + field);
        try {
            HashMap<String, String> map = getFieldDataMap(field);
            if (map == null) {
                JdCrashReport.postCaughtException(new Exception("clearValuesOfField异常，领域" + field + " map为null"));
                return;
            }
            map.clear();
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    public static synchronized String removeValueOfField(String field, String key) {
        String value = null;
        try {
            HashMap<String, String> map = getFieldDataMap(field);
            if (map == null) {
                JdCrashReport.postCaughtException(new Exception("removeValueOfField异常，领域" + field + " map为null"));
                return null;
            }
            value = map.remove(key);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        SFLogCollector.d(TAG, "removeValueOfField:" + field, " key:" + key + " value:" + value);
        return value;
    }
}
