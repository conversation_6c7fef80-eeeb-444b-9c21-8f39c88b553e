package com.xstore.sdk.floor.floorcore.utils;

import android.app.Activity;
import android.content.Context;
import android.util.DisplayMetrics;
import android.view.WindowManager;

public class ScreenUtils {

    private static float scale;
    private static int[] screenPx;

    // 单位转换
    public static final int dip2px(Context context, float dipValue) {
        if (scale == 0) {
            scale = context.getResources().getDisplayMetrics().density;
        }
        return (int) (dipValue * scale + 0.5f);
    }

    public static int getScreenWidth(Context context) {
        DisplayMetrics metric = new DisplayMetrics();
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        wm.getDefaultDisplay().getMetrics(metric);
        return metric.widthPixels;
    }

    public static int getScreenHeight(Context context) {
        DisplayMetrics metric = new DisplayMetrics();
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        wm.getDefaultDisplay().getMetrics(metric);
        return metric.heightPixels;
    }

    // 获取屏幕高度和宽度
    public static int[] getScreenPx(Context context) {
        if (screenPx == null) {
            DisplayMetrics metric = new DisplayMetrics();
            WindowManager mgr = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            mgr.getDefaultDisplay().getMetrics(metric);
            screenPx = new int[]{metric.widthPixels, metric.heightPixels};
        }

        return screenPx;
    }
}
