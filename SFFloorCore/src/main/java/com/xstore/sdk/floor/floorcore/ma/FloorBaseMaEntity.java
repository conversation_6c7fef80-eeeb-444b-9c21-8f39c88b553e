package com.xstore.sdk.floor.floorcore.ma;

import androidx.annotation.Nullable;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

/**
 * 楼层埋点基础对象
 * 不允许使用基础数据类型
 */
public class FloorBaseMaEntity extends BaseMaEntity {

//    /**
//     * 点击目标：1:点击楼层；2:点击商品；3:点击图片；4:点击加车按钮；5:点击更多；6:热区7: tab点击；8: 点击菜谱; 9: 收藏 10 广告轮播 11 热词搜索 12 视频 13 券 14 购卡;
//     */
//    public final static int TARGET_FLOOR_CLICK = 1;
//    public final static int TARGET_GOODS_CLICK = 2;
//    public final static int TARGET_PIC_CLICK = 3;
//    public final static int TARGET_ADD_CAR_CLICK = 4;
//    public final static int TARGET_MORE_CLICK = 5;
//    public final static int TARGET_HOT_ACTION_CLICK = 6;
//    public final static int TARGET_TAB_CLICK = 7;
//    public final static int TARGET_COOK_CLICK = 8;
//    public final static int TARGET_COLLECT_CLICK = 9;
//    public final static int TARGET_BANNER_AD_CLICK = 10;
//    public final static int TARGET_HOT_WORD_CLICK = 11;
//    public final static int TARGET_VIDEO_CLICK = 12;
//    public final static int TARGET_7FRESH_CARD_BUY_CLICK = 14;

//    /**
//     * 页面id  60 首页
//     */
//    public final String pageTypeId = "60";
//    /**
//     * 页面名称
//     */
//    public final String pageTypeName = "首页";
    /**
     * 组件uuid
     */
    public String componentUuid;
    /**
     * 组件code
     */
    public String componentCode;
    /**
     * 组件名称
     */
    public String componentName;
    /**
     * 楼层id
     */
    public String templateCode;
    /**
     * 楼层序号 默认1
     */
    public Integer floorIndex;


    /**
     * 必传参数
     *
     * @param componentUuid {@link #componentUuid}
     * @param componentCode {@link #componentCode}
     * @param componentName {@link #componentName}
     * @param templateCode  {@link #templateCode}
     * @param floorIndex    不可见楼层：弹窗、二楼、浮窗 没有这个字段
     */
    public FloorBaseMaEntity(String componentUuid, String componentCode, String componentName, String templateCode, Integer floorIndex) {
        this.componentUuid = componentUuid;
        this.componentCode = componentCode;
        this.componentName = componentName;
        this.templateCode = templateCode;
        this.floorIndex = floorIndex;
        //追加参数 以防忘记设置
        setPublicParam(new BaseMaPublicParam());
    }

    /**
     * 使用楼层对象直接构建埋点数据
     *
     * @param floorDetailBean 楼层对象
     */
    public FloorBaseMaEntity(@Nullable FloorDetailBean floorDetailBean) {
        if (floorDetailBean == null) {
            return;
        }
        this.componentUuid = floorDetailBean.getComponentUuid();
        this.componentCode = String.valueOf(floorDetailBean.getComponentCode());
        this.componentName = floorDetailBean.getComponentName();
        if (floorDetailBean.isLocalRecommend()) {
            this.templateCode = floorDetailBean.recommendFloorTemplateCode;
        } else {
            this.templateCode = floorDetailBean.getTemplateCode();
        }
        this.floorIndex = floorDetailBean.getRealIndex() + 2;
        //追加参数
        setPublicParam(new BaseMaPublicParam());
    }

    //////////////////////////////////////  公共业务参数部分 可能没有  //////////////////////////////////////////////////////
    /**
     * 楼层内的坑位id
     */
    public Integer index;
    /**
     * 图片名称
     */
    public String imageName;
    /**
     * 图片链接
     */
    public String imageUrl;
    /**
     * 跳转类型
     */
    public String urlType;
    /**
     * 跳转链接
     */
    public String url;
    /**
     * 点击跳转类型
     */
    public String target;

    /////////////////////////////////////////////////// 非公共参数部分 //////////////////////////////////////////////////////
    /**
     * 分页页码 2开始
     */
    public Integer page;

}
