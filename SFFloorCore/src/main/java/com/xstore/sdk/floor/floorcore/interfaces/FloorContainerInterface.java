package com.xstore.sdk.floor.floorcore.interfaces;

import android.app.Activity;
import android.view.View;
import android.widget.TextView;

import android.widget.RelativeLayout;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.datareport.JDMaUtils;

import java.util.Collection;
import java.util.List;

/**
 * 首页容器接口
 */
public interface FloorContainerInterface {
    /**
     * @return 返回宿主页面的activity
     */
    Activity getActivity();

    /**
     * @return 获取埋点接口
     */
    JDMaUtils.JdMaPageImp getJdMaPageImp();

    /**
     * @param templateCode 要发送到哪个楼层
     * @param Event        事件
     */
    void postFloorEvent(String templateCode, Object Event);

    /**
     * @param floorIndex 获取当前位置前有几个猜你喜欢
     * @return
     */
    int getMayLikeCountBeforePos(int floorIndex);

    /**
     * @param floorIndex 更新item修改
     */
    void notifyItemChange(int floorIndex);

    void notifyDataChange();

    void scrollToPos(int pos);


    /**
     * @param floorIndex 插入猜你喜欢
     */
    void insertMayLike(int floorIndex);

    /**
     * @return 直接获取全部数据
     */
    List<FloorDetailBean> getData();

    /**
     * 获取吸顶2
     *
     * @return
     */
    View getCellingView2();

    /**
     *
     */
    void enableLoadMore();

    /*
     * @return 返回当前的配置楼层
     */
    @Deprecated
    FloorDetailBean getConfigFloorBean();


    /**
     * @return  首页整体是否刚好滚动到了顶部
     * 首页列表内容滚动到顶部不代表 整体 滚动到了顶部
     * 因为还存在导航栏下放列表的paddingTop 改变
     */
    boolean isRealReachTop();

    void showCreateAddressTip();

    RecyclerView getParentRcv();

    RelativeLayout getFlContainer();
}
