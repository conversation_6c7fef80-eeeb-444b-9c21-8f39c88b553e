package com.xstore.sdk.floor.floorcore.utils;


import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;

import com.xstore.floorsdk.floorcore.R;

import androidx.annotation.Nullable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;

public class StringUtil {

    public static boolean isEmpty(String input) {
        if (input == null || "".equals(input)) {
            return true;
        }
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c != ' ' && c != '\t' && c != '\r' && c != '\n') {
                return false;
            }
        }
        return true;
    }

    public static boolean isNotEmpty(String s) {
        return (s != null && s.trim().length() > 0);
    }

    public static boolean safeEquals(String string1, String string2) {
        if (string1 == null && string2 == null) {
            return true;
        }
        if (string1 != null && string1.equals(string2)) {
            return true;
        }
        if (string2 != null && string2.equals(string1)) {
            return true;
        }
        return false;
    }


    public static boolean safeEqualsAndNotNull(String string1, String string2) {
        if (isEmpty(string1) && isEmpty(string2)) {
            return false;
        }
        if (string1 != null && string1.equals(string2)) {
            return true;
        }
        return false;
    }

    /**
     * 获取图片背景色
     *
     * @param defaultcolor 默认颜色 String 类型
     * @param backgroud    背景色
     * @return 返回背景色
     */
    public static int getSetColor(String defaultcolor, String backgroud) {
        int setColor = 0;
        try {
            setColor = Color.parseColor(defaultcolor);
        } catch (Exception e) {
        }
        if (!TextUtils.isEmpty(backgroud)) {
            if (backgroud.charAt(0) == '#' && backgroud.length() == 9) {
                StringBuffer stringBuffer = new StringBuffer(backgroud);
                String aphlaStr = stringBuffer.substring(stringBuffer.length() - 2);
                stringBuffer.insert(1, aphlaStr);
                stringBuffer.delete(stringBuffer.length() - 2, stringBuffer.length());
                backgroud = stringBuffer.toString();
            }

            try {
                setColor = Color.parseColor(backgroud);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return setColor;
    }

    /**
     * 获取图片背景色
     *
     * @param defaultcolor 默认颜色 int 类型
     * @param backgroud    背景色
     * @return 返回背景色
     */
    public static int getSetColor(int defaultcolor, String backgroud) {
        int setColor = defaultcolor;
        if (!TextUtils.isEmpty(backgroud)) {
            try {
                setColor = Color.parseColor(backgroud);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return setColor;
    }

    // 字符串是否为空
    public static boolean isNullByString(String str) {
        return str == null || "".equals(str) || "null".equals(str);
    }

    public static final String getDoubleToStringFilterZero(@Nullable Double d) {
        if(d == null) {
            return "0";
        }
        try {
            DecimalFormat df = new DecimalFormat("#############.###################");
            DecimalFormatSymbols dfs = new DecimalFormatSymbols();
            dfs.setDecimalSeparator('.');
            df.setDecimalFormatSymbols(dfs);
            String childPrice = df.format(d);
            return childPrice;
        } catch (Exception e) {
//            JdCrashReport.postCaughtException(e);
            e.printStackTrace();
        }
        return String.valueOf(d);
    }

    public static double divideNum(double v1, double v2) {
        double result = 0;
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        DecimalFormat format = new DecimalFormat("0.0");
        format.setRoundingMode(RoundingMode.HALF_UP);
        result = b1.divide(b2, 1, BigDecimal.ROUND_HALF_EVEN).doubleValue();
        return result;
    }

    /**
     * 优惠券 拼接顶部文案
     *
     * @param couponType 优惠券类型
     * @param amount     优惠金额
     * @param needMoney  需要金额
     * @return
     */
    public static String getCouponTips(Context activity, String couponType, String amount, String needMoney, String simpleRule) {
        String couponStr;
        if ("1".equals(couponType)) {
            couponStr = activity.getString(R.string.sf_floor_core_good_use_daijin_sale, amount);
        } else if ("2".equals(couponType) && !StringUtil.isNullByString(needMoney) && !StringUtil.isNullByString(amount)) {
            couponStr = activity.getString(R.string.sf_floor_core_good_use_enough_sale, needMoney, amount);
        } else if ("3".equals(couponType)) {
            couponStr = activity.getString(R.string.sf_floor_core_good_use_send_sale);
        } else if (!TextUtils.isEmpty(simpleRule)) {
            couponStr = "以下商品可使用" + simpleRule + "优惠券";
        } else {
            couponStr = activity.getString(R.string.sf_floor_core_good_use_sale);
        }
        return couponStr;
    }
}
