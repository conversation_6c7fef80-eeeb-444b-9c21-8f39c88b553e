package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;

/**
 * BaseData简介
 * 业务数据基类
 *
 * <AUTHOR>
 * @date 2019-12-6 13:50:26
 */
public class BaseData implements Serializable {

    /**
     * 业务请求是否成功
     */
    private boolean success;

    /**
     * 业务状态
     */
    private int businessCode;

    /**
     * 提示文案
     */
    private String msg;


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(int businessCode) {
        this.businessCode = businessCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
