package com.xstore.sdk.floor.floorcore.utils;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewParent;
import android.widget.Gallery;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.xstore.sevenfresh.service.sflog.SFLogCollector;

public class ViewUtil {

    public static void gone(View... viewList) {
        for (View view : viewList) {
            if (null != view && view.getVisibility() != View.GONE) {
                view.setVisibility(View.GONE);
            }
        }
    }

    public static void visible(View... viewList) {
        for (View view : viewList) {
            if (null != view && view.getVisibility() != View.VISIBLE) {
                view.setVisibility(View.VISIBLE);
            }
        }
    }

    public static void invisible(View... viewList) {
        for (View view : viewList) {
            if (null != view && view.getVisibility() != View.INVISIBLE) {
                view.setVisibility(View.INVISIBLE);
            }
        }
    }
    @Deprecated
    public static boolean isgone(View view) {
        if(view == null || view.getVisibility() != View.GONE){
            return false;
        }
        return true;
    }
    @Deprecated
    public static boolean isvisible(View view) {
        if(view == null || view.getVisibility() != View.VISIBLE){
            return false;
        }
        return true;
    }
    @Deprecated
    public static boolean isinvisible(View view) {
        if(view == null || view.getVisibility() != View.INVISIBLE){
            return false;
        }
        return true;
    }

    public static void alignGalleryToLeft(View parentView, Gallery gallery,
                                          int itemWidth, int spacing) {
        int galleryWidth = parentView.getWidth();// 得到Parent控件的宽度
        // 在这边我们必须先从资源尺寸中得到子控件的宽度跟间距，因为:
        // 1. 在运行时，我们无法得到间距(因为Gallery这个类，没有这样的权限)
        // 2.有可能在运行得宽度的时候，item资源还没有准备好。
        int offset = 0;
        if (galleryWidth <= itemWidth) {
            offset = galleryWidth / 2 - itemWidth / 2 - spacing;
        } else {
            offset = galleryWidth - itemWidth - 2 * spacing;
        }

        // 现在就可以根据更新的布局参数设置做对其了。
        MarginLayoutParams mlp = (MarginLayoutParams) gallery.getLayoutParams();
        mlp.setMargins(-offset, mlp.topMargin, mlp.rightMargin,
                mlp.bottomMargin);
    }

    /**
     * 设置TextView右侧图标和字体颜色
     *
     * @param context
     * @param view
     * @param right
     * @param color
     */
    public static void setViewRightDrawableAndTextColor(Context context,
                                                        TextView view, int right, int color) {
        view.setTextColor(context.getResources().getColor(color));
        setViewDrawable(context, view, 0, 0, right, 0);
    }

    /**
     * 设置TextView图标
     *
     * @param context
     * @param view
     * @param left
     * @param top
     * @param right
     * @param bottom
     */
    public static void setViewDrawable(Context context, TextView view, int left,
                                       int top, int right, int bottom) {
        Drawable leftDrawable = null, topDrawable = null, rightDrawable = null, bottomDrawable = null;
        if (left > 0) {
            leftDrawable = context.getResources().getDrawable(left);
            leftDrawable.setBounds(0, 0, leftDrawable.getMinimumWidth(),
                    leftDrawable.getMinimumHeight());
        }
        if (top > 0) {
            topDrawable = context.getResources().getDrawable(top);
            topDrawable.setBounds(0, 0, topDrawable.getMinimumWidth(),
                    topDrawable.getMinimumHeight());
        }
        if (right > 0) {
            rightDrawable = context.getResources().getDrawable(right);
            rightDrawable.setBounds(0, 0, rightDrawable.getMinimumWidth(),
                    rightDrawable.getMinimumHeight());
        }
        if (bottom > 0) {
            bottomDrawable = context.getResources().getDrawable(bottom);
            bottomDrawable.setBounds(0, 0, bottomDrawable.getMinimumWidth(),
                    bottomDrawable.getMinimumHeight());
        }
        view.setCompoundDrawables(leftDrawable, topDrawable, rightDrawable,
                bottomDrawable);
    }

    /**
     * 执行动画
     *
     * @param view
     */
    public static void startBackgroundAnimation(View view) {
        AnimationDrawable animationDrawable = (AnimationDrawable) view.getBackground();
        animationDrawable.start();
    }

    /**
     * 执行动画
     *
     * @param view
     */
    public static void startBackgroundAnimation(View view, int animo) {
        view.setBackgroundResource(animo);
        startBackgroundAnimation(view);
    }

//    /**
//     * 设置字体
//     *
//     * @param context
//     * @param v
//     * @param dis
//     */
//    public static void setSpecialModeTextSize(Context context, View v, int dis) {
//        if ("SCH-N719".equals(DeviceUtil.getModel(context))) {
//            if (v instanceof TextView) {
//                ((TextView) v).setTextSize(TypedValue.COMPLEX_UNIT_DIP, dis);
//            } else if (v instanceof Button) {
//                ((Button) v).setTextSize(TypedValue.COMPLEX_UNIT_DIP, dis);
//            }
//        }
//    }

//    /**
//     * 是否是特殊机型
//     *
//     * @param context
//     * @return
//     */
//    public static boolean isSpecialMode(Context context) {
//        if ("SCH-N719".equals(DeviceUtil.getModel(context))) {
//            return true;
//        } else {
//            return false;
//        }
//    }

    public static void setListViewHeightBasedOnChildren(ListView listView) {

        // 获取ListView对应的Adapter

        ListAdapter listAdapter = listView.getAdapter();

        if (listAdapter == null) {
            return;
        }
        int totalHeight = 0;

        for (int i = 0; i < listAdapter.getCount(); i++) { // listAdapter.getCount()返回数据项的数目
            View listItem = listAdapter.getView(i, null, listView);
            //listItem.measure(0, 0); // 计算子项View 的宽高
            int desiredWidth = View.MeasureSpec.makeMeasureSpec(listView.getWidth(), View.MeasureSpec.AT_MOST);
            listItem.measure(desiredWidth, 0);
            totalHeight += listItem.getMeasuredHeight(); // 统计所有子项的总高度

        }
        SFLogCollector.e("listview====height",totalHeight+"===="+listAdapter.getCount());
        ViewGroup.LayoutParams params = listView.getLayoutParams();

        params.height = totalHeight
                + (listView.getDividerHeight() * (listAdapter.getCount() - 1));
        // listView.getDividerHeight()获取子项间分隔符占用的高度
        // params.height最后得到整个ListView完整显示需要的高度
        listView.setLayoutParams(params);
    }

    public static void removeSelfFromParent(View view) {
        if (view != null) {
            ViewParent parent = view.getParent();
            if (parent != null && parent instanceof ViewGroup) {
                ViewGroup group = (ViewGroup) parent;
                group.removeView(view);
            }
        }
    }

    /**
     * 扩大View的触摸和点击响应范围,最大不超过其父View范围
     *
     * @param view
     * @param top
     * @param bottom
     * @param left
     * @param right
     */
    public static void expandViewTouchDelegate(final View view, final int top,
                                               final int bottom, final int left, final int right) {

        ((View) view.getParent()).post(new Runnable() {
            @Override
            public void run() {
                Rect bounds = new Rect();
                view.setEnabled(true);
                view.getHitRect(bounds);

                bounds.top -= top;
                bounds.bottom += bottom;
                bounds.left -= left;
                bounds.right += right;

                TouchDelegate touchDelegate = new TouchDelegate(bounds, view);

                if (View.class.isInstance(view.getParent())) {
                    ((View) view.getParent()).setTouchDelegate(touchDelegate);
                }
            }
        });
    }

    /**
     * 还原View的触摸和点击响应范围,最小不小于View自身范围
     *
     * @param view
     */
    public static void restoreViewTouchDelegate(final View view) {

        ((View) view.getParent()).post(new Runnable() {
            @Override
            public void run() {
                Rect bounds = new Rect();
                bounds.setEmpty();
                TouchDelegate touchDelegate = new TouchDelegate(bounds, view);

                if (View.class.isInstance(view.getParent())) {
                    ((View) view.getParent()).setTouchDelegate(touchDelegate);
                }
            }
        });
    }

    /**
     * 判断当前点击的位置是否能落在这个view当中
     *
     * @param rangeView 需要判定的view
     * @param ev 手指触摸事件
     * @return
     */
    public static boolean inViewRange(View rangeView, MotionEvent ev) {
        if (rangeView == null || rangeView.getWidth() == 0 && rangeView.getHeight() == 0 || ev == null) {
            return false;
        }
        int[] location = new int[2];
        rangeView.getLocationOnScreen(location);

        int rangeLeft = location[0];
        int rangeRight = rangeView.getWidth() + rangeLeft;
        int rangeTop = location[1];
        int rangeBottom = rangeView.getHeight() + rangeTop;

        if (ev.getX() >= rangeLeft &&
                ev.getX() <= rangeRight &&
                ev.getY() >= rangeTop &&
                ev.getY() <= rangeBottom) {
            SFLogCollector.d("lsp", "in range");
            return true;
        }
        return false;
    }

}
