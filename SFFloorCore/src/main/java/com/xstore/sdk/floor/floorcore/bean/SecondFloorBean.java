package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;

/**
 * 二楼数据模型
 * todo 【暂不处理】 就先扔这里了
 */
public class SecondFloorBean implements Serializable {
//     宽高比字段不用 直接剪裁
//     imageAspect

    /**
     * 二楼图片
     */
    private String imageSrc;

    /**
     * 跳转链接
     */
    private FloorAction actionVo;

    public String getImageSrc() {
        return imageSrc;
    }

    public void setImageSrc(String imageSrc) {
        this.imageSrc = imageSrc;
    }

    public void setActionVo(FloorAction actionVo) {
        this.actionVo = actionVo;
    }

    public FloorAction getActionVo() {
        return actionVo;
    }
}
