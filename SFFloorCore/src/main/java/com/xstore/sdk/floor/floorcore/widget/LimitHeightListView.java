package com.xstore.sdk.floor.floorcore.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ListView;

import com.xstore.floorsdk.floorcore.R;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;


/**
 * Created by lishupeng1 on 2017/6/28.
 */

public class LimitHeightListView extends ListView {
    private int limitHeight = 0;
    public LimitHeightListView(Context context) {
        super(context);
        init(null);
    }

    public LimitHeightListView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public LimitHeightListView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if(attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.sf_floor_core_LimitHeightListView);
            limitHeight = a.getDimensionPixelSize(R.styleable.sf_floor_core_LimitHeightListView_sf_floor_core_max_height, 0);
            a.recycle();
        }
        if(limitHeight == 0) {
            int height = ScreenUtils.getScreenHeight(getContext()) / 2;
            if(height > 0) {
                limitHeight = height;
            }
        }
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec
                ,MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE>>2,MeasureSpec.AT_MOST));
        if(limitHeight > 0) {
            int currentHeight = getMeasuredHeight();
            if(currentHeight > limitHeight) {
                setMeasuredDimension(getMeasuredWidth(), limitHeight);
            }
        }
    }

    public int getLimitHeight() {
        return limitHeight;
    }

    public void setLimitHeight(int limitHeight) {
        this.limitHeight = limitHeight;
    }
}
