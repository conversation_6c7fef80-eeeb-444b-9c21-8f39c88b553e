
package com.xstore.sdk.floor.floorcore.widget;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnDismissListener;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.SystemClock;
import android.text.Html;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;

import com.xstore.floorsdk.floorcore.R;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.ThreadUtils;


/**
 * 弹出 Dialog 工具类 (Description)
 *
 * <AUTHOR>
public class DialogUtils {
    public static final int BUTTON_DONE = 1;
    public static final int BUTTON_NEGATIVE = 2;
    public static final int BUTTON_POSITIVE = 3;

    public static CustomDialogBuilder showDialog(Context context) {
        return new CustomDialogBuilder(context);
    }

    public static Dialog createProgressDialog(Context context) {
        return createProgressDialog(context, true);
    }

    public static Dialog createProgressDialog(Context context, boolean needCancle) {
        Dialog dialog = new Dialog(context, R.style.sf_floor_core_ProgressDialogStyle);
        dialog.setContentView(R.layout.sf_floor_core_widget_custom_loading);
//        Drawable drawableProgress = context.getResources().getDrawable(R.drawable.new_refresh_progress_style);
//        dialog.setIndeterminateDrawable(drawableProgress);
//        dialog.setMessage("加载中...");
        dialog.setCancelable(needCancle);
        dialog.setCanceledOnTouchOutside(false);
        return dialog;
    }

    /**
     * 自定义弹出窗 创建辅助类 (Description)
     *
     * <AUTHOR>
     */
    public final static class CustomDialogBuilder {
        /**
         * 默认样式，切换为 ufo
         **/
        private static final int DEFAULT_STYLE = R.style.sf_floor_core_dialog_loading;
        //		private static final int DEFAULT_STYLE = R.style.dialog_loading;
        private Context mContext;
        private CustomDialogConfig mDlgConfig;
        private ConfigProperty mDlgConfigPropery;

        CustomDialogBuilder(Context context) {
            mContext = context;
            mDlgConfig = new CustomDialogConfig(context, DEFAULT_STYLE);
            mDlgConfigPropery = mDlgConfig.mConfigProperty;
        }

        public CustomDialogBuilder setTitle(CharSequence title) {
            mDlgConfigPropery.title = title;
            return this;
        }
        public CustomDialogBuilder setTitleColor(int titleColor) {
            mDlgConfigPropery.titleTextColor = titleColor;
            return this;
        }
        public CustomDialogBuilder setTitleSize(int titleSize) {
            mDlgConfigPropery.titleTextSize = titleSize;
            return this;
        }
        public CustomDialogBuilder setTitleBold(boolean titleBold) {
            mDlgConfigPropery.titleBold = titleBold;
            return this;
        }
        public CustomDialogBuilder setMessageSize(int messageSize) {
            mDlgConfigPropery.messageTextSize = messageSize;
            return this;
        }
        public CustomDialogBuilder setMessageColor(int messageColor) {
            mDlgConfigPropery.messageTextColor = messageColor;
            return this;
        }

        public CustomDialogBuilder setTitle(int resId) {
            return setTitle(mContext.getString(resId));
        }

        public CustomDialogBuilder setPositiveButton(String text, OnClickListener clk) {
            mDlgConfigPropery.positiveButtonText = text;
            mDlgConfigPropery.positiveClickListener = clk;
            return this;
        }

        public CustomDialogBuilder setPositiveButton(int res, OnClickListener clk) {
            return setPositiveButton(mContext.getString(res), clk);
        }

        public CustomDialogBuilder setPositiveButton(int res, OnClickListener clk, @ColorInt int color) {
            if (color != 0) {
                mDlgConfigPropery.positiveButtonTextColor = color;
            }
            return setPositiveButton(mContext.getString(res), clk);
        }

        public CustomDialogBuilder setNegativeButton(String text, OnClickListener clk) {
            mDlgConfigPropery.negativeButtonText = text;
            mDlgConfigPropery.negativeClickListener = clk;
            return this;
        }

        public CustomDialogBuilder setNegativeButton(int res, OnClickListener clk) {
            return setNegativeButton(mContext.getString(res), clk);
        }

        public CustomDialogBuilder setNegativeButton(int res, OnClickListener clk, @ColorInt int color) {
            if (color != 0) {
                mDlgConfigPropery.negativeButtonTextColor = color;
            }
            return setNegativeButton(res, clk);
        }

        public CustomDialogBuilder setStyle(int style) {
            mDlgConfigPropery.dialogStyle = style;
            return this;
        }

        public CustomDialogBuilder setDoneButton(String text, OnClickListener clk) {
            mDlgConfigPropery.doneButtonText = text;
            mDlgConfigPropery.doneClickListener = clk;
            return this;
        }

        public CustomDialogBuilder setDoneButton(String text) {
            mDlgConfigPropery.doneButtonText = text;
            return this;
        }

        public CustomDialogBuilder setDoneButton(int text, OnClickListener clk) {
            return setDoneButton(mContext.getString(text), clk);
        }

        public CustomDialogBuilder setDoneButton(int res, OnClickListener clk, @ColorInt int color) {
            if (color != 0) {
                mDlgConfigPropery.doneButtonTextColor = color;
            }
            return setDoneButton(res, clk);
        }

        public CustomDialogBuilder setCancelable(boolean cancelable) {
            mDlgConfigPropery.cancelable = cancelable;
            return this;
        }

        public CustomDialogBuilder setTitleIcon(int icon) {
            mDlgConfigPropery.titleIcon = icon;
            return this;
        }

        public CustomDialogBuilder setMessage(String msg) {
            mDlgConfigPropery.message = msg;
            return this;
        }

        public CustomDialogBuilder setIsLoading(boolean loading) {
            mDlgConfigPropery.isLoading = loading;
            return this;
        }

        public CustomDialogBuilder setMessage(int msg) {
            return setMessage(mContext.getString(msg));
        }

        public CustomDialogBuilder setCenterTitle(boolean isCenter) {
            mDlgConfigPropery.isCenter = isCenter;
            return this;
        }
        public CustomDialogBuilder setCenterMessage(boolean isCenter) {
            mDlgConfigPropery.isMessageCenter = isCenter;
            return this;
        }
        public CustomDialogBuilder setMessageBold(boolean isBold) {
            mDlgConfigPropery.isMessageBold = isBold;
            return this;
        }
        public CustomDialogBuilder setMessageSpanned(boolean isSpanned) {
            mDlgConfigPropery.isMessageSpanned = isSpanned;
            return this;
        }

        public void setDoneButtonEnabledText(String str) {
            if (mDlgConfig.btnDone != null) {
                mDlgConfig.btnDone.setEnabled(false);
                if (!TextUtils.isEmpty(str)){
                    mDlgConfig.btnDone.setText(str);
                }
                mDlgConfig.btnDone.setTextColor(ContextCompat.getColor(mContext, R.color.sf_floor_core_app_gray));
            }

        }

        public CustomDialogBuilder setDowning(boolean isDowning) {
            if (mDlgConfig.progressBar != null) {
                if (isDowning) {
                    mDlgConfig.progressBar.setVisibility(View.VISIBLE);
                    mDlgConfig.tvApkTotal.setVisibility(View.VISIBLE);
                    mDlgConfig.tvApkTotalPercent.setVisibility(View.VISIBLE);

                } else {
                    mDlgConfig.progressBar.setVisibility(View.GONE);
                    mDlgConfig.tvApkTotal.setVisibility(View.GONE);
                    mDlgConfig.tvApkTotalPercent.setVisibility(View.GONE);
                }
            }
            return this;
        }

        public CustomDialogBuilder setLittleMessage(String msg) {
            mDlgConfigPropery.littleMessage = msg;
            return this;
        }

        public CustomDialogBuilder setTitleBottomDrawable(int drawableId) {
            mDlgConfigPropery.titleDrawableBottomId = drawableId;
            return this;
        }

        public CustomDialogBuilder setMessageBottomDrawable(int drawableBottomId) {
            mDlgConfigPropery.messageDrawableBottomId = drawableBottomId;
            return this;
        }

        public CustomDialogBuilder setDoneButtonEnabled(boolean doneButtonEnabled) {
            mDlgConfigPropery.doneButtonEnabled = doneButtonEnabled;
            return this;
        }

        public CustomDialogBuilder setNegativeButtonEnabled(boolean negativeButtonEnabled) {
            mDlgConfigPropery.negativeButtonEnabled = negativeButtonEnabled;
            return this;
        }

        public CustomDialogBuilder setPositiveButtonEnabled(boolean positiveButtonEnabled) {
            mDlgConfigPropery.positiveButtonEnabled = positiveButtonEnabled;
            return this;
        }

        public CustomDialogBuilder setDimBackground(boolean dimBackground) {
            mDlgConfigPropery.dimBackground = dimBackground;
            return this;
        }

        public CustomDialogBuilder setDismissMilliseond(int millisecond) {
            mDlgConfigPropery.dismissDelayMillisecond = millisecond;
            return this;
        }

        public CustomDialogBuilder setDismissListener(OnDismissListener dismissListener) {
            mDlgConfigPropery.dismissListener = dismissListener;
            return this;
        }

        public CustomAlertDialog build() {
            return mDlgConfig.create();
        }

    }

    /**
     * 自定义对话框窗口
     *
     * <AUTHOR>
     */
    public static class CustomAlertDialog extends Dialog {
        private Context mContext;
        private CustomDialogConfig mDlgConfig;
        private long mDismissedAtTime;
        /**
         * 默认禁用系统的回车键
         */
        private boolean mEnableEnter;

        public CustomAlertDialog(Context context, int theme, CustomDialogConfig config) {
            super(context, theme);
            this.mContext = context;
            this.mDlgConfig = config;
            //
            //在 4.4 之下 ，windowMinWidthMajor 属性无效
            //强制改变窗口属性为 wrap_content
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.KITKAT) {
//                final DisplayMetrics metrics = getContext().getResources().getDisplayMetrics();
//                final boolean isPortrait = metrics.widthPixels < metrics.heightPixels;
                getWindow().getDecorView();
                getWindow().setLayout(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            }
        }

        public void setEnableEnterKey(boolean enable) {
            mEnableEnter = enable;
        }

        public void reCreate(final CustomDialogBuilder builder) {
            long delayMillis = getDismissDelayTime();
            if (delayMillis < 1) {
                mDlgConfig.reCreate(builder.mDlgConfig);
            } else {
                ThreadUtils.getMainHandler().postDelayed(new Runnable() {

                    @Override
                    public void run() {
                        mDlgConfig.reCreate(builder.mDlgConfig);
                    }
                }, delayMillis);
            }
        }

        @Override
        public void show() {
            super.show();
            mDismissedAtTime = SystemClock.uptimeMillis() + mDlgConfig.mConfigProperty.dismissDelayMillisecond;
        }

        /**
         * 返回 关闭窗口 的延迟时间
         *
         * @return
         * @date 2016年3月2日
         */
        private long getDismissDelayTime() {
            return mDismissedAtTime - SystemClock.uptimeMillis();
        }

        @Override
        public void dismiss() {
            //如果没有设置 延迟关闭，则立即关闭窗口
            if (mDlgConfig.mConfigProperty.dismissDelayMillisecond == 0) {
                super.dismiss();
            } else {
                ThreadUtils.getMainHandler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        internalDismiss();
                    }
                }, getDismissDelayTime());
            }
        }

        //无论没有设置 延迟关闭，都立即关闭窗口
        public void dismissNow() {
            super.dismiss();
        }

        private void internalDismiss() {
            super.dismiss();
        }

        @Override
        public boolean dispatchKeyEvent(KeyEvent event) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
                // 回车键
                if (!mEnableEnter) {
                    return true;
                }
            }
            return super.dispatchKeyEvent(event);
        }

        public void setPositiveButtonEnabled(boolean enabled) {
            if (mDlgConfig.btnPositive != null) {
                mDlgConfig.btnPositive.setEnabled(enabled);
            }
        }

//        public void setDownloadingProgress(int max, int progress) {
//            if (mDlgConfig.progressBar != null) {
//                mDlgConfig.progressBar.setMax(max);
//                mDlgConfig.progressBar.setProgress(progress);
//                if (mDlgConfig.tvApkTotal != null && max > 0) {
//                    mDlgConfig.tvApkTotal.setText(DataCleanManager.getFormatSize((double) max));
//                }
//                if (mDlgConfig.tvApkTotalPercent != null && !TextUtils.isEmpty(NumberUtils.myPercent(progress, max))) {
//                    mDlgConfig.tvApkTotalPercent.setText(NumberUtils.myPercent(progress, max));
//                }
//            }
//
//        }

        public void setNegativeButtonEnabled(boolean enabled) {
            if (mDlgConfig.btnNegative != null) {
                mDlgConfig.btnNegative.setEnabled(enabled);
            }

        }

        public void setDoneButtonEnabled(boolean enabled) {
            if (mDlgConfig.btnDone != null) {
                mDlgConfig.btnDone.setEnabled(enabled);
            }

        }
        public void setDoneButtonEnabledText(String str) {
            if (mDlgConfig.btnDone != null) {
                mDlgConfig.btnDone.setEnabled(false);
                if (!TextUtils.isEmpty(str)) {
                    mDlgConfig.btnDone.setText(str);
                }
                mDlgConfig.btnDone.setTextColor(ContextCompat.getColor(mContext, R.color.sf_floor_core_app_gray));
            }

        }


//        public void requestFocse() {
//            mDlgConfig.etInput.requestFocus();
//        }
//
//        public void setInputEnable(boolean enable) {
//            mDlgConfig.etInput.setVisibility(enable ? View.VISIBLE : View.GONE);
//        }
    }

    private static class ConfigProperty {

        int titleIcon;
        int dialogStyle;
        boolean cancelable = true;
        boolean isCenter = true;
        boolean isMessageCenter = false;
        boolean isMessageSpanned = false;//显示时是否Html.fromHtml()处理
        boolean isMessageBold = false;
        boolean isDowning = false;
        CharSequence title;
        int titleDrawableBottomId;
        int messageDrawableBottomId;
        String littleMessage;
        String message;
        String positiveButtonText;
        String negativeButtonText;
        String doneButtonText;
        OnClickListener positiveClickListener;
        OnClickListener negativeClickListener;
        OnClickListener doneClickListener;
        OnDismissListener dismissListener;
        boolean doneButtonEnabled = true;
        boolean negativeButtonEnabled = true;
        boolean positiveButtonEnabled = true;
        boolean dimBackground = false;
        boolean isLoading;
        int dismissDelayMillisecond;
        int positiveButtonTextColor;
        int negativeButtonTextColor;
        int doneButtonTextColor;
        int titleTextColor;
        int titleTextSize;
        boolean titleBold;
        int messageTextColor;
        int messageTextSize;
    }

    private static class CustomDialogConfig implements View.OnClickListener {
        Context context;
        ConfigProperty mConfigProperty;
        CustomAlertDialog dialog;
        EditText etInput;
        TextView tvMsg;
        TextView tvTitle;
        TextView tvLittleMsg;
        TextView tvApkTotal;
        TextView tvApkTotalPercent;
        ImageView ivDialog;
        RelativeLayout rlTitleBar;
        Button btnDone;
        Button btnPositive;
        Button btnNegative;
        View maskView;
        View dialogDivide;
        ProgressBar progressBar;

        public CustomDialogConfig(Context context, int style) {
            this.context = context;
            mConfigProperty = new ConfigProperty();
            mConfigProperty.dialogStyle = style;
        }

        /**
         * 重置配置文件
         **/
        private void resetConfig(CustomDialogConfig newConfig) {
            mConfigProperty = newConfig.mConfigProperty;
            newConfig.mConfigProperty = null;

            progressBar = null;
            etInput = null;
            tvMsg = null;
            tvTitle = null;
            ivDialog = null;
            tvLittleMsg = null;
            rlTitleBar = null;
            btnDone = null;
            btnPositive = null;
            btnNegative = null;
            btnDone = null;
            dialogDivide = null;
        }

        public CustomAlertDialog create() {
            dialog = new CustomAlertDialog(context, mConfigProperty.dialogStyle, this);
            View layout = initView();
            repairDialog(dialog, layout);
            return dialog;
        }

        private View initView() {
            View layout;
            if (mConfigProperty.isLoading) {
                layout = LayoutInflater.from(context).inflate(R.layout.sf_floor_core_widget_custom_loading, null);
            } else {
                layout = LayoutInflater.from(context).inflate(R.layout.sf_floor_core_widget_dialog_alert, null);
            }
            layout = LayoutInflater.from(context).inflate(R.layout.sf_floor_core_widget_dialog_alert, null);
            return layout;
        }

        public CustomAlertDialog reCreate(CustomDialogConfig newConfig) {
            resetConfig(newConfig);
            repairDialog(dialog, initView());
            return dialog;
        }

        public void repairDialog(Dialog dialog, View contentView) {

            // 如果有进度条
            if (mConfigProperty.isDowning) {
                progressBar = (ProgressBar) contentView.findViewById(R.id.progressBar);
                if (progressBar != null) {
                    progressBar.setVisibility(View.VISIBLE);
                    // fixes pre-Lollipop progressBar indeterminateDrawable tinting
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                        Drawable wrapDrawable = DrawableCompat.wrap(progressBar.getIndeterminateDrawable());
                        DrawableCompat.setTint(wrapDrawable, ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
                        progressBar.setIndeterminateDrawable(DrawableCompat.unwrap(wrapDrawable));
                    } else {
                        progressBar.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(context, R.color.sf_theme_color_level_1), PorterDuff.Mode.SRC_IN);
                    }
                }

            } else {
                progressBar = (ProgressBar) contentView.findViewById(R.id.progressBar);
                tvApkTotal = (TextView) contentView.findViewById(R.id.tv_apk_total);
                tvApkTotalPercent = (TextView) contentView.findViewById(R.id.tv_apk_total_percent);


                // fixes pre-Lollipop progressBar indeterminateDrawable tinting
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                    Drawable wrapDrawable = DrawableCompat.wrap(progressBar.getIndeterminateDrawable());
                    DrawableCompat.setTint(wrapDrawable, ContextCompat.getColor(context,R.color.sf_theme_color_level_1));
                    progressBar.setIndeterminateDrawable(DrawableCompat.unwrap(wrapDrawable));
                } else {
                    progressBar.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(context, R.color.sf_theme_color_level_1), PorterDuff.Mode.SRC_IN);
                }
                if (progressBar != null) {
                    progressBar.setVisibility(View.GONE);
                    tvApkTotal.setVisibility(View.GONE);
                    tvApkTotalPercent.setVisibility(View.GONE);
                }
            }
            // 如果有标题
            if (mConfigProperty.title != null) {
                tvTitle = (TextView) contentView.findViewById(R.id.tv_title);
                if (tvTitle != null) {
                    if(mConfigProperty.titleTextColor!=0){
                        tvTitle.setTextColor(mConfigProperty.titleTextColor);
                    }
                    tvTitle.setText(mConfigProperty.title);
                    if (mConfigProperty.isCenter) {
                        tvTitle.setGravity(Gravity.CENTER);
                        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13); //14SP
                    } else {
                        tvTitle.setGravity(Gravity.LEFT);
                        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15); //15SP
                    }
                    if(mConfigProperty.titleTextSize!=0){
                        tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, mConfigProperty.titleTextSize); //14SP
                    }
                    if(mConfigProperty.titleBold){
                        tvTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                    }
                }
            } else {
                tvTitle = (TextView) contentView.findViewById(R.id.tv_title);
                if (tvTitle != null) {
                    tvTitle.setVisibility(View.GONE);
                }
            }

            // 如果有标题
            if (mConfigProperty.titleIcon != 0) {
                ivDialog = (ImageView) contentView.findViewById(R.id.iv_dialog_title);
                int width = ScreenUtils.dip2px(context, 24);
                int height = ScreenUtils.dip2px(context, 24);
                if (mConfigProperty.title != null) {
                    width = ScreenUtils.dip2px(context, 35);
                    height = ScreenUtils.dip2px(context, 35);
                }
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(width, height);
                if (ivDialog != null) {
                    ivDialog.setLayoutParams(params);
                    ivDialog.setBackgroundResource(mConfigProperty.titleIcon);
                }

            } else {
                ivDialog = (ImageView) contentView.findViewById(R.id.iv_dialog_title);
                ivDialog.setVisibility(View.GONE);
            }
            // 如果有消息提示
            if (mConfigProperty.message != null) {
                tvMsg = null;
                tvMsg = (TextView) contentView.findViewById(R.id.tv_message);
                if (tvMsg != null) {
                    if (mConfigProperty.isMessageSpanned) {
                        try {
                            tvMsg.setText(Html.fromHtml(mConfigProperty.message));
                        } catch (Exception e) {
                            tvMsg.setText(mConfigProperty.message);
                        }
                    } else {
                        tvMsg.setText(mConfigProperty.message);
                    }
                    tvMsg.setVisibility(View.VISIBLE);
                    if(mConfigProperty.messageTextColor!=0){
                        tvMsg.setTextColor(mConfigProperty.messageTextColor);
                    }
                    if(mConfigProperty.messageTextSize!=0){
                        tvMsg.setTextSize(TypedValue.COMPLEX_UNIT_DIP, mConfigProperty.messageTextSize);
                    }
                    if (mConfigProperty.isMessageCenter) {
                        tvMsg.setGravity(Gravity.CENTER);
                    } else {
                        tvMsg.setGravity(Gravity.LEFT);
                    }
                    if (mConfigProperty.isMessageBold) {
                        tvMsg.getPaint().setFakeBoldText(true);
                    } else {
                        tvMsg.getPaint().setFakeBoldText(false);
                    }
                    if (mConfigProperty.messageDrawableBottomId != 0){
                        tvMsg.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, mConfigProperty.messageDrawableBottomId);
                    }
                }
            } else {
                tvMsg = null;
                tvMsg = (TextView) contentView.findViewById(R.id.tv_message);
                if (tvMsg != null) {
                    tvMsg.setVisibility(View.GONE);
                }
            }
            //
            if (!mConfigProperty.isLoading) {
                btnNegative = (Button) contentView.findViewById(R.id.btn_negative);
                btnPositive = (Button) contentView.findViewById(R.id.btn_positive);
                dialogDivide = (View) contentView.findViewById(R.id.view_dialog_portant);
                btnDone = (Button) contentView.findViewById(R.id.btn_done);
                btnNegative.setOnClickListener(this);
                btnPositive.setOnClickListener(this);
                btnDone.setOnClickListener(this);

                if (!TextUtils.isEmpty(mConfigProperty.doneButtonText)) {
                    btnDone.setText(mConfigProperty.doneButtonText);
                    btnDone.setVisibility(View.VISIBLE);
                    dialogDivide.setVisibility(View.GONE);
                    if (mConfigProperty.doneButtonTextColor != 0) {
                        btnDone.setTextColor(mConfigProperty.doneButtonTextColor);
                    }
                } else {
                    if (!TextUtils.isEmpty(mConfigProperty.negativeButtonText)) {
                        btnNegative.setText(mConfigProperty.negativeButtonText);
                        btnNegative.setVisibility(View.VISIBLE);
                        if (mConfigProperty.negativeButtonTextColor != 0) {
                            btnNegative.setTextColor(mConfigProperty.negativeButtonTextColor);
                        }
                    }
                    if (!TextUtils.isEmpty(mConfigProperty.positiveButtonText)) {
                        btnPositive.setText(mConfigProperty.positiveButtonText);
                        btnPositive.setVisibility(View.VISIBLE);
                        if (mConfigProperty.positiveButtonTextColor != 0) {
                            btnPositive.setTextColor(mConfigProperty.positiveButtonTextColor);
                            btnPositive.setTypeface(null, Typeface.BOLD);
                        }
                    }
                    if (!TextUtils.isEmpty(mConfigProperty.positiveButtonText) && !TextUtils.isEmpty(mConfigProperty.negativeButtonText)) {
                        dialogDivide.setVisibility(View.VISIBLE);
                    }
                }
            }


            dialog.setOnDismissListener(new DismissListenerImpl(this));
            dialog.setCancelable(mConfigProperty.cancelable);
            dialog.setCanceledOnTouchOutside(false);
            dialog.setContentView(contentView);
        }

        @Override
        public void onClick(View v) {
            final int id = v.getId();
            if (id == R.id.btn_positive) {
                if (mConfigProperty.positiveClickListener != null) {
                    mConfigProperty.positiveClickListener.onClick(dialog, BUTTON_POSITIVE);
                }
            } else if (id == R.id.btn_negative) {
                if (mConfigProperty.negativeClickListener != null) {
                    mConfigProperty.negativeClickListener.onClick(dialog, BUTTON_NEGATIVE);
                }
            } else if (id == R.id.btn_done) {
                if (mConfigProperty.doneClickListener != null) {
                    mConfigProperty.doneClickListener.onClick(dialog, BUTTON_DONE);
                }
            }
        }

        private void dismissDialog() {
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
                dialog = null;
            }
        }
    }

    /**
     * 窗口 dismiss 回调函数
     *
     * <AUTHOR>
     */
    private static class DismissListenerImpl implements OnDismissListener {
        private CustomDialogConfig mDlgConfig;

        public DismissListenerImpl(CustomDialogConfig config) {
            this.mDlgConfig = config;
        }

        @Override
        public void onDismiss(DialogInterface dialog) {
            // 如果有 dismissListener
            if (mDlgConfig.mConfigProperty.dismissListener != null) {
                mDlgConfig.mConfigProperty.dismissListener.onDismiss(dialog);
            }
            // 隐藏 阴影图片
            if (mDlgConfig.mConfigProperty.dimBackground && mDlgConfig.maskView != null) {
                mDlgConfig.maskView.setVisibility(View.GONE);
            }
        }

    }

//    /**
//     *
//     * @param context 上下文
//     * @param phone 电话号码
//     * @param isPersonalContact 是否是个人中心拨打电话
//     */
//    public static void showDialDialog(final Context context, final String phone, final boolean isPersonalContact) {
//        DialogUtils.showDialog(context).setCancelable(false)
//                .setStyle(R.style.alert)
//                .setTitleColor(ContextCompat.getColor(context, R.color.sf_floor_black))
//                .setCenterMessage(true)
//                .setMessageSize(15)
//                .setMessageBold(true)
//                .setMessage(phone)
//                .setPositiveButton(R.string.fresh_dail, new OnClickListener() {
//
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        DeviceUtils.toPhone(context, phone);
//                        if (isPersonalContact) {
//                            JDMaUtils.saveJDClick(JDMaCommonUtil.PERSONAL_ONLINE_SERVICE, "", "", null, new JDMaUtils.JdMaPageWrapper(context) {
//                                @Override
//                                public void notBaseActivity(Context context) {
//                                    JdCrashReport.postCaughtException(new Exception("DialogUtils.showDialDialog 中context 不是base："+ context));
//                                }
//                            });
//                        }
//                        dialog.dismiss();
//                    }
//                }, ContextCompat.getColor(context, R.color.sf_floor_color_level_1))
//                .setNegativeButton(R.string.fresh_cancel, new OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        dialog.dismiss();
//                        if (isPersonalContact) {
//                            JDMaUtils.saveJDClick(JDMaCommonUtil.PERSONAL_CONTACT, "", "", null, new JDMaUtils.JdMaPageWrapper(context) {
//                                @Override
//                                public void notBaseActivity(Context context) {
//                                    JdCrashReport.postCaughtException(new Exception("DialogUtils.showDialDialog 中context 不是base："+ context));
//                                }
//                            });
//                        }
//                    }
//                }, ContextCompat.getColor(context, R.color.sf_floor_color_level_1))
//                .build().show();
//    }

}
