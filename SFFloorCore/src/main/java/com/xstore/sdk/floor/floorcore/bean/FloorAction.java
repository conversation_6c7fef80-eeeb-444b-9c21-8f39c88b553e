package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;

/**
 * 公共跳转行为
 */
public class FloorAction implements Serializable {

    /**
     * 跳转类型
     */
    private int urlType;

    /**
     * 跳转链接
     */
    private String toUrl;

    /**
     * 默认构造
     */
    public FloorAction(){}

    /**
     * 手动构造
     * @param toUrl
     * @param urlType
     */
    public FloorAction(String toUrl, int urlType) {
        this.toUrl = toUrl;
        this.urlType = urlType;
    }

    public int getUrlType() {
        return urlType;
    }

    public void setUrlType(int urlType) {
        this.urlType = urlType;
    }

    public String getToUrl() {
        return toUrl;
    }

    public void setToUrl(String toUrl) {
        this.toUrl = toUrl;
    }
}
