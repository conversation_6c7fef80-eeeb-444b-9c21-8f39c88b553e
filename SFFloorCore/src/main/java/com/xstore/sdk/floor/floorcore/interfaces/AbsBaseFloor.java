package com.xstore.sdk.floor.floorcore.interfaces;

import android.content.Intent;
import android.view.View;
import android.widget.FrameLayout;

import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 基础楼层抽象类，提供一些默认实现，减轻方法实现压力
 */
public abstract class AbsBaseFloor implements FloorBaseInterface {

    @Override
    public FrameLayout getVideoContainer() {
        return null;
    }

    @Override
    public String getVideoPath() {
        return null;
    }


    @Override
    public void setPlayUI(boolean isPlaying, boolean rendingStart) {

    }

    @Override
    public long getFakeItemId() {
        return 0;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {

    }

    @Override
    public void onResume(boolean hidden) {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onHiddenChange(boolean hidden) {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onScroll(int dx, int dy) {

    }

    @Override
    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {

    }

    @Override
    public void onHeaderMoving(boolean isDragging, float percent, int offset, int headerHeight, int maxDragHeight) {

    }

    @Override
    public void onViewAttachedToWindow() {

    }

    @Override
    public void onViewDetachedFromWindow() {

    }

    @Override
    public void onFloorUpdateEvent(String templateCode, Object json) {

    }

    @Override
    public void setCellingFloor(boolean isCelling) {

    }

    @Override
    public int getFloorHeight() {
        return -1;
    }

    @Override
    public Boolean onFailedToRecycleView() {
        return null;
    }

    @Override
    public void onViewRecycled() {

    }

    @Override
    public RecyclerView getParentRcv() {

        return null;
    }

    @Override
    public RelativeLayout getFloorContainer() {
        return null;
    }



}
