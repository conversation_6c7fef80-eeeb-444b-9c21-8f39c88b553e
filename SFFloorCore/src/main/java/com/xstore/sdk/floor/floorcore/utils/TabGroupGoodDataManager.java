package com.xstore.sdk.floor.floorcore.utils;

import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TabGroupGoodDataManager {


    private int currentTabId;
    private Map<Integer, List<FloorDetailBean>> dataSource = new HashMap<>();

    private Map<Integer, Integer> nextPage = new HashMap<>();

    private static TabGroupGoodDataManager instance = new TabGroupGoodDataManager();

    public static TabGroupGoodDataManager getInstance() {
        return instance;
    }


    public void setCurrentTabId(int tabId) {
        this.currentTabId = tabId;
    }

    public int getCurrentTabId() {
        return currentTabId;
    }


    public List<FloorDetailBean> getData(Integer tabId) {
        return dataSource.get(tabId);
    }

    public void addNextPage(Integer tabId, int page) {
        nextPage.put(tabId, page);
    }

    public int getNextPage() {
        try {
            return nextPage.get(getCurrentTabId());
        } catch (Exception e) {

        }
        return 2;
    }


    public void addData(Integer tabId, FloorDetailBean floorDetailBean) {
        if (dataSource.containsKey(tabId)) {
            if (dataSource.get(tabId) == null) {
                List<FloorDetailBean> floorDetailBeans = new ArrayList<>();
                floorDetailBeans.add(floorDetailBean);
                dataSource.put(tabId, floorDetailBeans);
            } else {
                dataSource.get(tabId).add(floorDetailBean);
            }
        } else {
            List<FloorDetailBean> floorDetailBeans = new ArrayList<>();
            floorDetailBeans.add(floorDetailBean);
            dataSource.put(tabId, floorDetailBeans);
        }
    }

    public void addData(Integer tabId, List<FloorDetailBean> floorDetailBeans) {
        if (dataSource.containsKey(tabId)) {
            dataSource.get(tabId).addAll(floorDetailBeans);
        } else {
            dataSource.put(tabId, floorDetailBeans);
        }
    }

    public void clearData() {
        dataSource.clear();
    }

    public boolean isEmpty() {
        return dataSource.isEmpty();
    }

}
