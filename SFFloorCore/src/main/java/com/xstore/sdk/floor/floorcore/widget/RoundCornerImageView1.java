package com.xstore.sdk.floor.floorcore.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;

/**
 * Created by weichangfa on 2018/2/1.
 */

public class RoundCornerImageView1 extends androidx.appcompat.widget.AppCompatImageView {
    private float[] radiusArray = { 0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f };
    private float rate = 1;

    public RoundCornerImageView1(Context context) {
        super(context);
    }

    public RoundCornerImageView1(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * 设置四个角的圆角半径
     */
    public void setRadius(float leftTop, float rightTop, float rightBottom, float leftBottom) {
        radiusArray[0] = leftTop;
        radiusArray[1] = leftTop;
        radiusArray[2] = rightTop;
        radiusArray[3] = rightTop;
        radiusArray[4] = rightBottom;
        radiusArray[5] = rightBottom;
        radiusArray[6] = leftBottom;
        radiusArray[7] = leftBottom;

        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Path path = new Path();
        path.addRoundRect(new RectF(0, 0, getWidth(), getHeight()), radiusArray, Path.Direction.CW);
        canvas.clipPath(path);
        super.onDraw(canvas);
    }

    private boolean needFreshHeight;
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if(needFreshHeight) {
            int width = getMeasuredWidth();
            int height = (int) (width * rate);
            setMeasuredDimension(width, height);
        }
    }

    public void setRate(float rate) {
        this.rate = rate;
    }

    public void setNeed(boolean need) {
        if (needFreshHeight != need) {
            requestLayout();
        }
        this.needFreshHeight = need;
    }

}
