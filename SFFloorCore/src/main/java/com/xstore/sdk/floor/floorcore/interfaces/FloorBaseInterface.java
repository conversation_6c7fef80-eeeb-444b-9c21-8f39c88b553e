package com.xstore.sdk.floor.floorcore.interfaces;



/**
 * 楼层公共接口
 * FloorViewInterface  ui接口
 * FloorScrollInterface 滚动监听
 * FloorUpdateEvent 楼层间通信
 * FloorExposureInterface 楼层曝光
// * FloorTemplateInterface 用于获取注册楼层模板时的一些信息 比如楼层id
 *
 */
public interface FloorBaseInterface extends FloorViewInterface, FloorScrollInterface,
        FloorUpdateEvent, FloorExposureInterface, FloorLifecycle, VideoHolderInterface {
}
