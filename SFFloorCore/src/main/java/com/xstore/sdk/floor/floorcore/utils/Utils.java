package com.xstore.sdk.floor.floorcore.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.WindowManager;


import androidx.annotation.NonNull;


import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by xuchangqing on 2017/5/17.
 */

public class Utils {

    public static <T> List<T> deepCopy(List<T> src) throws IOException, ClassNotFoundException {

        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }

    public static String getFromAssets(Context context, String fileName) {
        InputStreamReader inputReader = null;
        BufferedReader bufReader = null;
        try {
            inputReader = new InputStreamReader(context.getResources().getAssets().open(fileName));
            bufReader = new BufferedReader(inputReader);
            String line = "";
            String result = "";
            while ((line = bufReader.readLine()) != null) {
                result += line;
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != inputReader) {
                    inputReader.close();
                }
                if (null != bufReader) {
                    bufReader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 获取保留两位小数的价格字串
     *
     * @param price
     * @return
     */
    public static String getPriceStr(BigDecimal price) {
        // 为空或小于等于零时返回空串
        if (null == price || price.min(new BigDecimal(0)).equals(price)) {
            return "0.00";
        }
        // 返回保留两位小数的价格
        else {
            BigDecimal setScale = price.setScale(2, BigDecimal.ROUND_HALF_DOWN);
            return setScale.toString();
        }
    }

    public static String changeData(String date) {
        String newDate = "";
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date dateCurrent = fmt.parse(date);
            newDate = fmt.format(dateCurrent);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return newDate;
    }

    public static String addString(String s1, String s2) {
        if (TextUtils.isEmpty(s1)) {
            s1 = "0";
        }
        if (TextUtils.isEmpty(s2)) {
            s2 = "0";
        }
        return String.valueOf(new BigDecimal(s1).add(new BigDecimal(s2)));
    }

    public static String reduceString(String s1, String s2) {
        if (TextUtils.isEmpty(s1)) {
            s1 = "0";
        }
        if (TextUtils.isEmpty(s2)) {
            s2 = "0";
        }
        return String.valueOf(new BigDecimal(s1).subtract(new BigDecimal(s2)));
    }

    public static int compare(String s1, String s2) {
        try {
            if (TextUtils.isEmpty(s1) || "null".equals(s1)) {
                s1 = "0";
            }
            if (TextUtils.isEmpty(s2) || "null".equals(s2)) {
                s2 = "0";
            }
            return new BigDecimal(s1).compareTo(new BigDecimal(s2));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static String getDate() {
        try {
            Date now = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMddHH");//可以方便地修改日期格式
            return dateFormat.format(now);
        } catch (Exception e) {
            return "";
        }
    }

    public static String multiplyString(String s1, String s2) {
        if (TextUtils.isEmpty(s1)) {
            s1 = "1";
        }
        if (TextUtils.isEmpty(s2)) {
            s2 = "1";
        }
        return String.valueOf(new BigDecimal(s1).multiply(new BigDecimal(s2)));
    }

    /**
     * 设置页面的透明度
     *
     * @param bgAlpha 1表示不透明
     */
    public static void setBackgroundAlpha(Activity activity, float bgAlpha) {
        WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
        lp.alpha = bgAlpha;
        if (bgAlpha == 1) {
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);//不移除该Flag的话,在有视频的页面上的视频会出现黑屏的bug
        } else {
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);//此行代码主要是解决在华为手机上半透明效果无效的bug
        }
        activity.getWindow().setAttributes(lp);
    }

    public static int getMaxValue(int[] firstPosition, int[] lastPositions) {
        int max = 0;
        if (firstPosition != null && firstPosition.length > 0) {
            max = firstPosition[0];
        }
        if (firstPosition != null) {
            for (int value : firstPosition) {
                if (value > max) {
                    max = value;
                }
            }
        }
        if (lastPositions != null) {
            for (int value : lastPositions) {
                if (value > max) {
                    max = value;
                }
            }
        }
        return max;
    }
    public static int getMinValue(int[] firstPosition, int[] lastPositions) {
        int min = 0;
        if (firstPosition != null && firstPosition.length > 0) {
            min = firstPosition[0];
        }
        if (firstPosition != null) {
            for (int value : firstPosition) {
                if (value < min) {
                    min = value;
                }
            }
        }
        if (lastPositions != null) {
            for (int value : lastPositions) {
                if (value < min) {
                    min = value;
                }
            }
        }
        return min;
    }

    public static int getScreenHeight(@NonNull Context context) {
        return context.getResources().getDisplayMetrics().heightPixels;
    }

    public static int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, Resources.getSystem().getDisplayMetrics());
    }

    public static int pxToDp(float px) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_PX, px, Resources.getSystem().getDisplayMetrics());
    }


}
