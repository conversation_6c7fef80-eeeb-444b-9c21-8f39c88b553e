package com.xstore.sdk.floor.floorcore.interfaces;

import android.app.Activity;
import android.content.DialogInterface;
import android.os.Bundle;

import com.xstore.sevenfresh.addressstore.bean.TenantShopInfo;

import java.util.List;

/**
 * 这个地方获取的是一些公共的全局信息
 */
public interface FloorConfig {


    /**
     * @return 获取用户标识
     */
    String getPin();

    /**
     * 跳转页面
     * action行为
     *
     * @param activity
     * @param bundle
     */
    void startPage(Activity activity, Bundle bundle);

    boolean isLogin();

    /**
     * @param toast 弹出文案
     */
    void showToast(String toast);

    /**
     * @return 是否同意了隐私条款
     */
    boolean hasAgreePolicy();

    /**
     * 点击了同意隐私
     */
    void agreePolicy();

    /**
     * 展示同音隐私条款弹框
     *
     * @param activity
     * @param onClickListener
     * @param onClickListener1
     */
    void showSecretDialog(Activity activity, DialogInterface.OnClickListener onClickListener, DialogInterface.OnClickListener onClickListener1);

    /**
     * @return 获取业态值
     */
    String getBizCode();

    /**
     * @return 获取领域接口
     */
    String getFunctionId();

    /**
     * @return todo 临时方案 返回收藏接口名  后续改为接口返回
     */
    String getCollectFunctionId();

    /**
     * @return todo 获取邀请有礼接口 后续改为接口返回
     */
    String getApplyWelfareFunctionId();

    /**
     * @return 获取plus门店列表 todo 后续改为接口返回
     */
    String getPlusShopFunctionId();
    /**
     *
     * @return 获取消息未读数量接口
     */
    String getMessageUnReadCountId();

    /**
     * @return 获取业态名称
     */
    String getBizName();

    /**
     * 展示多门店弹窗
     *
     * @param activity
     * @param tenantShopInfos
     * @param storeChangeCallback
     */
    void showSelectStoreDialog(Activity activity, List<TenantShopInfo> tenantShopInfos, boolean onlyOneIsShow, StoreChangeCallback storeChangeCallback);

    /**
     * 获取商品item是否展示无货找相似
     */
    boolean getProductShowFindSimilar();

    /**
     * @return 是否是云超
     */
    boolean isCloudMarket();

}
