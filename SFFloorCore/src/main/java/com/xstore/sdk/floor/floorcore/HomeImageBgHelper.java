package com.xstore.sdk.floor.floorcore;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.WindowManager;


import com.xstore.sdk.floor.floorcore.utils.StringUtil;

/**
 * 首页沉浸式效果切图帮助类
 */
public class HomeImageBgHelper {

    // 背景图Y轴540px是轮播图背景图的裁剪起点（约定的）
    public static final int FIX_TOP_HEIGHT = 540;
    // 背景图的宽度 固定1125
    public static final int BG_IMAGE_WIDTH = 1125;

    /**
     * 上下文
     */
    private static Context appContext;

    /**
     * 单例
     */
    private static class Holder {
        /**
         * 实例
         */
        private static final HomeImageBgHelper INSTANCE = new HomeImageBgHelper();
    }

    /**
     * 私有构造
     */
    private HomeImageBgHelper() {
    }

    /**
     * @param app 获取实例
     * @return
     */
    public static HomeImageBgHelper getInstance(Context app) {
        appContext = app.getApplicationContext();
        return Holder.INSTANCE;
    }


    /**
     * 获取顶部背景图
     */
    public String getTopBgImage(int targetHeight, String url) {
        if (StringUtil.isEmpty(url)) {
            return url;
        }
        WindowManager wm = (WindowManager) appContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        int targetCropBgHeight = (int) (targetHeight * 1.0f / outMetrics.widthPixels * BG_IMAGE_WIDTH);
        if (targetCropBgHeight > FIX_TOP_HEIGHT) {
            return url + getSuffix(FIX_TOP_HEIGHT, 0, 0);
        } else {
            return url + getSuffix(targetCropBgHeight, 0, FIX_TOP_HEIGHT - targetCropBgHeight);
        }
    }

//    public String getOtherPic(int backHeight, String url) {
//        if (StringUtil.isEmpty(url)) {
//            return url;
//        }
//        WindowManager wm = (WindowManager) appContext.getSystemService(Context.WINDOW_SERVICE);
//        DisplayMetrics outMetrics = new DisplayMetrics();
//        wm.getDefaultDisplay().getMetrics(outMetrics);
//        int targetCropBgHeight = (int) (backHeight * 1.0f / outMetrics.widthPixels * BG_IMAGE_WIDTH);
//        return url + getSuffix(FIX_TOP_HEIGHT, 0, FIX_TOP_HEIGHT);
//    }

    /**
     * 拼接裁剪参数
     *
     * @param height 要获取的高度
     * @param startX 裁剪的x轴起点
     * @param startY 裁剪的y轴起点
     * @return 剪裁用的url
     */
    public String getSuffix(int height, int startX, int startY) {
        StringBuilder stringBuilder = new StringBuilder("!cr_1125x");
        stringBuilder.append(height);
        stringBuilder.append("_");
        stringBuilder.append(startX);
        stringBuilder.append("_");
        stringBuilder.append(startY);
        return stringBuilder.toString();
    }

}
