package com.xstore.sdk.floor.floorcore.utils;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.jd.framework.json.JDJSON;
import com.jingdong.sdk.baseinfo.BaseInfo;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.List;

public class FoldUtils {

    /**
     * @return 判断是否为折叠屏手机
     */
    public static boolean isFoldType(Context context) {
        String enableRecognizeFold = PreferenceUtil.getMobileConfigString("fit-fold-enableRecognizeFold", "true");
        if (!"true".equals(enableRecognizeFold)) {
            return false;
        }
        String model = BaseInfo.getDeviceModel();
        if (!TextUtils.isEmpty(model)) {
            String deviceList = PreferenceUtil.getMobileConfigString("deviceList", "[]");
            try {
                List<String> list = JDJSON.parseArray(deviceList, String.class);
                if (list != null && list.contains(model)) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if ("HUAWEI".equalsIgnoreCase(BaseInfo.getDeviceManufacture()) && context.getPackageManager().hasSystemFeature("com.huawei.hardware.sensor.posture")) {
            return true;
        }
        return false;
    }

    /**
     * @param context
     * @return 当前是否运行在平行视界模式下
     */
    public static boolean isInMagicWindow(Context context) {
        if (context == null) {
            return false;
        }
        String enableRecognizeMagicWindow = PreferenceUtil.getMobileConfigString("enableRecognizeMagicWindow", "true");
        if (!"true".equals(enableRecognizeMagicWindow)) {
            //不去识别的话 就认定他已经在平行世界模式下了
            return true;
        }
        String configuration = context.getResources().getConfiguration().toString();
        return configuration.contains("hwMultiwindow-magic") || configuration.contains("hw-magic-windows") || configuration.contains("miui-magic-windows");
    }


}
