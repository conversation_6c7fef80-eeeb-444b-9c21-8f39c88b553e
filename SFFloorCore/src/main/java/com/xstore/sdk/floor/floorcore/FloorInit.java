package com.xstore.sdk.floor.floorcore;

import android.app.Application;
import android.content.Context;

import com.xstore.sdk.floor.floorcore.interfaces.FloorConfig;


public class FloorInit {

    private static FloorConfig floorConfig;
    private static Context app;


    public static FloorConfig getFloorConfig() {
        return floorConfig;
    }

    public static Context getApplication() {
        return app;
    }

    public static void init(Application app, FloorConfig floorConfig) {
        FloorInit.app = app;
        FloorInit.floorConfig = floorConfig;
    }
}
