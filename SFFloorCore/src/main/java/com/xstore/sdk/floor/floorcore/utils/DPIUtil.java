package com.xstore.sdk.floor.floorcore.utils;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;

import com.boredream.bdcodehelper.utils.DisplayUtils;


public class DPIUtil {

    private static float mDensity = DisplayMetrics.DENSITY_DEFAULT;
    private static DisplayMetrics defaultDisplay;

    public static void setDensity(float density) {
        mDensity = density;
    }

    public static float getDensity() {
        return mDensity;
    }

    public static DisplayMetrics getDefaultDisplay(Context context) {
        defaultDisplay = context.getResources().getDisplayMetrics();
        return defaultDisplay;
    }

    public static int percentWidth(Context context, float percent) {
        return (int) (getWidth(context) * percent);
    }

    public static int percentHeight(Context context, float percent) {
        return (int) (getHeight(context) * percent);
    }

    public static int dip2px(float dipValue) {
        return (int) (dipValue * mDensity + 0.5f);
    }

    public static int px2dip(Context context, float pxValue) {
        return (int) (pxValue / mDensity + 0.5f);
    }

    public static int getWidth(Context context) {
        return getDefaultDisplay(context).widthPixels;
    }

    public static int getHeight(Context context) {
        return getDefaultDisplay(context).heightPixels;
    }

    public static int px2sp(Context context, float pxValue) {
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }


    /**
     * 转换宽度
     *
     * @param designWidth
     * @param designValue
     * @return
     */
    public static int getWidthByDesignValue(Context context, double designWidth, int designValue) {
        return (int) ((float) (getWidth(context) * designWidth) / (float) designValue + 0.5F);
    }


    /**
     * 设置view的宽高
     *
     * @param view
     * @param width
     * @param height
     */
    private static void setWidthAndHeight(View view, int width, int height) {
        if (view == null) {
            return;
        }
        ViewGroup.LayoutParams params = view.getLayoutParams();
        if (params != null) {
            params.width = width;
            params.height = height;
        }
        view.setLayoutParams(params);
    }

    /**
     * 转换宽度
     *
     * @param designWidth
     * @param designValue
     * @return
     */
    public static int getWidthByDesignValueFitFold(Context context, double designWidth, int designValue) {
        if (getHeight(context) * 1.0f / getWidth(context) < 1.334) {
            return DisplayUtils.dp2px(context, (float) designWidth);
        }
        return (int) ((float) (getWidth(context) * designWidth) / (float) designValue + 0.5F);
    }


    /**
     * 依据设计稿 设置view的宽高
     *
     * @param view
     * @param width
     * @param height
     */
    public static void setWidthAndHeightDpi(View view, int width, int height, int designValue) {
        if (width > 0) {
            width = DPIUtil.getWidthByDesignValue(view.getContext(), width, designValue);
        }
        if (height > 0) {
            height = DPIUtil.getWidthByDesignValue(view.getContext(), height, designValue);
        }
        setWidthAndHeight(view, width, height);
    }

    /**
     * 依据设计稿 设置view的宽高
     *
     * @param view
     * @param width
     * @param height
     */
    public static void setWidthAndHeightDpiFitFold(View view, int width, int height, int designValue) {
        if (width > 0) {
            width = DPIUtil.getWidthByDesignValueFitFold(view.getContext(), width, designValue);
        }
        if (height > 0) {
            height = DPIUtil.getWidthByDesignValueFitFold(view.getContext(), height, designValue);
        }
        setWidthAndHeight(view, width, height);
    }

    /**
     * 设置padding
     *
     * @param v
     * @param padding
     * @param designValue
     */
    public static void setPaddingDpi(View v, int padding, int designValue) {
        if (v == null) {
            return;
        }
        padding = getWidthByDesignValue(v.getContext(), padding, designValue);
        v.setPadding(padding, padding, padding, padding);
    }

    public static void setPaddingDpi(View v, float left, float top, float right, float bottom, int designValue) {
        if (v == null) {
            return;
        }
        left = getWidthByDesignValue(v.getContext(), left, designValue);
        top = getWidthByDesignValue(v.getContext(), top, designValue);
        right = getWidthByDesignValue(v.getContext(), right, designValue);
        bottom = getWidthByDesignValue(v.getContext(), bottom, designValue);
        v.setPadding((int) left, (int) top, (int) right, (int) bottom);
    }

    /**
     * 设置margin
     *
     * @param v
     * @param top
     * @param bottom
     * @param left
     * @param right
     */
    public static void setMarginDpi(View v, int left, int top, int right, int bottom, int design) {
        if (v == null) {
            return;
        }
        ViewGroup.LayoutParams param = v.getLayoutParams();
        if (!(param instanceof ViewGroup.MarginLayoutParams)) {
            return;
        }
        if (top != 0) {
            top = getWidthByDesignValue(v.getContext(), top, design);
        }
        if (bottom != 0) {
            bottom = getWidthByDesignValue(v.getContext(), bottom, design);
        }
        if (left != 0) {
            left = getWidthByDesignValue(v.getContext(), left, design);
        }
        if (right != 0) {
            right = getWidthByDesignValue(v.getContext(), right, design);
        }
        ((ViewGroup.MarginLayoutParams) param).leftMargin = left;
        ((ViewGroup.MarginLayoutParams) param).topMargin = top;
        ((ViewGroup.MarginLayoutParams) param).rightMargin = right;
        ((ViewGroup.MarginLayoutParams) param).bottomMargin = bottom;
        v.setLayoutParams(param);
    }

}
