package com.xstore.sdk.floor.floorcore.utils;

public class RecyclerViewUtils {

    public static int getMinValue(int[] firstPosition,int[] lastPositions){
        int min =0;
        if(firstPosition!=null&&firstPosition.length>0){
            min = firstPosition[0];
        }
        for (int value : firstPosition) {
            if (value <min) {
                min = value;
            }
        }
        for (int value : lastPositions) {
            if (value  < min) {
                min = value;
            }
        }
        return min;
    }

    public static int getMaxValue(int[] firstPosition,int[] lastPositions){
        int max =0;
        if(firstPosition!=null&&firstPosition.length>0){
            max = firstPosition[0];
        }
        for (int value : firstPosition) {
            if (value > max) {
                max = value;
            }
        }
        for (int value : lastPositions) {
            if (value > max) {
                max = value;
            }
        }
        return max;
    }

}
