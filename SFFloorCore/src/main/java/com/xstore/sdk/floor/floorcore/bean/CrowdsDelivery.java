package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;
import java.util.List;

public class CrowdsDelivery implements Serializable {

    private Integer code;
    private Boolean success;
    private Object message;
    private Boolean crowdsDeliveryResult;
    private List<String> crowdsDeliveryGroupIds;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }

    public Boolean getCrowdsDeliveryResult() {
        return crowdsDeliveryResult;
    }

    public void setCrowdsDeliveryResult(Boolean crowdsDeliveryResult) {
        this.crowdsDeliveryResult = crowdsDeliveryResult;
    }

    public List<String> getCrowdsDeliveryGroupIds() {
        return crowdsDeliveryGroupIds;
    }

    public void setCrowdsDeliveryGroupIds(List<String> crowdsDeliveryGroupIds) {
        this.crowdsDeliveryGroupIds = crowdsDeliveryGroupIds;
    }
}
