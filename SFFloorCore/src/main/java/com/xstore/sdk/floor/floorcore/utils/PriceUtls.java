package com.xstore.sdk.floor.floorcore.utils;

import android.content.Context;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.widget.TextView;


import com.xstore.floorsdk.floorcore.R;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuComparePrice;

import java.text.DecimalFormatSymbols;

/**
 * Created by xuchangqing on 2017/5/14.
 */
@Deprecated
public class PriceUtls {

    public static String getDote2(float cent, int centFactor) {
        if (cent == 0d) {
            return "0";
        }
        java.text.DecimalFormat df = new java.text.DecimalFormat(",###,##0.00");
        DecimalFormatSymbols dfs = new DecimalFormatSymbols();
        dfs.setDecimalSeparator('.');
        df.setDecimalFormatSymbols(dfs);
        return df.format(cent / centFactor);
    }

    public static void setPrice(TextView tv, String price, boolean needTag) {
        setPrice(tv, price, needTag, false);
    }

    /**
     * 统一设置价格
     *
     * @param tv
     * @param price
     * @param needTag
     * @param needSpace
     */
    public static void setPrice(TextView tv, String price, boolean needTag, boolean needSpace) {

        if (null == tv) {
            return;
        }
        // if (StringUtil.isNullByString(price) || (!SpannableUtils.isEmpty(price) && (price.contains("-") || "0.00".equals(price)) || DyUtils.compare("0",price) == 0)) {
        if (StringUtil.isNullByString(price) || (!TextUtils.isEmpty(price) && price.contains("-"))) {
            tv.setText(R.string.sf_floor_core_no_price);
        } else {
            if (needTag) {
                if (needSpace) {
                    tv.setText("¥ " + price);
                } else {
                    tv.setText("¥" + price);
                }
            } else {
                tv.setText(price);
            }

        }
    }

    public static void setPrudcutDetailPrice(Context context, TextView tv, String price, boolean needTag, int size) {
        if (size == 0) {
            size = 10;
        }
        if (null == tv) {
            return;
        }
        if (StringUtil.isNullByString(price) || (!TextUtils.isEmpty(price) && (price.contains("-") || "暂无报价".equals(price)))) {
            tv.setText(R.string.sf_floor_core_no_price);
        } else {
            if (needTag) {
                //tv.setText("¥ " + price);
                tv.setText("¥" + price, TextView.BufferType.SPANNABLE);
                Spannable span = (Spannable) tv.getText();
                span.setSpan(new RelativeSizeSpan(0.7f), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                //tv.setText(StringUtil.getSizeSpanSpToPx(context, "¥ " + price, 0, 1, size));
            } else {
                tv.setText(price);
            }
        }
    }

    /**
     * 设置划线价
     *
     * @param tv
     * @param price
     * @param needTag 是否要展示"¥"
     */
    public static Spanned setMarketPrice(TextView tv, String price, boolean needTag, ProductDetailBean.WareInfoBean wareInfo) {
        return setMarketPrice(tv, price, needTag, false, "", false, wareInfo);
    }

    /**
     * 设置划线价
     *
     * @param tv
     * @param price
     * @param needTag 是否要展示"¥"
     * @param invisible 划线价空时展示状态，true：View.INVISIBLE false: View.GONE
     */
    public static Spanned setMarketPrice(TextView tv, String price, boolean needTag, boolean invisible, ProductDetailBean.WareInfoBean wareInfo) {
       return setMarketPrice(tv, price, needTag, false, "", invisible, wareInfo);
    }

    /**
     * 设置划线价
     *
     * @param tv
     * @param price
     * @param needTag 是否要展示"¥"
     * @param needUnit 是否要展示购买单位
     * @param buyUnit
     */
    public static Spanned setMarketPrice(TextView tv, String price, boolean needTag, boolean needUnit, String buyUnit, ProductDetailBean.WareInfoBean wareInfo) {
        return setMarketPrice(tv, price, needTag, needUnit, buyUnit, false, wareInfo);
    }

    /**
     * 统一商卡数据结构设置划线价
     * @param tvMarketPrice
     * @param comparePrice
     */
    public static void setMarketPrice(TextView tvMarketPrice, SkuComparePrice comparePrice) {
        if (tvMarketPrice == null) {
            return;
        }
        if (comparePrice == null || StringUtil.isNullByString(comparePrice.getValue()) || comparePrice.getValue().contains("-") || "暂无报价".equals(comparePrice.getValue())) {
            tvMarketPrice.setVisibility(View.GONE);
        } else {
            StringBuilder sb = new StringBuilder();
            if (comparePrice.getType() == 1) {//1:划线价、2:非划线价、3:500g价
                sb.append("<s>");
            }
            if (!comparePrice.getValue().contains("¥")) {
                sb.append("¥");
            }
            sb.append(comparePrice.getValue());
            if (comparePrice.getType() == 1) {
                sb.append("</s>");
            }
            Spanned formatText = Html.fromHtml(sb.toString());
            tvMarketPrice.setText(formatText);
            tvMarketPrice.setVisibility(View.VISIBLE);
            tvMarketPrice.getPaint().setAntiAlias(true);
        }
    }

    /**
     * 设置划线价
     *
     * @param tv
     * @param price
     * @param needTag 是否要展示"¥"
     * @param needUnit 是否要展示购买单位
     * @param buyUnit
     * @param invisible 划线价空时展示状态，true：View.INVISIBLE false: View.GONE
     */
    public static Spanned setMarketPrice(TextView tv, String price, boolean needTag, boolean needUnit, String buyUnit, boolean invisible, ProductDetailBean.WareInfoBean wareInfo) {
        String needStrikethrough = "";//v3.6.4原价是否划线  1：划线  2：不划线
        String marketPriceTypeDesc = "";//v3.6.4划线价前缀(为空时,直接不展示划价)
        String underlinePricePlace = "";
        if (null != wareInfo) {
            needStrikethrough = wareInfo.getNeedStrikethrough();
            marketPriceTypeDesc = wareInfo.getMarketPriceTypeDesc();
            underlinePricePlace = wareInfo.getUnderlinePricePlace();
            //价格覆盖
            if (!StringUtil.isNullByString(underlinePricePlace)) {
                price = underlinePricePlace;
            } else if ("2".equals(needStrikethrough)) {
                if (wareInfo.convertFromSkuInfoVo) {
                    // 新架构下价格取自marketPrice  不会取jdPrice
                    price = wareInfo.getMarketPrice();
                } else {
                    // 由于有些空间没有价格会员标 所以展示需要统一替换jdPrice 替换为vipPrice 此处划线价需要修改为 realJdPrice 获取实际的jdPrice
                    price = wareInfo.getRealJdPrice();
                }
            } else {
                price = wareInfo.getMarketPrice();
            }
        }

        if (tv != null && (StringUtil.isNullByString(price) || (!TextUtils.isEmpty(price) && (price.contains("-") || "暂无报价".equals(price))) || TextUtils.isEmpty(marketPriceTypeDesc))) {
            if (invisible) {
                tv.setVisibility(View.INVISIBLE);
            } else {
                tv.setVisibility(View.GONE);
            }
            return null;
        } else {
            StringBuilder sb = new StringBuilder();
            sb.append(marketPriceTypeDesc);
            if (!TextUtils.isEmpty(needStrikethrough) && needStrikethrough.equals("1")) {
                sb.append("<s>");
            }
            if (needTag) {
                sb.append("¥");
            }
            sb.append(price);
            if (needUnit && !StringUtil.isNullByString(buyUnit)) {
                if (!buyUnit.startsWith("/")) {
                    sb.append("/");
                }
                sb.append(buyUnit);
            }
            if (!TextUtils.isEmpty(needStrikethrough) && needStrikethrough.equals("1")) {
                sb.append("</s>");
            }
            Spanned formatText = Html.fromHtml(sb.toString());
            if (tv != null) {
                tv.setText(formatText);
                tv.setVisibility(View.VISIBLE);
                tv.getPaint().setAntiAlias(true);
            }
            return formatText;
        }
    }


    public static void setPrice(TextView tv, String price) {

        if (null == tv) {
            return;
        }
        if (StringUtil.isNullByString(price) || !TextUtils.isEmpty(price) && (price.contains("-"))) {
            tv.setText(R.string.sf_floor_core_no_price);
        } else {
            tv.setText("¥" + price, TextView.BufferType.SPANNABLE);
            Spannable span = (Spannable) tv.getText();
            span.setSpan(new RelativeSizeSpan(0.7f), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    public static void setPrice(TextView tv, String price, int typeface) {

        if (null == tv) {
            return;
        }

        if (StringUtil.isNullByString(price) || !TextUtils.isEmpty(price) && (price.contains("-"))) {
            tv.setText(R.string.sf_floor_core_no_price);
        } else {
            SpannableStringBuilder span = new SpannableStringBuilder("¥" + price);
            span.setSpan(new RelativeSizeSpan(0.7f), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            span.setSpan(new StyleSpan(typeface), 1, span.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            tv.setText(span);
        }
    }

    public static void setAssisPrice(TextView tv, String price, int typeface) {

        if (null == tv) {
            return;
        }

        if (StringUtil.isNullByString(price) || !TextUtils.isEmpty(price) && (price.contains("-"))) {
            tv.setText("");
        } else {
            SpannableStringBuilder span = new SpannableStringBuilder("+¥" + price);
            span.setSpan(new RelativeSizeSpan(0.7f), 0, 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            span.setSpan(new StyleSpan(typeface), 2, span.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            tv.setText(span);
        }
    }

    public static void setPrice(TextView tv, String price, float proportion) {

        if (null == tv) {
            return;
        }

        if (StringUtil.isNullByString(price) || !TextUtils.isEmpty(price) && (price.contains("-"))) {
            tv.setText(R.string.sf_floor_core_no_price);
        } else {
            tv.setText("¥" + price, TextView.BufferType.SPANNABLE);
            Spannable span = (Spannable) tv.getText();
            span.setSpan(new RelativeSizeSpan(proportion), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    public static void setPriceRecommend(TextView tv, String price) {

        if (null == tv) {
            return;
        }

        if (StringUtil.isNullByString(price) || !TextUtils.isEmpty(price) && (price.contains("-"))) {
            tv.setText("--");
        } else {
            tv.setText("¥" + price, TextView.BufferType.SPANNABLE);
            Spannable span = (Spannable) tv.getText();
            span.setSpan(new RelativeSizeSpan(0.7f), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    /**
     * 结算页 设置价格 如果价格为空 则设置空串
     *
     * @param tv
     * @param price
     * @param resId
     */
    public static void setStringPrice(Context activity, TextView tv, String price, int resId) {
        if (tv == null) {
            return;
        }
        if (TextUtils.isEmpty(price)) {
            tv.setText("");
            return;
        }
        if (resId > 0 && activity != null) {
            tv.setText(activity.getString(resId, price));
            return;
        }
        tv.setText("");
    }

//    public static BigDecimal getTotalBigDecimal(CartBean.SuitPromotionsBean suitPromotionsBean) {
//        BigDecimal bigDecimal = new BigDecimal("0.00");
//        /**
//         * 添加为空判断，防止数据缺失造成bug
//         * liuhongshuo 2018-01-05
//         */
//
//        for (int i = 0; i < suitPromotionsBean.getWareInfos().size(); i++) {
//            CartBean.WareInfosBean wareInfosBean = suitPromotionsBean.getWareInfos().get(i);
//            if (wareInfosBean.getCheck() == 1) {//选中商品
//                bigDecimal = bigDecimal.add(new BigDecimal(suitPromotionsBean.getWareInfos().get(i).getTotalPrice()));
//            }
//        }
//        return bigDecimal;
//    }


    /**
     * 格式价格
     * @param p
     * @return
     */
    public static String formatPrice(String p) {
        if (p == null) {
            return null;
        }
        String str = null;
        if (p.startsWith("¥")) {
            return p;
        } else if (p.startsWith("￥")) {
            str = p.replaceAll("￥", "¥");
        } else {
            str = "¥" + p;
        }
        return str;
    }
}
