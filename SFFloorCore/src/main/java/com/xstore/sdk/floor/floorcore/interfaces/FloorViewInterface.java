package com.xstore.sdk.floor.floorcore.interfaces;

import android.content.Context;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

/**
 * 楼层View 实体，每个可用楼层必须实现该接口
 */
public interface FloorViewInterface {

    /**
     * 数据绑定 控制自身的 数据绘制 处理等内容
     *
     * @param context     引用上下文
     * @param floorContainer 楼层视图容器
     * @param holder      viewHolder
     * @param indexDetail 楼层对象数据
     * @param floorIndex  当前楼层index  是真实渲染的楼层位置 从0开始
     */
    void bindData(Context context, FloorContainerInterface floorContainer, @Nullable FloorBaseViewHolder holder,
                  @Nullable FloorDetailBean indexDetail, int floorIndex);


    /**
     * 创建楼层视图
     *
     * @param context        引用上下文
     * @param floorContainer 楼层视图容器
     * @return
     */
    View createView(Context context, FloorContainerInterface floorContainer);

    /**
     * 楼层item 绑定到视图
     */
    void onViewAttachedToWindow();

    /**
     * 楼层item 从视图剥离
     */
    void onViewDetachedFromWindow();

    /**
     * view回收
     */
    void onViewRecycled();

    /**
     * view 回收失败
     * @return null 代表由默认机制处理
     *
     * True if the View should be recycled, false otherwise. Note that if this method returns true,
     * RecyclerView will ignore the transient state of the View and recycle it regardless. If this
     * method returns false, RecyclerView will check the View's transient state again before giving
     * a final decision. Default implementation returns false.
     */
    Boolean onFailedToRecycleView();

    /**
     * @return 当前楼层是否为独占一行的
     */
    boolean isFullSpan();

    /**
     * 楼层数据解析
     * 由于每个楼层的数据都不尽相同，我们将每个楼层数据的解析工作闭环到楼层内部，业务自己处理数据解析
     *
     * @param bean    领域下发的楼层数据
     * @param isCache 是否是来自缓存的数据
     * @return 如果是静态楼层 则返回楼层对应的类型
     * 如果是动态楼层 则返回JDJsonObject
     * 这个对象可以为任意类型，业务在使用的时候需要自己判断
     *
     * 如果这个数据返回null的话，代表数据不合法，需要将该楼层剔除掉
     */
    Object convertData(FloorDetailBean bean, boolean isCache);

    /**
     * @param isCelling 设置当前楼层是否是吸顶的
     */
    void setCellingFloor(boolean isCelling);

    /**
     * @return 返回楼层的固定高度  只有值大于等于0的时候可以信任
     */
    int getFloorHeight();


    RecyclerView getParentRcv();

    RelativeLayout getFloorContainer();

}
