package com.xstore.sdk.floor.floorcore.utils;

import static com.xstore.sevenfresh.image.ImageloadUtils.checkIsSafe;
import static com.xstore.sevenfresh.image.ImageloadUtils.reformUrl;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.Target;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.floorcore.R;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.image.utils.GlideCustomRoundTransform;
import com.xstore.sevenfresh.image.utils.GlideRoundTransform;

/**
 * 图片帮助类，拓展ImageLoadUtils
 */
public class ImageHelper {

    public static void loadHomeBannerImage(Context context, final ImageView view, String resourceImageUrl, int height, float leftTopRadius, float rightTopRadius, float rightBottomRadius, float leftBottomRadius) throws Exception {
        String imageUrl;
        int width = ScreenUtils.getScreenWidth(context) - ScreenUtils.dip2px(context, 30);
        if (checkIsSafe(context)) {
            if (null != view) {
                imageUrl = reformUrl(resourceImageUrl);
                if (height == 0) {
                    height = Target.SIZE_ORIGINAL;
                }
                int finalHeight = height;
                SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                    public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
                        if (resource == null || resource.getWidth() <= 0 || resource.getHeight() <= 0) {
                            view.setImageBitmap(resource);
                            Exception e = new Exception("width and height must be > 0 " + resourceImageUrl);
                            JdCrashReport.postCaughtException(e);
                            return;
                        }

                        Matrix matrix = new Matrix();
                        matrix.postScale(ScreenUtils.getScreenWidth(context) * 1.0f / resource.getWidth(), finalHeight * 1.0f / resource.getHeight());
                        // 放大bitmap填充屏幕宽度
                        Bitmap scaleBitmap = Bitmap.createBitmap(resource, 0, 0, resource.getWidth(), resource.getHeight(), matrix, true);
                        // 裁剪屏bitmap
                        Bitmap cropBitmap = null;
                        if (ScreenUtils.dip2px(context, 15) + width < scaleBitmap.getWidth()) {
                            cropBitmap = Bitmap.createBitmap(scaleBitmap, ScreenUtils.dip2px(context, 15), 0, width, scaleBitmap.getHeight());
                        } else {
                            cropBitmap = scaleBitmap;
                        }
                        Bitmap cornerBitmap = Bitmap.createBitmap(cropBitmap.getWidth(), cropBitmap.getHeight(), Bitmap.Config.ARGB_8888);
                        Canvas canvas = new Canvas(cornerBitmap);
                        Paint paint = new Paint();
                        paint.setShader(new BitmapShader(cropBitmap, BitmapShader.TileMode.CLAMP, BitmapShader.TileMode.CLAMP));
                        paint.setAntiAlias(true);
                        RectF rectF = new RectF(0f, 0f, cropBitmap.getWidth(), cropBitmap.getHeight());
                        Path path = new Path();
                        float[] rad = {leftTopRadius, leftTopRadius, rightTopRadius, rightTopRadius, rightBottomRadius, rightBottomRadius, leftBottomRadius, leftBottomRadius};
                        path.addRoundRect(rectF, rad, Path.Direction.CW);
                        canvas.drawPath(path, paint);
                        view.setImageBitmap(cornerBitmap);
                    }
                };
                Glide.with(context).load(imageUrl).asBitmap().placeholder(R.drawable.sfser_image_placeholderid).override(width, height).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(simpleTarget);
            }
        }
    }

    public static void loadRoundImage(Context context, String imageurl, ImageView imageView, float allCorner) {
        if (checkIsSafe(context)) {
            final String url = reformUrl(imageurl);
            Glide.with(context)
                    .load(url)
                    .error(R.drawable.sf_theme_image_placeholder_square)
                    .transform(new CenterCrop(context), new GlideRoundTransform(context, allCorner))
                    .into(imageView);
        }
    }

    public static void loadRoundImage(Context context, String imageurl, ImageView imageView, float lt, float rt, float lb, float rb) {
        if (checkIsSafe(context)) {
            final String url = reformUrl(imageurl);
            Glide.with(context)
                    .load(url)
                    .error(R.drawable.sf_theme_image_placeholder_square)
                    .transform(new CenterCrop(context), new GlideCustomRoundTransform(context, lt, rt, lb, rb))
                    .into(imageView);
        }
    }

    /**
     * 设置楼层的背景色
     *
     * @param context
     * @param view
     * @param bgUrl
     * @param bgColor
     */
    public static void loadFloorBackGround(Context context, View view, String bgUrl, String bgColor) {
        if (!StringUtil.isNullByString(bgUrl)) {
            bgUrl = reformUrl(bgUrl);
            view.setTag(com.xstore.sevenfresh.image.R.id.sfser_image_glide_backgroud, bgUrl);
            ImageloadUtils.loadImageForBackground(context, view, bgUrl, 0, 0);
        } else {
            view.setTag(com.xstore.sevenfresh.image.R.id.sfser_image_glide_backgroud, null);
            view.setBackgroundColor(StringUtil.getSetColor(context.getString(R.string.sf_floor_core_default_floor_bg), bgColor));
        }
    }
}
