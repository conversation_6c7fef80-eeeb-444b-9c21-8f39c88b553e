package com.xstore.sdk.floor.floorcore.interfaces;


import com.xstore.sevenfresh.addressstore.bean.AddressInfoBean;
import com.xstore.sevenfresh.addressstore.bean.TenantShopInfo;

import java.util.List;

/**
 * 监听门店切换
 *
 * <AUTHOR>
 * @date 2022/12/30
 */
public interface StoreChangeCallback {
    void storeChanged(TenantShopInfo tenantShopInfo, AddressInfoBean addressInfoBean, List<TenantShopInfo> tenantShopInfos);
}
