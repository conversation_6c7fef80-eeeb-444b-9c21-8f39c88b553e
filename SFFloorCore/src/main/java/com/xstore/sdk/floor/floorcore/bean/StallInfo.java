package com.xstore.sdk.floor.floorcore.bean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-11-04
 * @ 档口信息module
 */
public class StallInfo implements Serializable {
    /**
     * 档口名称
     */
    private String stallName;
    /**
     * 档口ID
     */
    private String stallId;
    /**
     * 展示文字
     */
    private String slogan;
    /**
     * 档口详情url
     */
    private String stallDetailUrl;
    /**
     * 档口名称图片
     */
    private String stallAboutImgUrl;
    /**
     * 跳转action
     */
    private BaseEntityFloorItem.FloorsBean.ActionBean action;

    /**
     * 档口商品
     */
    private List<BaseEntityFloorItem.FloorsBean.ItemsBean> items;

    public String getStallName() {
        return stallName;
    }

    public void setStallName(String stallName) {
        this.stallName = stallName;
    }

    public String getStallAboutImgUrl() {
        return stallAboutImgUrl;
    }

    public void setStallAboutImgUrl(String stallAboutImgUrl) {
        this.stallAboutImgUrl = stallAboutImgUrl;
    }

    public List<BaseEntityFloorItem.FloorsBean.ItemsBean> getItems() {
        return items;
    }

    public void setItems(List<BaseEntityFloorItem.FloorsBean.ItemsBean> items) {
        this.items = items;
    }

    public BaseEntityFloorItem.FloorsBean.ActionBean getAction() {
        return action;
    }

    public void setAction(BaseEntityFloorItem.FloorsBean.ActionBean action) {
        this.action = action;
    }

    public String getStallId() {
        return stallId;
    }

    public void setStallId(String stallId) {
        this.stallId = stallId;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public String getStallDetailUrl() {
        return stallDetailUrl;
    }

    public void setStallDetailUrl(String stallDetailUrl) {
        this.stallDetailUrl = stallDetailUrl;
    }
}
