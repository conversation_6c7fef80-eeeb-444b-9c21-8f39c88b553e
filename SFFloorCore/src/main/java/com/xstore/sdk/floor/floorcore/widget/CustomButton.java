package com.xstore.sdk.floor.floorcore.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.widget.Button;


import com.xstore.sdk.floor.floorcore.utils.ViewUtil;

import java.math.BigDecimal;

@SuppressLint("AppCompatCustomView")
public class CustomButton extends Button {

    public CustomButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CustomButton(Context context) {
        super(context);
        init();
    }

    private void init() {
//        if (ViewUtil.isSpecialMode(getContext())) {
//            int size = (int) NumberUtils.round(getTextSize()
//                    / getContext().getResources().getDisplayMetrics().scaledDensity, 0, BigDecimal.ROUND_HALF_UP);
//            size += 4;
//            ViewUtil.setSpecialModeTextSize(getContext(), this, size);
//        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

    }

    @Override
    protected void drawableStateChanged() {
        super.drawableStateChanged();

        for (int state : getDrawableState()) {
            if (state == android.R.attr.state_focused || state == android.R.attr.state_pressed
                    || state == android.R.attr.state_selected) {
                getBackground().setAlpha((int) (255 * 0.97f));
                return;
            }
        }
        getBackground().setAlpha(255);
        return;
    }
}
