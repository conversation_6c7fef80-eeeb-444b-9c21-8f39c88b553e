package com.xstore.sdk.floor.floorcore;

import android.app.Activity;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;

import java.io.Serializable;


/**
 * 楼层跳转管理
 * 目前跳转逻辑都放在这里，方便日后动态配置 抽离
 */
public class FloorJumpManager {

    /**
     * 跳转类型 {@link FloorActionConstants}
     */
    public static final String URL_TYPE = "urltype";
    /**
     * 跳转参数 鲜橙返回
     */
    public static final String TO_URL = "url";
    /**
     * 跳转商详类型
     */
    public static final String JUMP_DETAIL_TYPE = "jumpDetailType";
    /**
     * 其他参数int
     */
    public static final String ELSEInt = "elseInt";
    /**
     * 额外的跳转参数
     */
    public static final String TO_URL_EXTRA = "toUrlExtra";
    /**
     * 是否需要登录
     */
    public static final String NEED_LOGIN = "needLogin";
    /**
     * 从哪里跳转过来的 新首页的跳转逻辑和老的有一些区别 比如不需要埋点，登录成功后需要结果等
     */
    public static final String JUMP_FROM = "jumpFrom";
    /**
     * 启动页面
     */
    public static final String REQUEST_CODE = "requestCode";
    /**
     * 启动参数
     */
    public static final String REQUEST_EXTRA = "requestExtra";
    /**
     * 楼层位置
     */
    public static final String FLOOR_INDEX = "floorIndex";

    // 只能优惠券跳转 所需参数
    public static final String COUPONID = "couponId";
    public static final String COUPONIDISTODAY = "couponIdIsToday";
    public static final String TIPSCONTENT = "tips";
    public static final String BATCHKEY = "batchKey";


    private static FloorJumpManager floorJumpManager = new FloorJumpManager();

    private FloorJumpManager() {
    }

    public static FloorJumpManager getInstance() {
        return floorJumpManager;
    }

    /**
     * 跳转公共方法
     *
     * @param activity
     * @param bundle
     */
    public void jumpAction(Activity activity, Bundle bundle) {
//        int urlType = bundle.getInt("urltype");
//        String url = bundle.getString("url", "");
        if (bundle != null) {
            //来自首页sdk的跳转
            bundle.putString(JUMP_FROM, "homeFloorSdk");
        }
        FloorInit.getFloorConfig().startPage(activity, bundle);
    }

    /**
     * 7club 视频页
     */
    String CLUB_VIDEO = "/sevenclub/video";
    String K_CONTENT_ID = "contentId";
    public static final String K_CONTENT_TYPE = "contentType";


    public void showAddressCheckDialog() {

    }


    ///////////////////////////  内部跳转方法  //////////////////////////

    /**
     * 跳转首页地址列表
     *
     * @param activity
     */
    public void startAddressReceiverActivity(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_ADDRESS_LIST);
        jumpAction(activity, bundle);
    }

    /**
     * 创建收货地址
     * @param activity
     */
    public void createAddress(Activity activity){
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_CREATE_ADDRESS);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转堂食
     *
     * @param activity
     */
    public void jumpDineIn(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_DINE_IN);
        jumpAction(activity, bundle);

//        ARouter.getInstance()
//                            .build(URIPath.PurchaseProcess.DINEIN_CATEGORY)
//                .navigation();

//        if (FloorInit.getFloorConfig().isLogin()) {
//            FloorJumpManager.getInstance().jumpDineIn();
//        } else {
//            FloorJumpManager.getInstance().startLoginActivity();
//        }
    }

    /**
     * 跳转付款码
     *
     * @param activity
     */
    public void startPayCode(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_PAY_CODE);
        jumpAction(activity, bundle);

//        if (FloorInit.getFloorConfig().isLogin()) {
//            FloorJumpManager.getInstance().startPayCode(floorContainer.getActivity(), 0);
//        } else {
//            //去登陆
//            FloorJumpManager.getInstance().startLoginActivity(floorContainer.getActivity(), REQUEST_CODE_LOGIN_FOR_HOME_PAYCODE, true);
//        }
    }


    /**
     * 跳转新版消息中心
     *
     * @param activity
     */
    public void startMessageCenter(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_MESSAGE_CENTER);
        jumpAction(activity, bundle);
    }



    /**
     * 扫一扫登录
     */
    public static final int REQUEST_CODE_LOGIN_FOR_SCAN = 10026;

    /**
     * 跳转扫一扫
     *
     * @param activity
     */
    public void jumpScan(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_SCAN_CODE);
        jumpAction(activity, bundle);

//        ARouter.getInstance().build(URIPath.Common.SCAN_NEW).withString("from", "").navigation();
//        if (!FloorInit.getFloorConfig().isLogin()) {
//            FloorJumpManager.getInstance().startLoginActivity(floorContainer.getActivity(), REQUEST_CODE_LOGIN_FOR_SCAN, true);
//        } else {
//            FloorJumpManager.getInstance().jumpScan();
//        }
    }

    /**
     * 跳转搜索列表
     *
     * @param activity
     */
    public void startSearchActivity(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_SEARCH_HOME);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuId    商品id
     * @param skuName  商品名称
     * @param imageUrl 商品主图
     */
    public void jumpProductDetail(Activity activity, String skuId, String skuName, String imageUrl) {
        jumpProductDetail(activity, skuId, skuName, imageUrl, "");
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuId    商品id
     * @param skuName  商品名称
     * @param imageUrl 商品主图
     * @param isTakeaway 餐饮外卖标： 1：是，其他：不是
     */
    public void jumpProductDetail(Activity activity, String skuId, String skuName, String imageUrl, String isTakeaway) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_GOOD_DETAIL);
        bundle.putString(TO_URL, "skuId=" + skuId);
        try {
            Uri.Builder b = new Uri.Builder();
            b.appendQueryParameter("skuName", skuName);
            b.appendQueryParameter("imageUrl", imageUrl);
            b.appendQueryParameter("skuId", skuId);
            b.appendQueryParameter("isTakeaway", isTakeaway);
            bundle.putString(TO_URL_EXTRA, b.build().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        jumpAction(activity, bundle);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoVoBean
     */
    public void jumpProductDetail(Activity activity, SkuInfoVoBean skuInfoVoBean) {
        jumpProductDetail(activity, skuInfoVoBean, false, 0);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoVoBean
     * @param needAddCartBroadcast
     */
    public void jumpProductDetail(Activity activity, SkuInfoVoBean skuInfoVoBean, @Deprecated boolean needAddCartBroadcast) {
        jumpProductDetail(activity, skuInfoVoBean, needAddCartBroadcast, 0);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoVoBean
     * @param needAddCartBroadcast
     * @param jumpType
     */
    public void jumpProductDetail(Activity activity, SkuInfoVoBean skuInfoVoBean, @Deprecated boolean needAddCartBroadcast, int jumpType) {
        if (skuInfoVoBean == null || skuInfoVoBean.getSkuBaseInfoRes() == null
                || StringUtil.isNullByString(skuInfoVoBean.getSkuBaseInfoRes().getSkuId())) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_GOOD_DETAIL);
        bundle.putString(TO_URL, "skuId=" + skuInfoVoBean.getSkuBaseInfoRes().getSkuId());
        bundle.putInt(JUMP_DETAIL_TYPE, jumpType);
        bundle.putBoolean("needAddCartBroadcast", needAddCartBroadcast);
        try {
            Uri.Builder b = new Uri.Builder();
            b.appendQueryParameter("skuName", skuInfoVoBean.getSkuBaseInfoRes().getSkuName());
            b.appendQueryParameter("imageUrl", skuInfoVoBean.getDetailShowImg());
            b.appendQueryParameter("skuId", skuInfoVoBean.getSkuBaseInfoRes().getSkuId());
            b.appendQueryParameter("brokerInfo", skuInfoVoBean.getBrokerInfo());
            if(skuInfoVoBean.getSmartAv() != null && !TextUtils.isEmpty(skuInfoVoBean.getSmartAv().getRankSortId())) {
                b.appendQueryParameter("rankSortId", skuInfoVoBean.getSmartAv().getRankSortId());
            }
            if (skuInfoVoBean.getSkuBaseInfoRes().getProductExInfo() != null) {
                b.appendQueryParameter("isTakeaway", skuInfoVoBean.getSkuBaseInfoRes().getProductExInfo().getIsTakeaway());
            }
            bundle.putString(TO_URL_EXTRA, b.build().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        jumpAction(activity, bundle);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoBean
     */
    public void jumpProductDetail(Activity activity, SkuInfoBean skuInfoBean) {
        jumpProductDetail(activity, skuInfoBean, false, 0);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoBean
     * @param needAddCartBroadcast
     */
    public void jumpProductDetail(Activity activity, SkuInfoBean skuInfoBean, @Deprecated boolean needAddCartBroadcast) {
        jumpProductDetail(activity, skuInfoBean, needAddCartBroadcast, 0);
    }

    /**
     * 跳转商详页
     *
     * @param activity
     * @param skuInfoBean
     * @param needAddCartBroadcast
     * @param jumpType
     */
    public void jumpProductDetail(Activity activity, SkuInfoBean skuInfoBean, @Deprecated boolean needAddCartBroadcast, int jumpType) {
        if (skuInfoBean == null || StringUtil.isNullByString(skuInfoBean.getSkuId())) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_GOOD_DETAIL);
        bundle.putString(TO_URL, "skuId=" + skuInfoBean.getSkuId());
        bundle.putInt(JUMP_DETAIL_TYPE, jumpType);
        bundle.putBoolean("needAddCartBroadcast", needAddCartBroadcast);
        try {
            Uri.Builder b = new Uri.Builder();
            b.appendQueryParameter("skuName", skuInfoBean.getSkuName());
            b.appendQueryParameter("imageUrl", skuInfoBean.getDetailShowImg());
            b.appendQueryParameter("skuId", skuInfoBean.getSkuId());
            b.appendQueryParameter("brokerInfo", skuInfoBean.getBrokerInfo());
            if(skuInfoBean.getMarketEntrance() != null && skuInfoBean.getMarketEntrance().getType() == SkuEnumInterface.MarketEntrance.RANK) {
                b.appendQueryParameter("rankSortId", String.valueOf(skuInfoBean.getMarketEntrance().getSubType()));
            }
            if (skuInfoBean.getLogicInfo() != null) {
                b.appendQueryParameter("isTakeaway", skuInfoBean.getLogicInfo().getIsTakeaway());
            }
            bundle.putString(TO_URL_EXTRA, b.build().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        jumpAction(activity, bundle);
    }

    /**
     * 打开一个新的h5页面
     *
     * @param activity  上下文
     * @param url       跳转链接
     * @param needLogin 是否需要登录
     */
    public void startH5(Activity activity, String url, boolean needLogin) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_M);
        bundle.putString(TO_URL, url);
        bundle.putBoolean(NEED_LOGIN, needLogin);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转商品搜索列表
     *
     * @param activity
     * @param keyword  搜索关键词
     */
    public void startProductList(Activity activity, String keyword) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_PRODUCT_LIST);
        bundle.putString(TO_URL, keyword);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转商品搜索结果页
     *
     * @param activity
     * @param fromType
     * @param searchKeyword
     * @param keywordClickFrom
     * @param rankSortIndex
     */
    public void startProductList(Activity activity, int fromType, String searchKeyword, String keywordClickFrom, int rankSortIndex) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_PRODUCT_LIST);
        bundle.putString(TO_URL, searchKeyword);
        bundle.putInt("fromType", fromType);
        bundle.putString("keywordClickFrom", keywordClickFrom);
        bundle.putInt("rankSortIndex", rankSortIndex);
        jumpAction(activity, bundle);
    }

    /**
     * @param activity
     * @param requestCode 如果他不为空则代表需要startActivityForResult
     * @param extra
     */
    public void startLoginActivity(Activity activity, Integer requestCode, Bundle extra) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_LOGIN);
        bundle.putSerializable(REQUEST_CODE, requestCode);
        bundle.putBundle(REQUEST_EXTRA, extra);
        jumpAction(activity, bundle);
    }

    public void jumpVideo(Activity activity, long contentId, int floorIndex) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_VIDEO);
        bundle.putString(TO_URL, String.valueOf(contentId));
        bundle.putInt(FLOOR_INDEX, floorIndex);
        jumpAction(activity, bundle);
//        ARouter.getInstance().build(CLUB_VIDEO)
//                .withLong(K_CONTENT_ID, contentId)
//                .withInt(K_CONTENT_TYPE, contentType)
//                .withInt(K_FROM_RECOMMEND_POSITON, floorIndex)
//                .navigation();
    }

    public void jumpUseIntellectCoupon(Activity activity, String couponId, String couponIdIsToday, String tips, String batchKey) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_USE_INTELLIGENCE_COUPON);
        bundle.putString(COUPONID, couponId);
        bundle.putString(COUPONIDISTODAY, couponIdIsToday);
        bundle.putString(TIPSCONTENT, tips);
        bundle.putString(BATCHKEY, batchKey);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转找相似列表
     *
     * @param activity
     * @param skuId
     * @param sourcePage
     */
    public void jumpSimilarList(Activity activity, String skuId, String sourcePage) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_SIMILAR_LIST);
        bundle.putString(TO_URL, skuId);
        bundle.putString("sourcePage", sourcePage);
        jumpAction(activity, bundle);
    }

    /**
     * 立即预定
     *
     * @param activity
     * @param wareInfo
     */
    public void preSaleJustNow(Activity activity, Serializable wareInfo) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_PRE_SALE_NOW);
        bundle.putSerializable("wareInfo", wareInfo);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转换购页
     *
     * @param activity
     * @param promotionBean
     */
    public void jumpRepurchase(Activity activity, ProductDetailBean.WareInfoBean.PromotionTypesBean promotionBean) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_INCREASE_REPURCHASE);
        bundle.putSerializable("promotion", promotionBean);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转购物车
     *
     * @param activity
     */
    public void jumpCart(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_CART);
        jumpAction(activity, bundle);
    }

    /**
     * 回首页
     *
     * @param activity
     */
    public void backHomePage(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_BACK_HOME);
        jumpAction(activity, bundle);
    }

    /**
     * 跳转常购清单
     *
     * @param activity
     */
    public void jumpToFrequentPurchase(Activity activity) {
        Bundle bundle = new Bundle();
        bundle.putInt(URL_TYPE, FloorActionConstants.URL_TYPE_FREQUENTPURCHASE);
        jumpAction(activity, bundle);
    }

}
