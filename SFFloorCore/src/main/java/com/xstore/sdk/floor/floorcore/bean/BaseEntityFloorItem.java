package com.xstore.sdk.floor.floorcore.bean;


import com.xstore.sdk.floor.floorcore.utils.ObjectLocals;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.addressstore.bean.TenantShopInfo;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by weichangfa on 2017/11/29.
 */

public class BaseEntityFloorItem implements Serializable {

    private boolean success;

    private String title;
    private String recommendedForYouTitle;//为你推荐
    private String navImage;//"顶部导航栏背景图,在首页数据的最外层，图片尺寸需要校验"
    private String navigationImage;// 新版首页顶部导航栏背景图
    private boolean recommendedForYou; // 是否有推荐楼层
    private List<FloorsBean> floors;
    private int navIconStyle;// 首页按钮是否为浅色模式，1为浅色
    /**
     * 首页弹窗 moudle
     */
    private FloorsBean floor;
    /**
     * 红包弹窗
     */
    private String redPackgeToast;
    private long storeId;
    private int remainingFloor; // 剩余楼层数量
    private int totalPage; // 推荐的总页数量
    private int page; // 推荐的页数
    private int totalCount; // 推荐的数量
    private boolean isNewcustomer; // 是否是老用户
    /**
     * 是否是定位回来的的 刷新
     */
    private boolean isLocationFresh;
    /**
     * 请求是否成功
     */
    private boolean requestError;
    /**
     * appTheme : {"tintColor":"abar选中文字高亮颜色","appThemeItems":[{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"}]}
     */

    private AppThemeBean appTheme;  //主题换肤 首页底部 图片颜色以及文字颜色
    private int startF;
    private int endF;

    /**
     * localLastRequest 本地参数 是否是lastRequest
     */
    private boolean localLastRequest;

    /**
     * localRequestStep 本地参数 请求序号
     */
    private int localRequestStep;

    /**
     * localType 本地请求类型
     */
    private String localType;
    /**
     * 解析数据的时间
     */
    private long currentParseDateTime;

    public String getRedPackgeToast() {
        return redPackgeToast;
    }

    public void setRedPackgeToast(String redPackgeToast) {
        this.redPackgeToast = redPackgeToast;
    }

    public FloorsBean getFloor() {
        return floor;
    }

    public void setFloor(FloorsBean floor) {
        this.floor = floor;
    }

    public long getCurrentParseDateTime() {
        return currentParseDateTime;
    }

    public void setCurrentParseDateTime(long currentParseDateTime) {
        this.currentParseDateTime = currentParseDateTime;
    }

    public boolean isRequestError() {
        return requestError;
    }

    public void setRequestError(boolean requestError) {
        this.requestError = requestError;
    }

    public boolean isLocationFresh() {
        return isLocationFresh;
    }

    public void setLocationFresh(boolean locationFresh) {
        isLocationFresh = locationFresh;
    }

    public boolean isLocalLastRequest() {
        return localLastRequest;
    }

    public void setLocalLastRequest(boolean localLastRequest) {
        this.localLastRequest = localLastRequest;
    }

    public int getLocalRequestStep() {
        return localRequestStep;
    }

    public void setLocalRequestStep(int localRequestStep) {
        this.localRequestStep = localRequestStep;
    }

    public String getLocalType() {
        return localType;
    }

    public void setLocalType(String localType) {
        this.localType = localType;
    }

    public String getNavImage() {
        return navImage;
    }

    public void setNavImage(String navImage) {
        this.navImage = navImage;
    }

    public String getNavigationImage() {
        return navigationImage;
    }

    public void setNavigationImage(String navigationImage) {
        this.navigationImage = navigationImage;
    }

    public int isNavIconStyle() {
        return navIconStyle;
    }

    public boolean isLightStyle() {
        return navIconStyle == 1;
    }

    public void setNavIconStyle(int navIconStyle) {
        this.navIconStyle = navIconStyle;
    }

    public boolean isNewcustomer() {
        return isNewcustomer;
    }

    public void setNewcustomer(boolean newcustomer) {
        isNewcustomer = newcustomer;
    }

    private Map<String, UnSupportActiveBean> activitys;//不支持动态布局的活动ID

    public Map<String, UnSupportActiveBean> getActivitys() {
        return activitys;
    }

    public void setActivitys(Map<String, UnSupportActiveBean> activitys) {
        this.activitys = activitys;
    }

    public String getRecommendedForYouTitle() {
        return recommendedForYouTitle;
    }

    public void setRecommendedForYouTitle(String recommendedForYouTitle) {
        this.recommendedForYouTitle = recommendedForYouTitle;
    }

    public boolean isRecommendedForYou() {
        return recommendedForYou;
    }

    public void setRecommendedForYou(boolean recommendedForYou) {
        this.recommendedForYou = recommendedForYou;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getRemainingFloor() {
        return remainingFloor;
    }

    public void setRemainingFloor(int remainingFloor) {
        this.remainingFloor = remainingFloor;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<FloorsBean> getFloors() {
        return floors;
    }

    public void setFloors(List<FloorsBean> floors) {
        this.floors = floors;
    }

    public long getStoreId() {
        return storeId;
    }

    public void setStoreId(long storeId) {
        this.storeId = storeId;
    }

    public AppThemeBean getAppTheme() {
        return appTheme;
    }

    public void setAppTheme(AppThemeBean appTheme) {
        this.appTheme = appTheme;
    }

    public int getStartF() {
        return startF;
    }

    public void setStartF(int startF) {
        this.startF = startF;
    }

    public int getEndF() {
        return endF;
    }

    public void setEndF(int endF) {
        this.endF = endF;
    }

    public static class FloorsBean implements Serializable {

        private int floorType;
        private int floorNum;
        private String name;
        private String firstTitle;
        private String image;
        private String imgUrl;
        private int showType;
        private float backHeight;
        private float contentHeight;
        private int multipleBackPic;
        // 是否使用本地缓存数据
        private boolean useLocalCacheData;
        /**
         * 动态图片
         */
        private String imageGif;
        private String secondTitle;
        private int popCondition;//弹屏广告是否需要登录
        private String thirdTitle;
        private ActionBean action;
        private List<ActionBean> actions;
        private ActionBean moreAction;  //首页包裹式 楼层 查看更多字段  moreAction 支持所有跳转类型
        private List<ItemsBean> items;
        private List<Rule> ruleList;
        private String imageTitle;
        private String themeColor;
        /**
         * 档口集合
         */
        private List<StallInfo> stallInfoList;
        /**
         * 红包弹屏的 领取成功图片以及action
         */
        private RedPackageVision successVision;
        /**
         * 红包弹屏的 领取失败图片以及action
         */
        private RedPackageVision failVision;
        private boolean started;
        private String time;
        private JSONObject buriedPointVo;
        private long seckillId;
        private long restseckillTime;
        private long secondkillTime; //秒杀数据
        private int horizontalScrollIndicator;// 1 代表横向滑动 0 代表竖向滑动
        private int pageRecommend;// 用来推荐埋点
        private int positionRecommend;// 用来推荐埋点
        /**
         * 商品 postion
         */
        private int positionGoods;
        /**
         * 屏幕宽度
         */
        private int width;
        /**
         * 用来动态设置图片价格
         */
        private List<DynamicLablesBean> dynamicLabels;

        private String activityId;  // 活动id
        private BuriedPointVoV2 buriedPointVoV2;


        private int floorStyle;//"floorStyle”:”0-是丰富的标题，有主副标题，有查看更多；1—只有一句文案，firstTitle-主标题,理论上只有一句文案的标题前端不支持跳转，魔法石把这两个标题柔和到一块，有认知风险"
        private String moreUrl;
        private String backGroudColor;
        private String contentbackGroudColor;// 新鲜快报 椭圆背景色;
        private String titleColor;// 标题颜色;
        private String pictureAspect;
        private String leftImage;
        private String cashbalances;
        private String mypoints;
        private boolean isFirstRecommend; //用来设置推荐的top 间距
        private boolean isMemeberCover; //用来判断是否 需要会员专区覆盖

        private String successImage; //"领取成功的图片",
        private String alreadyReceivedImage; //"已经领取的图片",
        private String failImage; //领取失败/抢光了/活动结束了的图片-总之就是没有领到"

        /**
         * 天降正义
         */

        private int animation;//  0:关闭 1:打开
        private List<Long> couponBatchIds;// 优惠券批次ID
        private String bgImageForOne;// 一张优惠券弹窗背景
        private String bgImageForMore;// 多张优惠券背景图
        private int distributionPopulation;

        private int frontBtnType;//领劵前按钮类型 0:文字 1:图片
        private String frontBtnTitle;//领劵前按钮的内容
        private String frontOpenFontColor;//领劵前文字颜色
        private String frontStartBgColor; //领劵前按钮背景为颜色时开始渐变色
        private String frontEndBgColor; //领劵前按钮背景为颜色时结束渐变色
        private String frontBtnImage; //领劵前按钮背景为图片

        private int backBtnType;//领劵后按钮类型 0:文字 1:图片
        private String backBtnTitle; //领劵后按钮的内容
        private String backOpenFontColor; //领劵后文字颜色
        private String backStartBgColor; //领劵后按钮背景为颜色时开始渐变色
        private String backEndBgColor; //领劵后按钮背景为颜色时结束渐变色
        private String backBtnImage; //领劵后按钮背景为图片

        /**
         * 轻松一刻封面图
         */
        private String coverImg;


        private String xmlUrl;

        private SeckillBean seckill;//秒杀
        private PromotionBean promotion;//促销
        private PromotionBean assemble;//拼团

        private int selectedIndex;//选中group中哪一个index
        private List<FoodItemBean> group;//速食快餐

        private List<TabBean> tab;
        //一下用于 优惠券楼层
        private String title; //标题,每天10点抢开店红包 && 7club气泡展示文字
        private String subTitle;// 运营语
        private String clsTag; //TAB埋点
        private String discountDesc; //满100减20
        private String validate; //有效期
        private String batchId; //优惠券批次I
        private String encodeBatchId; //优惠券批次I
        private int totalNumber; //优惠券总数
        private int remainNumber; //剩余优惠券
        private int status; //优惠券状态

        private int tabPosition;  //tab  开始楼层数据
        private int tabEndPosition;//tab  结束楼层数据


        private String availableCouponAmount;//  新人有礼 优惠价格
        private String freshPersonBackGround;//  新人有礼 背景色
        private String availableCouponInfo;// 新人有礼 2 张首单券即将过期
        private long couponDueTime;// 新人有礼 倒计时：23:11:30

        private boolean haveReceived;// 新人有礼 是否领过券
        private boolean hasNewPersonCoupon = true;// 新人有礼 是否有可用券

        public JSONObject getBuriedPointVo() {
            return buriedPointVo;
        }

        // 7lcube专用 floorType 103
        private String bgStartColor;// 气泡背景渐变开始颜色
        private String bgEndColor;// 气泡背景渐变结束颜色
        private String fontColor;// 气泡文字颜色

        private int isShowTop;
        private String link;
        private int styleType;
        private int isShowAdvert;

        private String floorBgImg; // 组件背景图片 1125 x 273
        private String onGoingFloorBgImg;// 进行中组件背景图片 1125 x 273
        private String overallBgImg;// 红包雨整体氛围背景图 750 x 1624
        private String bottomBgImg;// 红包雨底部氛围背景图 752 x 580
        private String redPacketImg;// 红包戳中前图片 270 x 270
        private String redPacketClickImg;// 红包戳中后图片 270 x 270
        private String countDownBgImg;// 倒计时弹窗背景图 750 x 1054
        private String countDownColor; // 倒计时颜色
        private String modalBgImg; // 结束后弹框背景图 584 x 834
        //场次状态 1:距离下一场活动>2小时，展示预告   2:距离下一场活动<2小时，展示倒计时        3:进行中未参与    4:进行中已参与    5:活动已结束
        private Integer couponRainStatus;
        //下一场开始时间："" 状态1、4时使用
        private String nextStartTime;
        //H5红包雨落地页
        private String activityUrl;

        public void setBuriedPointVo(JSONObject buriedPointVo) {
            this.buriedPointVo = buriedPointVo;
        }

        public String getFloorBgImg() {
            return floorBgImg;
        }

        public void setFloorBgImg(String floorBgImg) {
            this.floorBgImg = floorBgImg;
        }

        public String getOnGoingFloorBgImg() {
            return onGoingFloorBgImg;
        }

        public void setOnGoingFloorBgImg(String onGoingFloorBgImg) {
            this.onGoingFloorBgImg = onGoingFloorBgImg;
        }

        public String getOverallBgImg() {
            return overallBgImg;
        }

        public void setOverallBgImg(String overallBgImg) {
            this.overallBgImg = overallBgImg;
        }

        public String getBottomBgImg() {
            return bottomBgImg;
        }

        public void setBottomBgImg(String bottomBgImg) {
            this.bottomBgImg = bottomBgImg;
        }

        public String getRedPacketImg() {
            return redPacketImg;
        }

        public void setRedPacketImg(String redPacketImg) {
            this.redPacketImg = redPacketImg;
        }

        public String getRedPacketClickImg() {
            return redPacketClickImg;
        }

        public void setRedPacketClickImg(String redPacketClickImg) {
            this.redPacketClickImg = redPacketClickImg;
        }

        public String getCountDownBgImg() {
            return countDownBgImg;
        }

        public void setCountDownBgImg(String countDownBgImg) {
            this.countDownBgImg = countDownBgImg;
        }

        public String getCountDownColor() {
            return countDownColor;
        }

        public void setCountDownColor(String countDownColor) {
            this.countDownColor = countDownColor;
        }

        public String getModalBgImg() {
            return modalBgImg;
        }

        public void setModalBgImg(String modalBgImg) {
            this.modalBgImg = modalBgImg;
        }

        public Integer getCouponRainStatus() {
            return couponRainStatus == 0 ? 5 : couponRainStatus;
        }

        public void setCouponRainStatus(Integer couponRainStatus) {
            this.couponRainStatus = couponRainStatus;
        }

        public String getNextStartTime() {
            return nextStartTime;
        }

        public void setNextStartTime(String nextStartTime) {
            this.nextStartTime = nextStartTime;
        }

        public String getActivityUrl() {
            return activityUrl;
        }

        public void setActivityUrl(String activityUrl) {
            this.activityUrl = activityUrl;
        }

        /**
         * 首页楼层是否曝光过
         */
        private boolean hasShowView = false;

        /**
         * newCustomerGoods 是否新人专享价
         */
        private boolean newCustomerGoods;

        /**
         * recommendTabId 首页底部推荐tabid
         */
        private String recommendTabId;
        /**
         * 每个楼层解析数据的时间
         */
        private long currentFloorParseDateTime;

        /**
         *  自动滑动定位。0:关，1:开
         */
        private int autoPosition;
        /**
         *      * 新人专享价加车是否提示
         *      
         */
        private boolean newPeopleAddCartTip;


        public boolean isUseLocalCacheData() {
            return useLocalCacheData;
        }

        public void setUseLocalCacheData(boolean useLocalCacheData) {
            this.useLocalCacheData = useLocalCacheData;
        }

        public int getAutoPosition() {
            return autoPosition;
        }

        public void setAutoPosition(int autoPosition) {
            this.autoPosition = autoPosition;
        }


        public String getImageGif() {
            return imageGif;
        }

        public void setImageGif(String imageGif) {
            this.imageGif = imageGif;
        }


        public List<Rule> getRuleList() {
            return ruleList;
        }

        public void setRuleList(List<Rule> ruleList) {
            this.ruleList = ruleList;
        }

        public int getShowType() {
            return showType;
        }

        public void setShowType(int showType) {
            this.showType = showType;
        }

        public String getImageTitle() {
            return imageTitle;
        }

        public void setImageTitle(String imageTitle) {
            this.imageTitle = imageTitle;
        }

        public String getThemeColor() {
            return StringUtil.isEmpty(themeColor) ? "#004C45" : themeColor;
        }

        public void setThemeColor(String themeColor) {
            this.themeColor = themeColor;
        }


        public int getIsShowTop() {
            return isShowTop;
        }

        public void setIsShowTop(int isShowTop) {
            this.isShowTop = isShowTop;
        }

        public int getStyleType() {
            return styleType;
        }

        public void setStyleType(int styleType) {
            this.styleType = styleType;
        }

        public int getIsShowAdvert() {
            return isShowAdvert;
        }

        public void setIsShowAdvert(int isShowAdvert) {
            this.isShowAdvert = isShowAdvert;
        }

        public BuriedPointVoV2 getBuriedPointVoV2() {
            return buriedPointVoV2;
        }

        public void setBuriedPointVoV2(BuriedPointVoV2 buriedPointVoV2) {
            this.buriedPointVoV2 = buriedPointVoV2;
        }

        /**
         * 红包成功 失败action
         */
        public static class RedPackageVision implements Serializable {
            private ActionBean action;
            private String image;

            public ActionBean getAction() {
                return action;
            }

            public void setAction(ActionBean action) {
                this.action = action;
            }

            public String getImage() {
                return image;
            }

            public void setImage(String image) {
                this.image = image;
            }
        }

        public RedPackageVision getSuccessVision() {
            return successVision;
        }

        public void setSuccessVision(RedPackageVision successVision) {
            this.successVision = successVision;
        }

        public RedPackageVision getFailVision() {
            return failVision;
        }

        public void setFailVision(RedPackageVision failVision) {
            this.failVision = failVision;
        }


        public float getBackHeight() {
            return backHeight;
        }

        public void setBackHeight(float backHeight) {
            this.backHeight = backHeight;
        }

        public float getContentHeight() {
            return contentHeight;
        }

        public void setContentHeight(float contentHeight) {
            this.contentHeight = contentHeight;
        }

        public int getMultipleBackPic() {
            return multipleBackPic;
        }

        public void setMultipleBackPic(int multipleBackPic) {
            this.multipleBackPic = multipleBackPic;
        }

        /**
         * 天降红包
         */
        public int getAnimation() {
            return animation;
        }

        public void setAnimation(int animation) {
            this.animation = animation;
        }

        public List<Long> getCouponBatchIds() {
            return couponBatchIds;
        }

        public String getBgImageForOne() {
            return bgImageForOne;
        }

        public String getBgImageForMore() {
            return bgImageForMore;
        }

        public int getDistributionPopulation() {
            return distributionPopulation;
        }

        public int setDistributionPopulation(int distributionPopulation) {
            return this.distributionPopulation = distributionPopulation;
        }

        public int getFrontBtnType() {
            return frontBtnType;
        }

        public String getFrontBtnTitle() {
            return frontBtnTitle;
        }

        public String getFrontOpenFontColor() {
            return frontOpenFontColor;
        }

        public String getFrontStartBgColor() {
            return frontStartBgColor;
        }

        public String getFrontEndBgColor() {
            return frontEndBgColor;
        }

        public String getFrontBtnImage() {
            return frontBtnImage;
        }

        public int getBackBtnType() {
            return backBtnType;
        }

        public String getBackBtnTitle() {
            return backBtnTitle;
        }

        public String getBackOpenFontColor() {
            return backOpenFontColor;
        }

        public String getBackStartBgColor() {
            return backStartBgColor;
        }

        public String getBackEndBgColor() {
            return backEndBgColor;
        }

        public String getBackBtnImage() {
            return backBtnImage;
        }

        public void setCouponBatchIds(List<Long> couponBatchIds) {
            this.couponBatchIds = couponBatchIds;
        }

        public void setBgImageForOne(String bgImageForOne) {
            this.bgImageForOne = bgImageForOne;
        }

        public void setBgImageForMore(String bgImageForMore) {
            this.bgImageForMore = bgImageForMore;
        }

        public void setFrontBtnType(int frontBtnType) {
            this.frontBtnType = frontBtnType;
        }

        public void setFrontBtnTitle(String frontBtnTitle) {
            this.frontBtnTitle = frontBtnTitle;
        }

        public void setFrontOpenFontColor(String frontOpenFontColor) {
            this.frontOpenFontColor = frontOpenFontColor;
        }

        public void setFrontStartBgColor(String frontStartBgColor) {
            this.frontStartBgColor = frontStartBgColor;
        }

        public void setFrontEndBgColor(String frontEndBgColor) {
            this.frontEndBgColor = frontEndBgColor;
        }

        public void setFrontBtnImage(String frontBtnImage) {
            this.frontBtnImage = frontBtnImage;
        }

        public void setBackBtnType(int backBtnType) {
            this.backBtnType = backBtnType;
        }

        public void setBackBtnTitle(String backBtnTitle) {
            this.backBtnTitle = backBtnTitle;
        }

        public void setBackOpenFontColor(String backOpenFontColor) {
            this.backOpenFontColor = backOpenFontColor;
        }

        public void setBackStartBgColor(String backStartBgColor) {
            this.backStartBgColor = backStartBgColor;
        }

        public void setBackEndBgColor(String backEndBgColor) {
            this.backEndBgColor = backEndBgColor;
        }

        public void setBackBtnImage(String backBtnImage) {
            this.backBtnImage = backBtnImage;
        }

        /**
         * 定投优惠券组件信息
         */
        private String floorUuid;//楼层唯一标识


        public String getFloorUuid() {
            return floorUuid;
        }

        public void setFloorUuid(String floorUuid) {
            this.floorUuid = floorUuid;
        }

        public String getImgUrl() {
            return imgUrl;
        }

        public void setImgUrl(String imgUrl) {
            this.imgUrl = imgUrl;
        }


        /**
         * 拖地页楼层数据
         */
        private TenantShopInfo tenantShopInfo;

        public TenantShopInfo getTenantShopInfo() {
            return tenantShopInfo;
        }

        public void setTenantShopInfo(TenantShopInfo tenantShopInfo) {
            this.tenantShopInfo = tenantShopInfo;
        }

        public boolean isNewPeopleAddCartTip() {
            return newPeopleAddCartTip;
        }

        public void setNewPeopleAddCartTip(boolean newPeopleAddCartTip) {
            this.newPeopleAddCartTip = newPeopleAddCartTip;
        }

        public boolean isHasShowView() {
            return hasShowView;
        }

        public void setHasShowView(boolean hasShowView) {
            this.hasShowView = hasShowView;
        }

        public int getPositionGoods() {
            return positionGoods;
        }

        public void setPositionGoods(int positionGoods) {
            this.positionGoods = positionGoods;
        }

        public String getCoverImg() {
            return coverImg;
        }

        public void setCoverImg(String coverImg) {
            this.coverImg = coverImg;
        }

        public List<StallInfo> getStallInfoList() {
            return stallInfoList;
        }

        public void setStallInfoList(List<StallInfo> stallInfoList) {
            this.stallInfoList = stallInfoList;
        }


        public long getCurrentFloorParseDateTime() {
            return currentFloorParseDateTime;
        }

        public void setCurrentFloorParseDateTime(long currentFloorParseDateTime) {
            this.currentFloorParseDateTime = currentFloorParseDateTime;
        }

        public int getFloorNum() {
            return floorNum;
        }

        public void setFloorNum(int floorNum) {
            this.floorNum = floorNum;
        }

        public String getRecommendTabId() {
            return recommendTabId;
        }

        public void setRecommendTabId(String recommendTabId) {
            this.recommendTabId = recommendTabId;
        }

        public List<DynamicLablesBean> getDynamicLabels() {
            return dynamicLabels;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public void setDynamicLabels(List<DynamicLablesBean> dynamicLabels) {
            this.dynamicLabels = dynamicLabels;
        }

        public boolean isHasNewPersonCoupon() {
            return hasNewPersonCoupon;
        }

        public void setHasNewPersonCoupon(boolean hasNewPersonCoupon) {
            this.hasNewPersonCoupon = hasNewPersonCoupon;
        }

        public String getAvailableCouponAmount() {
            return availableCouponAmount;
        }

        public void setAvailableCouponAmount(String availableCouponAmount) {
            this.availableCouponAmount = availableCouponAmount;
        }

        public String getFreshPersonBackGround() {
            return freshPersonBackGround;
        }

        public void setFreshPersonBackGround(String freshPersonBackGround) {
            this.freshPersonBackGround = freshPersonBackGround;
        }

        public String getAvailableCouponInfo() {
            return availableCouponInfo;
        }

        public void setAvailableCouponInfo(String availableCouponInfo) {
            this.availableCouponInfo = availableCouponInfo;
        }

        public long getCouponDueTime() {
            return couponDueTime;
        }

        public void setCouponDueTime(long couponDueTime) {
            this.couponDueTime = couponDueTime;
        }

        public boolean isHaveReceived() {
            return haveReceived;
        }

        public void setHaveReceived(boolean haveReceived) {
            this.haveReceived = haveReceived;
        }

        public String getClsTag() {
            return clsTag;
        }

        public void setClsTag(String clsTag) {
            this.clsTag = clsTag;
        }

        public String getEncodeBatchId() {
            return encodeBatchId;
        }

        public void setEncodeBatchId(String encodeBatchId) {
            this.encodeBatchId = encodeBatchId;
        }

        public int getTabPosition() {
            return tabPosition;
        }

        public boolean isNewCustomerGoods() {
            return newCustomerGoods;
        }

        public void setNewCustomerGoods(boolean newCustomerGoods) {
            this.newCustomerGoods = newCustomerGoods;
        }

        public String getBgStartColor() {
            return bgStartColor;
        }

        public String getBgEndColor() {
            return bgEndColor;
        }

        public String getFontColor() {
            return fontColor;
        }

        public void setBgStartColor(String bgStartColor) {
            this.bgStartColor = bgStartColor;
        }

        public void setBgEndColor(String bgEndColor) {
            this.bgEndColor = bgEndColor;
        }

        public void setFontColor(String fontColor) {
            this.fontColor = fontColor;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            FloorsBean that = (FloorsBean) o;
            return floorType == that.floorType &&
                    popCondition == that.popCondition &&
                    started == that.started &&
                    seckillId == that.seckillId &&
                    restseckillTime == that.restseckillTime &&
                    secondkillTime == that.secondkillTime &&
                    horizontalScrollIndicator == that.horizontalScrollIndicator &&
                    pageRecommend == that.pageRecommend &&
                    positionRecommend == that.positionRecommend &&
                    floorStyle == that.floorStyle &&
                    isFirstRecommend == that.isFirstRecommend &&
                    isMemeberCover == that.isMemeberCover &&
                    selectedIndex == that.selectedIndex &&
                    totalNumber == that.totalNumber &&
                    remainNumber == that.remainNumber &&
                    status == that.status &&
                    tabPosition == that.tabPosition &&
                    tabEndPosition == that.tabEndPosition &&
                    currentPosition == that.currentPosition &&
                    newCustomerGoods == that.newCustomerGoods &&
                    ObjectLocals.equals(availableCouponAmount, that.availableCouponAmount) &&
                    ObjectLocals.equals(name, that.name) &&
                    ObjectLocals.equals(firstTitle, that.firstTitle) &&
                    ObjectLocals.equals(image, that.image) &&
                    ObjectLocals.equals(secondTitle, that.secondTitle) &&
                    ObjectLocals.equals(thirdTitle, that.thirdTitle) &&
                    ObjectLocals.equals(action, that.action) &&
                    ObjectLocals.equals(actions, that.actions) &&
                    ObjectLocals.equals(moreAction, that.moreAction) &&
                    ObjectLocals.equals(items, that.items) &&
                    ObjectLocals.equals(time, that.time) &&
                    ObjectLocals.equals(activityId, that.activityId) &&
                    ObjectLocals.equals(moreUrl, that.moreUrl) &&
                    ObjectLocals.equals(backGroudColor, that.backGroudColor) &&
                    ObjectLocals.equals(contentbackGroudColor, that.contentbackGroudColor) &&
                    ObjectLocals.equals(titleColor, that.titleColor) &&
                    ObjectLocals.equals(pictureAspect, that.pictureAspect) &&
                    ObjectLocals.equals(leftImage, that.leftImage) &&
                    ObjectLocals.equals(cashbalances, that.cashbalances) &&
                    ObjectLocals.equals(mypoints, that.mypoints) &&
                    ObjectLocals.equals(successImage, that.successImage) &&
                    ObjectLocals.equals(alreadyReceivedImage, that.alreadyReceivedImage) &&
                    ObjectLocals.equals(failImage, that.failImage) &&
                    ObjectLocals.equals(xmlUrl, that.xmlUrl) &&
                    ObjectLocals.equals(seckill, that.seckill) &&
                    ObjectLocals.equals(promotion, that.promotion) &&
                    ObjectLocals.equals(assemble, that.assemble) &&
                    ObjectLocals.equals(group, that.group) &&
                    ObjectLocals.equals(tab, that.tab) &&
                    ObjectLocals.equals(title, that.title) &&
                    ObjectLocals.equals(discountDesc, that.discountDesc) &&
                    ObjectLocals.equals(validate, that.validate) &&
                    ObjectLocals.equals(batchId, that.batchId) &&
                    ObjectLocals.equals(memberActions, that.memberActions);
        }

        public void setTabPosition(int tabPosition) {
            this.tabPosition = tabPosition;
        }

        public int getTabEndPosition() {
            return tabEndPosition;
        }

        public void setTabEndPosition(int tabEndPosition) {
            this.tabEndPosition = tabEndPosition;
        }

        private Long remainingTime;//剩余时间
        private Long durationTime;//持续时间

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubTitle() {
            return subTitle;
        }

        public void setSubTitle(String subTitle) {
            this.subTitle = subTitle;
        }

        public String getDiscountDesc() {
            return discountDesc;
        }

        public void setDiscountDesc(String discountDesc) {
            this.discountDesc = discountDesc;
        }

        public String getValidate() {
            return validate;
        }

        public void setValidate(String validate) {
            this.validate = validate;
        }

        public String getBatchId() {
            return batchId;
        }

        public void setBatchId(String batchId) {
            this.batchId = batchId;
        }

        public int getTotalNumber() {
            return totalNumber;
        }

        public void setTotalNumber(int totalNumber) {
            this.totalNumber = totalNumber;
        }

        public int getRemainNumber() {
            return remainNumber;
        }

        public void setRemainNumber(int remainNumber) {
            this.remainNumber = remainNumber;
        }

        public List<TabBean> getTab() {
            return tab;
        }

        public void setTab(List<TabBean> tab) {
            this.tab = tab;
        }

        public String getSecondTitle() {
            return secondTitle;
        }

        public void setSecondTitle(String secondTitle) {
            this.secondTitle = secondTitle;
        }

        public String getTitleColor() {
            return titleColor;
        }

        public void setTitleColor(String titleColor) {
            this.titleColor = titleColor;
        }

        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        /**
         * memberActions : {"leftMemberAction":{"title":"文案","action":{}},"rightMemberAction":{"title":"文案","action":{}}}
         */


        private MemberActionsBean memberActions;  //会员专区 增加营销文案配置

        public MemberActionsBean getMemberActions() {
            return memberActions;
        }

        public void setMemberActions(MemberActionsBean memberActions) {
            this.memberActions = memberActions;
        }

        public static class MemberActionsBean implements Serializable {
            private MemberAction leftMemberAction;
            private MemberAction rightMemberAction;

            public MemberAction getLeftMemberAction() {
                return leftMemberAction;
            }

            public void setLeftMemberAction(MemberAction leftMemberAction) {
                this.leftMemberAction = leftMemberAction;
            }

            public MemberAction getRightMemberAction() {
                return rightMemberAction;
            }

            public void setRightMemberAction(MemberAction rightMemberAction) {
                this.rightMemberAction = rightMemberAction;
            }
        }

        public static class MemberAction {

            private String title;
            private ActionBean action;

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public ActionBean getAction() {
                return action;
            }

            public void setAction(ActionBean action) {
                this.action = action;
            }
        }

        public String getXmlUrl() {
            return xmlUrl;
        }

        public void setXmlUrl(String xmlUrl) {
            this.xmlUrl = xmlUrl;
        }

        public ActionBean getMoreAction() {
            return moreAction;
        }

        public void setMoreAction(ActionBean moreAction) {
            this.moreAction = moreAction;
        }

        public String getSuccessImage() {
            return successImage;
        }

        public void setSuccessImage(String successImage) {
            this.successImage = successImage;
        }

        public String getAlreadyReceivedImage() {
            return alreadyReceivedImage;
        }

        public void setAlreadyReceivedImage(String alreadyReceivedImage) {
            this.alreadyReceivedImage = alreadyReceivedImage;
        }

        public String getFailImage() {
            return failImage;
        }

        public void setFailImage(String failImage) {
            this.failImage = failImage;
        }

        public int getFloorStyle() {
            return floorStyle;
        }

        public void setFloorStyle(int floorStyle) {
            this.floorStyle = floorStyle;
        }

        public String getContentbackGroudColor() {
            return contentbackGroudColor;
        }

        public void setContentbackGroudColor(String contentbackGroudColor) {
            this.contentbackGroudColor = contentbackGroudColor;
        }

        public int getPositionRecommend() {
            return positionRecommend;
        }

        public void setPositionRecommend(int positionRecommend) {
            this.positionRecommend = positionRecommend;
        }

        public int getPageRecommend() {
            return pageRecommend;
        }

        public void setPageRecommend(int pageRecommend) {
            this.pageRecommend = pageRecommend;
        }

        public boolean isMemeberCover() {
            return isMemeberCover;
        }

        public void setMemeberCover(boolean memeberCover) {
            isMemeberCover = memeberCover;
        }

        public boolean isFirstRecommend() {
            return isFirstRecommend;
        }

        public void setFirstRecommend(boolean firstRecommend) {
            isFirstRecommend = firstRecommend;
        }

        public String getCashbalances() {
            return cashbalances;
        }

        public void setCashbalances(String cashbalances) {
            this.cashbalances = cashbalances;
        }

        public String getMypoints() {
            return mypoints;
        }

        public void setMypoints(String mypoints) {
            this.mypoints = mypoints;
        }

        public String getLeftImage() {
            return leftImage;
        }

        public void setLeftImage(String leftImage) {
            this.leftImage = leftImage;
        }

        private int currentPosition;  //用来 设置商品间距离


        public int getCurrentPosition() {
            return currentPosition;
        }

        public void setCurrentPosition(int currentPosition) {
            this.currentPosition = currentPosition;
        }


        public long getSecondkillTime() {
            return secondkillTime;
        }

        public void setSecondkillTime(long secondkillTime) {
            this.secondkillTime = secondkillTime;
        }

        public String getBackGroudColor() {
            return backGroudColor;
        }

        public void setBackGroudColor(String backGroudColor) {
            this.backGroudColor = backGroudColor;
        }

        public List<ActionBean> getActions() {
            return actions;
        }

        public void setActions(List<ActionBean> actions) {
            this.actions = actions;
        }

        public int getHorizontalScrollIndicator() {
            return horizontalScrollIndicator;
        }

        public void setHorizontalScrollIndicator(int horizontalScrollIndicator) {
            this.horizontalScrollIndicator = horizontalScrollIndicator;
        }

        public String getMoreUrl() {
            return moreUrl;
        }

        public void setMoreUrl(String moreUrl) {
            this.moreUrl = moreUrl;
        }

        public int getPopCondition() {
            return popCondition;
        }

        public void setPopCondition(int popCondition) {
            this.popCondition = popCondition;
        }

        public String getPictureAspect() {
            return StringUtil.isEmpty(pictureAspect) ? "4.12" : pictureAspect;
        }

        public void setPictureAspect(String pictureAspect) {
            this.pictureAspect = pictureAspect;
        }

        public boolean isStarted() {
            return started;
        }

        public void setStarted(boolean started) {
            this.started = started;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public long getSeckillId() {
            return seckillId;
        }

        public void setSeckillId(long seckillId) {
            this.seckillId = seckillId;
        }

        public long getRestseckillTime() {
            return restseckillTime;
        }

        public void setRestseckillTime(long restseckillTime) {
            this.restseckillTime = restseckillTime;
        }

        public int getFloorType() {
            return floorType;
        }

        public void setFloorType(int floorType) {
            this.floorType = floorType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getFirstTitle() {
            if (firstTitle == null) {
                return "";
            }
            return firstTitle;
        }

        public void setFirstTitle(String firstTitle) {
            this.firstTitle = firstTitle;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getSencondTitle() {
            if (secondTitle == null) {
                return "";
            }
            return secondTitle;
        }

        public void setSencondTitle(String sencondTitle) {
            this.secondTitle = sencondTitle;
        }

        public String getThirdTitle() {
            return thirdTitle;
        }

        public void setThirdTitle(String thirdTitle) {
            this.thirdTitle = thirdTitle;
        }

        public ActionBean getAction() {
            return action;
        }

        public void setAction(ActionBean action) {
            this.action = action;
        }

        public List<ItemsBean> getItems() {
            return items;
        }

        public void setItems(List<ItemsBean> items) {
            this.items = items;
        }

        public SeckillBean getSeckill() {
            return seckill;
        }

        public void setSeckill(SeckillBean seckill) {
            this.seckill = seckill;
        }

        public PromotionBean getPromotion() {
            return promotion;
        }

        public void setPromotion(PromotionBean promotion) {
            this.promotion = promotion;
        }

        public PromotionBean getAssemble() {
            return assemble;
        }

        public void setAssemble(PromotionBean assemble) {
            this.assemble = assemble;
        }

        public List<FoodItemBean> getGroup() {
            return group;
        }

        public void setGroup(List<FoodItemBean> group) {
            this.group = group;
        }

        public List getComboBeanList() {
            List<Object> groupList = new ArrayList<>();
            if (seckill != null && !seckill.isItemsEmpty()) {
                groupList.add(seckill);
            }
            if (promotion != null && !promotion.isItemsEmpty()) {
                groupList.add(promotion);
            }
            if (assemble != null && !assemble.isItemsEmpty()) {
                groupList.add(assemble);
            }
            return groupList;
        }

        public int getSelectedIndex() {
            return selectedIndex;
        }

        public void setSelectedIndex(int selectedIndex) {
            this.selectedIndex = selectedIndex;
        }

        public boolean equalsSeckill(SeckillBean seckillBean) {
            return seckillBean != null && seckill != null && seckill.equals(seckillBean);
        }

        public boolean hasComboFloorEmpty() {
            return getComboBeanList().isEmpty();
        }

        public Long getRemainingTime() {
            return remainingTime;
        }

        public void setRemainingTime(Long remainingTime) {
            this.remainingTime = remainingTime;
        }

        public Long getDurationTime() {
            return durationTime;
        }

        public void setDurationTime(Long durationTime) {
            this.durationTime = durationTime;
        }

        public static class ActionBean implements Serializable {
            /**
             * toUrl : hhhhpppppppp
             * urlType : 3
             */

            private String toUrl;
            private String clsTag;
            private int urlType;
            private float startX;
            private float startY;
            private float endX;
            private float endY;

            //
            private String pageType;//rn, flutter
            private List<KeyValue> params;

            public String getClsTag() {
                return clsTag;
            }

            public void setClsTag(String clsTag) {
                this.clsTag = clsTag;
            }

            public String getToUrl() {
                return toUrl;
            }

            public void setToUrl(String toUrl) {
                this.toUrl = toUrl;
            }

            public int getUrlType() {
                return urlType;
            }

            public void setUrlType(int urlType) {
                this.urlType = urlType;
            }

            public float getStartX() {
                return startX;
            }

            public void setStartX(float startX) {
                this.startX = startX;
            }

            public float getStartY() {
                return startY;
            }

            public void setStartY(float startY) {
                this.startY = startY;
            }

            public float getEndX() {
                return endX;
            }

            public void setEndX(float endX) {
                this.endX = endX;
            }

            public float getEndY() {
                return endY;
            }

            public void setEndY(float endY) {
                this.endY = endY;
            }

            public String getPageType() {
                return pageType;
            }

            public void setPageType(String pageType) {
                this.pageType = pageType;
            }

            public List<KeyValue> getParams() {
                return params;
            }

            public String getJsonParams() {
                if (params == null || params.isEmpty()) {
                    return "";
                }
                StringBuffer jsonParams = new StringBuffer("{");
                try {
                    //jsonParams = new Gson().toJson(params);
                    for (int i = 0; i < params.size(); i++) {
                        KeyValue keyValue = params.get(i);

                        jsonParams.append("\"");
                        jsonParams.append(keyValue.getKey());
                        jsonParams.append("\"");
                        jsonParams.append(":");
                        jsonParams.append(keyValue.getValue());

                        if (i != params.size() - 1) {
                            jsonParams.append(",");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                jsonParams.append("}");
                return jsonParams.toString();
            }

            public void setParams(List<KeyValue> params) {
                this.params = params;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }
                if (o == null || getClass() != o.getClass()) {
                    return false;
                }
                ActionBean that = (ActionBean) o;
                return urlType == that.urlType &&
                        Float.compare(that.startX, startX) == 0 &&
                        Float.compare(that.startY, startY) == 0 &&
                        Float.compare(that.endX, endX) == 0 &&
                        Float.compare(that.endY, endY) == 0 &&
                        ObjectLocals.equals(toUrl, that.toUrl) &&
                        ObjectLocals.equals(pageType, that.pageType) &&
                        ObjectLocals.equals(params, that.params);
            }

            @Override
            public int hashCode() {

                return ObjectLocals.hash(toUrl, clsTag, urlType, startX, startY, endX, endY);
            }

            public ActionBean cloneThis() {
                ActionBean actionBean = new ActionBean();
                actionBean.setToUrl(toUrl);
                actionBean.setClsTag(clsTag);
                actionBean.setUrlType(urlType);
                actionBean.setStartX(startX);
                actionBean.setStartY(startY);
                actionBean.setEndX(endX);
                actionBean.setEndY(endY);
                actionBean.setPageType(pageType);
                actionBean.setParams(params);

                return actionBean;
            }

            @Override
            public String toString() {
                StringBuffer sb = new StringBuffer();
                sb.append("[ toUrl = " + toUrl);
                sb.append(" , urlType = " + urlType);
                sb.append(" , clsTag = " + clsTag);
                sb.append(" , startX = " + startX);
                sb.append(" , startY = " + startY);
                sb.append(" , endX = " + endX);
                sb.append(" , endY = " + endY);
                sb.append(" , pageType = " + pageType);
                sb.append(" , params = " + params);
                sb.append(" ]");

                return sb.toString();
            }
        }


        public static class KeyValue implements Serializable {

            private String key;
            private String value;

            public String getKey() {
                return key;
            }

            public void setKey(String key) {
                this.key = key;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }
        }

        public static class ItemsBean extends ProductDetailBean.WareInfoBean implements Serializable {

            private ActionBean action;
            private String image;
            private String title;
            private String backImage;
            private String backgroundMainColor;
            /**
             * 首页楼层是否曝光过
             */
            private boolean hasShowView = false;
            //private String clsTag;
            private String flagImage; //商品 图片的描述
            // 团长说
            private String introduce;

            // 购买人数
            private String purchasePersonNum;

            /**
             * discountInfo 打折描述 4.3折
             */
            private String discountInfo;

            //拼团活动ID
            private String groupActivityId;

            //已售件数
            private String groupSaleNum;

            //商品商城价格
            private String basePrice;

            //商品团购价
            private String grouponPrice;

            //按钮状态
            private Integer buttonOption;

            //按钮名称
            private String buttonText;

            //团购图片
            private String groupImage;

            //团购标题
            private String grouponTitle;

            //团购人数
            private String memberCount;


            //团标识
            private String grouponSign;

            // 省*
            private String saveMoney;

            /**
             * 是否执行过新闻动画
             */
            private boolean hasDoNewsAnimal;


            /**
             * 用来动态设置图片价格 用于轮播图
             */

            private List<DynamicLablesBean> dynamicLabels;

            public List<DynamicLablesBean> getDynamicLabels() {
                return dynamicLabels;
            }

            public void setDynamicLabels(List<DynamicLablesBean> dynamicLabels) {
                this.dynamicLabels = dynamicLabels;
            }

            public boolean isHasShowView() {
                return hasShowView;
            }

            public void setHasShowView(boolean hasShowView) {
                this.hasShowView = hasShowView;
            }

            public String getGrouponSign() {
                return grouponSign;
            }

            public void setGrouponSign(String grouponSign) {
                this.grouponSign = grouponSign;
            }


            /**
             * CREAT_GROUPON(1, "立即开团")
             * JOIN_GROUPON(2, "立即参团")
             * SHARE_GROUPON(3,"邀请好友")
             * SUBMIT_ORDER(4,"提交订单")
             * FREEZE(5,"冻结按钮")
             */
            public Integer getButtonOption() {
                return buttonOption;
            }

            public void setButtonOption(Integer buttonOption) {
                this.buttonOption = buttonOption;
            }

            public String getSkuIntroduce() {
                return skuIntroduce;
            }

            public void setSkuIntroduce(String skuIntroduce) {
                this.skuIntroduce = skuIntroduce;
            }

            public boolean isHasDoNewsAnimal() {
                return hasDoNewsAnimal;
            }

            public void setHasDoNewsAnimal(boolean hasDoNewsAnimal) {
                this.hasDoNewsAnimal = hasDoNewsAnimal;
            }

            public String getBackImage() {
                return backImage;
            }

            public void setBackImage(String backImage) {
                this.backImage = backImage;
            }

            public String getBackgroundMainColor() {
                return backgroundMainColor;
            }

            public void setBackgroundMainColor(String backgroundMainColor) {
                this.backgroundMainColor = backgroundMainColor;
            }

            //推荐语
            private String skuIntroduce;


            public String getIntroduce() {
                return introduce;
            }

            public void setIntroduce(String introduce) {
                this.introduce = introduce;
            }

            public String getPurchasePersonNum() {
                return purchasePersonNum;
            }

            public void setPurchasePersonNum(String purchasePersonNum) {
                this.purchasePersonNum = purchasePersonNum;
            }

            public String getFlagImage() {
                return flagImage;
            }

            public void setFlagImage(String flagImage) {
                this.flagImage = flagImage;
            }

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }


            public ActionBean getAction() {
                return action;
            }

            public void setAction(ActionBean action) {
                this.action = action;
            }

            public String getImage() {
                return image;
            }

            public void setImage(String image) {
                this.image = image;
            }

            public String getDiscountInfo() {
                return discountInfo;
            }

            public void setDiscountInfo(String discountInfo) {
                this.discountInfo = discountInfo;
            }

            public String getGroupActivityId() {
                return groupActivityId;
            }

            public void setGroupActivityId(String groupActivityId) {
                this.groupActivityId = groupActivityId;
            }

            public String getGroupSaleNum() {
                return groupSaleNum;
            }

            public void setGroupSaleNum(String groupSaleNum) {
                this.groupSaleNum = groupSaleNum;
            }

            public String getBasePrice() {
                return basePrice;
            }

            public void setBasePrice(String basePrice) {
                this.basePrice = basePrice;
            }

            public String getGrouponPrice() {
                return grouponPrice;
            }

            public void setGrouponPrice(String grouponPrice) {
                this.grouponPrice = grouponPrice;
            }

            public String getButtonText() {
                return buttonText;
            }

            public void setButtonText(String buttonText) {
                this.buttonText = buttonText;
            }

            public String getGroupImage() {
                return groupImage;
            }

            public void setGroupImage(String groupImage) {
                this.groupImage = groupImage;
            }

            public String getGrouponTitle() {
                return grouponTitle;
            }

            public void setGrouponTitle(String grouponTitle) {
                this.grouponTitle = grouponTitle;
            }

            public String getMemberCount() {
                return memberCount;
            }

            public void setMemberCount(String memberCount) {
                this.memberCount = memberCount;
            }

            public String getSaveMoney() {
                return saveMoney;
            }

            public void setSaveMoney(String saveMoney) {
                this.saveMoney = saveMoney;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }
                if (o == null || getClass() != o.getClass()) {
                    return false;
                }
                ItemsBean itemsBean = (ItemsBean) o;
                ProductDetailBean.WareInfoBean wareInfoBean = (ProductDetailBean.WareInfoBean) o;
                return ObjectLocals.equals(purchasePersonNum, itemsBean.purchasePersonNum) &&
                        ObjectLocals.equals(action, itemsBean.action) &&
                        ObjectLocals.equals(image, itemsBean.image) &&
                        ObjectLocals.equals(title, itemsBean.title) &&
                        ObjectLocals.equals(flagImage, itemsBean.flagImage) &&
                        ObjectLocals.equals(introduce, itemsBean.introduce) &&
                        ObjectLocals.equals(discountInfo, itemsBean.discountInfo) &&
                        ObjectLocals.equals(groupActivityId, itemsBean.groupActivityId) &&
                        ObjectLocals.equals(groupSaleNum, itemsBean.groupSaleNum) &&
                        ObjectLocals.equals(basePrice, itemsBean.basePrice) &&
                        ObjectLocals.equals(grouponPrice, itemsBean.grouponPrice) &&
                        ObjectLocals.equals(buttonText, itemsBean.buttonText) &&
                        ObjectLocals.equals(groupImage, itemsBean.groupImage) &&
                        ObjectLocals.equals(grouponTitle, itemsBean.grouponTitle) &&
                        ObjectLocals.equals(memberCount, itemsBean.memberCount) &&
                        ObjectLocals.equals(buttonOption, itemsBean.buttonOption) &&
                        ObjectLocals.equals(skuIntroduce, itemsBean.skuIntroduce) &&
                        ObjectLocals.equals(grouponSign, itemsBean.grouponSign) &&
                        ObjectLocals.equals(hasShowView, itemsBean.hasShowView) &&
                        super.equals(wareInfoBean);
            }

        }

        /**
         * 秒杀
         */
        public static class SeckillBean implements Serializable {

            private ActionBean action;

            private Long restseckillTime;//秒杀时间
            //private Long beginDate;
            //private Long endDate;
            private boolean started;//
            //private String seckillId;
            private String firstTitle;
            private String secondTitle;

            private List<ItemsBean> items;//商品列表

            public ActionBean getAction() {
                return action;
            }

            public void setAction(ActionBean action) {
                this.action = action;
            }

            public Long getRestseckillTime() {
                return restseckillTime;
            }

            public void setRestseckillTime(Long restseckillTime) {
                this.restseckillTime = restseckillTime;
            }

            public boolean isStarted() {
                return started;
            }

            public void setStarted(boolean started) {
                this.started = started;
            }

            public String getFirstTitle() {
                return firstTitle == null ? "" : firstTitle;
            }

            public void setFirstTitle(String firstTitle) {
                this.firstTitle = firstTitle;
            }

            public String getSecondTitle() {
                return secondTitle == null ? "" : secondTitle;
            }

            public void setSecondTitle(String secondTitle) {
                this.secondTitle = secondTitle;
            }

            public List<ItemsBean> getItems() {
                return items;
            }

            public void setItems(List<ItemsBean> items) {
                this.items = items;
            }

            public boolean isItemsEmpty() {
                return items == null || items.isEmpty();
            }

            public SeckillBean cloneThis() {
                SeckillBean seckillBean = new SeckillBean();
                seckillBean.setAction(action);
                seckillBean.setRestseckillTime(restseckillTime);
                seckillBean.setStarted(started);
                seckillBean.setFirstTitle(firstTitle);
                seckillBean.setSecondTitle(secondTitle);
                seckillBean.setItems(items);

                return seckillBean;
            }

        }

        /**
         * 促销或拼团
         */
        public static class PromotionBean implements Serializable {

            private ActionBean action;
            private String firstTitle;
            private String secondTitle;

            private List<ItemsBean> items;

            public ActionBean getAction() {
                return action;
            }

            public void setAction(ActionBean action) {
                this.action = action;
            }

            public String getFirstTitle() {
                return firstTitle == null ? "" : firstTitle;
            }

            public void setFirstTitle(String firstTitle) {
                this.firstTitle = firstTitle;
            }

            public String getSecondTitle() {
                return secondTitle == null ? "" : secondTitle;
            }

            public void setSecondTitle(String secondTitle) {
                this.secondTitle = secondTitle;
            }

            public List<ItemsBean> getItems() {
                return items;
            }

            public void setItems(List<ItemsBean> items) {
                this.items = items;
            }

            public boolean isItemsEmpty() {
                return items == null || items.isEmpty();
            }
        }

        /**
         * 速食快餐item  推荐tab
         */
        public static class TabBean implements Serializable {
            /**
             * "title": "主标题",
             * "subTitle":"副标题",
             * "userInteractionEnabled":"是否可点击",
             */
            private boolean userInteractionEnabled = true;
            private List<FloorsBean> items;

            /**
             * title 标题
             */
            private String title;
            /**
             * tab 点击埋点
             */
            private JSONObject tabSourceBuriedPoint;
            /**
             * subTitle 副标题
             */
            private String subTitle;
            /**
             * tabId 类型id
             */
            private long tabId;
            /**
             * clsTag tab点击埋点
             */
            private String clsTag;


            /**
             * tabType tab类型
             */
            private String tabType;

            /**
             * 最外层 clstag
             */
            private String floorTags;
            private String titleStyle;
            private String noSelectedImgUrl;
            private String selectedImgUrl;
            private String pictureAspect;

            public String getTitleStyle() {
                return titleStyle;
            }

            public String getNoSelectedImgUrl() {
                return noSelectedImgUrl;
            }

            public String getSelectedImgUrl() {
                return selectedImgUrl;
            }

            public String getPictureAspect() {
                return pictureAspect;
            }

            public void setTitleStyle(String titleStyle) {
                this.titleStyle = titleStyle;
            }

            public void setNoSelectedImgUrl(String noSelectedImgUrl) {
                this.noSelectedImgUrl = noSelectedImgUrl;
            }

            public void setSelectedImgUrl(String selectedImgUrl) {
                this.selectedImgUrl = selectedImgUrl;
            }

            public void setPictureAspect(String pictureAspect) {
                this.pictureAspect = pictureAspect;
            }

            public JSONObject getTabSourceBuriedPoint() {
                return tabSourceBuriedPoint;
            }

            public void setTabSourceBuriedPoint(JSONObject tabSourceBuriedPoint) {
                this.tabSourceBuriedPoint = tabSourceBuriedPoint;
            }

            public String getFloorTags() {
                return floorTags;
            }

            public void setFloorTags(String floorTags) {
                this.floorTags = floorTags;
            }

            //内部传递
            private String recommendTabId;

            public String getRecommendTabId() {
                return recommendTabId;
            }

            public void setRecommendTabId(String recommendTabId) {
                this.recommendTabId = recommendTabId;
            }

            public String getTabType() {
                return tabType;
            }

            public void setTabType(String tabType) {
                this.tabType = tabType;
            }


            public long getTabId() {
                return tabId;
            }

            public void setTabId(long tabId) {
                this.tabId = tabId;
            }

            public String getClsTag() {
                return clsTag;
            }

            public void setClsTag(String clsTag) {
                this.clsTag = clsTag;
            }

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getSubTitle() {
                return subTitle;
            }

            public void setSubTitle(String subTitle) {
                this.subTitle = subTitle;
            }

            public boolean isUserInteractionEnabled() {
                return userInteractionEnabled;
            }

            public void setUserInteractionEnabled(boolean userInteractionEnabled) {
                this.userInteractionEnabled = userInteractionEnabled;
            }

            public List<FloorsBean> getItems() {
                return items;
            }

            public void setItems(List<FloorsBean> items) {
                this.items = items;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {return true;}
                if (o == null || getClass() != o.getClass()) {return false;}
                TabBean tabBean = (TabBean) o;
                return userInteractionEnabled == tabBean.userInteractionEnabled &&
                        tabId == tabBean.tabId &&
                        ObjectLocals.equals(items, tabBean.items) &&
                        ObjectLocals.equals(title, tabBean.title) &&
                        ObjectLocals.equals(subTitle, tabBean.subTitle) &&
                        ObjectLocals.equals(clsTag, tabBean.clsTag) &&
                        ObjectLocals.equals(tabType, tabBean.tabType) &&
                        ObjectLocals.equals(floorTags, tabBean.floorTags) &&
                        ObjectLocals.equals(recommendTabId, tabBean.recommendTabId);
            }

            @Override
            public int hashCode() {
                return ObjectLocals.hash(userInteractionEnabled, items, title, subTitle, tabId, clsTag, tabType, floorTags, recommendTabId);
            }

        }

        /**
         * 速食快餐item
         */
        public static class FoodItemBean implements Serializable {

            private String title;//早餐
            /**
             * tab 颜色
             */
            private String titleColor;
            private String image;//背景图
            private String normalFlag;//未选中状态图标
            private String selectedFlag;//选中状态图标
            /**
             * 宽高比
             */
            private String pictureAspect;

            private List<ItemsBean> items;//商品列表

            public String getTitleColor() {
                return titleColor;
            }

            public void setTitleColor(String titleColor) {
                this.titleColor = titleColor;
            }

            public String getPictureAspect() {
                return pictureAspect;
            }

            public void setPictureAspect(String pictureAspect) {
                this.pictureAspect = pictureAspect;
            }

            private String clsTag;//埋点

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getImage() {
                return image;
            }

            public void setImage(String image) {
                this.image = image;
            }

            public String getNormalFlag() {
                return normalFlag;
            }

            public void setNormalFlag(String normalFlag) {
                this.normalFlag = normalFlag;
            }

            public String getSelectedFlag() {
                return selectedFlag;
            }

            public void setSelectedFlag(String selectedFlag) {
                this.selectedFlag = selectedFlag;
            }

            public List<ItemsBean> getItems() {
                return items;
            }

            public void setItems(List<ItemsBean> items) {
                this.items = items;
            }

            public String getClsTag() {
                return clsTag;
            }
        }

    }

    public static class AppThemeBean implements Serializable {
        /**
         * tintColor : abar选中文字高亮颜色
         * appThemeItems : [{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"},{"normalImageUrl":"tabarItem 正常图片","selectedImageUrl":"selectedImageUrl"}]
         */

        private String tintColor;
        private List<AppThemeItemsBean> appThemeItems;

        public String getTintColor() {
            return tintColor;
        }

        public void setTintColor(String tintColor) {
            this.tintColor = tintColor;
        }

        public List<AppThemeItemsBean> getAppThemeItems() {
            return appThemeItems;
        }

        public void setAppThemeItems(List<AppThemeItemsBean> appThemeItems) {
            this.appThemeItems = appThemeItems;
        }

        public static class AppThemeItemsBean implements Serializable {
            /**
             * normalImageUrl : tabarItem 正常图片
             * selectedImageUrl : selectedImageUrl
             */

            private String normalImageUrl;
            private String selectedImageUrl;

            public String getNormalImageUrl() {
                return normalImageUrl;
            }

            public void setNormalImageUrl(String normalImageUrl) {
                this.normalImageUrl = normalImageUrl;
            }

            public String getSelectedImageUrl() {
                return selectedImageUrl;
            }

            public void setSelectedImageUrl(String selectedImageUrl) {
                this.selectedImageUrl = selectedImageUrl;
            }
        }
    }

    /**
     * 栏目聚合控制组件规则 一行n个
     */
    public static class Rule implements Serializable {
        private int showType;

        public int getShowType() {
            return showType;
        }

        public void setShowType(int showType) {
            this.showType = showType;
        }
    }



    public static class BuriedPointVoV2 {
        private String siteKey;
        private String keycount;
        private int tenantId;
        private String storeId;
        private int oPageId;
        private String pageName;
        private int pageTypeId;
        private String pageTypeName;
        private String componentUuid;
        private int componentCode;
        private String componentName;
        private int activityId;
        private int floorNum;
        private int firstModuleId;
        private String firstModuleName;

        public void setSiteKey(String siteKey) {
            this.siteKey = siteKey;
        }

        public String getSiteKey() {
            return siteKey;
        }

        public void setKeycount(String keycount) {
            this.keycount = keycount;
        }

        public String getKeycount() {
            return keycount;
        }

        public void setTenantId(int tenantId) {
            this.tenantId = tenantId;
        }

        public int getTenantId() {
            return tenantId;
        }

        public void setStoreId(String storeId) {
            this.storeId = storeId;
        }

        public String getStoreId() {
            return storeId;
        }

        public void setOPageId(int oPageId) {
            this.oPageId = oPageId;
        }

        public int getOPageId() {
            return oPageId;
        }

        public void setPageName(String pageName) {
            this.pageName = pageName;
        }

        public String getPageName() {
            return pageName;
        }

        public void setPageTypeId(int pageTypeId) {
            this.pageTypeId = pageTypeId;
        }

        public int getPageTypeId() {
            return pageTypeId;
        }

        public void setPageTypeName(String pageTypeName) {
            this.pageTypeName = pageTypeName;
        }

        public String getPageTypeName() {
            return pageTypeName;
        }

        public void setComponentUuid(String componentUuid) {
            this.componentUuid = componentUuid;
        }

        public String getComponentUuid() {
            return componentUuid;
        }

        public void setComponentCode(int componentCode) {
            this.componentCode = componentCode;
        }

        public int getComponentCode() {
            return componentCode;
        }

        public void setComponentName(String componentName) {
            this.componentName = componentName;
        }

        public String getComponentName() {
            return componentName;
        }

        public void setActivityId(int activityId) {
            this.activityId = activityId;
        }

        public int getActivityId() {
            return activityId;
        }

        public void setFloorNum(int floorNum) {
            this.floorNum = floorNum;
        }

        public int getFloorNum() {
            return floorNum;
        }

        public void setFirstModuleId(int firstModuleId) {
            this.firstModuleId = firstModuleId;
        }

        public int getFirstModuleId() {
            return firstModuleId;
        }

        public void setFirstModuleName(String firstModuleName) {
            this.firstModuleName = firstModuleName;
        }

        public String getFirstModuleName() {
            return firstModuleName;
        }
    }
}
