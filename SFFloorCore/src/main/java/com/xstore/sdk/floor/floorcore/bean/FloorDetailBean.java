package com.xstore.sdk.floor.floorcore.bean;


import com.xstore.sdk.floor.floorcore.adapter.entity.MultiItemEntity;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 楼层模型
 */
public class FloorDetailBean implements Serializable, MultiItemEntity {

    /**
     * 楼层序号
     */
    private int floorNum;
    /**
     * 组件号
     */
    private long componentCode;
    /**
     * 组件名称
     */
    private String componentName;
    /**
     * 属性名
     */
    private List<String> fieldName;
    /**
     * 组件预估高度
     */
    private double componentHigh;
    /**
     * 动态参数
     */
    private String dynamicParam;
    /**
     * 二级属性名
     */
    private String secondFieldName;
    /**
     * 二级动态参数
     */
    private String secondDynamicParam;
    /**
     * 组件数据
     */
    private String componentData;
    /**
     * 解析后的数据
     */
    private Object componentDataObject;

    /**
     * 猜你喜欢数据
     */
    private Object MayLikeDataObject;

    /**
     * 搭配购
     */
    private Object collocationDataObject;
    /**
     * 楼层模板id
     */
    private String templateCode;

    /**
     * uuid
     */
    private String componentUuid;

    /**
     * 数据解析时间     //本地
     */
    private long dataParseTime;
    /**
     * 楼层数据组合
     */
    private int groupNum;
    /**
     * 过滤后的吸顶楼层位置     //本地
     */
    private int realIndex;
    /**
     * 状态码
     */
    private int dataStatus;

    //本地参数 ！！！
    private String storeId;
    /**
     * 本地参数 围栏id 和上面的门店id一样，用来比较当前要渲染的数据和当前的门店id是否一致，不一致的话 就丢弃
     */
    private String fenceId;
    //本地参数 是否为推荐
    private boolean localRecommend;
    //推荐模板id
    public static String recommendFloorTemplateCode;
    //本地参数 跳转商详的类型 1普通、2迷你
    private int jumpType;

    //推荐楼层的子Rcv是否滑动到顶部
    private boolean ViewPagerRecommendChildRcvIsTop;

    //更新推荐楼层子Rcv滑动状态
    private int updateViewPagerRecommendChildRcvScrollState;

    private boolean hasSearchFloor;


    List<FloorDetailBean> vpRecommendFloorList;


    /**
     * 本地参数
     */
    public final HashMap<String, Object> localParams = new HashMap<>(1);

    public int getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(int dataStatus) {
        this.dataStatus = dataStatus;
    }

    public void setComponentCode(long componentCode) {
        this.componentCode = componentCode;
    }

    public void setComponentData(String componentData) {
        this.componentData = componentData;
    }

    public void setComponentHigh(int componentHigh) {
        this.componentHigh = componentHigh;
    }

    public void setDynamicParam(String dynamicParam) {
        this.dynamicParam = dynamicParam;
    }


    public void setFloorNum(int floorNum) {
        this.floorNum = floorNum;
    }

    public long getComponentCode() {
        return componentCode;
    }


    public int getFloorNum() {
        return floorNum;
    }

    public String getComponentData() {
        return componentData;
    }

    public String getDynamicParam() {
        return dynamicParam;
    }

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

    public List<String> getFieldName() {
        return fieldName;
    }

    public void setFieldName(List<String> fieldName) {
        this.fieldName = fieldName;
    }

    public double getComponentHigh() {
        return componentHigh;
    }

    public void setComponentHigh(double componentHigh) {
        this.componentHigh = componentHigh;
    }

    public String getSecondFieldName() {
        return secondFieldName;
    }

    public void setSecondFieldName(String secondFieldName) {
        this.secondFieldName = secondFieldName;
    }

    public boolean isHasSearchFloor() {
        return hasSearchFloor;
    }

    public void setHasSearchFloor(boolean hasSearchFloor) {
        this.hasSearchFloor = hasSearchFloor;
    }

    public String getSecondDynamicParam() {
        return secondDynamicParam;
    }

    public void setSecondDynamicParam(String secondDynamicParam) {
        this.secondDynamicParam = secondDynamicParam;
    }

    public int getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(int groupNum) {
        this.groupNum = groupNum;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    /**
     * @return 获取当前模板名称的id（hashcode）
     */
    public int getTemplateHashCode() {
        if (templateCode != null) {
            return templateCode.hashCode();
        }
        return 0;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public long getDataParseTime() {
        return dataParseTime;
    }

    public void setDataParseTime(long dataParseTime) {
        this.dataParseTime = dataParseTime;
    }

    public void setComponentDataObject(Object dataObject) {
        this.componentDataObject = dataObject;
    }

    public Object getComponentDataObject() {
        return componentDataObject;
    }

    @Override
    public int getItemType() {
        return getTemplateHashCode();
    }

    public void setRealIndex(int i) {
        realIndex = i;
    }

    public int getRealIndex() {
        return realIndex;
    }

    public String getComponentUuid() {
        return componentUuid;
    }

    public void setComponentUuid(String componentUuid) {
        this.componentUuid = componentUuid;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getFenceId() {
        return fenceId;
    }

    public void setFenceId(String fenceId) {
        this.fenceId = fenceId;
    }

    public boolean isLocalRecommend() {
        return localRecommend;
    }

    public void setLocalRecommend(boolean localRecommend) {
        this.localRecommend = localRecommend;
    }

    public Object getMayLikeDataObject() {
        return MayLikeDataObject;
    }

    public void setMayLikeDataObject(Object mayLikeDataObject) {
        MayLikeDataObject = mayLikeDataObject;
    }

    public Object getCollocationDataObject() {
        return collocationDataObject;
    }

    public void setCollocationDataObject(Object collocationDataObject) {
        this.collocationDataObject = collocationDataObject;
    }


    public boolean isViewPagerRecommendChildRcvIsTop() {
        return ViewPagerRecommendChildRcvIsTop;
    }

    public void setViewPagerRecommendChildRcvIsTop(boolean viewPagerRecommendChildRcvIsTop) {
        ViewPagerRecommendChildRcvIsTop = viewPagerRecommendChildRcvIsTop;
    }

    public int getUpdateViewPagerRecommendChildRcvScrollState() {
        return updateViewPagerRecommendChildRcvScrollState;
    }

    public void setUpdateViewPagerRecommendChildRcvScrollState(int updateViewPagerRecommendChildRcvScrollState) {
        this.updateViewPagerRecommendChildRcvScrollState = updateViewPagerRecommendChildRcvScrollState;
    }

    public List<FloorDetailBean> getVpRecommendFloorList() {
        return vpRecommendFloorList;
    }

    public void setVpRecommendFloorList(List<FloorDetailBean> vpRecommendFloorList) {
        this.vpRecommendFloorList = vpRecommendFloorList;
    }
}
