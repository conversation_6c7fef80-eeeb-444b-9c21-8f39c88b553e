package com.xstore.sdk.floor.floorcore;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.jd.framework.json.JDJSONObject;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.CacheConfig;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

import java.util.HashMap;
import java.util.List;

/**
 * 基础网络请求
 */
public class FloorBaseNetwork {

    public static String INNER_SDK_VERSION = "1";

    /**
     * 当前请求的围栏id
     */
    public static final String LOCAL_FENCE_ID = "localFenceId";

    /**
     * 请求首页数据 通用方法
     *
     * @param context      上下文
     * @param effect       loading动画
     * @param backString   透传值
     * @param pageType     请求页面类型
     * @param fieldName    属性值
     * @param varsExtra    动态参数
     * @param requestStep  请求时序
     * @param sdkVersion   当前请求的sdk版本  不传的话 默认会跟随容器版本
     * @param screenHeight 屏幕高度
     * @param callback     回调
     */
    public static void requestGql(Context context, int effect, String backString,
                                  int pageType, List<String> fieldName, JDJSONObject varsExtra,
                                  int requestStep, @Nullable String sdkVersion, int screenHeight, BaseFreshResultCallback callback) {
        FreshHttpSetting setting = new FreshHttpSetting();
        setting.setFunctionId(FloorInit.getFloorConfig().getFunctionId()); //index_7fresh_graphql_query
        setting.setEffect(effect);
        setting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        setting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        JDJSONObject jsonObject = new JDJSONObject();
        jsonObject.put("pageType", pageType);
        if (TextUtils.isEmpty(sdkVersion)) {
            jsonObject.put("sdkVersion", INNER_SDK_VERSION);
        } else {
            jsonObject.put("sdkVersion", sdkVersion);
        }
        jsonObject.put("sdkChannel", "android");
        jsonObject.put("screenHigh", screenHeight); //暂时获取所有的数据，9999应该可以一次拉取到所有的首页数据
        jsonObject.put("terminalType", 3);
        if (varsExtra != null) {
            jsonObject.putAll(varsExtra);
        }
//        if(TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
//            setting.putJsonParam("tenantId", "0");
//        }
        setting.putJsonParam("variables", jsonObject);
        setting.putJsonParam("fieldName", fieldName);
        setting.putJsonParam("bizCode", FloorInit.getFloorConfig().getBizCode());
        setting.setBackString(backString);
        setting.setResultCallback(callback);
        HashMap<String, Object> var = new HashMap<>();
        var.put("localStoreId", TenantIdUtils.getStoreId());
        var.put(LOCAL_FENCE_ID, TenantIdUtils.getFenceId());
        var.put("requestStep", requestStep);
        var.put("requestTime", System.currentTimeMillis());
        setting.setCustomVariables(var);
        FreshHttpGroupUtils.getHttpGroup().add(context, setting);
    }

    /**
     * 请求首页缓存数据 通用方法
     *
     * @param context      上下文
     * @param effect       loading动画
     * @param backString   透传值
     * @param pageType     请求页面类型
     * @param fieldName    属性值
     * @param varsExtra    动态参数
     * @param requestStep  请求时序
     * @param sdkVersion   当前请求的sdk版本  不传的话 默认会跟随容器版本
     * @param screenHeight 屏幕高度
     * @param callback     回调
     */
    public static void requestGqlByCache(Context context, int effect, String backString,
                                         int pageType, List<String> fieldName, JDJSONObject varsExtra,
                                         int requestStep, @Nullable String sdkVersion, int screenHeight,
                                         CacheConfig config, BaseFreshResultCallback callback) {

        FreshHttpSetting setting = new FreshHttpSetting();
        if(config == null || config.isNeedRequest()){
            setting.setFunctionId(FloorInit.getFloorConfig().getFunctionId());
        }else{
            setting.setFunctionId("index_7fresh_graphql_query");
        }
        setting.setEffect(effect);
        setting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        setting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        JDJSONObject jsonObject = new JDJSONObject();
        jsonObject.put("pageType", pageType);
        if (TextUtils.isEmpty(sdkVersion)) {
            jsonObject.put("sdkVersion", INNER_SDK_VERSION);
        } else {
            jsonObject.put("sdkVersion", sdkVersion);
        }
        jsonObject.put("sdkChannel", "android");
        jsonObject.put("screenHigh", screenHeight); //暂时获取所有的数据，9999应该可以一次拉取到所有的首页数据
        jsonObject.put("terminalType", 3);
        if (varsExtra != null) {
            jsonObject.putAll(varsExtra);
        }
//        if(TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
//            setting.putJsonParam("tenantId", "0");
//        }
        setting.putJsonParam("variables", jsonObject);
        setting.putJsonParam("fieldName", fieldName);
        if(config == null || config.isNeedRequest()){
            setting.putJsonParam("bizCode", FloorInit.getFloorConfig().getBizCode());
        }else {
            setting.putJsonParam("bizCode", "7fresh");
        }
        setting.setBackString(backString);
        setting.setResultCallback(callback);
        HashMap<String, Object> var = new HashMap<>();
        var.put("localStoreId", TenantIdUtils.getStoreId());
        var.put(LOCAL_FENCE_ID, TenantIdUtils.getFenceId());
        var.put("requestStep", requestStep);
        var.put("requestTime", System.currentTimeMillis());
        setting.setCustomVariables(var);
        setting.setCacheConfig(config);

        FreshHttpGroupUtils.getHttpGroup().add(context,setting);
    }

}
