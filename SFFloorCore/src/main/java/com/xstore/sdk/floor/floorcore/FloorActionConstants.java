package com.xstore.sdk.floor.floorcore;

/**
 * 首页sdk跳转行为
 */
public interface FloorActionConstants {

    //////////////////////////  鲜橙预占的跳转行为 start   ///////////////////////////
    /**
     * 没有行为--默认跳转h5
     */
    int URL_TYPE_NO_LINK = 0;
    /**
     * 商详
     * 规则为 找到最后一个=，截取后面的参数，获取其中的skuId，promotionId，prepayCardType跳转商详，兜底h5
     */
    int URL_TYPE_GOOD_DETAIL = 1;
    /**
     * 跳转h5页面  如果url为home  代表返回首页
     */
    int URL_TYPE_M = 3;
    /**
     * 跳转类目
     * url不是空，并且全是数字时候跳转到1级分类  否则切换到分类tab
     */
    int URL_TYPE_CATEGORY = 5;
    /**
     * 其他活动 h5
     */
    int URL_TYPE_OTHER = 6;
    /**
     * 商品列表  h5
     */
    int URL_TYPE_GOODS_LIST = 7;

    /**
     * 秒杀列表
     */
    int URL_TYPE_SEC_KILL_LIST = 201;
    /**
     * 热销排行
     */
    int URL_TYPE_HOT_SALE_RANK = 202;
    /**
     * 新品上市
     */
    int URL_TYPE_NEW_PRODUCT = 203;
    /**
     * 邀请有礼
     */
    int URL_TYPE_INVITE_GIFT = 204;
    /**
     * 新人专享
     */
    int URL_TYPE_NEW_USER = 205;
    /**
     * 明星日签
     */
    int URL_TYPE_STAR_SIGN = 206;
    /**
     * 跳扫码
     */
    int URL_TYPE_SCAN_CODE = 207;
    /**
     * 付款码
     */
    int URL_TYPE_PAY_CODE = 208;
    /**
     * 新人必看 跳转h5
     */
    int URL_TYPE_NEW_PERSONAL_LOOK = 210;
    /**
     * 助力砍价 跳转h5
     */
    int ULR_TYPE_HELP_SALE = 211;
    /**
     * 集卡游戏
     */
    int URL_TYPE_TRADE_CARD_GAME = 215;
    /**
     * 待评价订单
     */
    int URL_TYPE_ORDER_WAIT_EVALUATED = 216;
    /**
     * 接龙列表
     */
    int URL_TYPE_SOLITAIRE_LIST = 218;
    /**
     * 接龙详情
     */
    int URL_TYPE_SOLITAIRE_DETAIL = 219;
    /**
     * 菜谱
     */
    int URL_TYPE_SEVEN_TASTE = 220;
    /**
     * 网红商品
     */
    int URL_TYPE_ONLINE_CELEBRITY = 222;
    /**
     * 跳转小程序
     */
    int URL_TYPE_MINI_PROGRAM = 224;
    /**
     * 跳转到视频播放页面
     */
    int URL_TYPE_VIDEO = 226;
    /**
     * 堂食
     */
    int URL_TYPE_DINE_IN = 230;
    /**
     * 跳转话题列表
     */
    int URL_TYPE_TOPIC = 231;
    /**
     * 跳转直播间 目前只支持这一种
     */
    int URL_TYPE_LIVE_ROOM = 233;

    //////////////////////////  鲜橙预占的跳转行为 end   ///////////////////////////



    //////////////////////////  本地预占的跳转行为 start   ///////////////////////////
    /**
     * 跳转首页地址列表
     */
    int URL_TYPE_ADDRESS_LIST = 10000;
    /**
     * 跳转搜索主页
     */
    int URL_TYPE_SEARCH_HOME = 10001;
    /**
     * 跳转商品搜索列表
     */
    int URL_TYPE_PRODUCT_LIST = 10002;
    /**
     * 登录 可能需要关注登录结果
     */
    int URL_TYPE_LOGIN = 10003;
    /**
     * 领取只能优惠券
     */
    int URL_TYPE_USE_INTELLIGENCE_COUPON = 10004;
    /**
     * 找相似列表
     */
    int URL_TYPE_SIMILAR_LIST = 10005;
    /**
     * 立即预定
     */
    int URL_TYPE_PRE_SALE_NOW = 10006;
    /**
     * 跳转换购页
     */
    int URL_TYPE_INCREASE_REPURCHASE = 10007;
    /**
     * 跳转购物车
     */
    int URL_TYPE_CART = 10008;
    /**
     * 回首页
     */
    int URL_TYPE_BACK_HOME = 10009;
    /**
     * 跳转常购清单
     */
    int URL_TYPE_FREQUENTPURCHASE = 10010;
    /**
     * 跳转消息中心
     */
    int URL_TYPE_MESSAGE_CENTER = 10011;

    /**
     * 跳转到订单详情
     */
   int URL_TYPE_ORDER_DETAIL = 10012;

    /**
     * 搜索结果页面
     */
   int URL_TYPE_SEARCH_RESULT = 10013;

    /**
     * 泽拉图商品列表
     */
    int URL_TYPE_PRODUCT_ZELATU = 10014;


    /**
     * 首页新用户引导创建收货地址
     */
    int URL_TYPE_CREATE_ADDRESS = 10015;

    //////////////////////////  本地预占的跳转行为 end   ///////////////////////////


}
