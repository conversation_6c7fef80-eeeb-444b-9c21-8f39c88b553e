package com.xstore.sdk.floor.floorcore.bean;

import android.text.TextUtils;

/**
 * Created by xu.lu on 2018/11/20.
 */
public class ResponseData<T> {


    public static final String CODE_SUCC = "0";

    private String code;
    //private boolean success;//有些接口字此段在data中，如个人中心配置
    private String msg;

    private T data;//具体业务数据

    private int ret;


    public String getCode() {
        if(code == null) {
            return "";
        }
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean isSuccessForCode() {
        return !TextUtils.isEmpty(code) && CODE_SUCC.equals(code);
    }

    /*public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }*/

    public String getMsg() {
        if(msg == null){
            return "";
        }
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

}
