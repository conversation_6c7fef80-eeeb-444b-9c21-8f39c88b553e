package com.xstore.sdk.floor.floorcore.bean;

public class RestrictRuleBean {
    /**
     * 限购文案
     */
    private String buyLimitDesc;

    /**
     * 门店ID
     */
    private long storeId;
    /**
     * skuId
     */
    private long skuId;
    /**
     * 限购类型
     */
    private int ruleType;
    /**
     * 限购周期
     */
    private int restrictPeriod;
    /**
     * 最大购买量
     */
    private int purchaseMax;

    public long getStoreId() {
        return storeId;
    }

    public void setStoreId(long storeId) {
        this.storeId = storeId;
    }

    public long getSkuId() {
        return skuId;
    }

    public void setSkuId(long skuId) {
        this.skuId = skuId;
    }

    public int getRuleType() {
        return ruleType;
    }

    public void setRuleType(int ruleType) {
        this.ruleType = ruleType;
    }

    public int getRestrictPeriod() {
        return restrictPeriod;
    }

    public void setRestrictPeriod(int restrictPeriod) {
        this.restrictPeriod = restrictPeriod;
    }

    public int getPurchaseMax() {
        return purchaseMax;
    }

    public void setPurchaseMax(int purchaseMax) {
        this.purchaseMax = purchaseMax;
    }
    public String getBuyLimitDesc() {
        return buyLimitDesc;
    }

    public void setBuyLimitDesc(String buyLimitDesc) {
        this.buyLimitDesc = buyLimitDesc;
    }
}
