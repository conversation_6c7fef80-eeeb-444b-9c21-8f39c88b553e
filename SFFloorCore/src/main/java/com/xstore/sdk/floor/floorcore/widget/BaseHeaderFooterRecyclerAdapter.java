package com.xstore.sdk.floor.floorcore.widget;

import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

public abstract class BaseHeaderFooterRecyclerAdapter extends RecyclerView.Adapter<ViewHolder> {

    private View header;
    private View footer;

    private boolean isJump;

    public static final int TYPE_HEADER = 10001;
    public static final int TYPE_FOOTER = 10002;

    public BaseHeaderFooterRecyclerAdapter() {

    }

    public abstract ViewHolder onCreateItemViewHolder(ViewGroup var1, int var2);

    public abstract void onBindItemViewHolder(ViewHolder var1, int var2);

    public abstract int getRealItemCount();

    public abstract int getRealItemViewType(int position);

    public abstract void showFooter();

    public void setHeaderView(View headerView) {
        header = headerView;
    }

    public void setFooterView(View footerView) {
        footer = footerView;
    }

    public View getHeaderView() {
        return header;
    }

    public View getFooterView(){
        return footer;
    }

    public boolean isJump() {
        return isJump;
    }

    public void setJump(boolean jump) {
        isJump = jump;
    }

    public boolean isHeaderOrFooter(int position) {
        if (position == 0 && header != null) {
            return true;
        }
        return position == getItemCount() - 1 && footer != null;
    }

    @Override
    public final ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        ViewHolder holder = null;
        switch (viewType) {
            case TYPE_HEADER:
                holder = new SimpleViewHolder(header);
                break;
            case TYPE_FOOTER:
                holder = new SimpleViewHolder(footer);
                break;
            default:
                holder = onCreateItemViewHolder(parent, viewType);
                break;
        }

        return holder;
    }

    @Override
    public final void onBindViewHolder(ViewHolder holder, int position) {
        if (holder == null) {
            return;
        }
        switch (getItemViewType(position)) {
            case TYPE_HEADER:
                break;
            case TYPE_FOOTER:
                showFooter();
                break;
            default:
                onBindItemViewHolder(holder, position - (header == null ? 0 : 1));
                break;
        }
    }

    @Override
    public final int getItemCount() {
        return getRealItemCount() + (header == null ? 0 : 1) + (footer == null ? 0 : 1);
    }

    @Override
    public final int getItemViewType(int position) {
        if (position == 0 && header != null) {
            return TYPE_HEADER;
        }
        if (position == getItemCount() - 1 && footer != null) {
            return TYPE_FOOTER;
        }
        return getRealItemViewType(position - (header == null ? 0 : 1));
    }

    public static class SimpleViewHolder extends ViewHolder {

        public SimpleViewHolder(View itemView) {
            super(itemView);
        }
    }

}