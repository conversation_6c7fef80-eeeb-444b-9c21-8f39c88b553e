package com.xstore.sdk.floor.floorcore;


import com.xstore.sdk.floor.floorcore.bean.ImmersiveFloorBean;

import java.util.ArrayList;

//
public class HomeRefreshManager {

    /**
     * 不支持沉浸式
     */
    public static final int DISABLE = 0;

    /**
     * 默认的轮播沉浸式
     */
    public static final int DEFAULT = 1;

    /**
     * 楼层背景透明沉浸式
     */
    public static final int TRAN_BACKGROUND = 1;

    //
//    // 是否使用首页氛围下拉刷新效果
    private boolean useNewRefreshStyle = false;
    /**
     * 主题色
     */
    private String curMainColor;
    private String curBackImg;
    private int contentHeight;
    private int aggregationHeight;
    private int newUserGiftProgressHeight;
    /**
     * 搜索楼层高度
     */
    private int searchHeight;

    /**
     * 沉浸式样式，默认0 原来的banner沉浸式， 1 新增的导航栏沉浸式
     */
    private int style = DISABLE;

    /**
     * 导航栏沉浸式背景图
     */
    private String atmosphereBackgroundImage;
    /**
     * 导航栏沉浸式背景图宽度
     */
    private int atmosphereBackgroundImageWidth;
    /**
     * 导航栏沉浸式背景图高度
     */
    private int atmosphereBackgroundImageHeight;

    /**
     * 沉浸式背景色
     */
    private String atmosphereBackgroundColor;

    /**
     * 沉浸式楼层数组，这个数组维护了一组从0到n的连续楼层数据，以及楼层高度，用户容器支持沉浸式样式
     */
    private ArrayList<ImmersiveFloorBean> immersiveFloorBeans = new ArrayList<>();

    /**
     * 设置导航栏沉浸式
     *
     * @param img
     * @param width
     * @param height
     */
    public void setAtmosphere(String img, int width, int height, String color) {
        atmosphereBackgroundImage = img;
        atmosphereBackgroundImageWidth = width;
        atmosphereBackgroundImageHeight = height;
        atmosphereBackgroundColor = color;
    }


    //
    private static class Holder {
        private static final HomeRefreshManager INSTANCE = new HomeRefreshManager();
    }

    private HomeRefreshManager() {
    }

    public static HomeRefreshManager getInstance() {
        return Holder.INSTANCE;
    }

    private void checkNull(Object object, String objName) {
        if (object == null) {
            throw new RuntimeException(objName + "==null");
        }
    }

    public boolean useNewRefreshStyle() {
        return useNewRefreshStyle;
    }

    public void setUseNewRefreshStyle(boolean useNewRefreshStyle) {
        //先重置 后面再重新设置
        searchStylePos = -1;
        aggregationStylePos = -1;
        newUserGiftStylePos = -1;
        this.useNewRefreshStyle = useNewRefreshStyle;
        this.curBackImg = null;
        this.contentHeight = 0;
        this.aggregationHeight = 0;
        this.newUserGiftProgressHeight = 0;
        this.curMainColor = null;
        if (useNewRefreshStyle) {
            style = DEFAULT;
        } else {
            style = DISABLE;
        }
        immersiveFloorBeans.clear();
        this.atmosphereBackgroundImage = null;
        atmosphereBackgroundImageWidth = 0;
        atmosphereBackgroundImageHeight = 0;
        atmosphereBackgroundColor = null;
    }

    /**
     * @param immersiveFloorBeanArrayList 支持沉浸式的楼层 null 代表无
     */
    public void setUseNewRefreshStyle(ArrayList<ImmersiveFloorBean> immersiveFloorBeanArrayList) {
        //先重置老的沉浸式样式 后面再重新设置
        setUseNewRefreshStyle(false);
        if (immersiveFloorBeanArrayList == null || immersiveFloorBeanArrayList.isEmpty()) {
            return;
        }
        //
        immersiveFloorBeans.addAll(immersiveFloorBeanArrayList);
        style = TRAN_BACKGROUND;
    }

    public void removeItem(String templateCode) {
        try {
            for (int i = immersiveFloorBeans.size() - 1; i > 0; i--) {
                if (immersiveFloorBeans.get(i).floorDetailBean.getTemplateCode().equals(templateCode)) {
                    immersiveFloorBeans.remove(i);
                }
            }
        } catch (Exception e) {

        }

    }

    /**
     * 可以使用沉浸式效果的搜索楼层位置
     */
    private int searchStylePos = -1;
    /**
     * 可以使用沉浸式的功能聚合的楼层位置
     */
    private int aggregationStylePos = -1;

    /**
     * 可以使用沉浸式的新人三单礼进度的楼层位置
     */
    private int newUserGiftStylePos = -1;

    public int getSearchStylePos() {
        return searchStylePos;
    }

    public void setSearchStylePos(int searchStylePos) {
        this.searchStylePos = searchStylePos;
    }

    public int getAggregationStylePos() {
        return aggregationStylePos;
    }

    public void setAggregationStylePos(int aggregationStylePos) {
        this.aggregationStylePos = aggregationStylePos;
    }

    public int getNewUserGiftStylePos() {
        return newUserGiftStylePos;
    }

    public void setNewUserGiftStylePos(int newUserGiftStylePos) {
        this.newUserGiftStylePos = newUserGiftStylePos;
    }

    public int getContentHeight() {
        return contentHeight;
    }

    public void setContentHeight(int contentHeight) {
        this.contentHeight = contentHeight;
    }

    public String getCurMainColor() {
        return curMainColor;
    }

    public void setCurMainColor(String curMainColor) {
        this.curMainColor = curMainColor;
    }

    public String getCurBackImg() {
        return curBackImg;
    }

    public void setCurBackImg(String curBackImg) {
        this.curBackImg = curBackImg;
    }

    public void setAggregationHeight(int height) {
        aggregationHeight = height;
    }

    public int getAggregationHeight() {
        return aggregationHeight;
    }

    public int getNewUserGiftProgressHeight() {
        return newUserGiftProgressHeight;
    }

    public void setNewUserGiftProgressHeight(int newUserGiftProgressHeight) {
        this.newUserGiftProgressHeight = newUserGiftProgressHeight;
    }

    public int getSearchHeight() {
        return searchHeight;
    }

    public void setSearchHeight(int searchHeight) {
        this.searchHeight = searchHeight;
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public String getAtmosphereBackgroundImage() {
        return atmosphereBackgroundImage;
    }

    public int getAtmosphereBackgroundImageWidth() {
        return atmosphereBackgroundImageWidth;
    }

    public int getAtmosphereBackgroundImageHeight() {
        return atmosphereBackgroundImageHeight;
    }

    public ArrayList<ImmersiveFloorBean> getImmersiveFloorBeans() {
        return immersiveFloorBeans;
    }

    public String getAtmosphereBackgroundColor() {
        return atmosphereBackgroundColor;
    }
}
