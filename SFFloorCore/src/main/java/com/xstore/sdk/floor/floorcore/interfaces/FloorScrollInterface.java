package com.xstore.sdk.floor.floorcore.interfaces;


import androidx.recyclerview.widget.RecyclerView;

/**
 * 外层recycleView 滚动监听，用于子view 根据滚动距离调整自身透明度等动效
 */
public interface FloorScrollInterface {
    /**
     * 容器滚动 分发给楼层监听
     * @param dx
     * @param dy
     */
    void onScroll(int dx, int dy);

    /**
     * 滚动状态变化 分发给楼层
     * @param recyclerView
     * @param newState
     */
    void onScrollStateChanged(RecyclerView recyclerView, int newState);

    /**
     * 下拉刷新滚动
     *
     * @param isDragging
     * @param percent
     * @param offset
     * @param headerHeight
     * @param maxDragHeight
     */
    void onHeaderMoving(boolean isDragging, float percent, int offset, int headerHeight, int maxDragHeight);
}
