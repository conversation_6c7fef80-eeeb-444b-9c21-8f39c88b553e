apply plugin: 'com.android.library'
apply plugin: 'AuraAar'
apply plugin: 'AuraConfig'

android {
    compileSdk = rootProject.ext.android.compileSdkVersion
    //buildToolsVersion = rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    resourcePrefix  'sf_floor_core_'
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.5.0-alpha02'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    api DepSFCB.SFCBLbs
    api DepSFCB.SFCBBeanModule
    api DepSFCB.SFCBThemeResourceModule

    implementation DepSF.SFServiceStorageModule
    api DepSF.SFServiceNetWorkModule
    api DepSF.SFServiceImageModule
    api DepSF.SFServiceMTAManagerModule
    api DepJD.JDCrashReport
    api DepJD.android_sdk_jdjson
}