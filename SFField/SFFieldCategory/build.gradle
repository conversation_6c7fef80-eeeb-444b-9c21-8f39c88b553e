apply plugin: 'com.android.library'
apply plugin: 'AuraAar'
apply plugin: 'AuraConfig'

android {
    compileSdk = rootProject.ext.android.compileSdkVersion
    //buildToolsVersion = rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    resourcePrefix 'sf_field_category_'
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    //Android扩展库依赖
    implementation DepAndroid.appcompat
    implementation DepAndroid.material

    //功能组件层service依赖
    implementation DepSF.SFServiceImageModule
    implementation DepSF.SFServiceMTAManagerModule
    implementation DepSF.SFServiceNetWorkModule
    implementation DepSF.SFServiceSFLogCollectorModule
    implementation DepSF.SFServiceStorageModule
    implementation DepSF.SFServicePlaybase
    implementation DepSF.SFServiceUiKit


    implementation DepSFCB.SFCBLbs
    implementation DepSFCB.SFCBThemeResourceModule
    implementation DepSFCB.SFCBBeanModule
    implementation DepSFCB.SFCBCartService
    implementation DepSFCB.SFCBProductCardModule
    implementation DepSFCB.SFCBRustModule

    //中台依赖
    implementation DepJD.android_sdk_jdjson
    implementation DepJD.rollviewpager
    implementation DepJD.bdcodehelper
    implementation DepJD.JDCrashReport

    //外部三方依赖
    implementation DepEXT.smartRefreshLayout
    implementation DepEXT.secondFloorHeader
    implementation DepEXT.Gson
    implementation DepEXT.videoCache//视频缓存

    if (fieldSdkProjectDep_floorCore.toBoolean()) {
        //implementation project(path: ':HomeFloorSdk:SFFloors')
        implementation project(path: ':HomeFloorSdk:SFFloorCore')
    } else {
        //implementation DepSFFieldSDK.SFFloors//其实不需强依赖,加上是为了弱化业务层对楼层SDK内部感知
        implementation DepSFFieldSDK.SFFloorCore
    }

}