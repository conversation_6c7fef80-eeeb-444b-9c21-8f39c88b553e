package com.xstore.floorsdk.fieldcategory;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.CategoryRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.google.android.material.appbar.AppBarLayout;
import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.simple.SimpleMultiListener;
import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.floorsdk.fieldcategory.bean.BannerBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CategorySearchResult;
import com.xstore.floorsdk.fieldcategory.bean.CategoryWareInfoResult;
import com.xstore.floorsdk.fieldcategory.bean.DapeigouResult;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryFirstActionInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.OnProductRefreshOrLoadMoreListener;
import com.xstore.floorsdk.fieldcategory.interfaces.SFFieldCategoryConfig;
import com.xstore.floorsdk.fieldcategory.ma.CategoryProductExposureHelper;
import com.xstore.floorsdk.fieldcategory.ma.ThirdCategoryExposureHelper;
import com.xstore.floorsdk.fieldcategory.video.ShareAnimationPlayer;
import com.xstore.floorsdk.fieldcategory.widget.CategoryBannerPager;
import com.xstore.floorsdk.fieldcategory.widget.CategoryLinearLayoutManager;
import com.xstore.floorsdk.fieldcategory.widget.CategoryRefreshFooter;
import com.xstore.floorsdk.fieldcategory.widget.CategoryRefreshHeader;
import com.xstore.floorsdk.fieldcategory.widget.CategoryMoreView;
import com.xstore.floorsdk.fieldcategory.widget.CategorySortTypeFilter;
import com.xstore.floorsdk.fieldcategory.widget.ThirdCategoryFilter;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.interfaces.FloorLifecycle;
import com.xstore.sdk.floor.floorcore.utils.ObjectLocals;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuCartInfo;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;
import com.xstore.sevenfresh.productcard.holder.ProductListViewHolder;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.floorsdk.fieldcategory.video.HomeFloorPlayHelper;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 分类商品容器
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
public class CategoryProductContainer extends LinearLayout implements FloorLifecycle {
    /**
     * 每页商品数
     */
    public static final int PAGE_SIZE = 10;
    private View rootView;
    private Context mContext;
    private AppBarLayout appBarLayout;
    /**
     * 内容区域
     */
    private CoordinatorLayout contentLayout;
    /**
     * 广告轮播图View
     */
    private CategoryBannerPager bannerPager;
    /**
     * 三级分类筛选
     */
    private ThirdCategoryFilter thirdCategoryFilter;
    /**
     * 排序筛选
     */
    private CategorySortTypeFilter sortTypeFilter;
    /**
     * 刷新控件
     */
    private SmartRefreshLayout smartRefreshLayout;
    /**
     * 刷新控件-头部
     */
    private CategoryRefreshHeader categoryHeader;
    /**
     * 刷新控件-尾部
     */
    private CategoryRefreshFooter categoryFooter;
    /**
     * 商品列表
     */
    private CategoryRecyclerView rvProducts;
    /**
     * 空页面
     */
    private View noDataLayout;
    /**
     * 商品列表数据
     */
    private List<SkuInfoBean> productInfoList = new ArrayList<>();

    /**
     * 无货商品数据Map,K:分类Id, V:分类下无货商品数据对象
     */
    private HashMap<Long, CategoryWareInfoResult> sinkWareInfoMap = new HashMap<>();
    /**
     * 各分类的无货折叠是否已经展开, K:分类Id, V:是否已展开
     */
    private HashMap<Long, Boolean> sinkExpandedMap = new HashMap<>();
    /**
     * 列表已展示的三级分类Id
     */
    private List<Long> existThirdCateList = new ArrayList<>();
    /**
     * 当前二级分类下全部三级分类
     */
    private List<CategoryBean> thirdCategoryList;
    /**
     * 二级分类当前位置
     */
    private int currSecondCateIndex;
    /**
     * 下一个二级类目名称
     */
    private String nextSecondCateName;
    /**
     * 全部二级分类个数
     */
    private int totalSecondCateSize;
    /**
     * 第一个三级分类位置
     */
    private int firstThirdCateIndex;
    /**
     * 最后一个三级分类位置
     */
    private int lastThirdCateIndex;
    /**
     * 全部三级分类个数
     */
    private int totalThirdCateSize;
    /**
     * 最后一页无货商品个数
     */
    private int lastSinkPageCount;
    /**
     * 最后一个分类的当前页码
     */
    private int lastCurrPage;
    /**
     * 最后一个分类总页数
     */
    private int lastTotalPage;
    /**
     * 一级分类名
     */
    public String firstCateName;
    //    public String firstCateId;
    public String firstMid;
    /**
     * 二级分类名
     */
    public String secondCateName;
    //    public String secondCateId;
    public String secondMid;
    /**
     * 三级分类名
     */
    public String thirdCateName;
    //    public String thirdCateId;
    public String thirdMid;
    /**
     * 当前分类Id（二级或者三级）
     */
    private long currCId;
    /**
     * 中台类目id
     */
    private String currMid;
    /**
     * 当前排序方式
     */
    public String sortType;
    /**
     * 轮播图
     */
    private ArrayList<BannerBean> banners;
    private CategoryProductAdapter productAdapter;
    private CategoryLinearLayoutManager linearLayoutManager;
    /**
     * 商品刷新/加载更多监听回调
     */
    private OnProductRefreshOrLoadMoreListener productListener;
    /**
     * 分类视频播放帮助类
     */
    private HomeFloorPlayHelper recyclerviewPlayHelper;

    /**
     * 列表遮罩层
     */
    private View llProductMask;
    private CategoryContainerInterface categoryContainerInterface;
    private CategoryReporterInterface categoryFieldReporter;
    private CategoryProductExposureHelper categoryProductExposureHelper;

    private CategoryFirstActionInterface categoryFirstActionInterface;
    /**
     * clickThird 用户手动点击了三级分类
     */
    private boolean clickThird;
    /**
     * 接口返回结果
     */
    public CategoryWareInfoResult mCategoryWareInfo;

    /**
     * 自定义筛选条件
     */
    private ArrayList<FilterCriteriaVo> firstFilterCriteria = new ArrayList<>();
    /**
     * 选中的筛选条件
     */
    public FilterCriteriaVo mSelectedFilterCriteria;

    private ThirdCategoryExposureHelper thirdCategoryExposureHelper;

    public void setThirdCategoryExposureHelper(ThirdCategoryExposureHelper thirdCategoryExposureHelper) {
        this.thirdCategoryExposureHelper = thirdCategoryExposureHelper;
    }

    public void setCategoryFirstActionInterface(CategoryFirstActionInterface categoryFirstActionInterface) {
        this.categoryFirstActionInterface = categoryFirstActionInterface;
    }

    /**
     * handler
     */
    private Handler handler = new Handler();

    public CategoryProductContainer(Context context) {
        super(context);
        initView(context);
    }

    public CategoryProductContainer(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public CategoryProductContainer(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化参数数据
     */
    private void initBasicData(Bundle bundle) {
        if (bundle != null) {
            currSecondCateIndex = bundle.getInt("currSecondCateIndex", 0);
            nextSecondCateName = bundle.getString("nextSecondCateName", "");
            totalSecondCateSize = bundle.getInt("totalSecondCateSize", 0);
            sortType = bundle.getString("sortType");
//            firstCateId = bundle.getString("firstCateId", "");
            firstMid = bundle.getString("firstMid", "");
            firstCateName = bundle.getString("firstCateName", "");
//            secondCateId = bundle.getString("secondCateId", "");
            secondMid = bundle.getString("secondMid", "");
            secondCateName = bundle.getString("secondCateName", "");
//            thirdCateId = bundle.getString("thirdCateId", "");
            thirdMid = bundle.getString("thirdMid", "");
            thirdCateName = bundle.getString("thirdCategoryName", "");
            banners = (ArrayList<BannerBean>) bundle.getSerializable("banners");
            firstFilterCriteria = (ArrayList<FilterCriteriaVo>) bundle.getSerializable("firstFilterCriteria");
            mSelectedFilterCriteria = (FilterCriteriaVo) bundle.getSerializable("selectTimeFiler");
        }
    }

    public void setActionListener(OnProductRefreshOrLoadMoreListener productListener, CategoryContainerInterface categoryContainerInterface, CategoryReporterInterface categoryFieldReporter) {
        this.productListener = productListener;
        this.categoryContainerInterface = categoryContainerInterface;
        this.categoryFieldReporter = categoryFieldReporter;
        if (productAdapter != null) {
            productAdapter.setCategoryFieldReporter(categoryFieldReporter);
            productAdapter.setCategoryContainerInterface(categoryContainerInterface);
        }
        thirdCategoryFilter.setThirdCategoryExposureHelper(thirdCategoryExposureHelper);
        categoryProductExposureHelper = new CategoryProductExposureHelper(categoryFieldReporter);
    }

    /**
     * 每次切换二级分类后初次刷新商品数据
     *
     * @param cateId
     * @param thirdCategoryList
     * @param categoryWareInfo
     * @param isCanExposureByHand 是否可以曝光，如果当前的Viewpager 选中的index 是当前的则可以 否则不可以曝光，切换后需要手动重新曝光，曝光埋点中内部去重，
     * @param isChacheData        缓存数据不曝光
     */
    public void showProductList(Bundle bundle, int thirdIndex, long cateId, List<CategoryBean> thirdCategoryList, CategoryWareInfoResult categoryWareInfo, boolean isCanExposureByHand, boolean isChacheData) {
        initBasicData(bundle);
        this.lastSinkPageCount = categoryWareInfo != null ? categoryWareInfo.getSinkPageCount() : 0;
        this.currCId = cateId;
        this.currMid = categoryWareInfo != null ? categoryWareInfo.getMid() : "";
        this.mCategoryWareInfo = categoryWareInfo;
        initThirdCate(thirdCategoryList, cateId);
        bindWareInfoWithCateId(cateId, currMid, categoryWareInfo);
        //刷新数据，清空已存在三级分类Id列表
        existThirdCateList.clear();
        sinkWareInfoMap.clear();
        sinkExpandedMap.clear();
        productInfoList.clear();
        productInfoList.addAll(checkAndAddThirdTitle(categoryWareInfo == null ? null : categoryWareInfo.getProductCardVoList(), cateId, currMid, true));
        if (categoryWareInfo != null) {
            this.lastCurrPage = categoryWareInfo.getPage();
            this.lastTotalPage = categoryWareInfo.getTotalPage();
            productInfoList.addAll(filterStockOutWare(cateId, currMid, categoryWareInfo));
        } else {
            this.lastCurrPage = 0;
            this.lastTotalPage = 0;
        }

        bannerPager.setBannerData(banners, categoryFieldReporter, isCanExposureByHand);
        sortTypeFilter.setSortTypeData(sortType, firstFilterCriteria, mSelectedFilterCriteria);
        sortTypeFilter.setVisibility(VISIBLE);
        thirdCategoryFilter.setThirdCateData(thirdCategoryList, secondCateName, categoryFieldReporter, isCanExposureByHand);
        thirdCategoryFilter.setThirdCatePos(thirdIndex);

        setRefreshAndLoadMore();
        showProductData(0, productInfoList.size());
        postDelayAutoPlay();
        scrollToPosition(0, thirdIndex);

        //数据请求回来，列表未滚动时，需要手动执行一次曝光
        handler.post(new Runnable() {
            @Override
            public void run() {
                if (isChacheData || !isCanExposureByHand) {
                    return;
                }
                if (categoryProductExposureHelper != null) {
                    categoryProductExposureHelper.exposureByHand(rvProducts, RecyclerView.SCROLL_STATE_IDLE);
                }
            }
        });
    }

    public void reportExposePoint() {
        // 延迟一点上报 防止数据还未加载
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (categoryProductExposureHelper == null) {
                    categoryProductExposureHelper = new CategoryProductExposureHelper(categoryFieldReporter);

                }
                if (rvProducts == null) {
                    return;
                }
                categoryProductExposureHelper.exposureByHand(rvProducts, RecyclerView.SCROLL_STATE_IDLE);
                if (thirdCategoryFilter != null) {
                    thirdCategoryFilter.exposeReport();
                }
                if (bannerPager != null) {
                    bannerPager.setCanExpose(true);
                }
            }
        }, 100);
    }

    public void setFilterCriteriaVo(FilterCriteriaVo selectedFilter) {
        this.mSelectedFilterCriteria = selectedFilter;
        sortTypeFilter.setFilterCriteriaVo(mSelectedFilterCriteria);
    }


    /**
     * 刷新商品列表，使用场景如下：
     * 1、切换一级分类
     * 2、切换二级分类
     * 3、手动切换列表中没有的三级分类
     *
     * @param cateId
     * @param categoryWareInfo
     * @param isFromCache          是否是缓存数据
     * @param isSelectCurrentPager 是否是当前的viewPager页面
     */
    public void refreshProductList(Bundle bundle, int thirdIndex, long cateId, List<CategoryBean> thirdCategorys, CategoryWareInfoResult categoryWareInfo, boolean isFromCache, boolean isSelectCurrentPager, boolean isCickCate) {
        initBasicData(bundle);
        // 为啥需要handler post 没懂
        handler.post(new Runnable() {
            @Override
            public void run() {
                lastSinkPageCount = categoryWareInfo != null ? categoryWareInfo.getSinkPageCount() : 0;
                currCId = cateId;
                currMid = categoryWareInfo != null ? categoryWareInfo.getMid() : "";
                mCategoryWareInfo = categoryWareInfo;
                // 重置三级分类 数量以及列表
                initThirdCate(thirdCategorys, cateId);
                // 为商品信息设置分类内容归属
                bindWareInfoWithCateId(cateId, currMid, categoryWareInfo);

                if (bannerPager != null && !ObjectLocals.equals(banners, bannerPager.getBanners())) {
                    bannerPager.setBannerData(banners, categoryFieldReporter, !isFromCache && isSelectCurrentPager);
                }
                if (sortTypeFilter != null) {
                    sortTypeFilter.setSortTypeData(sortType, firstFilterCriteria, mSelectedFilterCriteria);
                    sortTypeFilter.setVisibility(VISIBLE);
                }
                // 曝光埋点
                if (thirdCategoryFilter != null) {
                    if (!thirdCategoryFilter.sameThird(thirdCategorys, thirdCategoryFilter.getThirdCateList())) {
                        // 如果是缓存数据过来的 则不曝光，否则曝光
                        thirdCategoryFilter.setThirdCateData(thirdCategorys, secondCateName, categoryFieldReporter, !isFromCache && isSelectCurrentPager);
                        thirdCategoryFilter.setThirdCatePos(thirdIndex);
                    }
                    // 有可能缓存数据已经设置过一次了 此时再补报一次，曝光方法内部去重所以可以补报
                    if (!isFromCache && isSelectCurrentPager) {
                        thirdCategoryFilter.exposeReport();
                    }
                }

                //刷新数据，清空已存在三级分类Id列表
                existThirdCateList.clear();
                sinkWareInfoMap.clear();
                sinkExpandedMap.clear();
                if (productInfoList == null) {
                    productInfoList = new ArrayList<>();
                }
                productInfoList.clear();
                productInfoList.addAll(checkAndAddThirdTitle(categoryWareInfo == null ? null : categoryWareInfo.getProductCardVoList(), cateId, currMid, true));
                if (categoryWareInfo != null) {
                    lastCurrPage = categoryWareInfo.getPage();
                    lastTotalPage = categoryWareInfo.getTotalPage();
                    productInfoList.addAll(filterStockOutWare(cateId, currMid, categoryWareInfo));
                } else {
                    lastCurrPage = 0;
                    lastTotalPage = 0;
                }
                setRefreshAndLoadMore();
                showProductData(0, productInfoList.size());
                postDelayAutoPlay();
                if (thirdCategorys != null && thirdCategorys.size() > 0 && thirdCategorys.get(0) != null
                        && currCId == thirdCategorys.get(0).getId()) {
                    scrollToPosition(0, thirdIndex);
                } else {
                    scrollToPosition(1, thirdIndex);
                }


            }
        });
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (categoryProductExposureHelper != null) {
                    categoryProductExposureHelper.updateProductList();
                    //数据请求回来，列表未滚动时，需要手动执行一次曝光
                    if (isSelectCurrentPager) {
                        categoryProductExposureHelper.exposureByHand(rvProducts, RecyclerView.SCROLL_STATE_IDLE);
                    }
                }
            }
        }, 50);
    }

    /**
     * 加载更多商品，使用场景如下：
     * 1、如果有三级分类，加载当前三级分类的下一页商品
     * 2、如果没有三级分类，加载当前二级分类的下一页商品
     * 3、加载当前二级分类下一个三级分类的第一页商品
     *
     * @param cateId
     * @param categoryWareInfo
     */
    public void loadMoreProduct(long cateId, CategoryWareInfoResult categoryWareInfo) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                mCategoryWareInfo = categoryWareInfo;
                lastSinkPageCount = categoryWareInfo != null ? categoryWareInfo.getSinkPageCount() : 0;
                if (thirdCategoryList != null && thirdCategoryList.size() > 0) {
                    for (CategoryBean category : thirdCategoryList) {
                        if (category != null && category.getId() != null && category.getId() == cateId) {
                            lastThirdCateIndex = thirdCategoryList.indexOf(category);
                            break;
                        }
                    }
                }
                if (categoryWareInfo != null && !StringUtil.isNullByString(categoryWareInfo.getMid())) {
                    currMid = categoryWareInfo.getMid();
                }
                bindWareInfoWithCateId(cateId, currMid, categoryWareInfo);
                if (productInfoList == null) {
                    productInfoList = new ArrayList<>();
                }
                int insertPosition = productInfoList.size();
                productInfoList.addAll(checkAndAddThirdTitle(categoryWareInfo == null ? null : categoryWareInfo.getProductCardVoList(), cateId, currMid, false));
                if (categoryWareInfo != null && categoryWareInfo.getProductCardVoList() != null) {
                    lastCurrPage = categoryWareInfo.getPage();
                    lastTotalPage = categoryWareInfo.getTotalPage();
                    productInfoList.addAll(filterStockOutWare(cateId, currMid, categoryWareInfo));
                }
                isPreLoading = false;
                setRefreshAndLoadMore();
                showProductData(insertPosition, productInfoList.size() - insertPosition);
            }
        });
    }

    /**
     * 插入数据，使用场景如下：
     * 1、下拉加载上一个三级分类，此时不分页，请求此分类下全部商品插入到当前商品列表头部
     * 2、点击某一个分类(二级或三级)的展开无货商品，此时不分页，请求剩余无货商品插入到列表中该分类尾部
     *
     * @param cateId
     * @param categoryWareInfo
     */
    public void insertProduct(long cateId, CategoryWareInfoResult categoryWareInfo) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                currCId = cateId;
                currMid = categoryWareInfo != null ? categoryWareInfo.getMid() : "";
                mCategoryWareInfo = categoryWareInfo;
                bindWareInfoWithCateId(cateId, currMid, categoryWareInfo);
                if (productInfoList == null) {
                    productInfoList = new ArrayList<>();
                }
                int needScrollToPosition = -1;
                if (totalThirdCateSize <= 0 || existThirdCateList.contains(cateId)) {
                    //如果没有三级分类或者已存在此三级分类Id，则是第二种情况，展开插入无货商品
                    insertExpandedSinkWare(categoryWareInfo == null ? null : categoryWareInfo.getProductCardVoList(), cateId);
                } else {
                    //第一种情况，上一个三级分类数据插入列表头部
                    if (thirdCategoryList != null && thirdCategoryList.size() > 0) {
                        for (CategoryBean category : thirdCategoryList) {
                            if (category != null && category.getId() != null && category.getId() == cateId) {
                                firstThirdCateIndex = thirdCategoryList.indexOf(category);
                                break;
                            }
                        }
                    }
                    List<SkuInfoBean> topWareInfos = new ArrayList<>();
                    topWareInfos.addAll(checkAndAddThirdTitle(categoryWareInfo == null ? null : categoryWareInfo.getProductCardVoList(), cateId, currMid, false));
                    if (categoryWareInfo != null) {
                        topWareInfos.addAll(filterStockOutWare(cateId, currMid, categoryWareInfo));
                    }
                    productInfoList.addAll(0, topWareInfos);
                    needScrollToPosition = topWareInfos.size() - 1;
                    showProductData(0, productInfoList.size());
                    postDelayAutoPlay();
                }
                setRefreshAndLoadMore();
                if (needScrollToPosition != -1) {
                    scrollToPosition(needScrollToPosition, thirdCategoryFilter.getThirdCateIndex());
                }
            }
        });
    }

    /**
     * 插入展开的无货数据
     *
     * @param wareInfos
     * @param cateId
     */
    private void insertExpandedSinkWare(List<SkuInfoBean> wareInfos, long cateId) {
        if (wareInfos == null) {
            wareInfos = new ArrayList<>();
        }
        if (sinkExpandedMap.containsKey(cateId) && sinkExpandedMap.get(cateId)) {//已展开
            if (productInfoList == null || productInfoList.size() <= 0) {
                return;
            }
            int expandTipIndex = getExpandTipIndex(cateId);
            if (expandTipIndex != -1) {
                List<SkuInfoBean> tempWareInfos = new ArrayList<>();
                if (sinkWareInfoMap.containsKey(cateId)) {
                    //加上之前保存的无货数据
                    tempWareInfos.addAll(sinkWareInfoMap.get(cateId).getProductCardVoList());
                }
                //加上这次请求回来的数据
                tempWareInfos.addAll(wareInfos);
                //点击展开的时候，说明商品数据里已经添加了展开提示条目，需要先去掉
                productInfoList.remove(expandTipIndex);
                productInfoList.addAll(expandTipIndex, tempWareInfos);
                showProductData(expandTipIndex, tempWareInfos.size());
            }
        }
    }

    /**
     * 滑动到指定位置
     *
     * @param position
     */
    private void scrollToPosition(int position, int thirdCateIndex) {
        if (linearLayoutManager != null) {
//            int firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
//            int top = 0;
//            if (rvProducts != null && rvProducts.getChildAt(firstVisibleItemPosition) != null) {
//                top = rvProducts.getChildAt(firstVisibleItemPosition).getTop();
//            }
//            if (position == 1) {
//                linearLayoutManager.scrollToPositionWithOffset(position, 0);
//            } else {
//                linearLayoutManager.scrollToPositionWithOffset(position, top);
//            }
            linearLayoutManager.scrollToPositionWithOffset(position, 0);
        }

        //更新一下banner，当前选中的是后面的三级分类，没有banner
        if (thirdCateIndex > 0) {
            bannerPager.setBannerVisible(false);
        } else {
            bannerPager.setBannerVisible(true);
        }
        if (position == 0) {
            ViewGroup.LayoutParams layoutParams = appBarLayout.getLayoutParams();
            CoordinatorLayout.Behavior behavior = ((CoordinatorLayout.LayoutParams) layoutParams).getBehavior();
            if (behavior instanceof AppBarLayout.Behavior) {
                AppBarLayout.Behavior appBarLayoutBehavior = (AppBarLayout.Behavior) behavior;
                int topAndBottomOffset = appBarLayoutBehavior.getTopAndBottomOffset();
                if (topAndBottomOffset != 0) {
                    appBarLayoutBehavior.setTopAndBottomOffset(0);
                    appBarLayout.setExpanded(true, true);
                }
            }

            //针对下滑到下个分类偶尔不置顶的情况
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (linearLayoutManager != null) {
                        linearLayoutManager.scrollToPositionWithOffset(0, 0);
                    }
                }
            }, 100);
        }
    }

    /**
     * 获取某分类的展开提示条在列表中的位置
     *
     * @param cateId
     * @return
     */
    private int getExpandTipIndex(long cateId) {
        int expandTipIndex = -1;
        for (SkuInfoBean wareInfo : productInfoList) {
            if (wareInfo != null && wareInfo.getViewType() == CategoryProductAdapter.VIEW_TYPE_EXPAND_TIP
                    && wareInfo.getCateId() == cateId) {
                expandTipIndex = productInfoList.indexOf(wareInfo);
                break;
            }
        }
        return expandTipIndex;
    }

    /**
     * 更新选中的三级分类位置
     *
     * @param thirdIndex
     */
    public void updateThirdCatePos(int thirdIndex) {
        if (thirdCategoryFilter != null) {
            thirdCategoryFilter.setThirdCatePos(thirdIndex);
        }
    }

    public int getCurrentThirdCate() {
        return thirdCategoryFilter == null ? 0 : thirdCategoryFilter.getThirdCateIndex();
    }

    /**
     * 三级分类在列表中是否已存在
     *
     * @param thirdCateId
     * @return
     */
    public boolean isThirdCateExist(long thirdCateId) {
        return existThirdCateList.contains(thirdCateId);
    }

    /**
     * 滚动到某个已存在的三级分类
     *
     * @param thirdCateId
     */
    public void scrollToExistThirdCate(long thirdCateId, String thirdMid) {
        if (!existThirdCateList.contains(thirdCateId)) {
            return;
        }
        if (productInfoList == null || productInfoList.size() <= 0) {
            return;
        }

        currCId = thirdCateId;
        currMid = thirdMid;
        boolean isScroll = false;
        for (SkuInfoBean wareInfo : productInfoList) {
            if (wareInfo != null && wareInfo.getViewType() == CategoryProductAdapter.VIEW_TYPE_TITLE
                    && wareInfo.getCateId() == thirdCateId) {
//                //todo 当前选中的是0 要切到1 并且数据都已经有了！！
//                //从第一个三级分类切换到了其他 那么需要插入的效果，banner需要吸顶  banners != null && banners.size() > 0 &&
//                boolean isInsertEffect = thridIndex != thirdCategoryFilter.getThirdCateIndex() && thirdCategoryFilter.getThirdCateIndex() == 0;

                int positionIndex = productInfoList.indexOf(wareInfo) + 1;
                scrollToPosition(positionIndex, thirdCategoryFilter.getThirdCateIndex());
                isScroll = true;
                break;
            }
        }
        if (!isScroll) {
            // 三级分类首个没有找到 则滑动到顶部
            scrollToPosition(0, 0);
        }
        postDelayAutoPlay();
    }

    /**
     * 页面数据展示
     *
     * @param insertStartIndex 插入列表的开始位置
     * @param insertCount      插入列表的开始位置
     */
    private void showProductData(int insertStartIndex, int insertCount) {
        if (productInfoList == null) {
            productInfoList = new ArrayList<>();
        }
        if (productInfoList.isEmpty()) {
//            noDataLayout.setVisibility(VISIBLE);
//            smartRefreshLayout.setVisibility(GONE);
            thirdCategoryExposureHelper.thirdCategoryEmptyPageExpose(firstMid, firstCateName, secondMid, secondCateName, null, null);
            SkuInfoBean noDataWareInfo = new SkuInfoBean();
            productAdapter.setNoDataItemHeight(rvProducts.getHeight() - ScreenUtils.dip2px(mContext, 55));
            noDataWareInfo.setViewType(CategoryProductAdapter.VIEW_TYPE_NO_SECOND_DATA_TYPE);
            productInfoList.add(noDataWareInfo);
        }

        smartRefreshLayout.setVisibility(VISIBLE);
        noDataLayout.setVisibility(GONE);
        if (thirdCategoryFilter.getThirdCateIndex() > 0) {
            //当前选中的是后面的三级分类，没有banner
            bannerPager.setBannerVisible(false);
        } else {
            bannerPager.setBannerVisible(true);
        }

        if (!hasNextPageInCurrCate()) {
            if (lastThirdCateIndex >= totalThirdCateSize - 1) {
                if(productInfoList.get(productInfoList.size() -1).getViewType() != CategoryProductAdapter.VIEW_TYPE_FOOTER) {
//                    CategoryMoreView nextSecondCateView = new CategoryMoreView(getContext());
//                    productAdapter.setFooterView(nextSecondCateView);
                    SkuInfoBean footerWareInfo = new SkuInfoBean();
                    footerWareInfo.setViewType(CategoryProductAdapter.VIEW_TYPE_FOOTER);
                    productInfoList.add(footerWareInfo);
                    insertCount += 1;
                }
                String footerTxt= "";
                //最后一个三级分类
                if (currSecondCateIndex >= totalSecondCateSize - 1) {
                    //最后一个二级分类
                    // 判断 当前是否为最后一个一级类目
                    String nextFirstCateName = "";
                    if (categoryFirstActionInterface != null) {
                        nextFirstCateName = categoryFirstActionInterface.getNextCategoryName();
                    }
                    if (TextUtils.isEmpty(nextFirstCateName)) {
//                        nextSecondCateView.setData("");
                        footerTxt = "";
                    } else {
                        footerTxt = nextFirstCateName;
//                        nextSecondCateView.setData(nextFirstCateName);
                    }

                } else {
                    //加载下一个二级分类
                    footerTxt = nextSecondCateName;
//                    nextSecondCateView.setData(nextSecondCateName);
                }
//                if (nextSecondCateView.getParent() != null
//                        && nextSecondCateView.getParent() instanceof ViewGroup) {
//                    ((ViewGroup) nextSecondCateView.getParent()).removeView(nextSecondCateView);
//                }
                productAdapter.setFooterTxt(footerTxt);
            } else {
//                productAdapter.setFooterView(null);
            }
        } else {
//            productAdapter.setFooterView(null);
        }
        //更新数据列表
        if (itemAnimator != null && itemAnimator.isRunning()) {
            // 如果动画过程中则延迟400ms更新数据 防止动画过程中数据更新导致动画错乱
            int finalInsertCount = insertCount;
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    productAdapter.setListData(productInfoList, insertStartIndex, finalInsertCount);
                }
            }, 420);
        } else {
            productAdapter.setListData(productInfoList, insertStartIndex, insertCount);
        }
    }

    /**
     * 设置刷新加载
     */
    private void setRefreshAndLoadMore() {
        if (smartRefreshLayout != null) {
            smartRefreshLayout.finishRefresh();
            smartRefreshLayout.finishLoadMore();
//            if (firstThirdCateIndex <= 0 && currSecondCateIndex <= 0) {
//                smartRefreshLayout.setEnableRefresh(false);
//            } else {
            smartRefreshLayout.setEnableRefresh(true);
//            }
            if (!hasNextPageInCurrCate()
                    && lastThirdCateIndex >= totalThirdCateSize - 1
                    && currSecondCateIndex >= totalSecondCateSize - 1) {
                String nextFirstCateName = "";
                if (categoryFirstActionInterface != null) {
                    nextFirstCateName = categoryFirstActionInterface.getNextCategoryName();
                }
                if (TextUtils.isEmpty(nextFirstCateName)) {
                    smartRefreshLayout.setEnableLoadMore(false);
                } else {
                    smartRefreshLayout.setEnableLoadMore(true).setFooterHeight(55);
                }
            } else {
                smartRefreshLayout.setEnableLoadMore(true).setFooterHeight(55);
            }
            if (categoryHeader != null) {
                if (firstThirdCateIndex <= 0) {
                    if (currSecondCateIndex <= 0) {
                        categoryHeader.setScrollFirstCate(true);
                        categoryHeader.setPullLastCate(false);
                        smartRefreshLayout.setHeaderTriggerRate(0.5f);
                    } else {
                        categoryHeader.setScrollFirstCate(false);
                        categoryHeader.setPullLastCate(true);
                        smartRefreshLayout.setHeaderTriggerRate(1.0f);
                    }
                } else {
                    categoryHeader.setPullLastCate(false);
                    categoryHeader.setScrollFirstCate(false);
                    smartRefreshLayout.setHeaderTriggerRate(0.1f);
                }
            }
            if (categoryFooter != null) {
                if (!hasNextPageInCurrCate() && lastThirdCateIndex >= totalThirdCateSize - 1) {
                    categoryFooter.setLoadNextCate(true);
                    smartRefreshLayout.setEnableAutoLoadMore(false);
                } else {
                    categoryFooter.setLoadNextCate(false);
                    smartRefreshLayout.setEnableAutoLoadMore(true);
                }
                smartRefreshLayout.setFooterTriggerRate(0.1f);
            }
        }
    }

    /**
     * 初始化三级分类
     *
     * @param thirdCategorys
     * @param cateId
     */
    private void initThirdCate(List<CategoryBean> thirdCategorys, long cateId) {
        totalThirdCateSize = 0;
        firstThirdCateIndex = 0;
        lastThirdCateIndex = 0;
        this.thirdCategoryList = thirdCategorys;
        if (thirdCategorys != null && thirdCategorys.size() > 0) {
            totalThirdCateSize = thirdCategorys.size();
            // 遍历三级分类 内容
            for (CategoryBean category : thirdCategorys) {
                if (category != null && category.getId() != null && category.getId() == cateId) {
                    firstThirdCateIndex = thirdCategorys.indexOf(category);
                    lastThirdCateIndex = firstThirdCateIndex;
                    thirdCateName = category.getName();
                    break;
                }
            }
        }
    }

    /**
     * 商品模型绑定分类Id
     *
     * @param cateId
     * @param categoryWareInfo
     */
    private void bindWareInfoWithCateId(long cateId, String mid, CategoryWareInfoResult categoryWareInfo) {
        if (categoryWareInfo != null && categoryWareInfo.getProductCardVoList() != null && categoryWareInfo.getProductCardVoList().size() > 0) {
            for (SkuInfoBean wareInfo : categoryWareInfo.getProductCardVoList()) {
                if (wareInfo != null) {
                    wareInfo.setCateId(cateId);
                    wareInfo.setMid(mid);
                }
            }
        }
    }

    /**
     * 检查是否需要添加三级分类Title及空数据条目，如果需要则添加
     *
     * @param wareInfos
     * @param cateId
     * @param isClickCate 是否是点击二级分类，如果是则不插入首个三级分类名称，否则插入
     * @return
     */
    private List<SkuInfoBean> checkAndAddThirdTitle(List<SkuInfoBean> wareInfos, long cateId, String mid, boolean isClickCate) {
        List<SkuInfoBean> temWareInfos = new ArrayList<>();
        boolean needAdd = true;
        if (isClickCate) {
            if (thirdCategoryList != null && thirdCategoryList.size() > 0 && thirdCategoryList.get(0) != null && cateId == thirdCategoryList.get(0).getId()) {
                needAdd = false;
            }
        }
        //有三级分类
        if (totalThirdCateSize > 0 && cateId != 0) {
            if (!existThirdCateList.contains(cateId)) {
                //列表里没有此三级分类Title,添加,已添加三级分类Title，无需再次添加
                SkuInfoBean thirdCateTitleWare = new SkuInfoBean();
                thirdCateTitleWare.setViewType(CategoryProductAdapter.VIEW_TYPE_TITLE);
                thirdCateTitleWare.setCateId(cateId);
                thirdCateTitleWare.setMid(mid);
                thirdCateTitleWare.setCateName(getThirdCateName(cateId));
                if (needAdd) {
                    temWareInfos.add(thirdCateTitleWare);
                } else {
                }
                existThirdCateList.add(cateId);
                if (wareInfos == null || wareInfos.size() <= 0) {
                    //如果请求的商品数据是空的，添加空Item
                    SkuInfoBean noDataWare = new SkuInfoBean();
                    noDataWare.setViewType(CategoryProductAdapter.VIEW_TYPE_NO_THIRD_DATA);
                    noDataWare.setCateId(cateId);
                    noDataWare.setMid(mid);
                    temWareInfos.add(noDataWare);
                    thirdCategoryExposureHelper.thirdCategoryEmptyPageExpose(firstMid, firstCateName, secondMid, secondCateName, cateId + "", getThirdCateName(cateId));
                }
            }
        }
        return temWareInfos;
    }

    /**
     * 根据分类Id获取三级分类的位置
     *
     * @param cateId
     * @return
     */
    private int getThirdCateIndex(long cateId) {
        int index = -1;
        if (thirdCategoryList != null && thirdCategoryList.size() > 0) {
            for (CategoryBean category : thirdCategoryList) {
                if (category != null && category.getId() != null && category.getId() == cateId) {
                    index = thirdCategoryList.indexOf(category);
                    break;
                }
            }
        }
        return index;
    }

    /**
     * 根据分类Id获取三级分类名
     *
     * @param cateId
     * @return
     */
    private String getThirdCateName(long cateId) {
        String cateName = "";
        if (thirdCategoryList != null && thirdCategoryList.size() > 0) {
            for (CategoryBean category : thirdCategoryList) {
                if (category != null && category.getId() != null && category.getId() == cateId) {
                    cateName = category.getName();
                    break;
                }
            }
        }
        return cateName;
    }

    /**
     * 过滤无货商品，添加折叠条目
     *
     * @param cateId
     * @param categoryWareInfo
     * @return
     */
    private List<SkuInfoBean> filterStockOutWare(long cateId, String mid, CategoryWareInfoResult categoryWareInfo) {
        if (sinkWareInfoMap.containsKey(cateId)) {
            sinkWareInfoMap.remove(cateId);
        }
        if (categoryWareInfo == null || categoryWareInfo.getProductCardVoList() == null || categoryWareInfo.getProductCardVoList().size() <= 0) {
            return new ArrayList<>();
        }
        int sinkCount = categoryWareInfo.getSinkCount();
        int sinkPageCount = categoryWareInfo.getSinkPageCount();
        List<SkuInfoBean> wareInfos = categoryWareInfo.getProductCardVoList();
        // V4.8.4 上线临时增加无货未沉底判断，辅助服务端异常上报
        try {
            for (int n = (wareInfos.size() - sinkPageCount - 1); 0 <= n; n--) {
                SkuInfoBean sku = wareInfos.get(n);
                boolean isPreSale = false;// 预售品不上报
                SkuCartInfo skuCartInfo = sku.getCartInfo();
                if (skuCartInfo != null && skuCartInfo.getType() == SkuEnumInterface.CartBtnType.PRE_SALE) {
                    isPreSale = true;
                }
                if (!isPreSale && sku.getStockStatus() == 1) {//无货，拼接：分类_商品失效_${大族广场店}_${131229}_${新鲜水果}_${热销推荐}_${【产地直采】巨无霸冬枣约300g(单果25g+)}_${480481}
                    String log = "分类_商品失效_" + TenantIdUtils.getStoreId() + "_" + sku.firstCateName + "_" + sku.secondCateName + "_" + sku.getSkuName() + "_" + sku.getSkuId();
                    SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                    errorLog.type = 9528;
                    errorLog.errorCode = log;
                    errorLog.ext1 = JDJSON.toJSONString(sku);
                    errorLog.location = "分类页";
                    SFLogCollector.reportBusinessErrorLog(errorLog);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sinkPageCount > 1 || (sinkPageCount > 0 && sinkCount > sinkPageCount)) {
            List<SkuInfoBean> temWareInfos = new ArrayList<>();
            List<SkuInfoBean> currCateSinkWare = new ArrayList<>();
            for (int i = 0; i < wareInfos.size(); i++) {
                if (i <= wareInfos.size() - sinkPageCount) {
                    temWareInfos.add(wareInfos.get(i));
                } else {
                    currCateSinkWare.add(wareInfos.get(i));
                }
            }
            CategoryWareInfoResult categorySinkWareInfo = new CategoryWareInfoResult();
            categorySinkWareInfo.setCid(cateId);
            categorySinkWareInfo.setPage(categoryWareInfo.getPage());
            categorySinkWareInfo.setSinkCount(sinkCount);
            categorySinkWareInfo.setSinkPageCount(sinkPageCount);
            categorySinkWareInfo.setProductCardVoList(currCateSinkWare);
            sinkWareInfoMap.put(cateId, categorySinkWareInfo);
            SkuInfoBean wareInfo = new SkuInfoBean();
            wareInfo.setViewType(CategoryProductAdapter.VIEW_TYPE_EXPAND_TIP);
            wareInfo.setCateId(cateId);
            wareInfo.setMid(mid);
            wareInfo.setSinkCount(sinkCount);
            temWareInfos.add(wareInfo);
            sinkExpandedMap.put(cateId, false);
            return temWareInfos;
        } else {
            return wareInfos;
        }
    }

    public void finishRefresh() {
        smartRefreshLayout.finishRefresh();
        smartRefreshLayout.finishLoadMore();
    }

    /**
     * 打开无货列表
     */
    private void openSinkProductList(SkuInfoBean wareInfo) {
        if (wareInfo == null) {
            return;
        }
        if (productListener != null && wareInfo.getCateId() != null
                && sinkWareInfoMap.containsKey(wareInfo.getCateId())) {
            CategoryWareInfoResult sinkWareInfo = sinkWareInfoMap.get(wareInfo.getCateId());
            if (sinkWareInfo.getSinkCount() > sinkWareInfo.getSinkPageCount()) {
                //无货总数量大于当页无货数量，说明有下一页，请求接口
                productListener.onSinkProductLoadMore(wareInfo.getCateId(), wareInfo.getMid(), sinkWareInfo.getPage() + 1,
                        sinkWareInfo.getSinkCount() - sinkWareInfo.getSinkPageCount(),
                        getFilterSinkSkuIds(wareInfo.getCateId()));
            } else {//如果没有下一页，那么直接加上之前保存的无货商品，刷新列表
                if (productInfoList != null && productInfoList.size() > 0) {
                    //去掉折叠提示条
                    int expandTipIndex = getExpandTipIndex(wareInfo.getCateId());
                    if (expandTipIndex > 0) {
                        List<SkuInfoBean> tempWareInfos = new ArrayList<>();
                        productInfoList.remove(expandTipIndex);
                        if (sinkWareInfoMap.containsKey(wareInfo.getCateId())) {
                            //加上已有的无货商品数据
                            tempWareInfos.addAll(sinkWareInfoMap.get(wareInfo.getCateId()).getProductCardVoList());
                            productInfoList.addAll(expandTipIndex, tempWareInfos);
                        }
                        showProductData(expandTipIndex, tempWareInfos.size());
                    }
                }
            }
            sinkExpandedMap.put(wareInfo.getCateId(), true);
            if (categoryFieldReporter != null) {
                categoryFieldReporter.invalidCommodityUnfoldClick(sinkWareInfo.getSinkCount());
            }
        }
    }

    /**
     * 获取已有的无货商品skuId列表
     *
     * @return
     */
    private List<String> getFilterSinkSkuIds(long cateId) {
        List<String> skuIds = new ArrayList<>();
        if (productInfoList != null && productInfoList.size() > 0) {
            int expandTipIndex = getExpandTipIndex(cateId);
            if (expandTipIndex >= 1 && productInfoList.get(expandTipIndex - 1) != null) {
                skuIds.add(productInfoList.get(expandTipIndex - 1).getSkuId());
            }
        }
        if (sinkWareInfoMap.containsKey(cateId)) {
            for (SkuInfoBean wareInfo : sinkWareInfoMap.get(cateId).getProductCardVoList()) {
                if (wareInfo != null) {
                    skuIds.add(wareInfo.getSkuId());
                }
            }
        }
        return skuIds;
    }

    /**
     * 初始化view
     *
     * @param context
     */
    private void initView(Context context) {
        mContext = context;
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_product_container, this, true);
        appBarLayout = rootView.findViewById(R.id.app_bar_layout);
        contentLayout = rootView.findViewById(R.id.cdl_content_layout);
        bannerPager = rootView.findViewById(R.id.banner_pager);
        thirdCategoryFilter = rootView.findViewById(R.id.third_cate_filter);
        sortTypeFilter = rootView.findViewById(R.id.sort_type_filter);

        llProductMask = rootView.findViewById(R.id.product_list_mask);

        initRecyclerView();
        initSmartRefreshLayout();
        initEmptyLayout();

        thirdCategoryFilter.setThirdCategoryCallback(thirdCategoryCallback);
        sortTypeFilter.setSortTypeCallback(sortTypeCallback);

//        nextSecondCateView = new CategoryMoreView(getContext());
        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                if (verticalOffset < 0 && productListener != null) {
                    productListener.foldFirstCate(true, true);
                }
            }
        });

//        contentLayout.setOnCoordinatorScrollListener(new CategoryCoordinatorLayout.OnCoordinatorScrollListener() {
//            @Override
//            public void scrollUp() {
//                if (productListener != null) {
//                    productListener.foldFirstCate(true, true);
//                }
//            }
//
//            @Override
//            public void scrollDown() {
//                if (currSecondCateIndex == 0 && productListener != null) {
//                    productListener.foldFirstCate(false, true);
//                }
//            }
//        });
    }

    private CategoryCoumterItemAnimator itemAnimator;

    private boolean isBackground = false;


    private void initRecyclerView() {
        rvProducts = rootView.findViewById(R.id.rv_products);
        linearLayoutManager = new CategoryLinearLayoutManager(mContext);
        rvProducts.setLayoutManager(linearLayoutManager);

        rvProducts.setFakeCallback(new CategoryRecyclerView.FakeCallback() {
            @Override
            public long getFakeItemId(RecyclerView.ViewHolder holder) {
                if (holder instanceof ProductListViewHolder) {
                    return ((ProductListViewHolder) holder).getFakeItemId();
                }
                return holder.getItemId();
            }
        });

        productAdapter = new CategoryProductAdapter((Activity) mContext);
        productAdapter.setCategoryContainerInterface(categoryContainerInterface);
        rvProducts.setAdapter(productAdapter);
        if (SFFieldCategoryConfig.getInstance().isOpenCategoryVideo()) {//根据开关是否展示分类商卡的视频
            recyclerviewPlayHelper = new HomeFloorPlayHelper();
            recyclerviewPlayHelper.init(rvProducts, ShareAnimationPlayer.PlayType.CATEGORY_LIST);
        }
        // 先使用默认动画实验下 确定是否是自定义动画导致
//        RecyclerView.ItemAnimator itemAnimator = rvProducts.getItemAnimator();
//        if (itemAnimator != null) {
//            itemAnimator.setAddDuration(0);
//            itemAnimator.setChangeDuration(400);
//            itemAnimator.setMoveDuration(0);
//            itemAnimator.setRemoveDuration(0);
//            if (itemAnimator instanceof SimpleItemAnimator) {
//                ((SimpleItemAnimator) itemAnimator).setSupportsChangeAnimations(true);
//            }
//        }
        if (itemAnimator == null) {
            itemAnimator = new CategoryCoumterItemAnimator();
            itemAnimator.setAddDuration(400);
            itemAnimator.setChangeDuration(400);
            itemAnimator.setMoveDuration(0);
            itemAnimator.setRemoveDuration(0);
        }
        rvProducts.setItemAnimator(itemAnimator);
        productAdapter.setOnAdapterItemClickListener(new CategoryProductAdapter.OnAdapterItemClickListener() {
            @Override
            public void getExpand(SkuInfoBean wareInfo) {
                openSinkProductList(wareInfo);
            }

            @Override
            public void addCartClick(SkuInfoBean wareInfo, int position) {
                DapeigouManager.getInstance().postDapeigouData(mContext, wareInfo.getSkuId(), new BaseFreshResultCallback<String, ResponseData<DapeigouResult>>() {
                    @Override
                    public ResponseData<DapeigouResult> onData(String data, FreshHttpSetting httpSetting) {
                        ResponseData<DapeigouResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<DapeigouResult>>() {
                        }.getType());
                        return responseData;
                    }

                    @Override
                    public void onEnd(ResponseData<DapeigouResult> object, FreshHttpSetting httpSetting) {
                        // 已经后台不可见了 则不插入数据
                        if (isBackground) {
                            return;
                        }
                        if (itemAnimator != null && itemAnimator.isRunning()) {
                            return;
                        }
                        if (smartRefreshLayout == null || smartRefreshLayout.isRefreshing() || smartRefreshLayout.isLoading()) {
                            // 下拉刷新中 或者 加载更多中 防止数据冲突，直接丢弃
                            return;
                        }
                        if (!isCanInsertData(position)) {
                            // 判断当前recyleView 可见的position范围如果插入的posion不在范围内 则不插入
                            return;
                        }
                        SkuInfoBean item = productAdapter.getItem(position);
                        if (item == null || wareInfo == null || !TextUtils.equals(wareInfo.getSkuId(), item.getSkuId())) {
                            // 说明 数据内容发生了变化 直接丢弃
                            return;
                        }
                        if (object != null && object.getData() != null && object.getData().getProductCardVoList() != null && object.getData().getProductCardVoList().size() > 0) {
                            SkuInfoBean dapeiGouWareInfo = wareInfo.copyData();
                            dapeiGouWareInfo.setViewType(CategoryProductAdapter.VIEW_TYPE_DAPEIGOU_TYPE);
                            dapeiGouWareInfo.setDapeigouList(object.getData().getProductCardVoList());
                            productInfoList.add(position + 1, dapeiGouWareInfo);
                            // 增加payload 避免整个item刷新 局部刷新 提升性能
                            productAdapter.notifyItemChanged(position, "divider");
                            if (productAdapter != null) {
                                productAdapter.addDapeigouItem(dapeiGouWareInfo, position);
                            }
                        }
                    }

                    @Override
                    public void onError(FreshHttpException error) {

                    }
                });

            }
        });
        rvProducts.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (dy > 0 && productListener != null) {
                    productListener.foldFirstCate(true, true);
                }
                if (linearLayoutManager == null) {
                    return;
                }

                int findFirstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
                if (productInfoList != null && findFirstVisibleItemPosition < productInfoList.size() && findFirstVisibleItemPosition > -1) {
                    SkuInfoBean wareInfo = productInfoList.get(findFirstVisibleItemPosition);
                    if (null == wareInfo) {
                        return;
                    }
                    if (clickThird) {
                        clickThird = false;
                    } else {
                        if (wareInfo.getCateId() != null && currCId != wareInfo.getCateId()) {
                            if (productListener != null) {
                                int pos = getThirdCateIndex(wareInfo.getCateId());
                                productListener.onScrollToThirdCate(pos);
                            }
                            currCId = wareInfo.getCateId();
                            currMid = wareInfo.getMid();
                            thirdCateName = getThirdCateName(wareInfo.getCateId());
                        }
                    }
                }

                preload(recyclerView);
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (categoryProductExposureHelper != null) {
                    categoryProductExposureHelper.exposureByHand(recyclerView, newState);
                }
                if (categoryContainerInterface != null) {
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        categoryContainerInterface.slideAi7HalfHide(false);
                    } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                        categoryContainerInterface.slideAi7HalfHide(true);
                    }
                }

            }
        });
    }

    private boolean isPreLoading = false;

    /**
     * 判断当前recyleView 可见的position范围如果插入的posion不在范围内 则不插入
     *
     * @return
     */
    private boolean isCanInsertData(int position) {
        if (productInfoList == null || productInfoList.size() == 0 || position >= productInfoList.size() || linearLayoutManager == null) {
            return false;
        }
        int firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
        int laseVisibleItemPosition = linearLayoutManager.findLastVisibleItemPosition();
        if (position < firstVisibleItemPosition || position > laseVisibleItemPosition) {
            return false;
        }
        return true;
    }


    public void preload(RecyclerView recyclerView) {
        if (isPreLoading) {
            return;
        }
        if (recyclerView.getAdapter() instanceof CategoryProductAdapter) {
            CategoryProductAdapter productAdapter = (CategoryProductAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int lastPosition = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                int size = productAdapter.getItemCount() - ((productAdapter.getFooterView() == null) ? 0 : 1);
                if (lastPosition == size - 4) {
                    isPreLoading = true;
                    loadMore(false, false);
                }
            }
        }
    }

    private void initSmartRefreshLayout() {
        smartRefreshLayout = rootView.findViewById(R.id.smart_refresh_layout);
        categoryHeader = rootView.findViewById(R.id.layout_header);
        categoryFooter = rootView.findViewById(R.id.layout_footer);
        smartRefreshLayout.setEnableAutoLoadMore(true);
        smartRefreshLayout.setOnMultiListener(new SimpleMultiListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                if (productListener != null) {
                    if (totalThirdCateSize > 0 && firstThirdCateIndex > 0) {
                        //下拉加载上一个三级分类
//                        skipInsert = true;
                        productListener.onPullThirdCate(firstThirdCateIndex - 1);
                    } else if (totalSecondCateSize > 0 && currSecondCateIndex > 0) {
                        productListener.onChangeSecondCate(currSecondCateIndex - 1);
                    } else if (totalSecondCateSize > 0 && currSecondCateIndex <= 0) {
                        productListener.foldFirstCate(false, true);
                        finishRefresh();
                    }
                }
            }

            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                loadMore(true, true);
            }
        });
    }

    private void loadMore(boolean canSwichSecondCate, boolean canSwichFirstCate) {
        if (productListener != null) {
            if (hasNextPageInCurrCate()) {
                productListener.onProductLoadMore(lastCurrPage + 1, PAGE_SIZE);
            } else if (totalThirdCateSize > 0 && lastThirdCateIndex < totalThirdCateSize - 1) {
                productListener.onLoadThirdCate(lastThirdCateIndex + 1);
            } else if (totalSecondCateSize > 0 && currSecondCateIndex < totalSecondCateSize - 1) {
                if (canSwichSecondCate) {
                    try {
                        SFLogCollector.e("CategoryProductContainer", "last:" + linearLayoutManager.findLastVisibleItemPosition() + " count:" + productAdapter.getItemCount());
                        if (linearLayoutManager.findLastVisibleItemPosition() < productAdapter.getItemCount() - 1) {
                            SFLogCollector.e("CategoryProductContainer", "wtf ");
                            BaseMaEntity ma = new BaseMaEntity();
                            HashMap<String, Object> ext = new HashMap<String, Object>();
                            ext.put("findLastVisibleItemPosition", linearLayoutManager.findLastVisibleItemPosition());
                            ext.put("getItemCount", productAdapter.getItemCount());
                            ext.put("lastTotalPage", lastTotalPage);
                            ext.put("lastCurrPage", lastCurrPage);
                            ext.put("lastSinkPageCount", lastSinkPageCount);
                            ext.put("totalThirdCateSize", totalThirdCateSize);
                            ext.put("lastThirdCateIndex", lastThirdCateIndex);
                            ext.put("totalSecondCateSize", totalSecondCateSize);
                            ext.put("currSecondCateIndex", currSecondCateIndex);
                            ma.setMa7FextParam(ext);
                            JDMaUtils.save7FClick("test_category_load_more", null, ma);
                        } else {
                            productListener.onChangeSecondCate(currSecondCateIndex + 1);
                        }
                    } catch (Exception e) {
                        JdCrashReport.postCaughtException(e);
                        e.printStackTrace();
                    }
                }
            } else {
                if (canSwichFirstCate) {
                    if (categoryFirstActionInterface != null) {
                        String nextPage = categoryFirstActionInterface.getNextCategoryName();
                        if (!TextUtils.isEmpty(nextPage)) {
                            categoryFirstActionInterface.doSwitchNextCategory();
                            finishRefresh();
                        }
                    }
                }
            }
        }
    }

    private void initEmptyLayout() {
        noDataLayout = rootView.findViewById(R.id.no_data_layout);
    }


    /**
     * 当前分类是否还有下一页
     * 如果当前页有无货数据，无论是折叠还是展开状态，上拉加载时都没有下一页
     *
     * @return
     */
    private boolean hasNextPageInCurrCate() {
        return lastTotalPage > 0 && lastCurrPage < lastTotalPage && lastSinkPageCount <= 0;
    }

    private CategorySortTypeFilter.SortTypeCallback sortTypeCallback = new CategorySortTypeFilter.SortTypeCallback() {

        @Override
        public void changeSortType(String sortType) {
            CategoryProductContainer.this.sortType = sortType;
            if (productListener != null) {
                productListener.changeSortType(sortType);
            }
        }

        @Override
        public void changeSortMaiDian(String eventId) {
            if (categoryFieldReporter != null) {
                if (CategoryConstant.Value.SORT_DEFAULT.equals(eventId)) {
                    categoryFieldReporter.sortTypeDefaultClick();
                } else if (CategoryConstant.Value.SORT_PRICE_ASC.equals(eventId)) {
                    categoryFieldReporter.sortPriceAscClick();
                } else if (CategoryConstant.Value.SORT_PRICE_DESC.equals(eventId)) {
                    categoryFieldReporter.sortPriceDescClick();
                } else if (CategoryConstant.Value.SORT_PROMOTION_DESC.equals(eventId)) {
                    categoryFieldReporter.sortPromotionClick();
                } else if (CategoryConstant.Value.SORT_AMOUNT_DESC.equals(eventId)) {
                    categoryFieldReporter.sortAmountClick();
                }
            }
        }

        @Override
        public void changeTimeFilter(FilterCriteriaVo selectFilter, boolean needRefresh) {
            mSelectedFilterCriteria = selectFilter;
            if (productListener != null) {
                productListener.changeTimeFilter(selectFilter, needRefresh);
            }
        }

        @Override
        public void changeTimeFilterMaiDian(String filterId) {
            if (categoryFieldReporter != null) {
                if (filterId != null) {
                    categoryFieldReporter.promiseSelectPromiseClick(filterId);
                } else {
                    categoryFieldReporter.promiseSelectEntranceClick();
                }
            }
        }

        @Override
        public void onUpdateShowFilter(boolean show) {
            llProductMask.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    };

    private ThirdCategoryFilter.ThirdCategoryCallback thirdCategoryCallback = new ThirdCategoryFilter.ThirdCategoryCallback() {


        @Override
        public void changeThirdCate(boolean b, int pos, boolean b1) {
            clickThird = true;
            if (productListener != null) {
                productListener.changeThirdCate(b, pos, b1);
            }
        }

        @Override
        public void onUpdateShowFilter(boolean show) {
            llProductMask.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    };

    /**
     * 是否是当前选中的
     *
     * @param hidden
     */
    @Override
    public void onResume(boolean hidden) {
        isBackground = false;
    }

    @Override
    public void onPause() {
        isBackground = true;
    }

    @Override
    public void onHiddenChange(boolean hidden) {

    }

    @Override
    public void onDestroy() {
        if (bannerPager != null) {
            bannerPager.onDestroy();
        }
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (null != recyclerviewPlayHelper) {
            recyclerviewPlayHelper.onDestroy();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {

    }

    // 列表内容刚刚填充或刷新时，手动触发可视区域第一个高光视频并播放
    private Runnable autoPlayRunnable = new Runnable() {
        @Override
        public void run() {
            if (recyclerviewPlayHelper != null) {
                recyclerviewPlayHelper.autoPlay();
            }
        }
    };

    /**
     * 如果领域视频播放开关打开，先移除，在增加，延迟1.5秒，确保1.5秒内不会被多次执行高光时刻视频的自动播放检测
     */
    private void postDelayAutoPlay() {
        if (SFFieldCategoryConfig.getInstance().isOpenCategoryVideo()) {
            handler.removeCallbacks(autoPlayRunnable);
            handler.postDelayed(autoPlayRunnable, 1000);
        }
    }
}
