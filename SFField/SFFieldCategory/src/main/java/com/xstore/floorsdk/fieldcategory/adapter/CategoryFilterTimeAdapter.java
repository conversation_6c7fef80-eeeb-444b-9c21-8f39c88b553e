package com.xstore.floorsdk.fieldcategory.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/23
 */
public class CategoryFilterTimeAdapter extends RecyclerView.Adapter<CategoryFilterTimeAdapter.ViewHolder> {

    private Context context;
    private List<FilterCriteriaVo> data;
    private FilterCriteriaVo selectFilter;

    public CategoryFilterTimeAdapter(Context context, List<FilterCriteriaVo> data, FilterCriteriaVo selectFilter) {
        this.context = context;
        this.data = data;
        this.selectFilter = selectFilter;
    }

    @NonNull
    @Override
    public CategoryFilterTimeAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_category_pop_filter_time_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryFilterTimeAdapter.ViewHolder holder, int position) {
        FilterCriteriaVo item = data.get(position);
        if (item == null) {
            return;
        }

        if (selectFilter != null && selectFilter.getId()!=null && selectFilter.getId().equals(item.getId())) {
            holder.ivSelected.setVisibility(View.VISIBLE);
            setSelectItem(holder, item.getTitle(), item.getSelectedImg(), R.color.sf_theme_color_level_1);
        } else {
            holder.ivSelected.setVisibility(View.GONE);
            setSelectItem(holder, item.getTitle(), item.getNotSelectedImg(), R.color.sf_field_category_color_252525);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (filterClickListener != null) {
                    filterClickListener.onItemClick(item);
                }
            }
        });

    }

    private void setSelectItem(ViewHolder holder, String title, String selectedImg, int color) {
        if (holder == null) {
            return;
        }
        if (!StringUtil.isNullByString(selectedImg)) {
            holder.ivFilter.setVisibility(View.VISIBLE);
            holder.tvFilter.setVisibility(View.GONE);
            ImageloadUtils.loadImage(context, holder.ivFilter, selectedImg);
        } else if (!StringUtil.isNullByString(title)) {
            holder.ivFilter.setVisibility(View.GONE);
            holder.tvFilter.setVisibility(View.VISIBLE);
            holder.tvFilter.setText(title);
            holder.tvFilter.setTextColor(context.getResources().getColor(color));
        } else {
            holder.ivFilter.setVisibility(View.GONE);
            holder.tvFilter.setVisibility(View.GONE);
        }
    }


    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvFilter;
        ImageView ivFilter;
        ImageView ivSelected;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvFilter = itemView.findViewById(R.id.tv_filter_key);
            ivFilter = itemView.findViewById(R.id.iv_filter_key);
            ivSelected = itemView.findViewById(R.id.iv_filter_selected);
        }
    }

    private FilterClickListener filterClickListener;

    public void setFilterClickListener(FilterClickListener listener) {
        filterClickListener = listener;
    }

    public interface FilterClickListener {
        void onItemClick(FilterCriteriaVo selectFilter);
    }
}
