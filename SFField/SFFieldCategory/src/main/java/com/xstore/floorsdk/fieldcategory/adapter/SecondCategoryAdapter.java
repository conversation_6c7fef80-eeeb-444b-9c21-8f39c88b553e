package com.xstore.floorsdk.fieldcategory.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 二级分类列表适配器
 *
 * <AUTHOR>
 * @date 2020/03/24
 */
public class SecondCategoryAdapter extends RecyclerView.Adapter<SecondCategoryAdapter.SecondCateHolder> {

    private final Source source;
    /**
     * context
     */
    private Context context;
    /**
     * 二级分类数据
     */
    private List<CategoryBean> secondCategory;
    /**
     * 当前选中的二级分类位置
     */
    private int currSecondCateIndex;

    private OnItemClickListener onItemClickListener;

    public CategoryBean getItem(int i) {
        if(i < 0 || secondCategory == null || secondCategory.size() == 0 || i >= secondCategory.size()) {
            return null;
        }
        return secondCategory.get(i);
    }

    public int findIndexByCid1(@Nullable String cid1) {
        if (cid1 == null || secondCategory == null || secondCategory.size() == 0) {
            return 0;
        }
        for(int i = 0 ; i < secondCategory.size(); i++) {
            if(cid1.equals(String.valueOf(secondCategory.get(i).getId()))) {
                return i;
            }
        }
        return 0;
    }

    public interface OnItemClickListener {
        /**
         * 点击回调
         *
         * @param position
         */
        void onClick(int position);
    }


    /**
     * 分类来源
     */
    public enum Source {
        /** 主页Tab */
        MAIN_TAB,
        /** 分类页 */
        CATEGORY_PAGE;
    }
    /**
     * 构造方法
     *
     * @param context
     * @param secondCategory
     * @param currSecondCateIndex
     */
    public SecondCategoryAdapter(Context context, List<CategoryBean> secondCategory, int currSecondCateIndex, Source source) {
        this.context = context;
        this.secondCategory = secondCategory;
        this.currSecondCateIndex = currSecondCateIndex;
        this.source = source;
    }

    public void setSecondCategory(List<CategoryBean> secondCategory, int currSecondCateIndex) {
        this.secondCategory = secondCategory;
        this.currSecondCateIndex = currSecondCateIndex;
        notifyDataSetChanged();
    }

    public long getSelectCid() {
        if (secondCategory == null || currSecondCateIndex < 0 || currSecondCateIndex >= secondCategory.size()) {
            return 0;
        }
        return secondCategory.get(currSecondCateIndex).getId();
    }

    public void setCurrSecondCateIndex(int currSecondCateIndex) {
        this.currSecondCateIndex = currSecondCateIndex;
        notifyDataSetChanged();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public void release() {
        onItemClickListener = null;
        if (secondCategory != null) {
            secondCategory.clear();
        }
        if (context != null) {
            context = null;
        }
    }

    @NonNull
    @Override
    public SecondCateHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_category_group_item, viewGroup, false);
        return new SecondCateHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SecondCateHolder holder, int i) {
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        holder.itemView.setLayoutParams(layoutParams);

        final CategoryBean category = secondCategory.get(i);
        String icon = null;
        switch (source) {
            case MAIN_TAB:
                icon = category.getTagIcon();
                break;
            case CATEGORY_PAGE:
            default:
                icon = category.getImageUrl();
                break;
        }
        String name = category.getName();
        int nameLength = 6;
        if (!TextUtils.isEmpty(icon)) {
            nameLength = 5;
        }
        if (name.length() > nameLength) {
            name = name.substring(0, 5) + "...";
        }
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) holder.llSecondCate.getLayoutParams();
        //设置当前选中状态
        if (currSecondCateIndex == i) {
            holder.icon.setVisibility(View.VISIBLE);
            lp.leftMargin = DPIUtil.dip2px(7);
            lp.rightMargin = DPIUtil.dip2px( 7);
            holder.llSecondCate.setLayoutParams(lp);

            holder.cateName.setTextColor(context.getResources().getColor(R.color.sf_field_category_color_1D1F2B));
            holder.itemView.setBackgroundResource(R.drawable.sf_field_category_shape_child_cate_select);
            holder.cateName.getPaint().setFakeBoldText(true);
            holder.cateName.postInvalidate();
            holder.cateName.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        } else {
            holder.icon.setVisibility(View.GONE);
            lp.leftMargin = DPIUtil.dip2px(4);
            lp.rightMargin = DPIUtil.dip2px(4);
            holder.llSecondCate.setLayoutParams(lp);

            holder.cateName.setTextColor(context.getResources().getColor(R.color.sf_field_category_color_252525));
            if (i == currSecondCateIndex - 1) {
                holder.itemView.setBackgroundResource(R.drawable.sf_field_category_child_cate_unselect_top);
            } else if (i == currSecondCateIndex + 1) {
                holder.itemView.setBackgroundResource(R.drawable.sf_field_category_child_cate_unselect_bottom);
            } else {
                holder.itemView.setBackgroundColor(context.getResources().getColor(R.color.sf_field_category_app_background));
            }
            holder.cateName.getPaint().setFakeBoldText(false);
            holder.cateName.postInvalidate();
            holder.cateName.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        }

        holder.cateName.setText(name);

        if (!TextUtils.isEmpty(icon)) {
            if (icon.startsWith("//")) {
                icon = "https:" + icon;
            }
            holder.hotIcon.setVisibility(View.VISIBLE);
            ImageloadUtils.loadImage(context, holder.hotIcon, icon);
        } else {
            holder.hotIcon.setVisibility(View.GONE);
        }
        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onClick(i);
            }
        });
    }

    @Override
    public int getItemCount() {
        return secondCategory == null ? 0 : secondCategory.size();
    }

    public int getCurrSecondCateIndex() {
        return currSecondCateIndex;
    }

    /**
     * 获取当前选中的类目
     * @return
     */
    public CategoryBean getCurSelectedItem() {
        if (secondCategory == null || secondCategory.size() == 0 || currSecondCateIndex >= secondCategory.size()) {
            return null;
        }
            return secondCategory.get(currSecondCateIndex);
    }

    class SecondCateHolder extends RecyclerView.ViewHolder {
        /**
         * 分类名
         */
        TextView cateName;
        /**
         * 促销标
         */
        ImageView hotIcon;
        /**
         * 选中指示器
         */
        TextView icon;
        /**
         * 二级分类名容器
         */
        LinearLayout llSecondCate;

        public SecondCateHolder(@NonNull View itemView) {
            super(itemView);
            cateName = itemView.findViewById(R.id.group_name);
            hotIcon = itemView.findViewById(R.id.hot_icon);
            icon = itemView.findViewById(R.id.icon);
            llSecondCate = itemView.findViewById(R.id.ll_second_cate);
        }
    }
}
