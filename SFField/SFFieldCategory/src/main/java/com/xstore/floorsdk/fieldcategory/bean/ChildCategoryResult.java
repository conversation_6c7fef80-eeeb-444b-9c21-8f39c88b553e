package com.xstore.floorsdk.fieldcategory.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 子分类请求结果实体类
 *
 * <AUTHOR>
 * @date 2022/11/07
 */
public class ChildCategoryResult implements Serializable {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 子分类列表
     */
    private List<CategoryBean> childCidList;
    /**
     * 选中的分类id
     */
    private Long cid;
    /**
     * 中台分类ID
     */
    private String mid;
    /**
     * 类目下商品信息列表
     */
    private CategoryWareInfoResult categorySkuInfoDto;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public List<CategoryBean> getChildCidList() {
        return childCidList;
    }

    public void setChildCidList(List<CategoryBean> childCidList) {
        this.childCidList = childCidList;
    }

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public CategoryWareInfoResult getCategorySkuInfoDto() {
        return categorySkuInfoDto;
    }

    public void setCategorySkuInfoDto(CategoryWareInfoResult categorySkuInfoDto) {
        this.categorySkuInfoDto = categorySkuInfoDto;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }
}
