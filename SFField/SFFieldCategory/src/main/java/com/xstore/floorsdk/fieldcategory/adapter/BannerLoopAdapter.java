package com.xstore.floorsdk.fieldcategory.adapter;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.jude.rollviewpager.RollPagerView;
import com.jude.rollviewpager.adapter.LoopPagerAdapter;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.BannerBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * BannerLoopAdapter简介
 * 类目广告
 *
 * <AUTHOR>
 * @date 2020-6-23 9:37:04
 */
public class BannerLoopAdapter extends LoopPagerAdapter {
    /**
     * data  轮播图图片的数据源
     */
    private List<BannerBean> data = new ArrayList<>();

    /**
     * context
     */
    private Context context;

    private CategoryReporterInterface categoryFieldReporter;

    /**
     * 构造方法
     */
    public BannerLoopAdapter(RollPagerView viewPager) {
        super(viewPager);
        context = viewPager.getContext();
    }

    /**
     * 设置数据
     *
     * @param newDatas 新数据
     */
    public void setData(List<BannerBean> newDatas, CategoryReporterInterface categoryFieldReporter) {
        if (data.size() > 0) {
            data.clear();
        }
        this.categoryFieldReporter = categoryFieldReporter;
        if (newDatas != null) {
            data.addAll(newDatas);
        }
        this.notifyDataSetChanged();
    }

    public List<BannerBean> getData() {
        return data;
    }

    @Override
    public View getView(ViewGroup container, final int position) {
        ImageView view = new ImageView(container.getContext());
        view.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        setImageClick(view, data.get(position),categoryFieldReporter);
        return view;
    }

    @Override
    public int getRealCount() {
        return data == null ? 0 : data.size();
    }

    public static void setImageClick(ImageView view, BannerBean bannerBean, CategoryReporterInterface reporter) {
        view.setScaleType(ImageView.ScaleType.CENTER_CROP);
        ImageloadUtils.loadCustomRoundCornerImage(view.getContext(), bannerBean.getImage(),
                view, 10, 10, 10,
                10, R.drawable.sf_theme_image_placeholder_rect_small);

        view.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (null != bannerBean.getAction()) {
                    Bundle bundle = new Bundle();
                    bundle.putInt("urltype", bannerBean.getAction().getUrlType());
                    bundle.putString("url", bannerBean.getAction().getToUrl());
                    bundle.putString("clsTag", bannerBean.getAction().getClsTag());
                    try {
                        Context context = view.getContext();
                        if (!(context instanceof Activity) && context instanceof ContextWrapper) {
                            context = ((ContextWrapper) context).getBaseContext();
                        }
                        FloorInit.getFloorConfig().startPage((Activity) context, bundle);
                    } catch (Exception e) {
                        e.printStackTrace();
                        JdCrashReport.postCaughtException(e);
                    }
                    if (reporter != null) {
                        reporter.bannerClick(bannerBean.getAction().getToUrl());
                    }
                }
            }
        });
    }

    public interface ClickListener {
        void onItemClick(BannerBean floorsBean);
    }

}
