package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.TranslateAnimation;
import android.widget.PopupWindow;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.jingdong.sdk.baseinfo.BaseInfo;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class DropDownFirstCategoryPop extends PopupWindow {

    private final Context context;
    private final View mask;
    private final View llContent;
    private final FirstCategoryAdapter adapter;
    private Long selectedId;

    public DropDownFirstCategoryPop(Context context, List<CategoryBean> categories, Long selectedId, PopClickListener clickListener) {
        this.context = context;
        this.selectedId = selectedId;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View contentView = inflater.inflate(R.layout.sf_field_category_pop_drow_down_first_category, null);
        setContentView(contentView);

        llContent = contentView.findViewById(R.id.ll_content);
        View tvFoldUp = contentView.findViewById(R.id.tv_fold_up);
        tvFoldUp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissDialog();
                if (clickListener != null) {
                    clickListener.onFoldClick();
                }
            }
        });

        mask = contentView.findViewById(R.id.mask);
        mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissDialog();
            }
        });
        mask.startAnimation(AnimationUtils.loadAnimation(context, R.anim.sf_field_category_pop_win_alpha_in));

        RecyclerView rv = contentView.findViewById(R.id.rvPullDownMenu);
        rv.setLayoutManager(new GridLayoutManager(context, 5) {
            @Override
            public void setMeasuredDimension(Rect childrenBounds, int wSpec, int hSpec) {
                int height = DPIUtil.getWidthByDesignValueFitFold(context, 800, 375);
                if (height > BaseInfo.getScreenHeight() / 5 * 4) {
                    height = BaseInfo.getScreenHeight() / 5 * 4;
                }
                super.setMeasuredDimension(childrenBounds, wSpec, View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.AT_MOST));
            }
        });
        adapter = new FirstCategoryAdapter(context, categories, selectedId, true);
        adapter.setClickListener(new FirstCategoryAdapter.ClickListener() {
            @Override
            public void onItemClick(@NotNull View view, int position, @NotNull CategoryBean cate) {
                if (clickListener != null) {
                    clickListener.onItemClick(view, position, cate);
                }
                dismissDialog();
            }
        });
        adapter.setClickSameItemListener(new FirstCategoryAdapter.ClickSameItemListener() {
            @Override
            public void clickSameItem() {
                dismissDialog();
            }
        });
        rv.setAdapter(adapter);
        rv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                if (parent.getChildLayoutPosition(view) % 5 == 0) {
                    outRect.left = DisplayUtils.dp2px(context, 5);
                } else if (parent.getChildLayoutPosition(view) % 5 == 4) {
                    outRect.right = DisplayUtils.dp2px(context, 5);
                }
            }
        });

        //设置按钮监听
        //设置SelectPicPopupWindow弹出窗体的宽
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        //设置SelectPicPopupWindow弹出窗体的高
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        //设置SelectPicPopupWindow弹出窗体可点击
        this.setFocusable(true);
        this.setClippingEnabled(false);
        //mMenuView添加OnTouchListener监听判断获取触屏位置如果在选择框外面则销毁弹出框
        setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        setOutsideTouchable(true);

    }

    @Override
    public void showAsDropDown(View anchor) {
        try {
            if (llContent != null) {
                TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                        Animation.RELATIVE_TO_SELF, -1, Animation.RELATIVE_TO_SELF, 0);
                animation.setDuration(200);
                llContent.startAnimation(animation);
            }

            if (Build.VERSION.SDK_INT < 24) {
                super.showAsDropDown(anchor);
            } else {
                int[] location = new int[2];
                anchor.getLocationInWindow(location);
                super.showAtLocation(anchor, Gravity.NO_GRAVITY, location[0], location[1] + anchor.getHeight());
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }
    }

    private void dismissDialog() {
        mask.startAnimation(AnimationUtils.loadAnimation(context, R.anim.sf_field_category_pop_win_alpha_out));
        if (llContent != null) {
            TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                    Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, -1);
            animation.setDuration(200);
            llContent.startAnimation(animation);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    dismiss();
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        } else {
            dismiss();
        }
    }


    @Override
    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }
    }

    public void updateSelectCate(long categoryId) {
        this.selectedId = categoryId;
        adapter.setSelectedCategoryId(categoryId);
    }

    public void updateCategories(List<CategoryBean> categories) {
        adapter.updateCategories(categories);
    }

    public interface PopClickListener {
        void onItemClick(View view, int position, CategoryBean cate);

        void onFoldClick();
    }
}