package com.xstore.floorsdk.fieldcategory.interfaces;


import android.view.View;
import android.view.ViewGroup;

import com.xstore.sevenfresh.datareport.JDMaUtils;

/**
 * 分类页容器接口
 *
 * <AUTHOR>
 * @date 2022/11/02
 */
public interface CategoryContainerInterface {
    /**
     * @return 获取埋点接口
     */
    JDMaUtils.JdMaPageImp getJdMaPageImp();
    /**
     * 获取意见反馈图标
     */
    View getFeedBackView();
    /**
     * 获取购物车图标图标
     */
    View getCartIconView();
    /**
     * 获取一级分类接口
     */
    String getFirstCategoryUrl();

    /**
     * 获取二级分类接口
     */
    String getChildCategoryUrl();

    /**
     * 获取商品列表接口
     */
    String getWareInfoByCidUrl();

    /**
     * 携带三级类目列表请求商品接口（此接口会把无货的三级类目过滤掉）
     * 只有切换二级类目时并且二级类目下的三级类目大于1才调用这个接口
     * @return
     */
    String getWareInfoByCid3s();

    /**
     * 获取缺货商品接口
     */
    String getSinkWareInfoUrl();

    /**
     * 分类的商品列表是否要展示视频
     */
    boolean isOpenCategoryVideo();

    /**
     * 分类领域SDK埋点是否可以使用Rust
     */
    boolean isReportCanUseRust();

    /**
     * 半隐藏AI小七
     * @param hide true进入半隐藏态，false退出半隐藏态
     */
    void slideAi7HalfHide(boolean hide);
}
