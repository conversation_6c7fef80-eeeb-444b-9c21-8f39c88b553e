package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.RelativeLayout;

import com.xstore.sevenfresh.service.sflog.SFLogCollector;

public class CategoryRelativeLayout extends RelativeLayout {

    private float downY;
    /**
     * ignore
     */
    private boolean ignore;
    /**
     * rangeView
     */
    private View rangeView;

    private Listener listener;

    private float lastY;
    /**
     * isUp
     */
    private ScrollDirection direction = ScrollDirection.NONE;

    /**
     * ScrollDirection简介
     * 滑动方向
     * <AUTHOR>
     * @date 2020-6-22 9:16:49
     */
    public enum ScrollDirection {
        /** 上 */
        UP,
        /** 下 */
        DOWN,
        /** 无 */
        NONE;
    }

    public CategoryRelativeLayout(Context context) {
        super(context);
    }

    public CategoryRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CategoryRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        SFLogCollector.d("conTOuch", "x:" + ev.getX() + " y:" + ev.getY());
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //如果手指落下的区域不是指定区域 那么 忽略
                if (!inViewRange(ev)) {
                    ignore = true;
                    return super.dispatchTouchEvent(ev);
                }
                downY = ev.getY();
                lastY = downY;
                break;
            case MotionEvent.ACTION_MOVE:
                if(ignore) {
                    return super.dispatchTouchEvent(ev);
                }
                if (ev.getY() - lastY <= -2) {
                    direction = ScrollDirection.UP;
                    SFLogCollector.d("lsp", "up:" + (ev.getY() - lastY));
                    //up
//                    int scrollY = (int) (ev.getY() - downY);
//                    if (scrollY < -10) {
                    int minScrollDistance = ViewConfiguration.get(getContext()).getScaledTouchSlop() / 2;
                    if(minScrollDistance < 5) {
                        minScrollDistance = 5;
                    }
                    //up
                    if (listener != null && ev.getY() - downY <= -minScrollDistance) {
                        listener.scrollUpInRange();
                        SFLogCollector.d("lsp", "scrollUpInRange:" + (ev.getY() - downY));
                    }
//                    }
                } else if (ev.getY() - lastY >= 2) {
                    direction = ScrollDirection.DOWN;
                    SFLogCollector.d("lsp", "down: " + (ev.getY() - lastY));
                } else {
//                    up = false;
                    direction = ScrollDirection.NONE;
                }
                lastY = ev.getY();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                ignore = false;
                break;
            default:
                break;
        }
        return super.dispatchTouchEvent(ev);
    }

    private boolean inViewRange(MotionEvent ev) {
        if (rangeView == null || rangeView.getWidth() == 0 && rangeView.getHeight() == 0) {
            return false;
        }
        int[] location = new int[2];
        getLocationOnScreen(location);
        int[] rangeLocation = new int[2];
        rangeView.getLocationOnScreen(rangeLocation);

        int rangeLeft = rangeLocation[0] - location[0];
        int rangeRight = rangeView.getWidth() + rangeLeft;
        int rangeTop = rangeLocation[1] - location[1];
        int rangeBottom = rangeView.getHeight() + rangeTop;

        SFLogCollector.d("lsp", " range l:" + rangeLeft + " range r:" + rangeRight
                + " range t:" + rangeTop + " range b:" + rangeBottom);

        if (ev.getX() >= rangeLeft &&
                ev.getX() <= rangeRight &&
                ev.getY() >= rangeTop &&
                ev.getY() <= rangeBottom) {
            SFLogCollector.d("lsp", "in range");
            return true;
        }
        return false;
    }

    public void setRangeView(View v) {
        this.rangeView = v;
    }

    public void setListener(Listener l) {
        listener = l;
    }

    public interface Listener {
        void scrollUpInRange();
    }


    public ScrollDirection getDirection() {
        return direction;
    }
}
