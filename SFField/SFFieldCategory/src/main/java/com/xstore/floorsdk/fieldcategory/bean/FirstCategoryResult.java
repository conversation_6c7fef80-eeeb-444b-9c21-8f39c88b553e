package com.xstore.floorsdk.fieldcategory.bean;


import java.io.Serializable;
import java.util.List;

/**
 * 一级分类请求结果实体类
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public class FirstCategoryResult implements Serializable {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * msg
     */
    private String msg;
    /**
     * 所有一级分类数据列表
     */
    private List<CategoryBean> allCategoryList;
    /**
     * 自定义筛选条件
     */
    private CustomizeFilterCriteriaVo customizeFilterCriteriaVo;

    public CustomizeFilterCriteriaVo getCustomizeFilterCriteriaVo() {
        return customizeFilterCriteriaVo;
    }

    public void setCustomizeFilterCriteriaVo(CustomizeFilterCriteriaVo customizeFilterCriteriaVo) {
        this.customizeFilterCriteriaVo = customizeFilterCriteriaVo;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<CategoryBean> getAllCategoryList() {
        return allCategoryList;
    }

    public void setAllCategoryList(List<CategoryBean> allCategoryList) {
        this.allCategoryList = allCategoryList;
    }
}
