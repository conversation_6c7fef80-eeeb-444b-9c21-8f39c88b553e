package com.xstore.floorsdk.fieldcategory;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
public class CategoryConstant {
    /**
     * Key常量
     */
    public interface Key {
        //来源
        String FROM_SOURCE = "source";
        //一级分类id
        String FIRST_CATEGORY_ID = "categoryid";
        //二级分类id
        String SECOND_CATEGORY_ID = "cid2";
    }
    /**
     * Value常量
     */
    public interface Value {
        /**
         * 来自首页点击
         */
        int SOURCE_HOME_PAGE = 1;
        /**
         * 来自分类tab的跳转
         */
        int SOURCE_CATEGORY_PAGE = 2;
        /**
         * 首页分类tab内请求-老的分类页-目前已无用
         */
        int SOURCE_HOME_TAB = 3;

        String SORT_DEFAULT = "sort_default";//综合排序
        String SORT_PRICE_ASC = "sort_price_asc";//价格升序
        String SORT_PRICE_DESC = "sort_price_desc";//价格降序
        String SORT_PROMOTION_DESC = "sort_promotion_desc";//促销排序
        String SORT_AMOUNT_DESC = "sort_amount_desc";//销量排序
    }

}
