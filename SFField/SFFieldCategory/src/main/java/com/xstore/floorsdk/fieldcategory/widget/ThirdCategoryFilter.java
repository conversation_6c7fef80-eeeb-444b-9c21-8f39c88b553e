package com.xstore.floorsdk.fieldcategory.widget;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.adapter.ThirdCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.floorsdk.fieldcategory.ma.ThirdCategoryExposureHelper;
import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;

import java.util.List;

/**
 * 三级分类筛选条
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
public class ThirdCategoryFilter extends LinearLayout implements View.OnClickListener {
    private View rootView;
    /**
     * 三级分类列表
     */
    private RecyclerView rvThirdCategory;
    /**
     * 二级分类名称
     */
    private TextView tvSecondCateName;
    /**
     * 箭头
     */
    private ImageView ivThirdCateArrow;

    private List<CategoryBean> thirdCateList;

    private ThirdCategoryAdapter thirdAdapter;
    private ThirdCategoryAdapter popAdapter;
    private DropDownThirdCategoryPop dropDownThirdCategoryPop;
    private ThirdCategoryCallback thirdCategoryCallback;
    private CategoryReporterInterface categoryFieldReporter;

    private ThirdCategoryExposureHelper thirdCategoryExposureHelper;

    public void setThirdCategoryExposureHelper(ThirdCategoryExposureHelper thirdCategoryExposureHelper) {
        this.thirdCategoryExposureHelper = thirdCategoryExposureHelper;
    }

    public ThirdCategoryFilter(Context context) {
        super(context);
        initView(context);
    }

    public ThirdCategoryFilter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public ThirdCategoryFilter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化view
     *
     * @param context
     */
    private void initView(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_thrid_category_filter, this, true);

        rvThirdCategory = findViewById(R.id.rv_third_category);
        tvSecondCateName = findViewById(R.id.tv_second_cate_name);
        ivThirdCateArrow = findViewById(R.id.iv_third_arrow);
        ivThirdCateArrow.setOnClickListener(this);

        //三级分类
        CenterLayoutManager layoutManager = new CenterLayoutManager(getContext());
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvThirdCategory.setLayoutManager(layoutManager);
        rvThirdCategory.addItemDecoration(new SpacesItemDecoration(DisplayUtils.dp2px(getContext(), 10), 0, 0, 0));
        rvThirdCategory.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (isCanExposure && thirdCategoryExposureHelper != null) {
                    thirdCategoryExposureHelper.exposureByHand(rvThirdCategory, newState);
                }
            }
        });
    }

    private boolean isCanExposure = false;
    private String secondCateName;

    /**
     * 设置数据
     *
     * @param thirdCateList
     * @param isCanExposure 是否可以曝光
     */
    public void setThirdCateData(List<CategoryBean> thirdCateList, String secondCateName, CategoryReporterInterface categoryFieldReporter, boolean isCanExposure) {
        this.isCanExposure = isCanExposure;
        this.secondCateName = secondCateName;
        if (thirdCateList == null || thirdCateList.isEmpty()) {
            setVisibility(GONE);
            return;
        }

        setVisibility(VISIBLE);
        this.thirdCateList = thirdCateList;
        this.categoryFieldReporter = categoryFieldReporter;

        tvSecondCateName.setText(secondCateName);

        int topBottom = DisplayUtils.dp2px(getContext(), 5);
        rvThirdCategory.setPadding(0, topBottom, 0, topBottom);

        if (thirdAdapter == null || !sameThird(thirdAdapter.getData(), thirdCateList)) {
            thirdAdapter = new ThirdCategoryAdapter(getContext(), thirdCateList, 0, false);
            thirdAdapter.setOnItemClickListener(new ThirdCategoryAdapter.ItemClickListener() {
                @Override
                public void clickItem(int pos) {
                    setThirdCatePos(pos);
                    if (thirdCategoryCallback != null) {
                        thirdCategoryCallback.changeThirdCate(true, pos, true);
                    }
                }
            });
//            thirdAdapter.setPadding(0, top, 0, top);
        }
        rvThirdCategory.setAdapter(thirdAdapter);

        rvThirdCategory.post(() -> {
            RecyclerView.LayoutManager layoutManager = rvThirdCategory.getLayoutManager();
            RecyclerView.Adapter adapter = rvThirdCategory.getAdapter();
            if (layoutManager instanceof LinearLayoutManager) {
                int lastPos = ((LinearLayoutManager) layoutManager).findLastCompletelyVisibleItemPosition();
                if (adapter != null && lastPos > -1 && lastPos < adapter.getItemCount() - 1) {
                    //还有更多 显示箭头
                    ivThirdCateArrow.setVisibility(View.VISIBLE);
                    ivThirdCateArrow.setRotation(0);
                } else {
                    //没有了
                    ivThirdCateArrow.setVisibility(View.GONE);
                    ivThirdCateArrow.setRotation(0);
                }
            }
        });
        if(thirdCateList.size() == 1){
            // 之所以这里隐藏是 防止其他get方法 一些内容未初始化导致存在问题
            setVisibility(View.GONE);
        }
    }

    public void exposeReport() {
        if (thirdCategoryExposureHelper != null) {
            thirdCategoryExposureHelper.exposureByHand(rvThirdCategory, RecyclerView.SCROLL_STATE_IDLE);
        }
    }

    public void setThirdCategoryCallback(ThirdCategoryCallback thirdCategoryCallback) {
        this.thirdCategoryCallback = thirdCategoryCallback;
    }

    public int getThirdCateIndex() {
        return thirdAdapter == null ? 0 : thirdAdapter.getSelectPos();
    }

    public List<CategoryBean> getThirdCateList() {
        return thirdAdapter == null ? null : thirdAdapter.getData();
    }


    public static boolean sameThird(List<CategoryBean> thirdCate1, List<CategoryBean> thirdCate2) {
        if (thirdCate1 == thirdCate2) {
            return true;
        }
        if (thirdCate1 != null && thirdCate2 != null) {
            if (thirdCate1.size() != thirdCate2.size()) {
                return false;
            }
            for (int i = 0; i < thirdCate1.size(); i++) {
                if (!thirdCate1.get(i).getId().equals(thirdCate2.get(i).getId())) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public void setThirdCatePos(int currThirdCateIndex) {
        if (thirdAdapter == null || currThirdCateIndex >= thirdAdapter.getItemCount() || currThirdCateIndex < 0) {
            return;
        }
        if (thirdAdapter != null) {
            thirdAdapter.setSetSelectPos(currThirdCateIndex);
            rvThirdCategory.postDelayed(() -> {
                rvThirdCategory.smoothScrollToPosition(currThirdCateIndex);
            }, 10);
        }
        //关闭展开的三级分类view
        if (dropDownThirdCategoryPop != null && dropDownThirdCategoryPop.isShowing()) {
            dropDownThirdCategoryPop.dismiss();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_third_arrow) {
            if (categoryFieldReporter != null) {
                categoryFieldReporter.allThirdCategoryClick();
            }
            popAdapter = new ThirdCategoryAdapter(getContext(), thirdAdapter.getData(), thirdAdapter.getSelectPos(), true);
            popAdapter.setOnItemClickListener(new ThirdCategoryAdapter.ItemClickListener() {
                @Override
                public void clickItem(int pos) {
                    setThirdCatePos(pos);
                    if (thirdCategoryCallback != null) {
                        thirdCategoryCallback.changeThirdCate(true, pos, true);
                    }
                }
            });

            dropDownThirdCategoryPop = new DropDownThirdCategoryPop(getContext());
            dropDownThirdCategoryPop.show(rootView, popAdapter);
            if (dropDownThirdCategoryPop.isShowing() && thirdCategoryCallback != null) {
                thirdCategoryCallback.onUpdateShowFilter(true);
            }
            dropDownThirdCategoryPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    tvSecondCateName.setVisibility(View.GONE);
                    arrowRotate(180, 360, ivThirdCateArrow);
                    if (thirdCategoryCallback != null) {
                        thirdCategoryCallback.onUpdateShowFilter(false);
                    }
                }
            });

            tvSecondCateName.setVisibility(View.VISIBLE);
            arrowRotate(0, 180, ivThirdCateArrow);
        }
    }

    /**
     * 三级分类的箭头动画
     *
     * @param start
     * @param end
     */
    private void arrowRotate(float start, float end, View view) {
        ObjectAnimator anim = ObjectAnimator.ofFloat(view, "rotation", start, end);
        anim.setDuration(300);
        anim.start();
    }

    public interface ThirdCategoryCallback {
        /**
         * 手动切换三级分类
         *
         * @param userClick
         * @param position
         * @param needRequestWare
         */
        void changeThirdCate(boolean userClick, int position, boolean needRequestWare);

        /**
         * 三级分类弹层是否展示
         *
         * @param show
         */
        void onUpdateShowFilter(boolean show);
    }
}
