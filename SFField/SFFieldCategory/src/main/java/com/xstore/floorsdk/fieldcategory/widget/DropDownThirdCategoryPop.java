package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.TranslateAnimation;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.adapter.ThirdCategoryAdapter;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;

/**
 * DropDownMenu简介
 * 三级分类下拉菜单
 *
 * <AUTHOR>
 * @date 2020-6-23 9:22:02
 */
public class DropDownThirdCategoryPop extends PopupWindow {

    private RecyclerView rvCollapseThirdCate;
    private Context context;
    private View mask;

    public DropDownThirdCategoryPop(Context context) {
        super(context);
        this.context = context;
        settingPopWindow();

    }

    /**
     * 设置window属性
     */
    public void settingPopWindow() {
        View contentView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_pop_third_category, null);

        rvCollapseThirdCate = contentView.findViewById(R.id.rv_collapse_third_cate);
        mask = contentView.findViewById(R.id.thrid_cate_mask);
        mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissDialog();
            }
        });

        rvCollapseThirdCate.setLayoutManager(new GridLayoutManager(context, 3));
        int marginLR = DisplayUtils.dp2px(context, 4);
        rvCollapseThirdCate.addItemDecoration(new SpacesItemDecoration(marginLR, marginLR, 0, 0));

        //设置SelectPicPopupWindow的View
        this.setContentView(contentView);
        //设置SelectPicPopupWindow弹出窗体的宽
        this.setWidth(ScreenUtils.getScreenWidth(context));
        //设置SelectPicPopupWindow弹出窗体的高
        this.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        //设置SelectPicPopupWindow弹出窗体可点击
        this.setFocusable(true);
        //设置SelectPicPopupWindow弹出窗体动画效果
//        this.setAnimationStyle(R.style.sf_field_category_pop_window);
        //实例化一个ColorDrawable颜色为半透明
        ColorDrawable dw = new ColorDrawable(0x00000000);
        //设置SelectPicPopupWindow弹出窗体的背景
        this.setBackgroundDrawable(dw);
        this.setClippingEnabled(false);
        //mMenuView添加OnTouchListener监听判断获取触屏位置如果在选择框外面则销毁弹出框
        this.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        // 设置允许在外点击消失
        this.setOutsideTouchable(true);
    }

    /**
     * 下拉式 弹出 pop菜单 parent 右下角
     *
     * @param parent
     * @param adapter
     */
    public void show(View parent, ThirdCategoryAdapter adapter) {
        try {
            setWidth(parent.getWidth());
            if (rvCollapseThirdCate != null) {
                TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                        Animation.RELATIVE_TO_SELF, -1, Animation.RELATIVE_TO_SELF, 0);
                animation.setDuration(200);
                rvCollapseThirdCate.startAnimation(animation);
            }
            showAsDropDown(parent, 0, 0);
            int marginTB = DisplayUtils.dp2px(context, 15);
            adapter.setPadding(0, 0, 0, marginTB);
            rvCollapseThirdCate.setAdapter(adapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void dismissDialog() {
        if (rvCollapseThirdCate != null) {
            TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                    Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, -1);
            animation.setDuration(200);
            rvCollapseThirdCate.startAnimation(animation);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    dismiss();
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        } else {
            dismiss();
        }
    }
}
