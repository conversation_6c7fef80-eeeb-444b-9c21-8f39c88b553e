package com.xstore.floorsdk.fieldcategory;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.jd.framework.json.JDJSON;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;

import java.util.ArrayList;

public abstract class LazyLoadFragment extends Fragment {

    protected boolean isDataLoaded = false; // 数据是否已经加载
    private int preloadingNumber = 3;
    private boolean onlyUseCache = false;

    /**
     * viewpager 当前的index 随着ViewPager切换是同步更新
     */
    protected int selectViewPagerIndex = 0;


    /**
     * 当前是否destroy
     */
    protected boolean isDestroy = true;


    /**
     * 选中的筛选条件
     */
    protected FilterCriteriaVo selectedFilterCriteriaVo;

    protected ArrayList<FilterCriteriaVo> firstFilterCriteria;

    public void setFirstFilterCriteria(ArrayList<FilterCriteriaVo> firstFilterCriteria) {
        this.firstFilterCriteria = firstFilterCriteria;
    }

    /**
     * 初始化时设置进来
     * @param selectViewPagerIndex
     */
    public void setSelectViewPagerIndex(int selectViewPagerIndex) {
        this.selectViewPagerIndex = selectViewPagerIndex;
    }

    public boolean isDestroy() {
        return isDestroy;
    }
    // 仅使用缓存数据 发生在一级分类数据是缓存数据时 二级分类等数据无需请求 只使用缓存数据就足够了
    public void setOnlyUseCache(boolean onlyUseCache) {
        // 不需要判断因为缓存变为请求数据时 fragment对象列表 重新创建了 所以内容都是新的 同一个对象内不会发生这个改变。
//        if (this.onlyUseCache != onlyUseCache) {
//            // 状态发生改变，预加载数据需要重新加载
//            isDataLoaded = false;
//        }
        this.onlyUseCache = onlyUseCache;
    }

    public void setPreloadingNumber(int number) {
        this.preloadingNumber = number;
    }


    public boolean isOnlyUseCache() {
        return onlyUseCache;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        isDestroy = false;
        if (isDifferenceLessNumber(selectViewPagerIndex, getItemIndex())) {
            loadData(selectViewPagerIndex == getItemIndex(), onlyUseCache);
            isDataLoaded = true;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        isDataLoaded = false;
        isDestroy = true;
    }

    /**
     * viewpager切换时外部直接调用进行预加载
     * 此时同步更新Viewpager的 currentIndex
     */
    public void preLoadingData(int selectIndex, FilterCriteriaVo filterCriteriaVo) {
        if(isDestroy){
            // 如果已经销毁了 直接return onCreate 地方会执行加载
            return;
        }
        this.selectViewPagerIndex = selectIndex;
        if (isDataLoaded) {
            // 已经预请求过接口 无需再次请求
            return;
        }
        // 只有需要加载数据时才需要更新筛选条件
        this.selectedFilterCriteriaVo = filterCriteriaVo;
        loadData(false, onlyUseCache);
        isDataLoaded = true;
    }

    /**
     *  viewpager 切换到当前 检测时效筛选条件是否发生改变 以及重新上报曝光埋点
     * @param selectIndex
     * @param filterCriteriaVo
     */
    public void refreshFragmentData(int selectIndex, FilterCriteriaVo filterCriteriaVo) {
        this.selectViewPagerIndex = selectIndex;
        if (isNeedRefreshData(filterCriteriaVo) || isDestroy) {
            this.selectedFilterCriteriaVo = filterCriteriaVo;
            // 其他页面内容 切换到当前页面，如果时效筛选条件发生了改变则强制刷新数据 数据加载成功后内部判断是否可以上报 可以的话自动上报 无需这里处理
            loadData(true, false);
            isDataLoaded = true;
            return;
        }
        // 补提曝光埋点上报，由于埋点上报过的内容会去重 所以无需担心重复上报
        reportExposePoint();
    }

    /**
     * 用于判断当前页面是否需要进行数据预加载
     * @param num1
     * @param num2
     * @return
     */
    public boolean isDifferenceLessNumber(int num1, int num2) {
        return Math.abs(num1 - num2) < preloadingNumber;
    }

    /**
     * 判断时效筛选条件是否发生变化，如果发生变化则刷新数据 仅限当前页面
     * @param filterCriteriaVo
     * @return
     */
    private boolean isNeedRefreshData(FilterCriteriaVo filterCriteriaVo) {
        if(!isDataLoaded){
            return true;
        }
        if (selectedFilterCriteriaVo != null && filterCriteriaVo != null) {
            return !TextUtils.equals(JDJSON.toJSONString(selectedFilterCriteriaVo), JDJSON.toJSONString(filterCriteriaVo));
        }

        return selectedFilterCriteriaVo != filterCriteriaVo;
    }

    /**
     * 用于预加载完成后的页面进行曝光埋点，补充曝光
     */
    protected abstract void reportExposePoint();

    /**
     * 是否为当前选中的viewPager 只有是的情况下才可以进行埋点曝光
     * @return
     */
    protected boolean isSelectViewPageCurrent() {
        return selectViewPagerIndex == getItemIndex();
    }

    /**
     * 子类实现这个方法来加载数据
     * @param showLoading  是否展示loading 当前页面展示是true 预加载的是false
     * @param onlyUseCache 是否只使用缓存数据，一级分类数据是缓存数据情况下 只使用缓存数据
     */
    protected abstract void loadData(boolean showLoading, boolean onlyUseCache);

    /**
     * 获取当前fragment在viewPager中是第几个
     * @return
     */
    protected abstract int getItemIndex();
}