package com.xstore.floorsdk.fieldcategory.adapter;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.HorizontalSpaceItemDecoration;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.SgmReportBusinessErrorLog;
import com.xstore.floorsdk.fieldcategory.base.BaseHeaderFooterRecyclerAdapter;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.floorsdk.fieldcategory.ma.DapeigouProductExposureHelper;
import com.xstore.floorsdk.fieldcategory.widget.CategoryMoreView;
import com.xstore.floorsdk.fieldcategory.widget.DapeigouDialog;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;
import com.xstore.sevenfresh.productcard.holder.ProductListViewHolder;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/09
 */
public class CategoryProductAdapter extends BaseHeaderFooterRecyclerAdapter {
    /**
     * 商品类型
     */
    public final static int VIEW_TYPE_PRODUCT = 0;
    /**
     * 标题类型
     */
    public final static int VIEW_TYPE_TITLE = 1;
    /**
     * 展开提示条类型
     */
    public final static int VIEW_TYPE_EXPAND_TIP = 2;
    /**
     * 三级分类空数据类型
     */
    public final static int VIEW_TYPE_NO_THIRD_DATA = 3;
    /**
     * 没有找到ItemView的类型
     */
    public final static int VIEW_TYPE_NO_TYPE = 4;

    public final static int VIEW_TYPE_DAPEIGOU_TYPE = 5;

    /**
     * 二级分类无数据类型
     */
    public final static int VIEW_TYPE_NO_SECOND_DATA_TYPE = 6;
    public final static int VIEW_TYPE_FOOTER = 7;

    /**
     * activity
     */
    private AppCompatActivity activity;
    /**
     * 布局加载器
     */
    private LayoutInflater inflater;
    /**
     * 商品数据列表
     */
    private List<SkuInfoBean> productList = new ArrayList<>();
    /**
     * 打开无货列表
     */
    private OnAdapterItemClickListener onAdapterItemClickListener;

    private CategoryReporterInterface categoryFieldReporter;

    private int noDataItemHeight = 0;

    private String footerTxt;

    public void setFooterTxt(String footerTxt) {
        this.footerTxt = footerTxt;
    }

    public CategoryProductAdapter(Activity activity) {
        //TODO activity
        this.activity = (AppCompatActivity) activity;
        this.inflater = LayoutInflater.from(activity);
    }

    public void setNoDataItemHeight(int noDataItemHeight) {
        this.noDataItemHeight = noDataItemHeight;
    }

    public List<SkuInfoBean> getProductList() {
        return productList;
    }

    /**
     * 透传商品数据
     *
     * @param list
     */
    public void setListData(List<SkuInfoBean> list, int insertStartIndex, int insertCount) {
        if (list == null) {
            return;
        }
        try {
            if (insertStartIndex > 0) {
                if (productList == null) {
                    productList = new ArrayList<>();
                }
                int oldSize = productList.size();
                productList.clear();
                productList.addAll(list);
                if (list.size() == oldSize) {
                    notifyItemRangeChanged(insertStartIndex, insertCount);
                } else {
                    notifyItemRangeChanged(insertStartIndex, 1);
                    notifyItemRangeInserted(insertStartIndex + 1, insertCount - 1);
                }
            } else {
                productList.clear();
                productList.addAll(list);
                notifyDataSetChanged();
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }
    }

    public void setOnAdapterItemClickListener(OnAdapterItemClickListener onAdapterItemClickListener) {
        this.onAdapterItemClickListener = onAdapterItemClickListener;
    }

    public void setCategoryFieldReporter(CategoryReporterInterface categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
    }

    @Override
    public int getRealItemCount() {
        return productList == null ? 0 : productList.size();
    }

    @Override
    public int getRealItemViewType(int position) {
        int viewType = VIEW_TYPE_NO_TYPE;
        SkuInfoBean wareInfo = getItem(position);
        if (wareInfo != null) {
            viewType = wareInfo.getViewType();
        }
        return viewType;
    }

    public SkuInfoBean getItem(int position) {
        if (productList == null) {
            return null;
        }
        if (position < 0 || productList.size() <= position) {
            return null;
        }
        return productList.get(position);
    }

    public synchronized void addDapeigouItem(SkuInfoBean addSkuInfoBean, int position) {
        if (productList == null) {
            return;
        }
        if (position < 0 || productList.size() <= position) {
            return;
        }

        try {
            position = position + 1;
            productList.add(position, addSkuInfoBean);
            int itemCount = productList.size() - position;
            notifyItemRangeChanged(position, itemCount);
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e, "addDapeigouItem");
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateItemViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case VIEW_TYPE_PRODUCT:
                View productView = inflater.inflate(R.layout.sf_card_product_list_item, parent, false);
                return new ProductListViewHolder(productView);
            case VIEW_TYPE_TITLE:
                View groupTitle = inflater.inflate(R.layout.sf_field_category_item_list_goods_title, parent, false);
                return new GroupTitleHolder(groupTitle);
            case VIEW_TYPE_EXPAND_TIP:
                View expandTip = inflater.inflate(R.layout.sf_field_category_item_ware_expand, parent, false);
                return new ExpandTipHolder(expandTip);
            case VIEW_TYPE_NO_THIRD_DATA:
                View noThirdData = inflater.inflate(R.layout.sf_field_category_item_no_data, parent, false);
                return new NoThirdDataHolder(noThirdData);
            case VIEW_TYPE_DAPEIGOU_TYPE:
                View dapeigouItem = inflater.inflate(R.layout.sf_field_category_item_dapeigou, parent, false);
                return new DAPEIGOUHolder(dapeigouItem);
            case VIEW_TYPE_NO_SECOND_DATA_TYPE:
                View noDataView = inflater.inflate(R.layout.sf_field_category_no_product_data_layout, parent, false);
                return new NoDataHolder(noDataView);
            case VIEW_TYPE_FOOTER:
                View nextSecondCateView = new CategoryMoreView(activity);
                return new SimpleViewHolder(nextSecondCateView);
            default:
                SgmReportBusinessErrorLog.reporCateCrashViewType(viewType);
                View simpleView = inflater.inflate(R.layout.sf_field_category_simple_view_layout, parent, false);
                return new SimpleViewHolder(simpleView);

        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position, @NonNull List<Object> payloads) {
        if (payloads.isEmpty()) {
            // 没有 payload，正常绑定全部数据
            super.onBindViewHolder(holder, position, payloads);
        } else {
            // 有 payload，只更新部分内容
            for (Object payload : payloads) {
                if (payload.equals("divider") && (holder instanceof ProductListViewHolder)) {
                    ProductListViewHolder viewHolder = (ProductListViewHolder) holder;
                    viewHolder.setDividerVisibility(View.GONE);
                }
            }
        }
    }

    @Override
    public void onBindItemViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        SkuInfoBean wareInfo = productList.get(position);
        if (wareInfo == null) {
            return;
        }
        if (holder instanceof GroupTitleHolder) {
            GroupTitleHolder titleHolder = (GroupTitleHolder) holder;
            titleHolder.tvTitle.setText(wareInfo.getCateName());
        } else if (holder instanceof ExpandTipHolder) {
            ExpandTipHolder expandTipHolder = (ExpandTipHolder) holder;
            expandTipHolder.tvExpandTip.setText(String.format(activity.getString(R.string.sf_field_category_today_sale_out_count_holder), wareInfo.getSinkCount()));
            expandTipHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onAdapterItemClickListener != null) {
                        onAdapterItemClickListener.getExpand(wareInfo);
                    }
                }
            });
        } else if (holder instanceof ProductListViewHolder) {
            ProductListViewHolder viewHolder = (ProductListViewHolder) holder;
            viewHolder.setCardBackground(null);
            viewHolder.bindData(activity, wareInfo, new ProductCardInterfaces() {
                @Override
                public int setCardAbilityType() {
                    return 0B1011;
                }

                @Override
                public void onCardClick(SkuInfoBean productInfoBean) {
                    if (productInfoBean == null) {
                        return;
                    }
                    if (NoDoubleClickUtils.isDoubleClick()) {
                        return;
                    }
                    FloorJumpManager.getInstance().jumpProductDetail(activity, productInfoBean, true, 0);
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        productInfoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.clickCommodity(productInfoBean);
                    }
                }

                @Override
                public void onAddCartClick(SkuInfoBean productInfoBean) {
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        productInfoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.clickAddCart(productInfoBean);
                    }
                }

                @Override
                public void onAddCartFinalClick(SkuInfoBean skuInfoBean) {
                    super.onAddCartFinalClick(skuInfoBean);
                    if (onAdapterItemClickListener != null) {
                        onAdapterItemClickListener.addCartClick(skuInfoBean, position);
                    }
                }

                @Override
                public void onRankClick(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
                    if (marketEntrance != null) {
                        FloorJumpManager.getInstance().startH5(activity, marketEntrance.getToUrl(), false);
                        if (categoryFieldReporter != null) {
                            // pageIndex 埋点使用 值不正确 设置为正确位置
                            productInfoBean.setPageIndex(String.valueOf(position + 1));
                            categoryFieldReporter.clickRank(productInfoBean, marketEntrance);
                        }
                    }
                }

                @Override
                public void onRankExposure(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        productInfoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.showRank(productInfoBean, marketEntrance);
                    }
                }

                @Override
                public void bookNowClick(SkuInfoBean productInfoBean) {
                    FloorJumpManager.getInstance().preSaleJustNow(activity, productInfoBean);
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        productInfoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.clickBookNow(productInfoBean);
                    }
                }

                @Override
                public void bookNowExposure(SkuInfoBean productInfoBean) {
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        productInfoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.showBookNow(productInfoBean);
                    }
                }

                @Override
                public void findSimilarClick(SkuInfoBean skuInfoBean) {
                    if (skuInfoBean != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        skuInfoBean.setPageIndex(String.valueOf(position + 1));
                        FloorJumpManager.getInstance().jumpSimilarList(activity, skuInfoBean.getSkuId(), "1");
                    }
                }

                @Override
                public void JKClick(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (skuInfoVoBean != null && marketEntrance != null) {
                        Bundle bundle = new Bundle();
                        bundle.putInt(FloorJumpManager.URL_TYPE, marketEntrance.getUrlType());
                        bundle.putString(FloorJumpManager.TO_URL, marketEntrance.getToUrl());
                        FloorJumpManager.getInstance().jumpAction(activity, bundle);

                        if (categoryFieldReporter != null) {
                            // pageIndex 埋点使用 值不正确 设置为正确位置
                            skuInfoVoBean.setPageIndex(String.valueOf(position + 1));
                            categoryFieldReporter.clickJk(skuInfoVoBean, marketEntrance);
                        }
                    }
                }

                @Override
                public void JKExpose(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        skuInfoVoBean.setPageIndex(String.valueOf(position + 1));
                        categoryFieldReporter.showJk(skuInfoVoBean, marketEntrance);
                    }
                }
            });
            // 判断下一个是否为搭配购数据 如果是 则隐藏当前的下划线
            if (position + 1 < getItemCount() && getItem(position + 1) != null &&
                    getItem(position + 1).getDapeigouList() != null && getItem(position + 1).getDapeigouList().size() > 0) {
                viewHolder.setDividerVisibility(View.GONE);
            } else {
                viewHolder.setDividerVisibility(View.VISIBLE);
            }


        } else if (holder instanceof DAPEIGOUHolder) {
            try {
                DAPEIGOUHolder dapeigouHolder = (DAPEIGOUHolder) holder;
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(activity);
                linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
                dapeigouHolder.dapeigouView.setLayoutManager(linearLayoutManager);
                List<SkuInfoBean> skuInfoBeans = wareInfo.getDapeigouList();
                dapeigouHolder.titleView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (skuInfoBeans == null && skuInfoBeans.size() < 4) {
                            return;
                        }
                        if (categoryFieldReporter != null) {
                            categoryFieldReporter.dapeigouProductClickMore(categoryContainerInterface, wareInfo);
                        }
                        DapeigouDialog dapeigouDialog = new DapeigouDialog(activity, skuInfoBeans, wareInfo.getSkuId(), categoryFieldReporter, categoryContainerInterface);
                        dapeigouDialog.show();
                    }
                });
                if (skuInfoBeans != null && skuInfoBeans.size() > 3) {
                    dapeigouHolder.moreView.setVisibility(View.VISIBLE);
                } else {
                    dapeigouHolder.moreView.setVisibility(View.GONE);
                }
                int size = skuInfoBeans.size();
                DapeigouAdapter dapeigouAdapter = new DapeigouAdapter(activity, size > 3 ? 82 : 84, 146, skuInfoBeans);
                int defaultItemWidth = ScreenUtils.dip2px(activity, 6);
                int startEndItemWidth = ScreenUtils.dip2px(activity, 8);
                dapeigouAdapter.setProductCardInterfaces(new ProductCardInterfaces() {
                    @Override
                    public int setCardAbilityType() {
                        return 0;
                    }

                    @Override
                    public void onCardClick(SkuInfoBean skuInfoBean) {
                        super.onCardClick(skuInfoBean);
                        if (skuInfoBean == null) {
                            return;
                        }
                        if (NoDoubleClickUtils.isDoubleClick()) {
                            return;
                        }
                        FloorJumpManager.getInstance().jumpProductDetail(activity, skuInfoBean, true, 0);

                        if (categoryFieldReporter != null) {
                            categoryFieldReporter.dapeigouProductClick(categoryContainerInterface, skuInfoBean, wareInfo.getSkuId());
                        }
                    }

                    @Override
                    public void onAddCartClick(SkuInfoBean skuInfoBean) {
                        super.onAddCartClick(skuInfoBean);
                        if (categoryFieldReporter != null) {
                            categoryFieldReporter.dapeigouProductAddCart(categoryContainerInterface, skuInfoBean, wareInfo.getSkuId());
                        }
                    }
                });

                HorizontalSpaceItemDecoration itemDecoration = new HorizontalSpaceItemDecoration(defaultItemWidth, startEndItemWidth, startEndItemWidth);
                // 需要先移除已添加的 ItemDecoration 否则会由于复用导致添加多个decoration
                for (int i = 0; i < dapeigouHolder.dapeigouView.getItemDecorationCount(); i++) {
                    RecyclerView.ItemDecoration decoration = dapeigouHolder.dapeigouView.getItemDecorationAt(i);
                    dapeigouHolder.dapeigouView.removeItemDecoration(decoration);
                }
                // 需要先移除已添加的 ItemDecoration 否则会由于复用导致添加多个ScrollListener
                dapeigouHolder.dapeigouView.clearOnScrollListeners();
                DapeigouProductExposureHelper dapeigouProductExposureHelper = new DapeigouProductExposureHelper(categoryFieldReporter);
                dapeigouHolder.dapeigouView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        super.onScrollStateChanged(recyclerView, newState);
                        if (dapeigouProductExposureHelper != null) {
                            dapeigouProductExposureHelper.exposureByHand(categoryContainerInterface, dapeigouHolder.dapeigouView, newState, wareInfo.getSkuId());
                        }
                    }
                });
                dapeigouHolder.dapeigouView.addItemDecoration(itemDecoration);
                dapeigouHolder.dapeigouView.setAdapter(dapeigouAdapter);
//             dapeigouAdapter.setData(skuInfoBeans);
                dapeigouHolder.dapeigouView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (dapeigouProductExposureHelper != null) {
                            dapeigouProductExposureHelper.exposureByHand(categoryContainerInterface, dapeigouHolder.dapeigouView, RecyclerView.SCROLL_STATE_IDLE, wareInfo.getSkuId());
                        }
                    }
                }, 100);
            } catch (Exception e) {
                JdCrashReport.postCaughtException(e, "DapeigouViewholder setData");
            }
        } else if (holder instanceof NoDataHolder) {
            NoDataHolder noDataHolder = (NoDataHolder) holder;
            noDataHolder.reduceHeight();
        } else if (holder instanceof SimpleViewHolder && holder.itemView != null && holder.itemView instanceof CategoryMoreView) {
            CategoryMoreView categoryMoreView = (CategoryMoreView) holder.itemView;
            categoryMoreView.setData(footerTxt);
        }
    }

    @Override
    public void onDetachedFromRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onDetachedFromRecyclerView(recyclerView);
    }

    @Override
    public void showFooter(RecyclerView.ViewHolder holder) {
    }

    class GroupTitleHolder extends RecyclerView.ViewHolder {

        TextView tvTitle;

        public GroupTitleHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.title);
        }
    }

    class ExpandTipHolder extends RecyclerView.ViewHolder {

        TextView tvExpandTip;

        public ExpandTipHolder(@NonNull View itemView) {
            super(itemView);
            tvExpandTip = itemView.findViewById(R.id.tv_ware_expand_tip);
        }
    }

    class NoThirdDataHolder extends RecyclerView.ViewHolder {

        TextView tvNoDataTip;

        public NoThirdDataHolder(@NonNull View itemView) {
            super(itemView);
            tvNoDataTip = itemView.findViewById(R.id.tv_cate_nodata_tip);
        }
    }

//    class SimpleViewHolder extends RecyclerView.ViewHolder {
//        public SimpleViewHolder(View itemView) {
//            super(itemView);
//        }
//    }

    class DAPEIGOUHolder extends RecyclerView.ViewHolder {
        View moreView;
        View titleView;
        RecyclerView dapeigouView;

        public DAPEIGOUHolder(View itemView) {
            super(itemView);
            titleView = itemView.findViewById(R.id.more_layout_title);
            moreView = itemView.findViewById(R.id.more_layout);
            dapeigouView = itemView.findViewById(R.id.dapeigou_recycler);
        }
    }

    class NoDataHolder extends RecyclerView.ViewHolder {
        // 增加一个方法，将 itemView 的高度减少 100 像素
        public void reduceHeight() {
            // 获取当前的布局参数
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            // 检查布局参数是否有效
            if (layoutParams != null) {
                layoutParams.height = noDataItemHeight;
                // 重新设置布局参数
                itemView.setLayoutParams(layoutParams);
            }
        }

        public NoDataHolder(View itemView) {
            super(itemView);
        }
    }

    public interface OnAdapterItemClickListener {
        void getExpand(SkuInfoBean wareInfo);

        void addCartClick(SkuInfoBean wareInfo, int position);
    }

    private CategoryContainerInterface categoryContainerInterface;

    public void setCategoryContainerInterface(CategoryContainerInterface categoryContainerInterface) {
        this.categoryContainerInterface = categoryContainerInterface;
    }
}
