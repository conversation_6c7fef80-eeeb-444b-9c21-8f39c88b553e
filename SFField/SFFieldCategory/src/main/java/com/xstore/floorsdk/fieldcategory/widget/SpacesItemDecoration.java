package com.xstore.floorsdk.fieldcategory.widget;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ItemDecoration;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/10/25.
 */

public class SpacesItemDecoration extends ItemDecoration {
    private int leftSpace;
    private int rightSpace;
    private int topSpace;
    private int bottomSpace;

    public SpacesItemDecoration(int leftSpace, int rightSpace, int topSpace, int bottomSpace) {
        this.leftSpace = leftSpace;
        this.rightSpace = rightSpace;
        this.topSpace = topSpace;
        this.bottomSpace = bottomSpace;
    }
    public SpacesItemDecoration(int rightSpace) {
        this.rightSpace = rightSpace;
    }
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.left = leftSpace;
        outRect.right = rightSpace;
        outRect.top = topSpace;
        outRect.bottom = bottomSpace;

        // Add top margin only for the first item to avoid double space between items
//        if (parent.getChildLayoutPosition(view) == (parent.getChildCount()-1)) {
//            outRect.right = leftSpace;
//        } else {
//            outRect.right = rightSpace;
//        }
    }
}
