package com.xstore.floorsdk.fieldcategory.notify;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;

public class FilterLiviData {
    private static FilterLiviData instance;
    private final MutableLiveData<FilterCriteriaVo> liveData;

    private FilterLiviData() {
        liveData = new MutableLiveData<>();
    }

    public static synchronized FilterLiviData getInstance() {
        if (instance == null) {
            instance = new FilterLiviData();
        }
        return instance;
    }

    public LiveData<FilterCriteriaVo> getLiveData() {
        return liveData;
    }

    public void updateData(FilterCriteriaVo data) {
        liveData.setValue(data);
    }
}
