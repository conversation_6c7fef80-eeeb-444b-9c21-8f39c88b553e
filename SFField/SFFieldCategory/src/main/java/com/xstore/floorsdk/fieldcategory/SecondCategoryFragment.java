package com.xstore.floorsdk.fieldcategory;

import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.DEFAULT_EFFECT;
import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.NO_EFFECT;

import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.adapter.SecondCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.BannerBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryWareInfoResult;
import com.xstore.floorsdk.fieldcategory.bean.ChildCategoryResult;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.floorsdk.fieldcategory.interfaces.ActionState;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryBridgeInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryFirstActionInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.OnProductRefreshOrLoadMoreListener;
import com.xstore.floorsdk.fieldcategory.ma.SecondCategoryExposureHelper;
import com.xstore.floorsdk.fieldcategory.ma.SecondCategoryFieldReporter;
import com.xstore.floorsdk.fieldcategory.ma.ThirdCategoryExposureHelper;
import com.xstore.floorsdk.fieldcategory.request.CategoryRequest;
import com.xstore.floorsdk.fieldcategory.widget.OverScrollLayout;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.fresh_network_business.FreshResultCallback;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class SecondCategoryFragment extends LazyLoadFragment implements OnProductRefreshOrLoadMoreListener {

    private static final String TAG = "SecondCategoryFragment";
    private AppCompatActivity mActivity;
    /**
     * srlSencondCate 二级类目包裹
     */
    private OverScrollLayout oslSencondCate;
    private View rootView;
    /**
     * 左侧二级分类列表
     */
    private RecyclerView secondCateListView;
    private RecyclerView.ItemDecoration secondCateDecoration;
    /**
     * 二级分类列表适配器
     */
    private SecondCategoryAdapter secondAdapter;
    /**
     * 二级分类
     */
    private List<CategoryBean> secondCategory = new ArrayList<>();
    /**
     * 二级分类名
     */
    public String secondCateName;
    /**
     * 选中的二级类目Id
     */
    public String secondCateId;
    public String secondMid;
    /**
     * 当前选中的二级分类位置
     */
    private int currSecondCateIndex;

    /**
     * 下个二级类目的名称
     */
    private String nextSecondCateName;
    /**
     * 当前分类Id（二级或者三级）
     */
    private long currCId;
    private String currMid;

    /**
     * 三级分类
     */
    private List<CategoryBean> thirdCategory = new ArrayList<>();
    /**
     * 当前选中的三级分类位置
     */
    private int currThirdCateIndex;

    /**
     * 三级分类名（埋点用）
     */
    public String thirdCategoryName;
    /**
     * 三级分类名
     */
    public String thirdCateId;
    public String thirdMid;
    private SecondCategoryFieldReporter categoryFieldReporter;


    /**
     * secNoDataLayout 二级类目无数据 空白页
     */
    private View secNoDataLayout;

    /**
     * 商品列表无数据也没有缓存时
     */
    private View productNoDataLayout;


    private CategoryProductContainer productContainer;

    /**
     * 当前操作状态
     */
    private ActionState actionState;

    private long categoryId = 0;
    private long beforeCategoryId2 = 0;
    public String firstMid;
    /**
     * 选中的一级类目名称
     */
    public String firstCateName;
    /**
     * 页面来源 {@link CategoryConstant}
     */
    private int source = 0;

    /**
     * 埋点使用 只要切换二级分类 生成新的
     */
    public String pvId;
    /**
     * 埋点使用 只要切换二级分类 生成新的
     */
    public String logId;

    private CategoryContainerInterface categoryContainerInterface;

    /**
     * 排序类型 1:综合排序, 2:价格升序 , 3:价格降序, 4:总销量升序,
     * 5:总销量降序 (如果此字段为空， 则走默认排序：销量降序)
     */
    public String sortType;

    /**
     * 是否切换二级分类 这里好奇怪 原来默认值居然是true
     */
    public boolean isChangeSecondCate = false;

    private int currentItemIndex;

    private ThirdCategoryExposureHelper thirdCategoryExposureHelper;

    //AB 实验上报 7fNewCategory 中的 buriedExpLabel 埋点上报到 touchstone_expids 这个字段 所有相关的都要
    private String buriedExpLabel;

    private CategoryFirstActionInterface categoryFirstActionInterface;

    public void setCategoryFirstActionInterface(CategoryFirstActionInterface categoryFirstActionInterface) {
        this.categoryFirstActionInterface = categoryFirstActionInterface;
    }

    public void setBuriedExpLabel(String buriedExpLabel) {
        this.buriedExpLabel = buriedExpLabel;
    }

    public void setCategoryContainerInterface(CategoryContainerInterface categoryContainerInterface) {
        this.categoryContainerInterface = categoryContainerInterface;

    }

    private CategoryBridgeInterface categoryBridgeInterface;

    public void setCategoryBridgeInterface(CategoryBridgeInterface categoryBridgeInterface) {
        this.categoryBridgeInterface = categoryBridgeInterface;
    }

    /**
     * 防止getActivity()空指针
     */
    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        mActivity = (AppCompatActivity) context;
    }

    private CategoryBean categoryBean;
    private int distance;

    public void setCategoryBean(CategoryBean categoryBean) {
        this.categoryBean = categoryBean;

    }

    public void setBeforeCategoryId2(long beforeCategoryId2) {
        this.beforeCategoryId2 = beforeCategoryId2;
    }

    public void setSource(int source) {
        this.source = source;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            currentItemIndex = getArguments().getInt("item_index");
        }
        if (categoryBean != null) {
            if (categoryBean.getId() != null) {
                categoryId = categoryBean.getId();
            }
            firstMid = categoryBean.getMid();
            firstCateName = categoryBean.getName();
        }
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (rootView == null) {
            mActivity = (AppCompatActivity) getActivity();
            rootView = inflater.inflate(R.layout.sf_field_second_category_fragment, container, false);
            initView();
            setBottomDistance();
        }
        return rootView;
    }


    public void setDistance(int distance) {
        if (this.distance != distance) {
            this.distance = distance;
        }

    }

    public static SecondCategoryFragment newInstance(int itemIndex) {
        SecondCategoryFragment fragment = new SecondCategoryFragment();
        Bundle args = new Bundle();
        args.putInt("item_index", itemIndex);
        fragment.setArguments(args);
        return fragment;
    }

    private SecondCategoryExposureHelper secondCategoryExposureHelper;

    private void initView() {
        //二级分类
        oslSencondCate = rootView.findViewById(R.id.osl_second_cate);
        secondCateListView = rootView.findViewById(R.id.rv_second_category);
        productNoDataLayout = rootView.findViewById(R.id.product_no_data);
        CenterLayoutManager secondCateLayoutManager = new CenterLayoutManager(getActivity());
        secondCateLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        secondCateListView.setLayoutManager(secondCateLayoutManager);
        initSecondCateDecoration();
        secNoDataLayout = rootView.findViewById(R.id.sec_no_data);
        productContainer = rootView.findViewById(R.id.sku_container);
        categoryFieldReporter = new SecondCategoryFieldReporter();
        if (thirdCategoryExposureHelper == null) {
            thirdCategoryExposureHelper = new ThirdCategoryExposureHelper();
        }
        thirdCategoryExposureHelper.setCategoryFieldReporter(categoryFieldReporter);
        productContainer.setThirdCategoryExposureHelper(thirdCategoryExposureHelper);
        productContainer.setActionListener(this, categoryContainerInterface, categoryFieldReporter);
        productContainer.setCategoryFirstActionInterface(categoryFirstActionInterface);
        if (categoryBridgeInterface != null) {
            selectedFilterCriteriaVo = categoryBridgeInterface.getFilterCriteriaVo();
            categoryFieldReporter.initData(categoryBridgeInterface.getJdMaPageImp(), productContainer);
            categoryFieldReporter.setBuriedExpLabel(buriedExpLabel);
        }
        secondCateListView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (!isSelectViewPageCurrent()) {
                    return;
                }
                if (secondCategoryExposureHelper == null) {
                    secondCategoryExposureHelper = new SecondCategoryExposureHelper(categoryFieldReporter);
                    secondCategoryExposureHelper.setFirstCategoryMid(firstMid);
                    secondCategoryExposureHelper.setFirstCategoryName(firstCateName);
                }
                secondCategoryExposureHelper.exposureByHand(secondCateListView, newState);
            }
        });
    }

    private void handSecondCateExpose() {
        if (!isSelectViewPageCurrent()) {
            return;
        }
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (secondCategoryExposureHelper == null) {
                    secondCategoryExposureHelper = new SecondCategoryExposureHelper(categoryFieldReporter);
                    secondCategoryExposureHelper.setFirstCategoryMid(firstMid);
                    secondCategoryExposureHelper.setFirstCategoryName(firstCateName);
                }
                secondCategoryExposureHelper.exposureByHand(secondCateListView, RecyclerView.SCROLL_STATE_IDLE);
            }
        }, 50);
    }

    @Override
    public void onResume() {
        super.onResume();
        if(productContainer != null) {
            productContainer.onResume(isSelectViewPageCurrent());
        }
        if (categoryBridgeInterface == null) {
            return;
        }

        if (categoryBridgeInterface.getBottomDistance() != distance) {
            this.distance = categoryBridgeInterface.getBottomDistance();
            setBottomDistance();
        }
    }

    private void initSecondCateDecoration() {
        secondCateDecoration = new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                int childPosition = parent.getChildAdapterPosition(view);
                if (childPosition == secondAdapter.getItemCount() - 1) {
                    outRect.bottom = DisplayUtils.dp2px(mActivity, 150);
                } else {
                    outRect.bottom = 0;
                }
            }
        };
    }

    private void setBottomDistance() {
        if (distance > 0) {
            secondCateListView.addItemDecoration(secondCateDecoration);
            oslSencondCate.setLastItemBottomMargin(DisplayUtils.dp2px(mActivity, distance));
        } else {
            secondCateListView.removeItemDecoration(secondCateDecoration);
            oslSencondCate.setLastItemBottomMargin(0);
        }
    }

    /**
     * 获取一级分类下子分类
     *
     * @param effect       预加载不展示loading 0 不展示loading 1 展示loading
     * @param onlyUseCache 只使用缓存数据  当一级分类是缓存数据时 二级分类数据 无需真实请求 直接使用缓存数据即可
     */
    private void getChildCategory(int effect, boolean onlyUseCache) {
        actionState = ActionState.PRODUCT_REFRESH;
        generateNewSearchPvId(1);
        CategoryRequest.getChildCategory(mActivity, onlyUseCache, effect, categoryId, firstCateName, beforeCategoryId2, firstMid, 1, CategoryProductContainer.PAGE_SIZE, pvId, logId, source, getCategoryQueryConditions(), categoryContainerInterface, new NewChildCategoryListener(categoryId));
        if (beforeCategoryId2 > 0) {
            beforeCategoryId2 = 0;
        }
    }

    /**
     * 新的搜索 要生成一个新的pvId，都是从第一页开始的，后续翻页不变
     * 新的搜索 要生成一个新的logId，第一页的logId等于pvId，后续分页再随机生成
     */
    private void generateNewSearchPvId(int currentPage) {
        if (currentPage == 1) {
            pvId = getRandomString(32);
            logId = pvId;
        } else {
            //当前页码是其他页 需要生成新的logId
            logId = getRandomString(32);
        }
    }

    /**
     * 获取一个随机值
     *
     * @param length 随机字符串的长度
     * @return 返回一个随机值
     */
    private String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(36);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 子类数据请求成功，展示二级、三级分类列表
     *
     * @param selectSecondIndex
     * @param needRequestWare   是否需要请求商品列表，缓存数据不请求，同时有第一页数据时也不需要再请求了
     * @param isShowLoading     是否需要展示loading
     */
    private void showSecondAndThirdCate(int selectSecondIndex, boolean needRequestWare, boolean isShowLoading) {
        if (secondCategory == null || secondCategory.size() <= 0) {
            secondCateListView.setVisibility(View.GONE);
            return;
        }
        if (selectSecondIndex >= secondCategory.size()) {
            return;
        }
        actionState = ActionState.PRODUCT_REFRESH;
        currSecondCateIndex = selectSecondIndex;
        CategoryBean currSecCate = secondCategory.get(currSecondCateIndex);
        secondCateName = currSecCate.getName();
        secondCateId = currSecCate.getId() + "";
        secondMid = currSecCate.getMid() + "";
        //如果存在下一个二级分类 那么就赋值
        if (secondCategory.size() > currSecondCateIndex + 1) {
            nextSecondCateName = secondCategory.get(currSecondCateIndex + 1).getName();
        }
        commonSetCurrCate3(currSecCate.getCid3(), currSecCate.getId(), currSecCate.getMid());
        //重置排序方式为综合
        sortType = CategoryConstant.Value.SORT_DEFAULT;

        //显示二级分类view
        secondCateListView.setVisibility(View.VISIBLE);
        if (secondAdapter == null) {
            secondAdapter = new SecondCategoryAdapter(requireContext(), secondCategory, currSecondCateIndex, SecondCategoryAdapter.Source.CATEGORY_PAGE);
            secondAdapter.setOnItemClickListener(new SecondCategoryAdapter.OnItemClickListener() {
                @Override
                public void onClick(int position) {
                    changeSecondCate(position, true, false);
                }
            });
            secondCateListView.setAdapter(secondAdapter);
        } else {
            secondAdapter.setSecondCategory(secondCategory, currSecondCateIndex);
        }

        secondCateListView.postDelayed(() -> {
            try {
                secondCateListView.smoothScrollToPosition(currSecondCateIndex);
            } catch (Exception e) {
                JdCrashReport.postCaughtException(e);
            }
        }, 5);

        // 原有逻辑 根据二级分类以及筛选条件获取当前 商品列表，缓存逻辑不获取商品数据
        if (needRequestWare) {
            //请求商品数据
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (isDestroy) {
                        return;
                    }
                    if (isChangeSecondCate && thirdCategory != null && thirdCategory.size() > 1) {
                        //切换二级类目时，当三级类目数量大于1时需调用此接口过滤掉无货的三级类目
                        getWareInfoByCid3s(isShowLoading, CategoryProductContainer.PAGE_SIZE, true, thirdCategory);
                    } else {
                        getWareInfoByCid(isShowLoading, 1, CategoryProductContainer.PAGE_SIZE, true, false);
                    }
                }
            }, 40);
        } else {

        }
    }

    /**
     * 点击二级类目
     *
     * @param position       点击的二级分类索引
     * @param userClick      是否是用户点击引起的切换
     * @param selectNextCate 是否滑动商品列表加载下一分类
     * @return
     */
    private boolean changeSecondCate(int position, boolean userClick, boolean selectNextCate) {
        if (position < 0 || currSecondCateIndex == position) {/** 点击当前选中的不响应*/
            return false;
        }
        if (userClick) {
            foldFirstCate(true, false);
        }
        isChangeSecondCate = true;
        actionState = ActionState.PRODUCT_REFRESH;
        showSecondAndThirdCate(position, true, true);

        if (userClick) {
            try {
                CategoryBean currSecCate = secondCategory.get(position);
                categoryFieldReporter.secondCategoryClick(currSecCate, firstMid, firstCateName, position + 1);
            } catch (Exception e) {
            }

        } else if (selectNextCate) {
            //上拉到下一分类
        } else {
            //下拉到下一分类
        }
        return false;
    }


    /**
     * 解析子分类数据
     *
     * @param fromCache
     */
    private void parseChildCategoryData(ChildCategoryResult childCategoryResult, boolean fromCache) {
        secNoDataLayout.setVisibility(View.GONE);
        try {
            if (childCategoryResult != null) {
                // 解析二级分类列表
                secondCategory = childCategoryResult.getChildCidList();
                if (secondCategory != null && secondCategory.size() > 0) {
                    //当前默认选中的二级分类 如果没有返回当前商品列表属于的二级分类ID 那么就按照没有商品算，此时如果是非缓存数据 那么就需要拉取商品列表
                    long currSecCateId = childCategoryResult.getCid() != null ? childCategoryResult.getCid() : 0;
                    long currThirCateId = childCategoryResult.getCid() != null ? childCategoryResult.getCid() : 0;
                    if (currSecCateId == 0) {
                        //当前没有默认选中的二级分类，则选中第一个
                        showSecondAndThirdCate(0, !fromCache, isShowLoading);
                    } else {
                        boolean findSecondCate = false;
                        // 遍历检查一默认选中的二级分类是否存在
                        for (CategoryBean secCate : secondCategory) {
                            boolean selectedCid3 = false;
                            if (secCate.getCid3() != null && secCate.getCid3().size() > 0) {
                                for (CategoryBean c3 : secCate.getCid3()) {
                                    if (c3.getId() == currSecCateId) {
                                        //纠正为当前选中的二级类目id
                                        currSecCateId = c3.getParentId();
                                        currThirCateId = c3.getId();
                                        selectedCid3 = true;
                                    }
                                }
                            }
                            if (selectedCid3 || (secCate != null && secCate.getId() != null && currSecCateId == secCate.getId())) {
                                currSecondCateIndex = secondCategory.indexOf(secCate);
                                //如果已经返回第一页商品数据，则不用再请求，直接展示
                                if (childCategoryResult.getCategorySkuInfoDto() != null && childCategoryResult.getCategorySkuInfoDto().getProductCardVoList() != null && childCategoryResult.getCategorySkuInfoDto().getProductCardVoList().size() > 0) {
                                    showSecondAndThirdCate(currSecondCateIndex, false, isShowLoading);
                                    //兼容里层取不到的mid的情况
                                    if (StringUtil.isNullByString(childCategoryResult.getCategorySkuInfoDto().getMid())) {
                                        childCategoryResult.getCategorySkuInfoDto().setCid(childCategoryResult.getCid());
                                        childCategoryResult.getCategorySkuInfoDto().setMid(childCategoryResult.getMid());
                                    }
                                    showCateProducts(currSecCateId, currThirCateId, childCategoryResult.getCategorySkuInfoDto(), pvId, logId, fromCache);
                                } else {
                                    showSecondAndThirdCate(currSecondCateIndex, !fromCache, isShowLoading);
                                }
                                findSecondCate = true;
                                break;
                            }
                        }
                        if (!findSecondCate) {
                            //没有找到默认的二级分类，则选中第一个
                            showSecondAndThirdCate(0, !fromCache, isShowLoading);
                        }
                    }
                    if (!fromCache && isSelectViewPageCurrent()) {
                        handSecondCateExpose();
                    }
                } else {
                    //二级分类是空的情况， 这种应该展示无数据页面 需要用户手动重新刷新更是  下面是原有逻辑 没看明白为啥需要这样
//                    showSecondAndThirdCate(0, false, isShowLoading);
//                    showCateProducts(0, 0, new CategoryWareInfoResult(), pvId, logId, fromCache);

                    getChildCategoryFail(fromCache);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }

    }

    @Override
    public void onProductLoadMore(int currPage, int pageSize) {
        actionState = ActionState.PRODUCT_LOAD_MORE;
        getWareInfoByCid(false, currPage, pageSize, true, true);
    }

    @Override
    public void onSinkProductLoadMore(long cateId, String mid, int page, int pageSize, List<String> filterSkuId) {
        actionState = ActionState.PRODUCT_INSERT;
        generateNewSearchPvId(page);
        CategoryRequest.getSinkWareInfo(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId, false), cateId, mid, sortType, false, page, pageSize, filterSkuId, pvId, logId, getCategoryQueryConditions(), categoryContainerInterface, firstCateName, secondCateName, thirdCategoryName);
    }

    @Override
    public void onPullThirdCate(int thirdIndex) {
        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
            return;
        }
        actionState = ActionState.PRODUCT_INSERT;
        changeThirdCate(false, thirdIndex, true);
    }

    @Override
    public void onLoadThirdCate(int thirdIndex) {
        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
            return;
        }
        generateNewSearchPvId(1);
        currThirdCateIndex = thirdIndex;
        actionState = ActionState.PRODUCT_LOAD_MORE;
        //为防止三级分类锚点闪动，上拉加载三级分类时暂不切换三级分类选中位置，当滚动到顶部时再切换
        long tempThirdCateId = thirdCategory.get(thirdIndex).getId();
        currCId = tempThirdCateId;
        currMid = thirdCategory.get(thirdIndex).getMid();
        thirdCateId = tempThirdCateId + "";
        thirdMid = currMid;
        thirdCategoryName = thirdCategory.get(thirdIndex).getName();
        CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId, true), NO_EFFECT, tempThirdCateId, currMid, sortType, true, 1, CategoryProductContainer.PAGE_SIZE, pvId, logId, source, getCategoryQueryConditions(), categoryContainerInterface, firstCateName, secondCateName, thirdCategoryName);
    }

    @Override
    public void onScrollToThirdCate(int thirdIndex) {
        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
            return;
        }
        changeThirdCate(false, thirdIndex, false);
    }

    @Override
    public void onChangeSecondCate(int secondIndex) {
        actionState = ActionState.PRODUCT_REFRESH;
        if (secondIndex == currSecondCateIndex) {
            return;
        }
        changeSecondCate(secondIndex, false, secondIndex > currSecondCateIndex);
    }

    @Override
    public void foldFirstCate(boolean fold, boolean scroll) {
    }

    /**
     * 展示商品数据
     * @param secondCateId
     * @param cateId
     * @param categoryWareInfoResult
     * @param pvId
     * @param logId
     * @param fromCache
     */
    private void showCateProducts(long secondCateId, long cateId, CategoryWareInfoResult categoryWareInfoResult, String pvId, String logId, boolean fromCache) {
        showCateProducts(secondCateId, cateId,categoryWareInfoResult, pvId, logId, fromCache, false);
    }

    /**
     * 展示商品数据
     * @param secondCateId
     * @param cateId
     * @param categoryWareInfoResult
     * @param pvId
     * @param logId
     * @param fromCache
     * @param needResetCate3
     */
    private void showCateProducts(long secondCateId, long cateId, CategoryWareInfoResult categoryWareInfoResult, String pvId, String logId, boolean fromCache, boolean needResetCate3) {
        if (isDestroy || secondAdapter == null) {
            return;
        }
        //这里需要判断一下 如果数据不匹配不是同一个二级类目下的数据 那么就不请求了
        if (secondCateId != secondAdapter.getSelectCid()) {
            //继续等
            SFLogCollector.i(TAG, "showCateProducts cid not same");
            return;
        }

        if (needResetCate3) {
            resetCate3AfterFilter(categoryWareInfoResult);
        }

        //商品列表绑定埋点所需数据
        handleWareInfoListMaData(categoryWareInfoResult, pvId, logId);

        //需要补全下一页的场景：
        //1.第一次进入这个二级分类，加载的是当前的第一个三级分类
        //2.需要清空三级分类的情景
        boolean needAutoLoadMore = false;

        if (isChangeSecondCate) {
            needAutoLoadMore = true;
            productContainer.showProductList(getArgumentsBundle(), currThirdCateIndex, cateId, thirdCategory, categoryWareInfoResult, isSelectViewPageCurrent(), fromCache);
            if (!fromCache && (categoryWareInfoResult == null || categoryWareInfoResult.getProductCardVoList() == null || categoryWareInfoResult.getProductCardVoList().size() == 0)) {
                SgmReportBusinessErrorLog.reportFirstPageDataEmpty(categoryId + "", firstCateName, secondCateId + "", secondCateName, thirdCateId, thirdCategoryName);
            }
        } else {
            if (actionState == ActionState.PRODUCT_LOAD_MORE) {
                productContainer.loadMoreProduct(cateId, categoryWareInfoResult);
            } else if (actionState == ActionState.PRODUCT_INSERT) {
                productContainer.insertProduct(cateId, categoryWareInfoResult);
            } else {
                needAutoLoadMore = true;
                productContainer.refreshProductList(getArgumentsBundle(), currThirdCateIndex, cateId, thirdCategory, categoryWareInfoResult, fromCache, isSelectViewPageCurrent(), true);
            }
        }

        actionState = ActionState.NO_ACTION;

        //在需要补足的场景下判断 是否满足一屏（10个）展示
        if (!fromCache && needAutoLoadMore) {
            if (categoryWareInfoResult.getProductCardVoList() == null || categoryWareInfoResult.getProductCardVoList().size() < 10 || categoryWareInfoResult.getSinkPageCount() > 1) {
                onLoadThirdCate(currThirdCateIndex + 1);
            }
        }
        //移动到这个位置
        isChangeSecondCate = false;
    }

    /**
     * 切换二级分类时会过滤掉无货的三级分类，接口请求数据回来后需要重新赋值当前类目数据
     * @param categoryWareInfoResult
     */
    private void resetCate3AfterFilter(CategoryWareInfoResult categoryWareInfoResult) {
        if (categoryWareInfoResult != null) {
            commonSetCurrCate3(categoryWareInfoResult.getHasProductCategories(), secondCategory.get(currSecondCateIndex).getId(), secondMid);
        }
    }

    /**
     * 切换二级分类时三级分类数据赋值收口
     * @param cate3List
     * @param secondCateId
     * @param secondMid
     */
    private void commonSetCurrCate3(List<CategoryBean> cate3List, long secondCateId, String secondMid) {
        if (cate3List != null && cate3List.size() > 0) {
            thirdCategory = cate3List;
            currThirdCateIndex = 0;
            currCId = thirdCategory.get(0).getId();
            thirdCategoryName = thirdCategory.get(0).getName();
            thirdCateId = thirdCategory.get(0).getId() + "";
            thirdMid = thirdCategory.get(0).getMid() + "";
            currMid = thirdCategory.get(0).getMid();
        } else { //当前二级分类没有三级分类
            thirdCategory = null;
            currThirdCateIndex = 0;
            thirdCategoryName = "";
            thirdCateId = "";
            thirdMid = "";
            currCId = secondCateId;
            currMid = secondMid;
        }
    }

    /**
     * 商品列表绑定埋点数据、cid、mid
     *
     * @param categoryWareInfoResult
     */
    private void handleWareInfoListMaData(CategoryWareInfoResult categoryWareInfoResult, String pvId, String logId) {
        if (categoryWareInfoResult != null && categoryWareInfoResult.getProductCardVoList() != null && categoryWareInfoResult.getProductCardVoList().size() > 0) {
            for (int i = 0; i < categoryWareInfoResult.getProductCardVoList().size(); i++) {
                SkuInfoBean wareInfoBean = categoryWareInfoResult.getProductCardVoList().get(i);
                if (wareInfoBean != null) {
                    if (!StringUtil.isNullByString(pvId)) {
                        wareInfoBean.setPvId(pvId);
                        wareInfoBean.setLogId(logId);
                    }
                    wareInfoBean.setPage(categoryWareInfoResult.getPage());
                    wareInfoBean.setPageIndex((i + 1) + "");
                    wareInfoBean.setTotalCount(categoryWareInfoResult.getTotalCount());
                    wareInfoBean.setCateId(currCId);
                    wareInfoBean.setMid(currMid);
                    wareInfoBean.setFirstMid(firstMid);
                    wareInfoBean.setFirstCateName(firstCateName);
                    wareInfoBean.setSecondMid(secondMid);
                    wareInfoBean.setSecondCateName(secondCateName);
                    wareInfoBean.setThirdMid(thirdMid);
                    wareInfoBean.setThirdCateName(thirdCategoryName);
                }
            }
        }
    }

    /**
     * 切换排序，刷新数据
     * 切换排序时需要清空数据列表，按照此排序请求当前二级分类下的第一个三级分类商品数据
     *
     * @param sortType
     */
    @Override
    public void changeSortType(String sortType) {
        actionState = ActionState.PRODUCT_REFRESH;
        this.sortType = sortType;
        changeSortTimeType();
    }

    /**
     * 切换综合、销量、价格、促销和时效筛选的
     */
    private void changeSortTimeType() {
        if (thirdCategory != null && thirdCategory.size() > 0) {
            //有三级分类，切换三级分类，刷新数据
            changeThirdCate(false, productContainer.getCurrentThirdCate(), true);
        } else {
            generateNewSearchPvId(1);
            //没有三级分类，刷新当前二级分类的数据
            CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId, false), DEFAULT_EFFECT, currCId, currMid, sortType, true, 1, CategoryProductContainer.PAGE_SIZE, pvId, logId, source, getCategoryQueryConditions(), categoryContainerInterface, firstCateName, secondCateName, thirdCategoryName);
        }
    }

    @Override
    public void changeTimeFilter(FilterCriteriaVo selectFilter, boolean needRefresh) {

        if (needRefresh) {
            this.selectedFilterCriteriaVo = selectFilter;
            if (categoryBridgeInterface != null) {
                categoryBridgeInterface.setFilterCriteriaVo(selectFilter);
            }
            actionState = ActionState.PRODUCT_REFRESH;
            changeSortTimeType();
        }
    }

    private void getChildCategoryFail(boolean fromCache) {
        if (!fromCache) {
            //如果二级类目数据获取失败，那么整个下面都是空白页
            if (secondCategory == null || secondCategory.size() == 0) {
                secNoDataLayout.setVisibility(View.VISIBLE);
                categoryFieldReporter.secondCategoryEmptyPageExpose(firstMid, firstCateName, null, null, null, null);
                SgmReportBusinessErrorLog.reportSecondCateEmpty(categoryId + "", firstCateName);
            }
        }
    }

    /**
     * 切换三级分类
     *
     * @param userClick       是否用户手动点击切换
     * @param position
     * @param needRequestWare
     */
    @Override
    public void changeThirdCate(boolean userClick, int position, boolean needRequestWare) {
        if (position < 0) {
            return;
        }
        if (thirdCategory == null || thirdCategory.size() <= 0 || position >= thirdCategory.size()) {
            return;
        }
        currThirdCateIndex = position;
        CategoryBean selectThirdCate = thirdCategory.get(position);
        currCId = selectThirdCate.getId();
        currMid = selectThirdCate.getMid();
        thirdCategoryName = selectThirdCate.getName();
        thirdCateId = selectThirdCate.getId() + "";
        thirdMid = selectThirdCate.getMid() + "";
        if (userClick) {
            if (productContainer != null && productContainer.isThirdCateExist(currCId)) {
                //如果列表里已经有了此三级分类，则不需要请求数据，直接滚动到此三级分类节点
                needRequestWare = false;
                productContainer.scrollToExistThirdCate(currCId, currMid);
            } else {
                actionState = ActionState.PRODUCT_REFRESH;
                needRequestWare = true;
            }
        }
        if (needRequestWare) {
            getWareInfoByCid(true, 1, CategoryProductContainer.PAGE_SIZE, actionState != ActionState.PRODUCT_INSERT, false);
        }

        //更新选中的状态
        if (productContainer != null) {
            productContainer.updateThirdCatePos(currThirdCateIndex);
        }

        if (userClick) {
            categoryFieldReporter.thirdCategoryClick(position + 1);
        }
    }


    /**
     * 获取分类下商品
     *
     * @param currPage
     * @param pageSize
     * @param pagination 是否分页
     */
    private void getWareInfoByCid(boolean isShowLoading, int currPage, int pageSize, boolean pagination, boolean isPreLoading) {
        int effect = isShowLoading ? DEFAULT_EFFECT : NO_EFFECT;
        generateNewSearchPvId(currPage);
        CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId, isPreLoading), effect, currCId, currMid, sortType, pagination, currPage, pageSize, pvId, logId, source, getCategoryQueryConditions(), categoryContainerInterface, firstCateName, secondCateName, thirdCategoryName);
        this.isShowLoading = true;
    }

    /**
     * 携带三级类目列表请求商品接口（此接口会把无货的三级类目过滤掉）
     * 只有切换二级类目时并且二级类目下的三级类目大于1才调用这个接口
     *
     * @param isShowLoading
     * @param pageSize
     * @param pagination 是否分页
     * @param cate3List
     */
    private void getWareInfoByCid3s(boolean isShowLoading, int pageSize, boolean pagination, List<CategoryBean> cate3List) {
        int effect = isShowLoading ? DEFAULT_EFFECT : NO_EFFECT;
        generateNewSearchPvId(1);
        CategoryRequest.getWareInfoByCid3s(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId, false, true), cate3List, effect, currCId, currMid, sortType, pagination, 1, pageSize, pvId, logId, source, getCategoryQueryConditions(), categoryContainerInterface, categoryId + "", firstMid, firstCateName, secondCateId, secondMid, secondCateName, thirdCategoryName);
        this.isShowLoading = true;
    }

    /**
     * 筛选条件
     *
     * @return
     */
    @NotNull
    private List<Object> getCategoryQueryConditions() {
        List<Object> queryConditions = new ArrayList<>();
        if (selectedFilterCriteriaVo != null && selectedFilterCriteriaVo.getQueryCondition() != null) {
            queryConditions.add(selectedFilterCriteriaVo.getQueryCondition());
        }
        return queryConditions;
    }

    private boolean isShowLoading = true;

    @Override
    protected void loadData(boolean showLoading, boolean onlyUseCache) {
        isShowLoading = showLoading;
        getChildCategory(showLoading ? DEFAULT_EFFECT : NO_EFFECT, onlyUseCache);

    }

    @Override
    protected int getItemIndex() {
        return currentItemIndex;
    }


    /**
     * 分类下商品请求回调
     */
    public class CateWareInfoListener extends FreshResultCallback<ResponseData<CategoryWareInfoResult>> {

        private final long secId;
        private String pvId;
        private String logId;
        private boolean hasData = false;
        private boolean isPreLoading = false;
        private boolean isFilterCate3 = false;

        public CateWareInfoListener(long secId, String pvId, String logId, boolean isPreLoading) {
            this.secId = secId;
            this.logId = logId;
            this.pvId = pvId;
            this.isPreLoading = isPreLoading;
        }

        public CateWareInfoListener(long secId, String pvId, String logId, boolean isPreLoading, boolean isFilterCate3) {
            this.secId = secId;
            this.logId = logId;
            this.pvId = pvId;
            this.isPreLoading = isPreLoading;
            this.isFilterCate3 = isFilterCate3;
        }

        @Override
        public void onEnd(ResponseData<CategoryWareInfoResult> object, FreshHttpSetting httpSetting) {
            if (isPreLoading && actionState != ActionState.PRODUCT_LOAD_MORE) {
                return;
            }
            try {
                productNoDataLayout.setVisibility(View.GONE);
                productContainer.setVisibility(View.VISIBLE);
                SFLogCollector.i("yyyyyy", "二级分类：" + secondCateName + " 三级分类：" + thirdCategoryName + "====CateWareInfoListener======" + httpSetting.isFromCache() + "=======");
                if (object != null && ResponseData.CODE_SUCC.equals(object.getCode()) && object.getData() != null) {
                    CategoryWareInfoResult wareInfoResult = object.getData();
                    if (isFilterCate3 && (((wareInfoResult.getHasProductCategories() == null || wareInfoResult.getHasProductCategories().isEmpty())
                            && (wareInfoResult.getNoProductCategories() == null || wareInfoResult.getNoProductCategories().isEmpty()))
                            || (wareInfoResult.getHasProductCategories() != null && !wareInfoResult.getHasProductCategories().isEmpty()
                            && (wareInfoResult.getProductCardVoList() == null || wareInfoResult.getProductCardVoList().isEmpty())))) {
                        //过滤三级类目后没返类目数据，或者返回了有货类目但第一页商品列表是空的，表示接口异常，此时用老接口重新请求
                        getWareInfoByCid(isShowLoading, 1, CategoryProductContainer.PAGE_SIZE, true, false);
                        return;
                    }
                    hasData = true;
                    // 防止自动拆箱导致空指针
                    showCateProducts(secId, wareInfoResult.getCid() == null ? currCId : wareInfoResult.getCid(), wareInfoResult, pvId, logId, httpSetting.isFromCache(), isFilterCate3);
                } else {
                    if (isFilterCate3) {
                        //过滤三级类目后没返数据，用老接口重新请求
                        getWareInfoByCid(isShowLoading, 1, CategoryProductContainer.PAGE_SIZE, true, false);
                        return;
                    }
                    hasData = false;
                    showCateProducts(secId, currCId, new CategoryWareInfoResult(), pvId, logId, true);
                }
            } catch (Exception e) {
                if (actionState == ActionState.PRODUCT_REFRESH) {
                    showProductNoDataLayout();
                }
                e.printStackTrace();
                JdCrashReport.postCaughtException(e);
            }
        }

        @Override
        public void onError(FreshHttpException error) {
            try {
                if (isFilterCate3) {
                    //过滤三级类目接口请求报错，用老接口重新请求
                    getWareInfoByCid(isShowLoading, 1, CategoryProductContainer.PAGE_SIZE, true, false);
                    return;
                }
                if (actionState == ActionState.PRODUCT_REFRESH) {
                    if (!hasData && error != null && error.getHttpSetting() != null && error.getHttpSetting().getCacheConfig() != null) {
                        CategoryRequest.getCacheData(mActivity, error.getHttpSetting());
                    } else {
                        showProductNoDataLayout();
                    }
                } else {
                    if (productContainer != null) {
                        productContainer.finishRefresh();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                JdCrashReport.postCaughtException(e);
            }
        }
    }


    private void showProductNoDataLayout() {
        try {
            productNoDataLayout.setVisibility(View.VISIBLE);
            categoryFieldReporter.secondCategoryEmptyPageExpose(categoryId + "", firstCateName, secondCateId, secondCateName, null, null);
            SgmReportBusinessErrorLog.reportProductNoDataLayout(categoryId + "", firstCateName, secondCateId, secondCateName, thirdCateId, thirdCategoryName);
            productContainer.setVisibility(View.GONE);
        } catch (Exception e) {

        }

    }

    /**
     * 根据一级分类 请求二级分类 以及三类分类数据结果callBack
     */
    public class NewChildCategoryListener extends FreshResultCallback<ResponseData<ChildCategoryResult>> {

        private final long cid;
        private boolean hasData = false;

        public NewChildCategoryListener(long categoryId) {
            this.cid = categoryId;
        }

        @Override
        public void onEnd(ResponseData<ChildCategoryResult> o, FreshHttpSetting httpSetting) {
            //首次判断id是否相同
            if (cid != 0 && cid != categoryId) {
                // 原有逻辑 理论不会出现这种异常 继续保留
                int itemCount = 0;
                if (secondAdapter != null) {
                    itemCount = secondAdapter.getItemCount();
                }
                JdCrashReport.postCaughtException(new Exception("new cate cid not same:" + cid + " c:" + categoryId + " 是否有数据:" + itemCount));
                return;
            }
            if (o != null && ResponseData.CODE_SUCC.equals(o.getCode()) && o.getData() != null) {
                hasData = true;
                // 获取到正常的数据 进行数据解析
                parseChildCategoryData(o.getData(), httpSetting.isFromCache());
            } else {
                hasData = false;
                getChildCategoryFail(false);
            }
        }

        @Override
        public void onError(FreshHttpException httpError) {
            //首次判断id是否相同
            if (cid != categoryId) {
                SFLogCollector.i(TAG, "NewChildCategoryListener cid not same err");
                int itemCount = 0;
                if (secondAdapter != null) {
                    itemCount = secondAdapter.getItemCount();
                }
                JdCrashReport.postCaughtException(new Exception("new cate cid not same:" + cid + " c:" + categoryId + " 是否有数据:" + itemCount));
                return;
            }
            if (!hasData && httpError != null && httpError.getHttpSetting().getCacheConfig() != null) {
                secondCategory = null;
                CategoryRequest.getCacheData(mActivity, httpError.getHttpSetting());
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (productContainer != null) {
            productContainer.onPause();
        }
    }


    @Override
    public void onDestroyView() {
        if (productContainer != null) {
            productContainer.onDestroy();
        }
        rootView = null;
        if (secondCateListView!= null) {
            secondCateListView.setAdapter(null);
        }
        if (secondAdapter != null) {
            secondAdapter.release();
            secondAdapter = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void reportExposePoint() {
        if (productContainer != null && isSelectViewPageCurrent()) {
            productContainer.reportExposePoint();
        }
        handSecondCateExpose();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (categoryBridgeInterface != null) {
            categoryBridgeInterface = null;
        }
        if (categoryContainerInterface != null) {
            categoryContainerInterface = null;
        }
    }

    /**
     * 获取参数集合
     *
     * @return
     */
    private Bundle getArgumentsBundle() {

        Bundle bundle = new Bundle();
        bundle.putInt("currSecondCateIndex", currSecondCateIndex);
        bundle.putString("nextSecondCateName", nextSecondCateName);
        bundle.putInt("totalSecondCateSize", secondCategory == null ? 0 : secondCategory.size());
        bundle.putString("sortType", sortType);
//        bundle.putString("firstCateId", firstCateId);
        bundle.putString("firstMid", firstMid);
        bundle.putString("firstCateName", firstCateName);
//        bundle.putString("secondCateId", secondCateId);
        bundle.putString("secondMid", secondMid);
        bundle.putString("secondCateName", secondCateName);
//        bundle.putString("thirdCateId", thirdCateId);
        bundle.putString("thirdMid", thirdMid);
        bundle.putString("thirdCategoryName", thirdCategoryName);

        CategoryBean curSecondCate = null;
        if (secondCategory != null) {
            curSecondCate = secondCategory.get(currSecondCateIndex);
        }
        ArrayList<BannerBean> banners = null;
        if (curSecondCate != null) {
            banners = (ArrayList<BannerBean>) curSecondCate.getImages();
        }
        bundle.putSerializable("banners", banners);
        bundle.putSerializable("firstFilterCriteria", firstFilterCriteria);
        bundle.putSerializable("selectTimeFiler", selectedFilterCriteriaVo);
        productContainer.setFilterCriteriaVo(selectedFilterCriteriaVo);
        return bundle;
    }
}