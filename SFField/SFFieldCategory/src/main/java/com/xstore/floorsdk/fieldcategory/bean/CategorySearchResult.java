package com.xstore.floorsdk.fieldcategory.bean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/06
 */
public class CategorySearchResult implements Serializable {

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * msg
     */
    private String msg;
    /**
     * 搜索结果数据
     */
    private CategoryKeyWordBean getDefaultKeyWord;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public CategoryKeyWordBean getGetDefaultKeyWord() {
        return getDefaultKeyWord;
    }

    public void setGetDefaultKeyWord(CategoryKeyWordBean getDefaultKeyWord) {
        this.getDefaultKeyWord = getDefaultKeyWord;
    }

    public class CategoryKeyWordBean implements Serializable {
        private List<CategoryKeyWordItemBean> keyWordItemList;

        public List<CategoryKeyWordItemBean> getKeyWordItemList() {
            return keyWordItemList;
        }

        public void setKeyWordItemList(List<CategoryKeyWordItemBean> keyWordItemList) {
            this.keyWordItemList = keyWordItemList;
        }
    }
}
