package com.xstore.floorsdk.fieldcategory.bean;

import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 分类下商品请求结果实体类
 *
 * <AUTHOR>
 * @date 2022/11/07
 */
public class CategoryWareInfoResult implements Serializable {
    /**
     * 是否成功--只有商品列表接口有这个字段，二级分类没有这个字段
     */
    private boolean success;
    /**
     * 当前类目id--只有商品列表接口有这个字段，二级分类没有这个字段
     */
    private Long cid;
    /**
     * 中台分类ID--只有商品列表接口有这个字段，二级分类没有这个字段
     */
    private String mid;
    /**
     * 无货商品总个数
     */
    private int sinkCount;
    /**
     * 当页无货商品个数
     */
    private int sinkPageCount;
    /**
     * 总数量
     */
    private int totalCount;
    /**
     * 页码
     */
    private int page;
    /**
     * 总页数
     */
    private int totalPage;
    /**
     * 分类页商品数据(old)
     */
    private List<SkuInfoVoBean> skuInfoVoList;
    /**
     * 分类页商品数据
     */
    private List<SkuInfoBean> productCardVoList;

    private List<CategoryBean> hasProductCategories;

    private List<CategoryBean> noProductCategories;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public int getSinkCount() {
        return sinkCount;
    }

    public void setSinkCount(int sinkCount) {
        this.sinkCount = sinkCount;
    }

    public int getSinkPageCount() {
        return sinkPageCount;
    }

    public void setSinkPageCount(int sinkPageCount) {
        this.sinkPageCount = sinkPageCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public List<SkuInfoVoBean> getSkuInfoVoList() {
        return skuInfoVoList;
    }

    public void setSkuInfoVoList(List<SkuInfoVoBean> skuInfoVoList) {
        this.skuInfoVoList = skuInfoVoList;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }

    public List<CategoryBean> getHasProductCategories() {
        return hasProductCategories;
    }

    public void setHasProductCategories(List<CategoryBean> hasProductCategories) {
        this.hasProductCategories = hasProductCategories;
    }

    public List<CategoryBean> getNoProductCategories() {
        return noProductCategories;
    }

    public void setNoProductCategories(List<CategoryBean> noProductCategories) {
        this.noProductCategories = noProductCategories;
    }

    /****接口返回的埋点所用字段****/
    private String hcCid3;
    private String mtest;
    private String isActiveFilt;
    private String firfilter;
    private String secfilter;
    private String source;

    public String getHcCid3() {
        return hcCid3;
    }

    public void setHcCid3(String hcCid3) {
        this.hcCid3 = hcCid3;
    }

    public String getMtest() {
        return mtest;
    }

    public void setMtest(String mtest) {
        this.mtest = mtest;
    }

    public String getIsActiveFilt() {
        return isActiveFilt;
    }

    public void setIsActiveFilt(String isActiveFilt) {
        this.isActiveFilt = isActiveFilt;
    }

    public String getFirfilter() {
        return firfilter;
    }

    public void setFirfilter(String firfilter) {
        this.firfilter = firfilter;
    }

    public String getSecfilter() {
        return secfilter;
    }

    public void setSecfilter(String secfilter) {
        this.secfilter = secfilter;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
    /****埋点所用字段****/
}
