package com.xstore.floorsdk.fieldcategory.interfaces;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;

import java.util.List;

/**
 * 商品列表刷新/加载更多监听回调
 * <AUTHOR>
 * @date 2022/11/03
 */
public interface OnProductRefreshOrLoadMoreListener {

    /**
     * 当前分类加载更多商品
     *
     * @param currPage
     * @param pageSize
     */
    void onProductLoadMore(int currPage, int pageSize);

    /**
     * 获取某分类下所有无货商品，不分页
     *
     * @param cateId
     * @param page
     * @param pageSize
     * @param filterSkuId
     */
    void onSinkProductLoadMore(long cateId,String mid, int page, int pageSize, List<String> filterSkuId);

    /**
     * 下拉加载上一个三级分类，此时不分页，请求此分类下全部商品插入到当前商品列表头部
     *
     * @param thirdIndex
     */
    void onPullThirdCate(int thirdIndex);

    /**
     * 上拉加载下一个三级分类，分页正常请求商品数据，拼接到当前商品列表尾部
     *
     * @param thirdIndex
     */
    void onLoadThirdCate(int thirdIndex);

    /**
     * 列表滚动切换三级分类，此时只切换分类，不请求数据
     *
     * @param thirdIndex
     */
    void onScrollToThirdCate(int thirdIndex);

    /**
     * 切换二级分类
     *
     * @param secondIndex
     */
    void onChangeSecondCate(int secondIndex);

    /**
     * 是否折叠一级分类
     *
     * @param fold
     * @param scroll 是否是滑动触发
     */
    void foldFirstCate(boolean fold, boolean scroll);

    /**
     * 切换排序方式
     *
     * @param sortType
     */
    void changeSortType(String sortType);

    /**
     * 切换三级分类
     *
     * @param userClick
     * @param position
     * @param needRequestWare
     */
    void changeThirdCate(boolean userClick, int position, boolean needRequestWare);
    /**
     * 切换时效筛选
     * @param selectFilter
     * @param needRefresh 是否需要接口刷新
     */
    void changeTimeFilter(FilterCriteriaVo selectFilter,boolean needRefresh);
}
