package com.xstore.floorsdk.fieldcategory.widget;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.annotation.Nullable;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.CategoryConstant;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.CategoryKeyWordItemBean;
import com.xstore.floorsdk.fieldcategory.bean.CategorySearchResult;
import com.xstore.floorsdk.fieldcategory.ma.FirstCategoryReporter;
import com.xstore.floorsdk.fieldcategory.request.CategoryRequest;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.FixViewFlipper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.service.sfuikit.imagespan.VerticalCenterSpan;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.List;

import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.getWidthByDesignValueFitFold;
import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.setMarginDpi;
import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.setPaddingDpi;
import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.setWidthAndHeightDpiFitFold;

/**
 * 分类顶部搜索框
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
public class CategorySearchView extends LinearLayout {
    /**
     * 存储当前轮播的搜索热词(别名 > 暗纹词)
     */
    public static final String HOT_WORDS_INFO = "HOTWORDSINFO";
    /**
     * 存储当前轮播的搜索热词(别名 > 暗纹词)
     */
    public static final String HOT_WORDS_INFO_ICON = "HOTWORDSINFOICON";
    /**
     * 存储当前的搜索词（暗纹词）
     */
    public static final String CUR_SEARCH_WORD = "CUR_SEARCH_WORD";
    /**
     * 存储当前搜索词 的跳转
     */
    public static final String CUR_SEARCH_JUMP = "CUR_SEARCH_JUMP";

    private View llTopBar;
    /**
     * 返回按钮
     */
    private ImageView ivBack;
    /**
     * 搜索框
     */
    private RelativeLayout rlSearch;
    /**
     * 搜索图标
     */
    private ImageView ivSearch;
    /**
     * 搜索暗纹词
     */
    private TextView tvHotWords;
    /**
     * 搜索轮播词
     */
    private FixViewFlipper vfSearch;

    /**
     * 当前正在展示的搜索数据
     */
    private CategorySearchResult.CategoryKeyWordBean curSearchBean = null;

    /**
     * 最新的搜索数据
     */
    private CategorySearchResult.CategoryKeyWordBean lastSearchBean = null;

    private FirstCategoryReporter categoryFieldReporter;

    public CategorySearchView(Context context) {
        super(context);
        initView();
    }

    public CategorySearchView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CategorySearchView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }


    /**
     * 初始化布局
     */
    private void initView() {
        View root = LayoutInflater.from(getContext()).inflate(R.layout.sf_field_category_top_search, this, true);

        llTopBar = root.findViewById(R.id.ll_top_bar);
        ivBack = root.findViewById(R.id.iv_back);
        rlSearch = root.findViewById(R.id.search_rl);
        ivSearch = root.findViewById(R.id.iv_my_order_search);
        tvHotWords = root.findViewById(R.id.tv_hot_words);
        vfSearch = root.findViewById(R.id.vf_hot_search_word);

        setWidthAndHeightDpiFitFold(llTopBar, ViewGroup.LayoutParams.MATCH_PARENT, 44, 375);
        setWidthAndHeightDpiFitFold(ivBack, 44, 44, 375);
        setPaddingDpi(ivBack, 5, 375);
        setWidthAndHeightDpiFitFold(rlSearch, ViewGroup.LayoutParams.MATCH_PARENT, 32, 375);
        setMarginDpi(rlSearch, 0, 0, 14, 0, 375);
        setWidthAndHeightDpiFitFold(ivSearch, 18, 18, 375);
        setMarginDpi(ivSearch, 13, 0, 0, 0, 375);
        tvHotWords.setTextSize(TypedValue.COMPLEX_UNIT_PX, getWidthByDesignValueFitFold(getContext(), 14, 375));
        setMarginDpi(tvHotWords, 8, 0, 5, 0, 375);

        ivBack.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getContext() instanceof Activity) {
                    ((Activity) getContext()).finish();
                }
            }
        });

        rlSearch.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NoDoubleClickUtils.isDoubleClick()) {
                    return;
                }
                startSearchActivity();
            }
        });
    }

    /**
     * 设置搜索样式
     *
     * @param fromType
     */
    public void setSearchStyle(int fromType, FirstCategoryReporter categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
        if (fromType == CategoryConstant.Value.SOURCE_CATEGORY_PAGE) {
            ivBack.setVisibility(View.GONE);
            setMarginDpi(rlSearch, 14, 0, 14, 0, 375);
        }

        CategoryRequest.getDefaultKeyWord(getContext(), new BaseFreshResultCallback<String, ResponseData<CategorySearchResult>>() {
            @Override
            public ResponseData<CategorySearchResult> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<CategorySearchResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<CategorySearchResult>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<CategorySearchResult> object, FreshHttpSetting httpSetting) {
                if (object != null && object.getData() != null && object.getData().getGetDefaultKeyWord() != null) {
                    lastSearchBean = object.getData().getGetDefaultKeyWord();
                } else {
                    lastSearchBean = null;
                }
                setSearchData();
            }

            @Override
            public void onError(FreshHttpException error) {
                lastSearchBean = null;
                setSearchData();
            }
        });
    }

    public void onResume(boolean hidden) {
        hiddenFlipping(hidden);
    }

    private void hiddenFlipping(boolean hidden) {
        if (hidden) {
            if (vfSearch != null && vfSearch.isFlipping()) {
                vfSearch.stopFlipping();
            }
        } else {
            if (vfSearch != null && vfSearch.getChildCount() > 1 && !vfSearch.isFlipping()) {
                vfSearch.startFlipping();
            }
        }
    }

    public void onPause() {
        hiddenFlipping(true);
    }

    public void onHiddenChanged(boolean hidden) {
        hiddenFlipping(hidden);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        hiddenFlipping(true);
    }

    /**
     * 设置搜索数据
     */
    private void setSearchData() {
        if (vfSearch == null || tvHotWords == null) {
            return;
        }
        if (vfSearch.isFlipping()) {
            vfSearch.stopFlipping();
        }
        if (vfSearch.getChildCount() > 0) {
            vfSearch.removeAllViews();
        }

        ArrayList<CategoryKeyWordItemBean> list = new ArrayList<>();
        if (lastSearchBean != null && lastSearchBean.getKeyWordItemList() != null && lastSearchBean.getKeyWordItemList().size() > 1) {
            vfSearch.setVisibility(View.VISIBLE);
            tvHotWords.setVisibility(View.GONE);

            if (curSearchBean == lastSearchBean) {
                //搜素数据没有变化，沿用之前的数据 这个时候getTag是能够拿到数据的
                list = (ArrayList<CategoryKeyWordItemBean>) vfSearch.getTag();
            } else {
                for (CategoryKeyWordItemBean item : lastSearchBean.getKeyWordItemList()) {
                    list.add(new CategoryKeyWordItemBean(item.getKeyword(), item.getIcon(), item.getUrl(), item.getSearchWord(), false));
                }
                vfSearch.setTag(list);
                curSearchBean = lastSearchBean;
            }
        } else {
            String hotword = PreferenceUtil.getString(HOT_WORDS_INFO + TenantIdUtils.getStoreId(), "");
            //如果没有轮播
            vfSearch.setTag(null);
            if (StringUtil.isNullByString(hotword)) {
                String bizName = !StringUtil.isNullByString(FloorInit.getFloorConfig().getBizName()) ? FloorInit.getFloorConfig().getBizName() : "";
                tvHotWords.setHint(getResources().getString(R.string.sf_field_category_edittext_hint, bizName));
            } else {
                tvHotWords.setHint(hotword);
            }
            vfSearch.setVisibility(View.GONE);
            tvHotWords.setVisibility(View.VISIBLE);
            curSearchBean = lastSearchBean;
            return;
        }

        vfSearch.setCallback(new FixViewFlipper.Callback() {
            @Override
            public void onSetDisplayedChild(int which) {
                //  这个地方是从1开始回调的  需要手动处理下0
                try {
                    //纠正  onSetDisplayedChild 传入的which 为上一个which+1 所以可能越界，需要当其越界时重置为0
                    if (which >= vfSearch.getChildCount()) {
                        which = 0;
                    } else if (which < 0) {
                        which = vfSearch.getChildCount() - 1;
                    }

                    List<CategoryKeyWordItemBean> keywordList = (List<CategoryKeyWordItemBean>) vfSearch.getTag();
                    if (keywordList.get(which).isExposure()) {
                        return;
                    }
                    keywordList.get(which).setExposure(true);
                    if (which >= vfSearch.getChildCount()) {
                        which = 0;
                    } else if (which < 0) {
                        which = vfSearch.getChildCount() - 1;
                    }
                    if (categoryFieldReporter != null) {
                        categoryFieldReporter.hotWordsExpose(keywordList.get(which));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        });

        //手动触发第0个回调
        if (vfSearch.getCallback() != null) {
            vfSearch.getCallback().onSetDisplayedChild(0);
        }

        LayoutInflater inflater = LayoutInflater.from(getContext());

        if (list != null && list.size() > 0) {
            for (CategoryKeyWordItemBean item : list) {
                if (StringUtil.isNullByString(item.getDisplayWord())) {
                    continue;
                }
                addSearchWord(item, vfSearch, inflater);
            }
        }
        try {
            vfSearch.setInAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.sf_field_category_push_up_in));
            vfSearch.setOutAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.sf_field_category_push_up_out));
            vfSearch.setFlipInterval(3000);
            if (vfSearch.getChildCount() > 1) {
                vfSearch.startFlipping();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加搜索轮播词的子View
     *
     * @param item
     * @param vfSearch
     * @param inflater
     */
    private void addSearchWord(CategoryKeyWordItemBean item, ViewFlipper vfSearch, LayoutInflater inflater) {
        View searchItem = inflater.inflate(R.layout.sf_field_category_search_item, null);
        TextView textView = searchItem.findViewById(R.id.tv_hot_words);
        //进行图文混排
        final  String TEXT_SPACE = "#icon";
        String textStr = item.getDisplayWord();
        String iconUrl = item.getIcon();
        //图片大小
        final int size = ScreenUtils.dip2px(getContext(),20);
        //不含icon占位词，直接展示
        if(!textStr.contains(TEXT_SPACE)){
            textView.setText(textStr);
        } else{
            int start = textStr.indexOf(TEXT_SPACE);
            //icon的url为空，去除所有占位词，展示
            //先展示文字，再加载展示icon
            // 多个占位词，只保留第一个
            String newTextStr = textStr.replaceAll(TEXT_SPACE,"");
            textView.setText(newTextStr);
            if(!TextUtils.isEmpty(iconUrl) && start != -1){
                String beforeStr = newTextStr.substring(0,start)+" "+newTextStr.substring(start);
                SpannableStringBuilder ssb = new SpannableStringBuilder(beforeStr);

                ImageloadUtils.loadImage(getContext(), iconUrl, new ImageloadUtils.LoadListener() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        post(new Runnable() {
                            @Override
                            public void run() {
                                Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, size, size, true);
                                Drawable resource = new BitmapDrawable(getResources(),scaledBitmap);
                                int width = resource.getIntrinsicWidth();
                                int height = resource.getIntrinsicHeight();
                                resource.setBounds(0, 0, Math.max(width, 0), Math.max(height, 0));
                                ssb.setSpan(new VerticalCenterSpan(resource,ScreenUtils.dip2px(getContext(),2)),start,start+1, 0);
                                textView.setText(ssb);
                            }
                        });
                    }

                    @Override
                    public void onFailed() {

                    }
                });
            }
        }

        searchItem.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NoDoubleClickUtils.isDoubleClick()) {
                    return;
                }
                //这里记一下当前点击的索索关键词
                saveSearchWord(item);

                startSearchActivity();
            }
        });
        searchItem.setTag(item);
        vfSearch.addView(searchItem);
    }

    private void startSearchActivity() {
        if (categoryFieldReporter != null) {
            categoryFieldReporter.clickSearchRow();
        }
        if (getContext() instanceof Activity) {
            FloorJumpManager.getInstance().startSearchActivity((Activity) getContext());
        }
    }

    /**
     * @param item 存储搜索词
     */
    public void saveSearchWord(CategoryKeyWordItemBean item) {
        if (item == null) {
            PreferenceUtil.saveString(HOT_WORDS_INFO + TenantIdUtils.getStoreId(), "");
            PreferenceUtil.saveString(HOT_WORDS_INFO_ICON + TenantIdUtils.getStoreId(), "");
            PreferenceUtil.saveString(CUR_SEARCH_WORD + TenantIdUtils.getStoreId(), "");
            PreferenceUtil.saveString(CUR_SEARCH_JUMP + TenantIdUtils.getStoreId(), "");
            return;
        }
        if (!TextUtils.isEmpty(item.getDisplayWord())) {
            PreferenceUtil.saveString(HOT_WORDS_INFO + TenantIdUtils.getStoreId(), item.getDisplayWord());
            PreferenceUtil.saveString(HOT_WORDS_INFO_ICON + TenantIdUtils.getStoreId(), TextUtils.isEmpty(item.getIcon())?"":item.getIcon());
        } else {
            PreferenceUtil.saveString(HOT_WORDS_INFO + TenantIdUtils.getStoreId(), "");
            PreferenceUtil.saveString(HOT_WORDS_INFO_ICON + TenantIdUtils.getStoreId(), "");

        }
        PreferenceUtil.saveString(CUR_SEARCH_WORD + TenantIdUtils.getStoreId(), item.getSearchWord());
        PreferenceUtil.saveString(CUR_SEARCH_JUMP + TenantIdUtils.getStoreId(), item.getUrl());
    }
}
