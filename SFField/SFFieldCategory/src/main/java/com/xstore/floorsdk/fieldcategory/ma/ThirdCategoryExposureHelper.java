package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.SecondCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.ThirdCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class ThirdCategoryExposureHelper {

    private Map<String, Boolean> exposuredCategoryMap = new HashMap<>();

    private SecondCategoryFieldReporter categoryFieldReporter;

    public ThirdCategoryExposureHelper() {
        if (exposuredCategoryMap == null) {
            exposuredCategoryMap = new HashMap<>();
        }
    }

    public void setCategoryFieldReporter(SecondCategoryFieldReporter categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
    }

    public void exposureByHand(RecyclerView recyclerView, int newState) {
        if (recyclerView == null) {
            return;
        }
        if (recyclerView.getAdapter() instanceof ThirdCategoryAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            ThirdCategoryAdapter thirdCategoryAdapter = (ThirdCategoryAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int first = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    CategoryBean itemData = thirdCategoryAdapter.getItem(i);
                    if (itemData == null) {
                        continue;
                    }
                    if (StringUtil.isNullByString(itemData.getMid())) {
                        continue;
                    }
                    if (exposuredCategoryMap.containsKey(itemData.getMid())
                            && exposuredCategoryMap.get(itemData.getMid())) {
                        continue;
                    }
                    exposuredCategoryMap.put(itemData.getMid(), true);
                    if (categoryFieldReporter != null) {
                        categoryFieldReporter.thirdCategoryExpose(itemData,i + 1);
                    }
                }
            }
        }
    }
    public void thirdCategoryEmptyPageExpose(String firstCategoryMid, String firstCategoryName, String secondCategoryId,String secondCategoryName,String thirdCategoryId,String thirdCategoryName) {
        if (categoryFieldReporter != null) {
            categoryFieldReporter.secondCategoryEmptyPageExpose(firstCategoryMid,firstCategoryName ,secondCategoryId,secondCategoryName,thirdCategoryId,thirdCategoryName);
        }

    }
}
