package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;

/**
 * 兼容RecyclerView的原生Bug
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CategoryLinearLayoutManager extends LinearLayoutManager {

    public CategoryLinearLayoutManager(Context context) {
        super(context);
    }

    public CategoryLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public CategoryLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (Exception e) {
            detachAndScrapAttachedViews(recycler);
            JdCrashReport.postCaughtException(e,"categoryLinearLayoutManager");
        }
    }
}
