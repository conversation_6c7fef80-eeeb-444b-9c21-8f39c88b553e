package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.xstore.sevenfresh.service.sflog.SFLogCollector;

/**
 * <AUTHOR>
 * @date 2022/11/11
 */
public class CategoryCoordinatorLayout extends CoordinatorLayout {
    private float lastY;
    private OnCoordinatorScrollListener listener;

    public CategoryCoordinatorLayout(@NonNull Context context) {
        super(context);
    }

    public CategoryCoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CategoryCoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                lastY = ev.getY();
                break;
            case MotionEvent.ACTION_MOVE:
//                SFLogCollector.d("yyyyyyyyyyyy", "===========" + (ev.getY() - lastY));
                if (ev.getY() - lastY <= -10) {
                    //up
                    if (listener != null) {
                        listener.scrollUp();
                    }
                } else if (ev.getY() - lastY >= 10) {
                    //down
                    if (listener != null) {
                        listener.scrollDown();
                    }
                }
                lastY = ev.getY();
                break;
            default:
                break;
        }

        return super.dispatchTouchEvent(ev);
    }

    public void setOnCoordinatorScrollListener(OnCoordinatorScrollListener l) {
        listener = l;
    }

    public interface OnCoordinatorScrollListener {
        void scrollUp();

        void scrollDown();
    }
}
