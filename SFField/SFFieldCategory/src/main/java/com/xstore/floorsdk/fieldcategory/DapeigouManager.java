package com.xstore.floorsdk.fieldcategory;

import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.NO_EFFECT;

import android.content.Context;

import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

import java.util.HashMap;
import java.util.Map;

public class DapeigouManager {

    private int mStep = 0;
    //分类页加车次数
    private int addNum = 0;
    private static DapeigouManager dapeigouManager;
    private Map<String, Boolean> skuMaps = new HashMap<String, Boolean>();

    private DapeigouStepInterface dapeigouStepInterface;

    public static DapeigouManager getInstance() {
        if (dapeigouManager == null) {
            dapeigouManager = new DapeigouManager();
        }
        return dapeigouManager;
    }

    private DapeigouManager() {

    }

    public void setDapeigouStepInterface(DapeigouStepInterface dapeigouStepInterface) {
        this.dapeigouStepInterface = dapeigouStepInterface;
    }

    //    // 设置步长
//    public void setStep(int step) {
//        this.mStep = step;
//    }

    public void postDapeigouData(Context context, String skuId, BaseFreshResultCallback callback) {
        if (dapeigouStepInterface != null) {
            mStep = dapeigouStepInterface.getStep();
        }
        if (mStep == 0) {
            return;
        }
        if (skuMaps == null) {
            skuMaps = new HashMap<>();
        }
        if (skuMaps.containsKey(skuId)) {
            return;
        }
        skuMaps.put(skuId, true);
        addNum++;
        if ((addNum - 1) % mStep != 0) {
            return;
        }
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setEffect(NO_EFFECT);
        httpSetting.setFunctionId("omnitech_category_getRecommendSkuList");
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setResultCallback(callback);
        httpSetting.putJsonParam("skuId", skuId);
        httpSetting.putJsonParam("sceneType", 0); //场景类型 0-类目搭配购
        httpSetting.putJsonParam("pageId", "0013");
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }

    public interface DapeigouStepInterface {
        int getStep();
    }
}
