package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.xstore.floorsdk.fieldcategory.CategoryConstant;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.ArrayList;

/**
 * 时效筛选、综合、销量、价格、促销的筛选条
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
public class CategorySortTypeFilter extends LinearLayout implements View.OnClickListener {
    private View rootView;
    /**
     * 时效筛选
     */
    private LinearLayout llFilter;
    private TextView tvFilter;
    private ImageView ivFilter;
    private ImageView ivFilterFold;
    /**
     * 综合
     */
    private TextView tvAll;
    /**
     * 销量
     */
    private TextView tvSoldCount;
    /**
     * 价格
     */
    private LinearLayout llPrice;
    private TextView tvPrice;
    private ImageView imgPrice;
    /**
     * 促销
     */
    private TextView tvPromotion;
    /**
     * 排序类型
     */
    private String sortType;
    private SortTypeCallback sortTypeCallback;

    /**
     * 自定义筛选条件
     */
    private ArrayList<FilterCriteriaVo> firstFilterCriteria = new ArrayList<>();
    /**
     * 选中的筛选条件
     */
    private FilterCriteriaVo mSelectedFilterCriteria;


    public CategorySortTypeFilter(Context context) {
        super(context);
        initView(context);
    }

    public CategorySortTypeFilter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public CategorySortTypeFilter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化view
     *
     * @param context
     */
    private void initView(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_sort_type_filter, this, true);

        llFilter = findViewById(R.id.filter_layout);
        tvFilter = findViewById(R.id.tv_filter);
        ivFilter = findViewById(R.id.iv_filter);
        ivFilterFold = findViewById(R.id.iv_fold);
        llFilter.setOnClickListener(this);

        tvAll = findViewById(R.id.tv_all);
        tvAll.setOnClickListener(this);

        tvSoldCount = findViewById(R.id.tv_sold_count);
        tvSoldCount.setOnClickListener(this);

        llPrice = findViewById(R.id.price_layout);
        llPrice.setOnClickListener(this);
        tvPrice = findViewById(R.id.tv_price);
        tvPrice.setOnClickListener(this);
        imgPrice = findViewById(R.id.price_img);

        tvPromotion = findViewById(R.id.tv_promotion);
        tvPromotion.setOnClickListener(this);

        setSearchQueryTextColor(tvAll);
    }

    /**
     * 设置数据
     *
     * @param sortType
     */
    public void setSortTypeData(String sortType, ArrayList<FilterCriteriaVo> filterCriteriaVos, FilterCriteriaVo selectedFilter) {
        this.sortType = sortType;
        if (CategoryConstant.Value.SORT_DEFAULT.equals(sortType)) {
            setSearchQueryTextColor(tvAll);
        } else if (CategoryConstant.Value.SORT_PRICE_ASC.equals(sortType)) {
            imgPrice.setImageResource(R.drawable.sf_theme_image_search_arrow_up);
            setSearchQueryTextColor(tvPrice);
        } else if (CategoryConstant.Value.SORT_PRICE_DESC.equals(sortType)) {
            imgPrice.setImageResource(R.drawable.sf_theme_image_search_arrow_down);
            setSearchQueryTextColor(tvPrice);
        } else if (CategoryConstant.Value.SORT_PROMOTION_DESC.equals(sortType)) {
            setSearchQueryTextColor(tvPromotion);
        } else if (CategoryConstant.Value.SORT_AMOUNT_DESC.equals(sortType)) {
            setSearchQueryTextColor(tvSoldCount);
        }
        if (this.firstFilterCriteria == null) {
            this.firstFilterCriteria = new ArrayList<>(); 
        }
        this.firstFilterCriteria.clear();
        if (filterCriteriaVos!=null) {
            this.firstFilterCriteria.addAll(filterCriteriaVos);
        }
        this.mSelectedFilterCriteria = selectedFilter;
        setTimeFilterValid(true);
    }

    // 控制刷新时效
    public void setFilterCriteriaVo(FilterCriteriaVo selectedFilter){
        this.mSelectedFilterCriteria = selectedFilter;
        setTimeFilterValid(true);
    }

    /**
     * 设置时效筛选是否有效
     * @param filterValid true 有效 false 无效
     */
    private void setTimeFilterValid(boolean filterValid) {
        if (firstFilterCriteria != null && firstFilterCriteria.size() > 0) {
            if (filterValid) {
                llFilter.setEnabled(true);
            } else {
                llFilter.setEnabled(false);
                FilterCriteriaVo filterAll = null;
                for (FilterCriteriaVo bean : firstFilterCriteria) {
                    if (bean != null && "all".equals(bean.getId())) {
                        filterAll = bean;
                        break;
                    }
                }
                if (filterAll != null) {
                    mSelectedFilterCriteria = filterAll;
                } else {
                    mSelectedFilterCriteria = firstFilterCriteria.get(0);
                }
            }
            setSelectedTimeFilter(filterValid);
        } else {
            llFilter.setVisibility(GONE);
        }
    }

    /**
     * 设置选中的效果
     * @param filterValid true 有效 false 无效
     */
    private void setSelectedTimeFilter(boolean filterValid) {
        if (mSelectedFilterCriteria != null) {
            llFilter.setVisibility(VISIBLE);
            if (filterValid) {
                ivFilterFold.setImageResource(R.drawable.sf_field_category_filter_time_down);
            } else {
                ivFilterFold.setImageResource(R.drawable.sf_field_category_filter_time_down_gray);
            }
            if (!StringUtil.isNullByString(mSelectedFilterCriteria.getSelectedImg())) {
                ivFilter.setVisibility(VISIBLE);
                tvFilter.setVisibility(GONE);
                ImageloadUtils.loadImage(getContext(), ivFilter, mSelectedFilterCriteria.getSelectedImg());
            } else if (!StringUtil.isNullByString(mSelectedFilterCriteria.getTitle())) {
                ivFilter.setVisibility(GONE);
                tvFilter.setVisibility(VISIBLE);
                tvFilter.setText(mSelectedFilterCriteria.getTitle());
                if (filterValid) {
                    tvFilter.setTextColor(getResources().getColor(R.color.sf_theme_color_level_1));
                } else {
                    tvFilter.setTextColor(getResources().getColor(R.color.sf_field_category_color_CACACA));
                }
                tvFilter.getPaint().setFakeBoldText(true);
            } else {
                llFilter.setVisibility(GONE);
            }
        } else {
            llFilter.setVisibility(GONE);
        }
    }

    public void setSortTypeCallback(SortTypeCallback sortTypeCallback) {
        this.sortTypeCallback = sortTypeCallback;
    }

    public ArrayList<FilterCriteriaVo> getFirstFilterCriteria() {
        return firstFilterCriteria;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_all) {
            //综合
            if (CategoryConstant.Value.SORT_DEFAULT.equals(sortType)) {
                return;
            }
            sortType = CategoryConstant.Value.SORT_DEFAULT;
            setTimeFilterValid(true);
            if (sortTypeCallback != null) {
                sortTypeCallback.changeSortType(sortType);
                sortTypeCallback.changeSortMaiDian(sortType);
            }
            setSearchQueryTextColor((TextView) v);
        } else if (id == R.id.tv_sold_count) {
            //销量排序
            if (CategoryConstant.Value.SORT_AMOUNT_DESC.equals(sortType)) {
                return;
            }
            sortType = CategoryConstant.Value.SORT_AMOUNT_DESC;
            setTimeFilterValid(true);
            if (sortTypeCallback != null) {
                sortTypeCallback.changeSortType(sortType);
                sortTypeCallback.changeSortMaiDian(sortType);
            }
            setSearchQueryTextColor((TextView) v);
        } else if (id == R.id.price_layout || id == R.id.tv_price) {
            //价格排序
            if (!CategoryConstant.Value.SORT_PRICE_ASC.equals(sortType)) {
                sortType = CategoryConstant.Value.SORT_PRICE_ASC;
                imgPrice.setImageResource(R.drawable.sf_theme_image_search_arrow_up);
            } else {
                sortType = CategoryConstant.Value.SORT_PRICE_DESC;
                imgPrice.setImageResource(R.drawable.sf_theme_image_search_arrow_down);
            }
            setTimeFilterValid(true);
            if (sortTypeCallback != null) {
                sortTypeCallback.changeSortType(sortType);
                sortTypeCallback.changeSortMaiDian(sortType);
            }
            setSearchQueryTextColor(tvPrice);
        } else if (id == R.id.tv_promotion) {
            //促销排序
            if (CategoryConstant.Value.SORT_PROMOTION_DESC.equals(sortType)) {
                return;
            }
            sortType = CategoryConstant.Value.SORT_PROMOTION_DESC;
            setTimeFilterValid(false);
            if (sortTypeCallback != null) {
                sortTypeCallback.changeTimeFilter(mSelectedFilterCriteria, false);
            }
            if (sortTypeCallback != null) {
                sortTypeCallback.changeSortType(sortType);
                sortTypeCallback.changeSortMaiDian(sortType);
            }
            setSearchQueryTextColor((TextView) v);
        } else if (id == R.id.filter_layout) {
            //时效筛选
            ivFilterFold.setImageResource(R.drawable.sf_field_category_filter_time_up);
            DropDownCategoryFilterTimePop filterTimePop = new DropDownCategoryFilterTimePop(getContext(),firstFilterCriteria,mSelectedFilterCriteria);
            filterTimePop.setFilterClickListener(new DropDownCategoryFilterTimePop.SelectFilterClickListener() {
                @Override
                public void onItemSelect(FilterCriteriaVo selectFilter) {
                    mSelectedFilterCriteria = selectFilter;
                    setTimeFilterValid(true);
                    if (sortTypeCallback != null) {
                        sortTypeCallback.changeTimeFilter(selectFilter,true);
                        if (selectFilter != null) {
                            sortTypeCallback.changeTimeFilterMaiDian(selectFilter.getId());
                        }
                    }
                }
            });
            filterTimePop.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    ivFilterFold.setImageResource(R.drawable.sf_field_category_filter_time_down);
                    if (sortTypeCallback != null) {
                        sortTypeCallback.onUpdateShowFilter(false);
                    }
                }
            });
            filterTimePop.showAsDropDown(rootView);
            if (filterTimePop.isShowing() && sortTypeCallback != null) {
                sortTypeCallback.onUpdateShowFilter(true);
                sortTypeCallback.changeTimeFilterMaiDian(null);
            }
        }
    }

    /**
     * 设置搜索筛选条件的字体颜色
     *
     * @param textView
     */
    private void setSearchQueryTextColor(TextView textView) {
        tvAll.setTextColor(getResources().getColor(R.color.sf_field_category_color_252525));
        tvAll.getPaint().setFakeBoldText(false);
        tvSoldCount.setTextColor(getResources().getColor(R.color.sf_field_category_color_252525));
        tvSoldCount.getPaint().setFakeBoldText(false);
        tvPrice.setTextColor(getResources().getColor(R.color.sf_field_category_color_252525));
        tvPrice.getPaint().setFakeBoldText(false);
        tvPromotion.setTextColor(getResources().getColor(R.color.sf_field_category_color_252525));
        tvPromotion.getPaint().setFakeBoldText(false);
        if (textView != tvPrice) {
            imgPrice.setImageResource(R.drawable.sf_field_category_search_arrow_normal);
        }
        if (null != textView) {
            textView.setTextColor(getResources().getColor(R.color.sf_theme_color_level_1));
            textView.getPaint().setFakeBoldText(true);
        }
    }

    public interface SortTypeCallback {
        /**
         * @param sortType
         */
        void changeSortType(String sortType);

        /**
         * @param eventId
         */
        void changeSortMaiDian(String eventId);

        /**
         * 切换时效筛选
         * @param selectFilter
         * @param needRefresh 是否需要接口刷新
         */
        void changeTimeFilter(FilterCriteriaVo selectFilter,boolean needRefresh);
        /**
         * @param filterId 筛选的id
         */
        void changeTimeFilterMaiDian(String filterId);
        /**
         * 时效筛选弹层是否展示
         * @param show
         */
        void onUpdateShowFilter(boolean show);
    }
}
