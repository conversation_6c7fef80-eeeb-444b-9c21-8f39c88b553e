package com.xstore.floorsdk.fieldcategory.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/06
 */
public class DapeigouResult implements Serializable {

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * msg
     */
    private String msg;

    private int code;

    private String skuId;

    private List<SkuInfoBean> productCardVoList;

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getMsg() {
        return msg;
    }

    public int getCode() {
        return code;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getSkuId() {
        return skuId;
    }
}
