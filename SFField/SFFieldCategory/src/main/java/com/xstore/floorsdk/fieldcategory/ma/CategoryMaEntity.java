package com.xstore.floorsdk.fieldcategory.ma;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;

public class CategoryMaEntity extends BaseMaEntity {
    public String skuId;
    public String skuName;
    /**
     * 所在当前页的位置
     */
    public String listPageIndex;
    /**
     * 所在当前页的位置
     */
    public String pos;
    /**
     * 所在的页数，分页的页数
     */
    public Integer listPageNum;
    /**
     * 所在的页数，分页的页数
     */
    public Integer page;

    /**
     * 对应 saleStatus 的值
     */
    public Integer skuStockStatus;
    /**
     * 二/三级前台类目id（一定是最末级的）
     */
    public String query;
    /**
     * 二/三级前台类目id（一定是最末级的）
     */
    public String keyword;
    /**
     * 根据接口返回上报
     */
    public String hc_cid3;
    /**
     * 单次检索请求id，翻页不变
     */
    public String pvid;
    /**
     * 单次检索分页id，翻页变化
     */
    public String logid;
    /**
     * 召回商品数，接口返回的末级分类下的总商品数量，而非当前分页下商品数量
     */
    public Integer recall_cnt;
    /**
     * 来源id ，组成“ pvid|事件id|时间戳”，没有pvid的时候报“-”
     */
    public String refer_id;
    /**
     * 根据接口返回上报
     */
    public String mtest;
    /**
     * 是否广告0-否，1-是
     */
    public String source;
    /**
     * 曝光价格（展示给用户购买单件的价格）
     */
    public String price;
    /**
     * 商品的每500g价格
     */
    public String price_500g;
    /**
     * 原始价格
     */
    public String ori_price;
    /**
     * sort_default-综合(默认)
     * sort_price_asc-价格升序
     * sort_price_desc-价格降序
     * sort_amount_desc-销量排序
     * sort_promotion_desc-促销降序
     */
    public String sort_type;
    /**
     * 是否主动筛选（外露筛选及筛选面板有筛选即为1，否则为0）
     */
    public String is_active_filt;
    public String firfilter;
    public String secfilter;
    public Integer skuType;
    public Integer secKill;
    /**
     * 分类搜-30
     */
    public String caller;
    /**
     * 商品个数（加车个数)
     */
    public Integer num;
    /**
     * 时效筛选的类型-用接口返回的
     * all   全部
     * b1v1  最快28分钟达
     * b2v1  7鲜云卖场
     */
    public String promiseType;
    /**
     * 是否是天天低价
     */
    public int edlp;

    /**
     * 是否为预售品 1 是 0 否
     */
    public int isPreSale;

    /**
     * 是否外卖 "1" 是，其他 不是
     */
    public String ifTakeaway;
    public Integer productCardType;

    /**
     * 推荐中台 策略标记
     */
    public String broker_info;
    /**
     * 服务端下发的clsTag，回传即可
     */
    public String clk;
    /**
     * 卖点行
     */
    public String sellPointList;
    /**
     * 促销行
     */
    public String tagList;

    public int index;// 从1开始

    public String touchstone_expids;

    public String skuPointStatus; //商品状态
    public String skuStatus; //商品上下架状态

    public String coreskuId;

    public String sitetype;
    public int isUseRust = 0;//是否使用rust替换了埋点参数，0：否 1：true

    // salePrice：红字价，枚举值
    public int salePriceType;
    // comparePrice：对比价
    public int comparePriceType;
    // discountPrice：预估到手价
    public int discountPriceType;
}
