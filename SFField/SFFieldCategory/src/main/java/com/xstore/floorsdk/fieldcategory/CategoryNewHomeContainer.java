package com.xstore.floorsdk.fieldcategory;

import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.getWidthByDesignValueFitFold;
import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.DEFAULT_EFFECT;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.gyf.barlibrary.ImmersionBar;
import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CustomizeFilterCriteriaVo;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.floorsdk.fieldcategory.bean.FirstCategoryResult;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryBridgeInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryFirstActionInterface;
import com.xstore.floorsdk.fieldcategory.ma.FirstCategoryExposureHelper;
import com.xstore.floorsdk.fieldcategory.ma.FirstCategoryReporter;
import com.xstore.floorsdk.fieldcategory.notify.ViewPagerLiviData;
import com.xstore.floorsdk.fieldcategory.request.CategoryRequest;
import com.xstore.floorsdk.fieldcategory.widget.CategorySearchView;
import com.xstore.floorsdk.fieldcategory.widget.DropDownFirstCategoryPop;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.interfaces.FloorLifecycle;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;
import com.xstore.sevenfresh.addressstore.constants.LbsBroadcastConstants;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.fresh_network_business.FreshResultCallback;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一级分类接口和二级分类接口现在是串行的
 * <p>
 * 分类页面逻辑：
 * 1、切一级分类：重置排序、重置二级分类、重置三级分类、刷新商品列表；
 * 2、切二级分类（手动点击切/滑动切）：重置排序、重置三级分类、刷新商品列表；
 * a、如果此二级分类没有三级分类，则请求二级分类下商品列表；
 * b、如果此二级分类有三级分类，则请求第一个三级分类下商品列表；
 * 3、手动点击切三级分类：（沿用当前的排序类型）
 * a、如果当前已展示此三级分类商品，则商品列表自动滚动到此三级分类节点；
 * b、如果当前未展示此三级分类商品，则请求此三级分类下的商品数据并沿用当前的排序类型，刷新商品列表
 * 4、滑动切三级分类：（沿用当前的排序类型）
 * a、上拉加载下一页，请求当前三级分类下一页商品数据，拼接到当前列表尾部；
 * b、上拉加载下一三级分类，请求此分类商品数据，拼接到当前列表尾部；
 * c、上拉加载下一二级分类，重走第2步切二级分类逻辑；
 * d、下拉到上一个三级分类，请求此三级分类全部商品数据（不分页）并插入到当前列表头部；
 * e、下拉到上一个二级分类，重走第2步切二级分类逻辑；
 * 5、切换排序方式（从二级分类维度）：切换时清除数据；
 * a、如果有三级分类按此排序方式请求当前二级分类下的第一个三级分类商品数据；
 * b、如果没有三级分类，则按此排序方式请求当前二级分类下的商品数据。
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public class CategoryNewHomeContainer extends FrameLayout implements FloorLifecycle, View.OnClickListener, CategoryBridgeInterface {

    private static final String TAG = "CategoryNewHomeContainer";


    private AppCompatActivity mActivity;


    /**
     * rvFirstCate
     */
    private RecyclerView rvFirstCate;
    /**
     * firstCateAdapter
     */
    private FirstCategoryAdapter firstCategoryAdapter;

    /**
     * firstCateManager
     */
    private CenterLayoutManager firstCateManager;
    /**
     * 全部按钮
     */
    private View llFoldAll;
    /**
     * 折叠全部的阴影
     */
    private View foldAllShadow;
    /**
     * 全部分类 下拉展示
     */
    private DropDownFirstCategoryPop dropDownFirstCategoryPop;

    private View rootView;
    /**
     * 容器
     */
    private RelativeLayout cmrl;
    /**
     * 轮播搜索词
     */
    private CategorySearchView searchView;

    /**
     * 门店Id
     */
    private String storeId;
    /**
     * 围栏id
     */
    private String fenceId;


    /**
     * categoryId 用户点击的一级类目id
     */
    public long categoryId;
    public String mid;

    /**
     * 上一个页面的二级类目id，只有第一次请求的时候使用，之后都是用户自己的点击行为
     */
    public long beforeCategoryId2;

    /**
     * 选中的一级类目名称
     */
    public String firstCateName;

    /**
     * 选中的一级类目Id
     */
    public String firstCateId;
    public String firstMid;


    /**
     * 页面来源 {@link CategoryConstant}
     */
    private int source = 0;

    /**
     * firstCateList 一级类目数据
     */
    public List<CategoryBean> firstCateList;


    private boolean mHidden;


    private FirstCategoryReporter categoryFieldReporter;

    private CategoryContainerInterface categoryContainerInterface;


    /**
     * secNoDataLayout 二级类目无数据 空白页
     */
    private View secNoDataLayout;
    private ViewPager containerViewPager;

    private SecondCategoryFragmentStateAdapter fragmentStateAdapter;
    /**
     * 距离底部距离
     */
    private int distance;

    /**
     * 自定义筛选条件
     */
    private ArrayList<FilterCriteriaVo> firstFilterCriteria = new ArrayList<>();
    /**
     * 选中的筛选条件
     */
    private FilterCriteriaVo mSelectedFilterCriteriaVo;

    /**
     * 右侧悬浮布局占位
     */
    private LinearLayout llFloatContainer;
    private FragmentManager fragmentManager;


    // 可以进行预加载的页面数量 配置为 3时 实际预加载为 前2 后2
    private int preLoadingPage = 3;
    private int pagerLimitSize = 0;
    private FirstCategoryExposureHelper firstCategoryExposureHelper;

    //AB 实验上报 7fNewCategory 中的 buriedExpLabel 埋点上报到 touchstone_expids 这个字段 所有相关的都要
    private String buriedExpLabel;

    public void setBuriedExpLabel(String buriedExpLabel) {
        this.buriedExpLabel = buriedExpLabel;
        if (categoryFieldReporter != null) {
            categoryFieldReporter.setBuriedExpLabel(buriedExpLabel);
        }
    }

    public void setPreLoadingPage(int preLoadingPage) {
        this.preLoadingPage = preLoadingPage;
    }

    public void setPagerLimitSize(int pagerLimitSize) {
        this.pagerLimitSize = pagerLimitSize;
    }

    public CategoryNewHomeContainer(Context context) {
        super(context);
        initRootView(context);
    }

    // 必须要外部传入 否则从activity 获取的 FragmentManager 不准确 可能会导致渲染失败， 未登录时可以复现
    public void setFragmentManager(FragmentManager fragmentManager) {
        this.fragmentManager = fragmentManager;
    }

    public CategoryNewHomeContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
        initRootView(context);
    }

    public CategoryNewHomeContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initRootView(context);
    }

    /**
     * 初始化视图
     *
     * @param context
     */
    private void initRootView(Context context) {
        mActivity = (AppCompatActivity) context;
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_new_container, this, true);
        initView();
        //注册
        IntentFilter filter1 = new IntentFilter();
        filter1.addAction(LbsBroadcastConstants.ACTION_UPDATE_ADDRESS);
//        filter1.addAction(Constant.ADDRESS_RED_PACKET);
        filter1.setPriority(Integer.MAX_VALUE);
        LocalBroadcastManager.getInstance(context).registerReceiver(addressChangeReceiver, filter1);
        categoryFieldReporter = new FirstCategoryReporter();

        //恢复到默认值
        ViewPagerLiviData.getInstance().updateData(-1);
    }

    /**
     * 初始化view
     */
    private void initView() {
        cmrl = rootView.findViewById(R.id.container);
        secNoDataLayout = rootView.findViewById(R.id.sec_first_no_data);
        secNoDataLayout.findViewById(R.id.refresh_text).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                CategoryRequest.getFirstCategory(mActivity, source, DEFAULT_EFFECT, categoryContainerInterface, new FirstCategoryListener());
            }
        });
        llFoldAll = rootView.findViewById(R.id.ll_fold_all);
        foldAllShadow = rootView.findViewById(R.id.view_all_shadow);
        searchView = rootView.findViewById(R.id.search_view);
        rvFirstCate = rootView.findViewById(R.id.rv_first_cate);
        firstCateManager = new CenterLayoutManager(mActivity, LinearLayoutManager.HORIZONTAL, false);
        rvFirstCate.setLayoutManager(firstCateManager);
        MarginLayoutParams params = (MarginLayoutParams) rvFirstCate.getLayoutParams();
        params.topMargin = getWidthByDesignValueFitFold(mActivity, 44, 375);
        rvFirstCate.setLayoutParams(params);
        containerViewPager = rootView.findViewById(R.id.product_view_pager);
        llFoldAll.setOnClickListener(this);
        llFloatContainer = rootView.findViewById(R.id.ll_float_container);

    }

    private void initFloatLayout() {
        try {
            if (categoryContainerInterface != null) {
                llFloatContainer.removeAllViews();
                View feedbackView = categoryContainerInterface.getFeedBackView();
                if (feedbackView != null) {
                    if (feedbackView.getParent() != null
                            && feedbackView.getParent() instanceof ViewGroup) {
                        ((ViewGroup) feedbackView.getParent()).removeView(feedbackView);
                    }
                    llFloatContainer.addView(feedbackView);
                }

                View cartIconView = categoryContainerInterface.getCartIconView();
                if (cartIconView != null) {
                    if (cartIconView.getParent() != null
                            && cartIconView.getParent() instanceof ViewGroup) {
                        ((ViewGroup) cartIconView.getParent()).removeView(cartIconView);
                    }
                    llFloatContainer.addView(cartIconView);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setFilterCriteriaVo(FilterCriteriaVo filterCriteriaVo) {
        this.mSelectedFilterCriteriaVo = filterCriteriaVo;
    }

    @Override
    public FilterCriteriaVo getFilterCriteriaVo() {
        return mSelectedFilterCriteriaVo;
    }


    @Override
    public JDMaUtils.JdMaPageImp getJdMaPageImp() {
        return categoryContainerInterface.getJdMaPageImp();
    }

    @Override
    public int getBottomDistance() {
        return distance;
    }

    private SecondCategoryFragment getCurrentCategoryFragment(int position) {
        if (fragmentStateAdapter != null) {
            return fragmentStateAdapter.getFragmentAt(position);
        }
        return null;
    }

    /**
     * 距离底部的距离
     *
     * @param distance
     */
    public void setBottomDistance(int distance) {
        this.distance = distance;
        SecondCategoryFragment currentCategoryFragment = getCurrentCategoryFragment(viewPagerCurrent);
        if (currentCategoryFragment != null) {
            currentCategoryFragment.setDistance(distance);
        }
        ViewGroup.LayoutParams params = llFloatContainer.getLayoutParams();
        if (params instanceof ViewGroup.MarginLayoutParams) {
            // 转换为MarginLayoutParams
            ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) params;
            // 设置marginBottom
            marginParams.bottomMargin = distance;
            // 应用新的布局参数
            llFloatContainer.setLayoutParams(marginParams);
        }
    }

    public String getFirstCateId() {
        return firstCateId;
    }

    public String getSecondCateId() {
        SecondCategoryFragment currentCategoryFragment = getCurrentCategoryFragment(viewPagerCurrent);
        if (currentCategoryFragment != null) {
            return currentCategoryFragment.secondCateId;
        }
        return "";
    }

    public String getSecondCateName() {
        SecondCategoryFragment currentCategoryFragment = getCurrentCategoryFragment(viewPagerCurrent);
        if (currentCategoryFragment != null) {
            return currentCategoryFragment.secondCateName;
        }
        return "";
    }

    public String getThirdCategoryName() {
        SecondCategoryFragment currentCategoryFragment = getCurrentCategoryFragment(viewPagerCurrent);
        if (currentCategoryFragment != null) {
            return currentCategoryFragment.thirdCategoryName;
        }
        return "";
    }

    /**
     * 设置数据
     *
     * @param bundle
     */
    public void setData(Bundle bundle, CategoryContainerInterface categoryContainerInterface) {
        this.categoryContainerInterface = categoryContainerInterface;
        if (categoryContainerInterface != null) {
            categoryFieldReporter.initData(categoryContainerInterface.getJdMaPageImp());
            categoryFieldReporter.setBuriedExpLabel(buriedExpLabel);
        }
        if (bundle != null) {
            source = bundle.getInt(CategoryConstant.Key.FROM_SOURCE);
            categoryId = bundle.getLong(CategoryConstant.Key.FIRST_CATEGORY_ID);
            beforeCategoryId2 = bundle.getLong(CategoryConstant.Key.SECOND_CATEGORY_ID);
        }

        if (source == CategoryConstant.Value.SOURCE_CATEGORY_PAGE && cmrl.getLayoutParams() instanceof LayoutParams) {
            LayoutParams layoutParams1 = (LayoutParams) cmrl.getLayoutParams();
            layoutParams1.topMargin = ImmersionBar.getStatusBarHeight(mActivity);
            cmrl.setLayoutParams(layoutParams1);
        }
        getFirstCategoryData();
        initFloatLayout();
    }

    private void getFirstCategoryData() {
        if (searchView != null) {
            searchView.setSearchStyle(source, categoryFieldReporter);
        }
        CategoryRequest.getFirstCategory(mActivity, source, DEFAULT_EFFECT, categoryContainerInterface, new FirstCategoryListener());
    }

    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {
    };

    @Override
    public void onResume(boolean hidden) {
        mHidden = hidden;
        if (hidden) {
            return;
        }
        if (searchView != null) {
            searchView.onResume(hidden);
        }
        compareStoreId();
    }

    @Override
    public void onPause() {
        if (searchView != null) {
            searchView.onPause();
        }
        mHidden = false;
    }

    /**
     * 比较判断storeId是否发生变化
     * 追加围栏id的判断
     *
     * @return true 门店id发生了变化
     */
    private boolean compareStoreId() {
        //如果门店id发生变化，或者门店id相同但是围栏id发生变化
        if (!TextUtils.isEmpty(storeId) && !storeId.equals(TenantIdUtils.getStoreId()) ||
                (TextUtils.equals(storeId, TenantIdUtils.getStoreId()) && !TextUtils.equals(fenceId, TenantIdUtils.getFenceId()))) {
            storeIdChanged();
            return true;
        }
        return false;
    }

    /**
     * 门店发生变化后进行刷新
     */
    private void storeIdChanged() {
        //他再首页的时候不要关闭 需要刷新 !!!!
        if (source == CategoryConstant.Value.SOURCE_CATEGORY_PAGE) {
            //重置选中的时效
            mSelectedFilterCriteriaVo = null;
            //重置类目id
            categoryId = 0;
            mid = null;
            //这里重置storeId,代表还未请求过二级类目的数据
            storeId = "";
            fenceId = "";
            getFirstCategoryData();
        } else {
            mActivity.finish();
        }
    }


    @Override
    public void onClick(View v) {
        if (NoDoubleClickUtils.isDoubleClick()) {
            return;
        }
        int id = v.getId();
        if (id == R.id.ll_fold_all) {
            categoryFieldReporter.allFirstCategoryClick();
            if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
                dropDownFirstCategoryPop.dismiss();
            }
            if (mHidden) {
                return;
            }
            if (dropDownFirstCategoryPop == null) {
                dropDownFirstCategoryPop = new DropDownFirstCategoryPop(mActivity, firstCateList, categoryId, new DropDownFirstCategoryPop.PopClickListener() {
                    @Override
                    public void onItemClick(@NotNull View view, int position, @NotNull CategoryBean cate) {
                        clickFirstCategory(view, position, cate);
                        //这里需要同步选中状态给外面的adapter
                        firstCategoryAdapter.setSelectedCategoryId(cate);
                    }

                    @Override
                    public void onFoldClick() {
                        //收起
                        categoryFieldReporter.foldFirstCategoryClick();
                    }
                });
            } else {
                dropDownFirstCategoryPop.updateSelectCate(categoryId);
            }
            dropDownFirstCategoryPop.showAsDropDown(searchView);
        }
    }

    /**
     * 一级分类请求回调
     */
    public class FirstCategoryListener extends FreshResultCallback<ResponseData<FirstCategoryResult>> {

        @Override
        public void onEnd(ResponseData<FirstCategoryResult> object, FreshHttpSetting httpSetting) {
            SFLogCollector.i("yyyyyy", "====FirstCategoryListener======" + httpSetting.isFromCache() + "=====");
            if (object != null && ResponseData.CODE_SUCC.equals(object.getCode()) && object.getData() != null) {
                //接口请求成功且有数据
                handleQueryCondition(object.getData().getCustomizeFilterCriteriaVo());
                //处理一级分类数据
                firstCateList = object.getData().getAllCategoryList();
                if (firstCateList != null && firstCateList.size() > 0) {
                    getFirstCateSuc(httpSetting.isFromCache());
                } else {
                    getFirstCateFail(httpSetting.isFromCache());
                }
            } else {
                getFirstCateFail(false);
            }

            if (!httpSetting.isFromCache()) {
                if (firstCateList == null || firstCateList.size() == 0) {
                    SgmReportBusinessErrorLog.reportFirstCateEmpty();
                }
                if (firstCateList != null && firstCateList.size() < 5) {
                    SgmReportBusinessErrorLog.reportFirstCateLess5();
                }
            }
        }

        @Override
        public void onError(FreshHttpException error) {
            if ((firstCateList == null || firstCateList.size() == 0) && error != null && error.getHttpSetting().getCacheConfig() != null) {
                CategoryRequest.getCacheData(mActivity, error.getHttpSetting());
            } else {
                getFirstCateFail(false);
            }
        }
    }

    public void getFirstCateSuc(boolean fromCache) {
        if (firstCateList == null || fragmentManager == null) {
            return;
        }
        containerViewPager.setVisibility(View.VISIBLE);
        secNoDataLayout.setVisibility(View.GONE);
        rvFirstCate.setVisibility(View.VISIBLE);
        //第一次进入，还没有一级类目id storeId为空代表着还没没请求过二级类目数据
        if (TextUtils.isEmpty(storeId)) {
            containerViewPager.setVisibility(View.GONE);
            storeId = TenantIdUtils.getStoreId();
            //判断第一次进入  围栏id就足以判断
            fenceId = TenantIdUtils.getFenceId();
            PreferenceUtil.getString(CategorySearchView.HOT_WORDS_INFO + storeId, "");
            if (categoryId == 0) {
                //通过读取一级缓存走到这里 无一级缓存的网络请求走到这里
                categoryId = firstCateList.get(0).getId();
                mid = firstCateList.get(0).getMid();
//                getChildCategory(true);
            } else {
                //通过首页点击分类和读取一级缓存走到这里
//                getChildCategory(true);
            }
        }

        //走到这里代表一开始进入类目页没有传入一级类目数据，没有正确上报pv 重报pv
        boolean checkFirstCateIndex = false;
        int firstCateIndex = 0;
        for (int i = firstCateList.size() - 1; i >= 0; i--) {
            CategoryBean c = firstCateList.get(i);
            if (c.getId() == null) {
                firstCateList.remove(i);
                continue;
            }

            if (c.getId() == categoryId) {
                firstCateName = c.getName();
                firstCateId = c.getId() + "";
                firstMid = c.getMid() + "";
                firstCateIndex = i;
                checkFirstCateIndex = true;
            } else {
                // 及其恶心 有时候外面传入的一级分类id是 mid
                if (c.getMid() != null) {
                    long cMid = -1;
                    try {
                        cMid = Long.parseLong(c.getMid());
                    } catch (Exception e) {
                        cMid = -1;
                    }
                    if (categoryId == cMid) {
                        firstCateName = c.getName();
                        firstCateId = c.getId() + "";
                        firstMid = c.getMid() + "";
                        categoryId = c.getId();
                    }
                }
            }
        }

        //确认当前的categoryId一级分类列表中，如果不在且一级列表是从网络返回的，则默认取第一个，再重新请求二级接口
        if (!checkFirstCateIndex && !fromCache) {
            CategoryBean category = firstCateList.get(0);
            beforeCategoryId2 = 0;
            if (category != null) {
                categoryId = category.getId();
                mid = category.getMid();
                firstCateId = category.getId() + "";
                firstMid = category.getMid() + "";
                firstCateName = category.getName();
                firstCateIndex = 0;
            }
        }

        firstCategoryAdapter = new FirstCategoryAdapter(mActivity, firstCateList, categoryId, false);
        firstCategoryAdapter.setClickListener(new FirstCategoryAdapter.ClickListener() {
            @Override
            public void onItemClick(View view, int position, CategoryBean cate) {
                clickFirstCategory(view, position, cate);
            }
        });
        if (pagerLimitSize != 0) {
            containerViewPager.setOffscreenPageLimit(pagerLimitSize);
        } else {
            containerViewPager.setOffscreenPageLimit(firstCateList.size());
        }
        if (isCacheData != fromCache) {
            isCacheData = fromCache;
        }
        // adapter中 内部缓存了fragmentList 是否有必要跟随OffscreenPageLimit 限制进行数据清空 如果需要 则在下面滚动监听器动态的移除
        rvFirstCate.setAdapter(firstCategoryAdapter);
        if (fragmentStateAdapter == null) {
            fragmentStateAdapter = new SecondCategoryFragmentStateAdapter(fragmentManager, this, categoryContainerInterface, new CategoryFirstActionInterface() {
                @Override
                public String getNextCategoryName() {
                    int nextPageIndex = viewPagerCurrent + 1;
                    if (firstCateList != null && nextPageIndex < firstCateList.size()) {
                        return firstCateList.get(nextPageIndex).getName();
                    }
                    return null;
                }

                @Override
                public void doSwitchNextCategory() {
                    int nextPageIndex = viewPagerCurrent + 1;
                    if (firstCateList != null && nextPageIndex < firstCateList.size()) {
                        selectViewPageIndex(nextPageIndex, false);
                    }
                }

                @Override
                public void doSwitchLastCategory() {

                }
            });
            containerViewPager.setAdapter(fragmentStateAdapter);
            containerViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    selectViewPageIndex(position, false);
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            });
        }
        viewPagerCurrent = firstCateIndex;
        fragmentStateAdapter.addData(firstCateList, fromCache);
        containerViewPager.setVisibility(View.VISIBLE);
        scrollFirstCate(firstCateIndex, true);
        //如果一级类目数量大于5，则可以滚动，并且展示全部，否则的话，不可以滚动，不展示全部
        if (firstCateList != null && firstCateList.size() > 5) {
            llFoldAll.setVisibility(View.VISIBLE);
            foldAllShadow.setVisibility(View.VISIBLE);
        } else {
            llFoldAll.setVisibility(View.GONE);
            foldAllShadow.setVisibility(View.GONE);
        }
        if (rvFirstCate.getItemDecorationCount() > 0) {
            rvFirstCate.removeItemDecorationAt(0);
        }
        rvFirstCate.addItemDecoration(new RecyclerView.ItemDecoration() {

            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {

                if (parent.getChildLayoutPosition(view) == firstCategoryAdapter.getItemCount() - 1 && firstCategoryAdapter.getItemCount() > 5) {
                    outRect.right = DisplayUtils.dp2px(mActivity, 40);
                } else if (parent.getChildLayoutPosition(view) == 0) {
                    outRect.left = getWidthByDesignValueFitFold(mActivity, 7, 375);
                }

            }
        });
        //更新弹框里的一级类目数据
        if (dropDownFirstCategoryPop != null) {
            dropDownFirstCategoryPop.updateCategories(firstCateList);
        }
        rvFirstCate.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (firstCategoryExposureHelper == null) {
                    firstCategoryExposureHelper = new FirstCategoryExposureHelper(categoryFieldReporter);
                }
                firstCategoryExposureHelper.exposureByHand(rvFirstCate, newState);
            }
        });
        if (!fromCache) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (firstCategoryExposureHelper == null) {
                        firstCategoryExposureHelper = new FirstCategoryExposureHelper(categoryFieldReporter);
                    }

                    firstCategoryExposureHelper.exposureByHand(rvFirstCate, RecyclerView.SCROLL_STATE_IDLE);
                }
            }, 50);
        }
    }

    private void selectViewPageIndex(int position, boolean isFirst) {
        viewPagerCurrent = position;
        scrollFirstCate(position, isFirst);
        // 一定要判null 为null 也是正常现象
        // OnPageChangeListener 中的 onPageSelected 会在 Adapter中的 fragment创建之前调用
        SecondCategoryFragment currentCategoryFragment = getCurrentCategoryFragment(position);
        if (currentCategoryFragment != null) {
            currentCategoryFragment.refreshFragmentData(position, mSelectedFilterCriteriaVo);
        }
        // 由于的setOffscreenPageLimit fragment数量一般会大于需要进行预加载的数量，所以重新执行一次预加载 fragment 内部判断是否已经加载完成，
        for (int i = 1; i < preLoadingPage; i++) {
            // 后面的
            int nextPosition = position + i;
            if ((nextPosition < fragmentStateAdapter.getCount()) && fragmentStateAdapter.getFragmentAt(nextPosition) != null) {
                fragmentStateAdapter.getFragmentAt(nextPosition).preLoadingData(position, mSelectedFilterCriteriaVo);
            }
            //前面的
            int lastPosition = position - i;
            if ((lastPosition > -1) && fragmentStateAdapter.getFragmentAt(lastPosition) != null) {
                fragmentStateAdapter.getFragmentAt(lastPosition).preLoadingData(position, mSelectedFilterCriteriaVo);
            }
        }
    }

    // 当前viewPager的位置
    private int viewPagerCurrent = -1;

    // 当前数据是否为缓存
    private boolean isCacheData = false;

    public void getFirstCateFail(boolean fromCache) {
        if (!fromCache) {
            if (firstCateList == null || firstCateList.size() == 0) {
                rvFirstCate.setVisibility(View.GONE);
            }
            secNoDataLayout.setVisibility(VISIBLE);
        } else {
            containerViewPager.setVisibility(GONE);
            secNoDataLayout.setVisibility(VISIBLE);
        }
    }

    @Override
    public void onHiddenChange(boolean hidden) {
        mHidden = hidden;
        if (!hidden) {
            if (TextUtils.isEmpty(storeId)) {
                getFirstCategoryData();
            }
            //判断storeId是否发生变化
            compareStoreId();
        } else {
            if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
                dropDownFirstCategoryPop.dismiss();
            }
        }
        if (searchView != null) {
            searchView.onHiddenChanged(hidden);
        }
    }

    /**
     * @param view
     * @param position
     * @param cate
     */
    private void clickFirstCategory(@NotNull View view, int position, @NotNull CategoryBean cate) {
        categoryFieldReporter.firstCategoryClick(cate, position + 1);
        scrollFirstCate(position, false);

    }

    private void scrollFirstCate(int index, boolean first) {
        firstCateManager.setFast(first);
        categoryId = firstCateList.get(index).getId();
        firstCateId = firstCateList.get(index).getId() + "";
        firstMid = firstCateList.get(index).getMid();
        firstCateName = firstCateList.get(index).getName();
        mid = firstCateList.get(index).getMid();

        if (index < 0) {
            return;
        }
        if (firstCategoryAdapter != null && firstCategoryAdapter.getSelectCategoryIndex() != index) {
            firstCategoryAdapter.setSelectedCategoryId(firstCateList.get(index).getId());
            if (first) {
                handler.postDelayed(() -> {
                    rvFirstCate.smoothScrollToPosition(index);
                }, 100);
            } else {
                rvFirstCate.smoothScrollToPosition(index);
            }
        }
        if (containerViewPager.getCurrentItem() != index) {
            containerViewPager.setCurrentItem(index, false);
        }
    }

    /**
     * 地址修改后接收者
     */
    private BroadcastReceiver addressChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            //这个地方原有的广播发送位置只有1个，其他场景没有发送该广播，应该属于历史逻辑没有维护更新，页面目前主要靠onResume刷新
            if (intent != null && LbsBroadcastConstants.ACTION_UPDATE_ADDRESS.equals(intent.getAction())) {
                if (!StringUtil.safeEqualsAndNotNull(storeId, TenantIdUtils.getStoreId()) && !mHidden) {
                    storeIdChanged();
                }
            }
        }
    };


    @Override
    public void onDestroy() {
        if (containerViewPager != null) {
            containerViewPager.clearOnPageChangeListeners();
        }
        handler.removeCallbacksAndMessages(null);
        if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
            dropDownFirstCategoryPop.dismiss();
        }

        LocalBroadcastManager.getInstance(mActivity).unregisterReceiver(addressChangeReceiver);
    }

    /**
     * 处理筛选条件
     */
    private void handleQueryCondition(CustomizeFilterCriteriaVo customizeFilterCriteriaVo) {
        if (firstFilterCriteria == null) {
            firstFilterCriteria = new ArrayList<>();
        }
        firstFilterCriteria.clear();
        if (customizeFilterCriteriaVo != null && customizeFilterCriteriaVo.getFirstFilterCriteria() != null) {
            firstFilterCriteria.addAll(customizeFilterCriteriaVo.getFirstFilterCriteria());
        }
        if (firstFilterCriteria != null && firstFilterCriteria.size() > 0) {
            mSelectedFilterCriteriaVo = firstFilterCriteria.get(0);
        } else {
            mSelectedFilterCriteriaVo = null;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
    }

    public class SecondCategoryFragmentStateAdapter extends FragmentStatePagerAdapter {
        List<CategoryBean> listBean;
        private FragmentManager fragmentManager;
        private CategoryBridgeInterface categoryBridgeInterface;
        private CategoryContainerInterface categoryContainerInterface;
        private CategoryFirstActionInterface categoryFirstActionInterface;
        private boolean onlyUseCache = false;

        private Map<String, WeakReference<SecondCategoryFragment>> fragmentMap = new HashMap<>();

        public SecondCategoryFragmentStateAdapter(@NonNull FragmentManager fm, CategoryBridgeInterface categoryBridgeInterface, CategoryContainerInterface categoryContainerInterface, CategoryFirstActionInterface categoryFirstActionInterface) {
            super(fm);
            this.fragmentManager = fm;
            this.categoryBridgeInterface = categoryBridgeInterface;
            this.categoryContainerInterface = categoryContainerInterface;
            this.categoryFirstActionInterface = categoryFirstActionInterface;
        }

        public void addData(List<CategoryBean> data, boolean onlyUseCache) {
            if (listBean == null) {
                listBean = new ArrayList<>();
            }
            listBean.clear();
            listBean.addAll(data);
            fragmentMap.clear();
            this.onlyUseCache = onlyUseCache;
            notifyDataSetChanged();
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;  // 这会强制所有页面重新加载
        }

        @NonNull
        @Override
        public Fragment getItem(int position) {
            long secondeCategoryId = 0;
            if (listBean.get(position).getId() == categoryId) {
                secondeCategoryId = beforeCategoryId2;
            }
            SecondCategoryFragment fragment = null;
            WeakReference<SecondCategoryFragment> weakRef = fragmentMap.get("Category_" + position);
            if (weakRef != null) {
                fragment = weakRef.get();
            }
            if (fragment == null) {
                fragment = SecondCategoryFragment.newInstance(position);
            }
            fragment.setBuriedExpLabel(buriedExpLabel);
            fragment.setSelectViewPagerIndex(viewPagerCurrent);
            fragment.setPreloadingNumber(preLoadingPage);
            fragment.setFirstFilterCriteria(firstFilterCriteria);
            fragment.setBeforeCategoryId2(secondeCategoryId);
            fragment.setDistance(distance);
            fragment.setSource(source);
            fragment.setCategoryBean(listBean.get(position));
            fragment.setOnlyUseCache(this.onlyUseCache);
            fragment.setCategoryBridgeInterface(categoryBridgeInterface);
            fragment.setCategoryContainerInterface(categoryContainerInterface);
            fragment.setCategoryFirstActionInterface(categoryFirstActionInterface);
            fragmentMap.remove("Category_" + position);
            fragmentMap.put("Category_" + position, new WeakReference<>(fragment));
            return fragment;
        }

        public SecondCategoryFragment getFragmentAt(int position) {
            WeakReference<SecondCategoryFragment> weakRef = fragmentMap.get("Category_" + position);
            return weakRef != null ? weakRef.get() : null;
        }

        @Override
        public int getCount() {
            return (listBean == null) ? 0 : listBean.size();
        }
    }

    /*
    * 刷新分类数据
    * 显示的时候，登录状态发生变化时，刷新数据
    * 因为原有逻辑：onHiddenChange()方法中，显示且(TextUtils.isEmpty(storeId)) 会刷新数据
    * 所以需要排除TextUtils.isEmpty(storeId)的情况
     */
    public void refreshCategoryData(boolean hidden, boolean loginStateChange) {
        if (!hidden) {
            if (!TextUtils.isEmpty(storeId) && loginStateChange) {
                getFirstCategoryData();
            }
        }
    }
}
