package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.jude.rollviewpager.RollPagerView;
import com.jude.rollviewpager.hintview.IconHintView;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.adapter.BannerLoopAdapter;
import com.xstore.floorsdk.fieldcategory.bean.BannerBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;

import java.util.ArrayList;

/**
 * banner轮播图
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
public class CategoryBannerPager extends LinearLayout {
    private View rootView;
    /**
     * 多个轮播图
     */
    private RollPagerView viewPager;
    /**
     * 单张图
     */
    private ImageView ivSingle;

    private BannerLoopAdapter bannerLoopAdapter;

    private ArrayList<BannerBean> banners;

    private CategoryReporterInterface categoryFieldReporter;

    public CategoryBannerPager(Context context) {
        super(context);
        initView(context);
    }

    public CategoryBannerPager(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public CategoryBannerPager(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化view
     *
     * @param context
     */
    private void initView(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_banner, this, true);
        viewPager = rootView.findViewById(R.id.view_pager);
        ivSingle = rootView.findViewById(R.id.iv_single_banner);

        bannerLoopAdapter = new BannerLoopAdapter(viewPager);
        viewPager.setAdapter(bannerLoopAdapter);

        viewPager.getViewPager().addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int i) {
                if (banners != null && banners.size() > 0) {
                    int position = i % banners.size();
                    postionIndex = position;
                    sendExpose(banners.get(position), position + 1);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    /**
     * 是否可以曝光
     */
    private boolean isCanExpose;

    /**
     * 设置数据
     *
     * @param banners
     * @param categoryFieldReporter
     * @param canExpose             是否可以曝光
     */
    public void setBannerData(ArrayList<BannerBean> banners, CategoryReporterInterface categoryFieldReporter, boolean canExpose) {
        if (banners == null || banners.isEmpty()) {
            setVisibility(GONE);
        }
        this.isCanExpose = canExpose;
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) viewPager.getLayoutParams();
        params.width = getBannerRealWidth(getContext());
        params.height = getBannerRealHeight(getContext());
        viewPager.setLayoutParams(params);

        LinearLayout.LayoutParams singleParams = (LinearLayout.LayoutParams) ivSingle.getLayoutParams();
        singleParams.width = getBannerRealWidth(getContext());
        singleParams.height = getBannerRealHeight(getContext());
        ivSingle.setLayoutParams(singleParams);

        setVisibility(VISIBLE);
        this.banners = banners;
        this.categoryFieldReporter = categoryFieldReporter;
        if (banners != null && banners.size() > 0) {
            setBannerVisible(true);
            if (banners.size() == 1) {
                viewPager.pause();
                BannerLoopAdapter.setImageClick(ivSingle, banners.get(0), categoryFieldReporter);
                sendExpose(banners.get(0), 1);
            } else {
                sendExpose(banners.get(0), 1);
                bannerLoopAdapter.setData(banners, categoryFieldReporter);
                viewPager.setAdapter(bannerLoopAdapter);
                viewPager.setHintView((new IconHintView(getContext(), R.drawable.sf_field_category_banner_circle_point_selected, R.drawable.sf_field_category_banner_circle_point_normal, ScreenUtils.dip2px(getContext(), 15))));
                viewPager.setPlayDelay(5000);
                viewPager.setHintPadding(0, 0, ScreenUtils.dip2px(getContext(), 0), ScreenUtils.dip2px(getContext(), 6));
            }
        } else {
            setBannerVisible(false);
            viewPager.pause();
        }
    }

    public void setCanExpose(boolean canExpose) {
        isCanExpose = canExpose;
        if (canExpose && banners != null && banners.size() > postionIndex) {
            sendExpose(banners.get(postionIndex), postionIndex + 1);
        }
    }

    private int postionIndex = 0;

    private void sendExpose(BannerBean floorsBean, int index) {
        if (!isCanExpose) {
            return;
        }
        if (floorsBean != null && !floorsBean.isHasShowView()) {
            floorsBean.setHasShowView(true);
            if (floorsBean.getAction() != null && categoryFieldReporter != null) {
                categoryFieldReporter.bannerExpose(floorsBean.getAction().getToUrl(), index);
            }
        }
    }

    /**
     * 设置广告图的可见性
     *
     * @param visible
     */
    public void setBannerVisible(boolean visible) {
        if (visible && banners != null) {
            if (banners.size() == 1) {
                viewPager.setVisibility(GONE);
                ivSingle.setVisibility(VISIBLE);
            } else {
                viewPager.setVisibility(VISIBLE);
                ivSingle.setVisibility(GONE);
            }
        } else {
            viewPager.setVisibility(GONE);
            ivSingle.setVisibility(GONE);
        }
    }

    public ArrayList<BannerBean> getBanners() {
        return banners;
    }

    public void onDestroy() {
        if (viewPager != null) {
            viewPager.pause();
        }
    }

    public static int getBannerWidth(Context context) {
        return ScreenUtils.getScreenWidth(context) - ScreenUtils.dip2px(context, 84);
    }

    public static int getBannerHeight(Context context) {
        return getBannerRealHeight(context) + DisplayUtils.dp2px(context, 10);
    }

    private static int getBannerRealWidth(Context context) {
        return getBannerWidth(context) - DisplayUtils.dp2px(context, 10) * 2;
    }

    private static int getBannerRealHeight(Context context) {
        return (int) (getBannerRealWidth(context) * 85.0 / 271);
    }

}
