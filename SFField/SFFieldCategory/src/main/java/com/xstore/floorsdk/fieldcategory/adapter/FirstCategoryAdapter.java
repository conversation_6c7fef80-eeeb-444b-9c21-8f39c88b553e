package com.xstore.floorsdk.fieldcategory.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.SgmReportBusinessErrorLog;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.YLCircleImageView;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
public class FirstCategoryAdapter extends RecyclerView.Adapter<FirstCategoryAdapter.ViewHolder> {

    private Context context;
    private List<CategoryBean> data;
    private long selectCategoryId = -1L;
    private boolean dropDown;
    private boolean fold = false;
    private ClickListener clickListener;
    private ClickSameItemListener clickSameItemListener;
    private int selectCategoryIndex = -1;

    public FirstCategoryAdapter(Context context, List<CategoryBean> data, Long categoryId, boolean dropDown) {
        this.context = context;
        this.data = data;
        this.selectCategoryId = categoryId;
        this.dropDown = dropDown;
    }

    public void setClickListener(ClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public void setClickSameItemListener(ClickSameItemListener clickSameItemListener) {
        this.clickSameItemListener = clickSameItemListener;
    }

    public void setSelectedCategoryId(CategoryBean cate) {
        selectCategoryId = cate.getId();
        notifyDataSetChanged();
    }

    public void setSelectedCategoryId(Long categoryId) {
        selectCategoryId = categoryId;
        notifyDataSetChanged();
    }

    public void updateCategories(List<CategoryBean> list) {
        this.data = list;
        notifyDataSetChanged();
    }

    public CategoryBean getItemData(int index) {
        if (index < 0 || data == null || data.size() == 0 || index >= data.size()) {
            return null;
        }
        return data.get(index);
    }

    @NonNull
    @Override
    public FirstCategoryAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_category_first_cate_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    @SuppressLint("RecyclerView")
    public void onBindViewHolder(@NonNull FirstCategoryAdapter.ViewHolder holder, int position) {
        if (dropDown) {
            DPIUtil.setWidthAndHeightDpiFitFold(holder.itemView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, 375);
            DPIUtil.setWidthAndHeightDpiFitFold(holder.ivCateLogo, 49, 49, 375);
            holder.ivCateLogo.setRadius(DPIUtil.getWidthByDesignValueFitFold(context, 20.0, 375));
            DPIUtil.setPaddingDpi(holder.tvCateName, 3.5f, 2f, 3.5f, 2.2f, 375);
            DPIUtil.setMarginDpi(holder.tvCateName, 0, 5, 0, 7, 375);
        } else {
            DPIUtil.setWidthAndHeightDpiFitFold(holder.itemView, 62, ViewGroup.LayoutParams.WRAP_CONTENT, 375);
            DPIUtil.setWidthAndHeightDpiFitFold(holder.ivCateLogo, 47, 47, 375);
            holder.ivCateLogo.setRadius(DPIUtil.getWidthByDesignValueFitFold(context, 18.0, 375));
            DPIUtil.setPaddingDpi(holder.tvCateName, 3f, 2f, 3f, 2.2f, 375);
            DPIUtil.setMarginDpi(holder.tvCateName, 0, 5, 0, 6, 375);
        }

//        DPIUtil.setMarginDpi(holder.ivCateLogo, 0, 1, 0, 0, 375);
        DPIUtil.setMarginDpi(holder.tvCateName, 0, 5, 0, 6, 375);
        holder.tvCateName.setTextSize(TypedValue.COMPLEX_UNIT_PX, DPIUtil.getWidthByDesignValueFitFold(context, 11.0, 375));
        holder.ivCateLogo.setVisibility(fold ? View.GONE : View.VISIBLE);


        CategoryBean category = data.get(position);
        if (category != null) {
            ImageloadUtils.loadImage(context, holder.ivCateLogo, category.getImageUrl());
            holder.tvCateName.setText(category.getName());

            if (StringUtil.isNullByString(category.getImageUrl())) {
                SgmReportBusinessErrorLog.reportFirstCateIconUrlEmpty(category.getId() + "", category.getName());
            }

            if (selectCategoryId == category.getId()) {
                selectCategoryIndex = position;
                holder.tvCateName.setBackgroundResource(R.drawable.sf_field_category_selector_cate_text);
                holder.tvCateName.setTextColor(context.getResources().getColor(R.color.sf_field_category_white));
                holder.tvCateName.getPaint().setFakeBoldText(true);
                holder.ivCateLogo.setBorderColor(context.getResources().getColor(R.color.sf_theme_color_level_1));
            } else {
                holder.tvCateName.setBackgroundResource(0);
                holder.tvCateName.setTextColor(context.getResources().getColor(R.color.sf_field_category_color_252525));
                holder.tvCateName.getPaint().setFakeBoldText(false);
                holder.ivCateLogo.setBorderColor(context.getResources().getColor(R.color.sf_field_category_white));
            }

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (selectCategoryId == category.getId()) {
                        if (clickSameItemListener != null) {
                            clickSameItemListener.clickSameItem();
                            return;
                        }
                    }
                    if (clickListener != null) {
                        clickListener.onItemClick(holder.itemView, position, category);
                        selectCategoryId = category.getId();
                        notifyDataSetChanged();
                    }
                }
            });
        }
    }

    public int getSelectCategoryIndex() {
        return selectCategoryIndex;
    }


    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        YLCircleImageView ivCateLogo;
        TextView tvCateName;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivCateLogo = itemView.findViewById(R.id.iv_cate_logo);
            tvCateName = itemView.findViewById(R.id.tv_cate_name);
        }
    }

    public interface ClickListener {
        void onItemClick(View view, int position, CategoryBean cate);
    }

    public interface ClickSameItemListener {
        void clickSameItem();
    }
}
