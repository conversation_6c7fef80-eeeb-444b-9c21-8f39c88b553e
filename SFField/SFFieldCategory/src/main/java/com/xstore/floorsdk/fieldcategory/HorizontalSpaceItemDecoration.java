package com.xstore.floorsdk.fieldcategory;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class HorizontalSpaceItemDecoration extends RecyclerView.ItemDecoration {

    private int defaultSpaceWidth;
    private int firstItemLeftSpace;
    private int lastItemRightSpace;

    public HorizontalSpaceItemDecoration(int defaultSpaceWidth, int firstItemLeftSpace, int lastItemRightSpace) {
        this.defaultSpaceWidth = defaultSpaceWidth;
        this.firstItemLeftSpace = firstItemLeftSpace;
        this.lastItemRightSpace = lastItemRightSpace;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int itemCount = state.getItemCount();
        if (position == 0) {
            // 第一个项
            outRect.left = firstItemLeftSpace;
            outRect.right = (defaultSpaceWidth / 2);
        } else if (position == itemCount - 1) {
            // 最后一个项
            outRect.left = (defaultSpaceWidth / 2);
            outRect.right = lastItemRightSpace;
        } else {
            // 中间的项
            outRect.left = defaultSpaceWidth / 2;
            outRect.right = defaultSpaceWidth / 2;
        }
    }
}