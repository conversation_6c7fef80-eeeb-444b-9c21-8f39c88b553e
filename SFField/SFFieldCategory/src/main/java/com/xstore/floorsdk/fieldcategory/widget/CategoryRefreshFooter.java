package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.scwang.smart.refresh.layout.api.RefreshFooter;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.constant.SpinnerStyle;
import com.xstore.floorsdk.fieldcategory.R;

public class CategoryRefreshFooter extends LinearLayout implements RefreshFooter {

    private TextView tvLoadingText;
    private ProgressBar progressBar;
    /**
     * 是否加载下一个分类
     */
    private boolean loadNextCate = false;

    public CategoryRefreshFooter(Context context) {
        super(context);
        initView();
    }

    public CategoryRefreshFooter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CategoryRefreshFooter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        setGravity(Gravity.CENTER_HORIZONTAL | Gravity.TOP);
        LayoutInflater.from(getContext()).inflate(R.layout.sf_field_category_refresh_footer, this);
        tvLoadingText = findViewById(R.id.tv_load_tip);
        progressBar = findViewById(R.id.footer_progressbar);
    }

    public void setLoadNextCate(boolean loadNextCate) {
        this.loadNextCate = loadNextCate;
    }

    @NonNull
    @Override
    public View getView() {
        return this;
    }

    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    @Override
    public void setPrimaryColors(int... colors) {

    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
        kernel.requestFloorDuration(0);
        kernel.requestRemeasureHeightFor(this);
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        return 0;
    }

    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState,
                               @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullUpToLoad:
                if (loadNextCate) {
                    tvLoadingText.setText(R.string.sf_field_category_pull_up_to_next_cate);
                    progressBar.setVisibility(GONE);
                    tvLoadingText.setVisibility(VISIBLE);
                } else {
                    progressBar.setVisibility(VISIBLE);
                    tvLoadingText.setVisibility(GONE);
                }
                break;
            case ReleaseToLoad:
                if (loadNextCate) {
                    tvLoadingText.setText(R.string.sf_field_category_release_to_next_cate);
                    progressBar.setVisibility(GONE);
                    tvLoadingText.setVisibility(VISIBLE);
                } else {
                    progressBar.setVisibility(VISIBLE);
                    tvLoadingText.setVisibility(GONE);
                }
                break;
            case Loading:
                tvLoadingText.setVisibility(GONE);
                break;
            case LoadFinish:
                tvLoadingText.setVisibility(GONE);
                progressBar.setVisibility(GONE);
                break;
            default:
                break;
        }
    }

    @Override
    public boolean setNoMoreData(boolean noMoreData) {
        return false;
    }
}
