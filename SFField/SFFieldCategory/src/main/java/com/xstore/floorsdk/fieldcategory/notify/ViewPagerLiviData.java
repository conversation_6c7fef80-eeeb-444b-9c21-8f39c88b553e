package com.xstore.floorsdk.fieldcategory.notify;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;

public class ViewPagerLiviData {
    private static ViewPagerLiviData instance;
    private final MutableLiveData<Integer> liveData;

    private ViewPagerLiviData() {
        liveData = new MutableLiveData<>();
    }

    public static synchronized ViewPagerLiviData getInstance() {
        if (instance == null) {
            instance = new ViewPagerLiviData();
        }
        return instance;
    }

    public LiveData<Integer> getLiveData() {
        return liveData;
    }

    public void updateData(Integer data) {
        if(liveData.getValue() != data){
            liveData.setValue(data);
        }
    }
}
