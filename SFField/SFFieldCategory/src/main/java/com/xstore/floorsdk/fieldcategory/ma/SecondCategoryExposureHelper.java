package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.SecondCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class SecondCategoryExposureHelper {

    private String firstCategoryMid;
    private String firstCategoryName;
    private Map<String, Boolean> exposuredCategoryMap = new HashMap<>();

    private SecondCategoryFieldReporter categoryFieldReporter;

    public void setFirstCategoryMid(String firstCategoryMid) {
        this.firstCategoryMid = firstCategoryMid;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public SecondCategoryExposureHelper(SecondCategoryFieldReporter categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
        if (exposuredCategoryMap == null) {
            exposuredCategoryMap = new HashMap<>();
        }
    }

    public void exposureByHand(RecyclerView recyclerView, int newState) {
        if (recyclerView == null) {
            return;
        }
        if (recyclerView.getAdapter() instanceof SecondCategoryAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            SecondCategoryAdapter secondCategoryAdapter = (SecondCategoryAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int first = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    CategoryBean itemData = secondCategoryAdapter.getItem(i);
                    if (itemData == null) {
                        continue;
                    }
                    if (StringUtil.isNullByString(itemData.getMid())) {
                        continue;
                    }
                    if (exposuredCategoryMap.containsKey(itemData.getMid())
                            && exposuredCategoryMap.get(itemData.getMid())) {
                        continue;
                    }
                    exposuredCategoryMap.put(itemData.getMid(), true);
                    if (categoryFieldReporter != null) {
                        categoryFieldReporter.secondCategoryExpose(itemData,  firstCategoryMid,firstCategoryName,i + 1);
                    }
                }
            }
        }
    }
}
