package com.xstore.floorsdk.fieldcategory.interfaces;

import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
import com.xstore.sevenfresh.datareport.JDMaUtils;

import java.util.ArrayList;

public interface CategoryBridgeInterface {
    void setFilterCriteriaVo(FilterCriteriaVo filterCriteriaVo);

    FilterCriteriaVo getFilterCriteriaVo();

//    boolean getNeedCache();

//    ArrayList<FilterCriteriaVo> getFliterList();

    JDMaUtils.JdMaPageImp getJdMaPageImp();

    int getBottomDistance();

}
