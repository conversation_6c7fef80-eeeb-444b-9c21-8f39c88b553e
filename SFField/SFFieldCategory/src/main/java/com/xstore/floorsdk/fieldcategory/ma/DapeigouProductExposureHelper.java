package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.DapeigouAdapter;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class DapeigouProductExposureHelper {

    private Map<String, Boolean> exposuredSkuMap = new HashMap<>();

    private CategoryReporterInterface categoryFieldReporter;

    public DapeigouProductExposureHelper(CategoryReporterInterface categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
    }

    public void updateProductList() {
        if (exposuredSkuMap == null) {
            exposuredSkuMap = new HashMap<>();
        } else {
            exposuredSkuMap.clear();
        }
    }

    public void exposureByHand(CategoryContainerInterface categoryContainerInterface, RecyclerView recyclerView, int newState, String coreSkuId) {
        if (recyclerView.getAdapter() instanceof DapeigouAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            DapeigouAdapter productAdapter = (DapeigouAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int first = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    SkuInfoBean productInfoBean = productAdapter.getItem(i);
                    if (productInfoBean == null) {
                        continue;
                    }
                    if (StringUtil.isNullByString(productInfoBean.getSkuId())) {
                        continue;
                    }
                    if (exposuredSkuMap.containsKey(productInfoBean.getSkuId())
                            && exposuredSkuMap.get(productInfoBean.getSkuId())) {
                        continue;
                    }
                    exposuredSkuMap.put(productInfoBean.getSkuId(), true);
                    if (categoryFieldReporter != null) {
                        categoryFieldReporter.dapeigouProductExposure(categoryContainerInterface, productInfoBean, coreSkuId);
                    }
                }
            }
        }
    }
}
