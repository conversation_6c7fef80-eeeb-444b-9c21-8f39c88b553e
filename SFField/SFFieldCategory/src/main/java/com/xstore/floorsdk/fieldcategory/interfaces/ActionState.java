package com.xstore.floorsdk.fieldcategory.interfaces;

/**
 * <AUTHOR>
 * @date 2022/11/03
 */
public enum ActionState {
    /**
     * 无操作
     */
    NO_ACTION,
    /**
     * 刷新商品，旧数据需要清空，使用场景如下：
     * 1、切换一级分类
     * 2、切换二级分类
     * 3、手动切换列表中没有的三级分类
     */
    PRODUCT_REFRESH,
    /**
     * 加载更多商品，使用场景如下：
     * 1、如果有三级分类，加载当前三级分类的下一页商品
     * 2、如果没有三级分类，加载当前二级分类的下一页商品
     * 3、加载当前二级分类下一个三级分类的第一页商品
     */
    PRODUCT_LOAD_MORE,
    /**
     * 插入商品，使用场景如下：
     * 1、下拉加载上一个三级分类，不分页
     * 2、点击某一个分类的展开无货商品，不分页
     */
    PRODUCT_INSERT
}
