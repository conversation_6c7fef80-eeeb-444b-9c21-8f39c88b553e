package com.xstore.floorsdk.fieldcategory.ma;

import android.text.TextUtils;

import com.jd.framework.json.JDJSON;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.CategoryProductContainer;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryKeyWordItemBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sevenfresh.commonbusiness.rustmodule.CompatibleGoodsModel;
import com.xstore.sevenfresh.commonbusiness.rustmodule.ExtMap;
import com.xstore.sevenfresh.commonbusiness.rustmodule.GoodsPointDataRetData;
import com.xstore.sevenfresh.commonbusiness.rustmodule.NumberOrString;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuSellPoint;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuTag;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;
import com.xstore.sevenfresh.productcard.utils.StringUtil;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分类领域埋点类
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SecondCategoryFieldReporter implements CategoryReporterInterface {

    private JDMaUtils.JdMaPageImp jdMaPageImp;
    private CategoryProductContainer productContainer;
    //AB 实验上报 7fNewCategory 中的 buriedExpLabel 埋点上报到 touchstone_expids 这个字段 所有相关的都要
    private String buriedExpLabel;

    public void setBuriedExpLabel(String buriedExpLabel) {
        this.buriedExpLabel = buriedExpLabel;
    }

    public void initData(JDMaUtils.JdMaPageImp jdMaPageImp, CategoryProductContainer productContainer) {
        this.jdMaPageImp = jdMaPageImp;
        this.productContainer = productContainer;
    }


    /**
     * 分类主页-二级类目入口点击
     */
    @Override
    public void secondCategoryClick(CategoryBean currSecCate, String firstCategoryMid, String firstCategoryName, int index) {
        if (currSecCate == null) {
            return;
        }
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = firstCategoryMid;
        publicparam.FIRSTMODULENAME = firstCategoryName;
        publicparam.SECONDMODULEID = currSecCate.getMid();
        publicparam.SECONDMODULENAME = currSecCate.getName();
        maEntity.index = index;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryMainPage_secondCategoryClick", jdMaPageImp, maEntity);
    }

    @Override
    public void secondCategoryExpose(CategoryBean currSecCate, String firstCategoryMid, String firstCategoryName, int index) {
        if (currSecCate == null) {
            return;
        }
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = firstCategoryMid;
        publicparam.FIRSTMODULENAME = firstCategoryName;
        publicparam.SECONDMODULEID = currSecCate.getMid();
        publicparam.SECONDMODULENAME = currSecCate.getName();
        maEntity.index = index;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure("categoryMainPage_secondCategoryExpose", null, maEntity, null, jdMaPageImp);

    }

    /**
     * 分类主页-三级类目入口点击
     */
    @Override
    public void thirdCategoryClick(int index) {
        if (productContainer == null) {
            return;
        }
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.index = index;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryMainPage_thirdCategoryClick", jdMaPageImp, maEntity);
    }

    @Override
    public void thirdCategoryExpose(CategoryBean currSecCate, int index) {
        if (currSecCate == null || productContainer == null) {
            return;
        }
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = currSecCate.getMid();
        publicparam.THIRDMODULENAME = currSecCate.getName();
        maEntity.index = index;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure("categoryMainPage_thirdCategoryExpose", null, maEntity, null, jdMaPageImp);
    }

    /**
     * 分类页-三级类目展开
     */
    @Override
    public void allThirdCategoryClick() {
        JDMaUtils.save7FClick("categoryMainPage_thirdCategory_all", jdMaPageImp, null);
    }

    /**
     * 分类列表-失效分堆-展开
     */
    @Override
    public void invalidCommodityUnfoldClick(int sinkCount) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("num", sinkCount);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        baseMaEntity.setPublicParam(publicparam);
        JDMaUtils.save7FClick("categoryListPage_invalidCommodityUnfold", jdMaPageImp, baseMaEntity);
    }

    /**
     * 分类列表-筛选-综合
     */
    @Override
    public void sortTypeDefaultClick() {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_sort_type_default", jdMaPageImp, maEntity);
    }

    /**
     * 分类列表-筛选-价格升序
     */
    @Override
    public void sortPriceAscClick() {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_sort_price_asc", jdMaPageImp, maEntity);
    }

    /**
     * 分类列表-筛选-价格降序
     */
    @Override
    public void sortPriceDescClick() {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_sort_price_desc", jdMaPageImp, maEntity);
    }

    /**
     * 分类列表-筛选-促销排序
     */
    @Override
    public void sortPromotionClick() {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_promotion_desc", jdMaPageImp, maEntity);
    }

    /**
     * 分类列表-筛选-销量排序
     */
    @Override
    public void sortAmountClick() {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_sort_amount_desc", jdMaPageImp, maEntity);
    }

    /**
     * 分类结果页-时效筛选-入口点击
     */
    @Override
    public void promiseSelectEntranceClick() {
        JDMaUtils.save7FClick("categoryListPage_promiseSelect_entranceClick", jdMaPageImp, null);
    }

    /**
     * 分类结果页-时效筛选-入口点击
     */
    @Override
    public void promiseSelectPromiseClick(String filterId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("promiseType", filterId);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("categoryListPage_promiseSelect_promiseClick", jdMaPageImp, baseMaEntity);
    }

    /**
     * 分类列表商品点击进商详
     */
    @Override
    public void clickCommodity(SkuInfoBean productInfoBean) {
        String eventId = "categoryListPage_clickCommodity";
        CategoryMaEntity maEntity = getCategoryMaEntity("2", productInfoBean, eventId, null);
        maEntity.touchstone_expids = buriedExpLabel;
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("realtime_feature",productInfoBean.getRealtime_feature());
        maEntity.setMa7FextParam(paramMap);
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
    }

    /**
     * 分类列表商品点击加车
     */
    @Override
    public void clickAddCart(SkuInfoBean productInfoBean) {

        String eventId = "categoryListPage_addCart";
        CategoryMaEntity maEntity = getCategoryMaEntity("1", productInfoBean, eventId, 1);
        maEntity.touchstone_expids = buriedExpLabel;
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("realtime_feature",productInfoBean.getRealtime_feature());
        maEntity.setMa7FextParam(paramMap);
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
        PreferenceUtil.saveStringMax100("carSkus", productInfoBean.getSkuId());
    }

    /**
     * 分类列表商品曝光
     */
    @Override
    public void productExposure(SkuInfoBean productInfoBean) {
        String eventId = "Classification_second";
        CategoryMaEntity maEntity = getCategoryMaEntity(null, productInfoBean, eventId, null);
        maEntity.touchstone_expids = buriedExpLabel;
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("realtime_feature",productInfoBean.getRealtime_feature());
        JDMaUtils.save7FExposure(eventId, paramMap, maEntity, null, jdMaPageImp);
    }

    private CategoryMaEntity getCategoryMaEntity(String clickType, SkuInfoBean productInfoBean, String eventId, Integer num) {
        return getCategoryMaEntity(clickType, productInfoBean, eventId, num, null, null);
    }

    @NotNull
    private CategoryMaEntity getCategoryMaEntity(String clickType, SkuInfoBean productInfoBean, String eventId, Integer num, String coreSkuId, String sitetype) {
        if (productInfoBean == null) {
            return null;
        }
        CategoryMaEntity maEntity = new CategoryMaEntity();
        maEntity.skuId = productInfoBean.getSkuId();
        maEntity.skuName = productInfoBean.getSkuName();
        maEntity.skuPointStatus = productInfoBean.getPointStatus();
        maEntity.skuStatus = productInfoBean.getStatus() + "";
        maEntity.coreskuId = coreSkuId;
        maEntity.sitetype = sitetype;
        if (productInfoBean.getLogicInfo() != null) {
            maEntity.ifTakeaway = productInfoBean.getLogicInfo().getIsTakeaway();
            maEntity.productCardType = productInfoBean.getLogicInfo().getProductCardType();
        }
        if (!TextUtils.isEmpty(productInfoBean.getPageIndex())) {
            try {
                maEntity.index = Integer.parseInt(productInfoBean.getPageIndex());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        maEntity.listPageIndex = productInfoBean.getPageIndex();
        maEntity.pos = productInfoBean.getPageIndex();
        maEntity.listPageNum = productInfoBean.getPage();
        maEntity.page = productInfoBean.getPage();
        maEntity.skuStockStatus = productInfoBean.getStockStatus();
        maEntity.query = productInfoBean.getMid() + "";
        maEntity.keyword = productInfoBean.getMid() + "";
        if (productInfoBean.getLogicInfo() != null) {
            maEntity.skuType = productInfoBean.getLogicInfo().isClearanceFlag() ? 1 : 0;
        }
        if( productInfoBean.getLogicInfo() != null && productInfoBean.getLogicInfo().getBoolTagMap() != null){
            maEntity.secKill = productInfoBean.getLogicInfo().getBoolTagMap().isSecKill() ? 1 : 0;
        }
        if (productInfoBean.getCartInfo() != null && productInfoBean.getCartInfo().getType() == SkuEnumInterface.CartBtnType.PRE_SALE) {
            maEntity.isPreSale = 1;
        } else {
            maEntity.isPreSale = 0;
        }
        maEntity.broker_info = productInfoBean.getBrokerInfo();
        maEntity.clk = productInfoBean.getClsTag();
        maEntity.sellPointList = getSellPointString(productInfoBean, 3);
        maEntity.tagList = getTagListString(productInfoBean, 3);
        maEntity.pvid = productInfoBean.getPvId();
        maEntity.logid = productInfoBean.getLogId();
        maEntity.recall_cnt = productInfoBean.getTotalCount();
        maEntity.refer_id = (!TextUtils.isEmpty(productInfoBean.getPvId())) ? productInfoBean.getPvId() + "|" + eventId + "|" + System.currentTimeMillis() : "-";
        if (productInfoBean.getSalePrice() != null) {
            maEntity.price = productInfoBean.getSalePrice().getValue();
        }
        maEntity.ori_price = productInfoBean.getOriPrice();
        maEntity.price_500g = productInfoBean.getMaFieldPrice500g();

        if(productInfoBean.getSalePrice()!=null){
            maEntity.salePriceType = productInfoBean.getSalePrice().getType();
        }
        if(productInfoBean.getComparePrice()!=null){
            maEntity.comparePriceType = productInfoBean.getComparePrice().getType();
        }
        if(productInfoBean.getDiscountPrice()!=null){
            maEntity.discountPriceType = productInfoBean.getDiscountPrice().getType();
        }

        if (productContainer != null) {
            maEntity.sort_type = productContainer.sortType;
            if (productContainer.mSelectedFilterCriteria != null) {
                maEntity.promiseType = productContainer.mSelectedFilterCriteria.getId();
            }
        }
        maEntity.caller = "30";
        maEntity.num = num;
        if (productContainer != null && productContainer.mCategoryWareInfo != null) {
            maEntity.hc_cid3 = productContainer.mCategoryWareInfo.getHcCid3() == null ? "-" : productContainer.mCategoryWareInfo.getHcCid3();
            maEntity.mtest = productContainer.mCategoryWareInfo.getMtest() == null ? "-" : productContainer.mCategoryWareInfo.getMtest();
            maEntity.is_active_filt = productContainer.mCategoryWareInfo.getIsActiveFilt() == null ? "0" : productContainer.mCategoryWareInfo.getIsActiveFilt();
            maEntity.firfilter = productContainer.mCategoryWareInfo.getFirfilter() == null ? "-" : productContainer.mCategoryWareInfo.getFirfilter();
            maEntity.secfilter = productContainer.mCategoryWareInfo.getSecfilter() == null ? "-" : productContainer.mCategoryWareInfo.getSecfilter();
            maEntity.source = productContainer.mCategoryWareInfo.getSource() == null ? "0" : productContainer.mCategoryWareInfo.getSource();
        }
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        if (clickType != null) {
            publicparam.CLICKTYPE = clickType;
        }
        publicparam.FIRSTMODULEID = productInfoBean.getFirstMid();
        publicparam.FIRSTMODULENAME = productInfoBean.getFirstCateName();
        publicparam.SECONDMODULEID = productInfoBean.getSecondMid();
        publicparam.SECONDMODULENAME = productInfoBean.getSecondCateName();
        publicparam.THIRDMODULEID = productInfoBean.getThirdMid();
        publicparam.THIRDMODULENAME = productInfoBean.getThirdCateName();
        if (TextUtils.isEmpty(publicparam.FIRSTMODULEID) && productContainer != null) {
            publicparam.FIRSTMODULEID = productContainer.firstMid;
        }
        if (TextUtils.isEmpty(publicparam.FIRSTMODULENAME) && productContainer != null) {
            publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        }
        if (TextUtils.isEmpty(publicparam.SECONDMODULEID) && productContainer != null) {
            publicparam.SECONDMODULEID = productContainer.secondMid;
        }
        if (TextUtils.isEmpty(publicparam.SECONDMODULENAME) && productContainer != null) {
            publicparam.SECONDMODULENAME = productContainer.secondCateName;
        }
        if (TextUtils.isEmpty(publicparam.THIRDMODULEID) && productContainer != null) {
            publicparam.THIRDMODULEID = productContainer.thirdMid;
        }
        if (TextUtils.isEmpty(publicparam.THIRDMODULENAME) && productContainer != null) {
            publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        }
        maEntity.setPublicParam(publicparam);
        return maEntity;
    }


    /**
     * 分类结果页-搜索行点击（点击搜索框）
     */
    @Override
    public void clickSearchRow() {
        JDMaUtils.save7FClick("categoryListPage_searchComponent_clickSearchRow", jdMaPageImp, null);
    }

    /**
     * 分类结果页-搜索按钮_暗文词曝光
     */
    @Override
    public void hotWordsExpose(CategoryKeyWordItemBean keyWordItem) {
        if (keyWordItem != null) {
            JDJSONObject jdjsonObject = new JDJSONObject();
            jdjsonObject.put("enkwd", keyWordItem.getSearchWord());
            jdjsonObject.put("hotwords", keyWordItem.getKeyword());
            jdjsonObject.put("type", keyWordItem.getUrl() != null ? 2 : 1);
            JDMaUtils.save7FExposure("categoryListPage_searchComponent_hotWordsExpose", null, null, jdjsonObject.toString(), jdMaPageImp);
        }
    }

    /**
     * 分类结果页-轮播图曝光
     */
    @Override
    public void bannerExpose(String url, int index) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("url", url);
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.index = index;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure("categoryListPage_bannerExpose", hashMap, maEntity, null, jdMaPageImp);
    }

    /**
     * 分类结果页-轮播图点击
     */
    @Override
    public void bannerClick(String url) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("url", url);
        CategoryMaEntity maEntity = new CategoryMaEntity();
        maEntity.setMa7FextParam(hashMap);
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryListPage_bannerClick", jdMaPageImp, maEntity);
    }

    /**
     * 分类结果页-商品卡片-榜单入口点击
     */
    @Override
    public void clickRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null) {
            HashMap<String, Object> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            hashMap.put("rankName", marketEntrance != null ? marketEntrance.getText() : "");
            hashMap.put("rankSortId", marketEntrance != null ? String.valueOf(marketEntrance.getSubType()) : "");
            CategoryMaEntity baseMaEntity = new CategoryMaEntity();
            baseMaEntity.setMa7FextParam(hashMap);
            baseMaEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FClick("categoryResultPage_categorySkuList_rankingListEntrance", "", skuId, null, jdMaPageImp, baseMaEntity);
        }
    }

    /**
     * 分类结果页-商品卡片-榜单入口曝光
     */
    @Override
    public void showRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null) {
            HashMap<String, String> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            hashMap.put("rankName", marketEntrance != null ? marketEntrance.getText() : "");
            hashMap.put("rankSortId", marketEntrance != null ? String.valueOf(marketEntrance.getSubType()) : "");
            CategoryMaEntity baseMaEntity = new CategoryMaEntity();
            baseMaEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FExposure("categoryResultPage_categorySkuList_rankingExpose", hashMap, baseMaEntity, null, jdMaPageImp);
        }
    }

    /**
     * 分类结果页-商品卡片-立即预定点击
     */
    @Override
    public void clickBookNow(SkuInfoBean productInfoBean) {
        if (productInfoBean != null) {
            HashMap<String, Object> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            if (productInfoBean.getSalePrice() != null) {
                hashMap.put("price", productInfoBean.getSalePrice().getValue());
            }
            hashMap.put("ori_price", productInfoBean.getOriPrice());
            hashMap.put("listPageIndex", productInfoBean.getPageIndex());

            CategoryMaEntity baseMaEntity = new CategoryMaEntity();
            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
            publicparam.FIRSTMODULEID = productInfoBean.getFirstMid();
            publicparam.FIRSTMODULENAME = productInfoBean.getFirstCateName();
            publicparam.SECONDMODULEID = productInfoBean.getSecondMid();
            publicparam.SECONDMODULENAME = productInfoBean.getSecondCateName();
            publicparam.THIRDMODULEID = productInfoBean.getThirdMid();
            publicparam.THIRDMODULENAME = productInfoBean.getThirdCateName();
            baseMaEntity.setPublicParam(publicparam);
            baseMaEntity.setMa7FextParam(hashMap);
            baseMaEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FClick("categoryResultPage_categorySkuList_preBooking_booknow_addCart", "", skuId, null, jdMaPageImp, baseMaEntity);
        }
    }

    /**
     * 分类结果页-商品卡片-立即预定曝光
     */
    @Override
    public void showBookNow(SkuInfoBean productInfoBean) {
        if (productInfoBean != null) {
            HashMap<String, String> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            CategoryMaEntity baseMaEntity = new CategoryMaEntity();
            baseMaEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FExposure("categoryResultPage_categorySkuList_preBooking_Expose", hashMap, baseMaEntity, null, jdMaPageImp);
        }
    }

    /**
     * 分类结果页-商品卡片-百科点击
     */
    @Override
    public void clickJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null) {
            HashMap<String, Object> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            hashMap.put("status", productInfoBean.getStatus() + "");
            if (marketEntrance != null && StringUtil.isNotEmpty(marketEntrance.getText())) {
                hashMap.put("keyword", marketEntrance.getText());
            }
            BaseMaEntity baseMaEntity = new BaseMaEntity();
            baseMaEntity.setMa7FextParam(hashMap);

            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
            publicparam.FIRSTMODULEID = productInfoBean.firstMid;
            publicparam.FIRSTMODULENAME = productInfoBean.firstCateName;
            publicparam.SECONDMODULEID = productInfoBean.secondMid;
            publicparam.SECONDMODULENAME = productInfoBean.secondCateName;
            publicparam.THIRDMODULEID = productInfoBean.thirdMid;
            publicparam.THIRDMODULENAME = productInfoBean.thirdCateName;
            baseMaEntity.setPublicParam(publicparam);

            JDMaUtils.save7FClick("categoryMainPage_clickHealthChannel", "", skuId, null, jdMaPageImp, baseMaEntity);
        }
    }

    /**
     * 分类结果页-商品卡片-百科曝光
     */
    @Override
    public void showJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null) {
            HashMap<String, String> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            hashMap.put("status", productInfoBean.getStatus() + "");
            if (marketEntrance != null && StringUtil.isNotEmpty(marketEntrance.getText())) {
                hashMap.put("keyword", marketEntrance.getText());
            }
            BaseMaEntity baseMaEntity = new BaseMaEntity();
            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
            publicparam.FIRSTMODULEID = productInfoBean.firstMid;
            publicparam.FIRSTMODULENAME = productInfoBean.firstCateName;
            publicparam.SECONDMODULEID = productInfoBean.secondMid;
            publicparam.SECONDMODULENAME = productInfoBean.secondCateName;
            publicparam.THIRDMODULEID = productInfoBean.thirdMid;
            publicparam.THIRDMODULENAME = productInfoBean.thirdCateName;
            baseMaEntity.setPublicParam(publicparam);

            JDMaUtils.save7FExposure("categoryMainPage_healthChannelExpose", hashMap, baseMaEntity, null, jdMaPageImp);
        }
    }

    /**
     * 获取卖点行数据字符串（埋点用）
     *
     * @param skuInfo 商品信息
     * @param num     正数，要前几个
     * @return 埋点字符串
     */
    public static String getSellPointString(SkuInfoBean skuInfo, int num) {
        if (skuInfo == null || skuInfo.getSellPointList() == null || skuInfo.getSellPointList().isEmpty()) {
            return null;
        }

        if (num < 0) {
            num = 2;
        }

        List<SkuSellPoint> sellPointList = skuInfo.getSellPointList();

        ArrayList<SkuSellPoint> subList;
        if (sellPointList.size() > num) {
            subList = new ArrayList<>(sellPointList.subList(0, num));
        } else {
            subList = new ArrayList<>(sellPointList);
        }

        return JDJSON.toJSONString(subList);
    }

    /**
     * 获取促销行数据字符串（埋点用）
     *
     * @param skuInfo 商品信息
     * @param maxNum  正数，要前几个
     * @return 埋点字符串
     */
    public static String getTagListString(SkuInfoBean skuInfo, int maxNum) {
        if (skuInfo == null || skuInfo.getTagList() == null || skuInfo.getTagList().isEmpty()) {
            return null;
        }

        if (maxNum < 0) {
            maxNum = 2;
        }

        List<SkuTag> sellPointList = skuInfo.getTagList();

        ArrayList<SkuTag> subList;
        if (sellPointList.size() > maxNum) {
            subList = new ArrayList<>(sellPointList.subList(0, maxNum));
        } else {
            subList = new ArrayList<>(sellPointList);
        }

        return JDJSON.toJSONString(subList);
    }


    public void secondCategoryEmptyPageExpose(String firstCategoryMid, String firstCategoryName, String secondCategoryId, String secondCategoryName, String thirdCategoryId, String thirdCategoryName) {
        CategoryMaEntity maEntity = new CategoryMaEntity();
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = firstCategoryMid;
        publicparam.FIRSTMODULENAME = firstCategoryName;
        publicparam.SECONDMODULEID = secondCategoryId;
        publicparam.SECONDMODULENAME = secondCategoryName;
        publicparam.CLICKTYPE = "-1";
        publicparam.THIRDMODULEID = thirdCategoryId;
        publicparam.THIRDMODULENAME = thirdCategoryName;
        maEntity.setPublicParam(publicparam);
        HashMap<String, Object> paramMap = new HashMap<>();
        String categoryRank = "一级";
        String categoryName = firstCategoryName;
        if (!TextUtils.isEmpty(secondCategoryName)) {
            categoryRank = "二级";
            categoryName = secondCategoryName;
        }
        if (!TextUtils.isEmpty(thirdCategoryName)) {
            categoryRank = "三级";
            categoryName = thirdCategoryName;
        }
        paramMap.put("categoryRank", categoryRank);
        paramMap.put("categoryName", categoryName);
        maEntity.setMa7FextParam(paramMap);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure("categoryMainPage_nullSkuExpose", null, maEntity, null, jdMaPageImp);

    }


    public void dapeigouProductExposure(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_SkuExpose";
        CategoryMaEntity maEntity = getCategoryMaEntity(null, productInfoBean, eventId, null, coreSkuId, "0");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure(eventId, null, maEntity, null, jdMaPageImp);
    }

    @Override
    public void dapeigouProductClick(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_SkuClick";
        CategoryMaEntity maEntity = getCategoryMaEntity("2", productInfoBean, eventId, null, coreSkuId, "0");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);

    }

    @Override
    public void dapeigouProductAddCart(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_Addcart";
        CategoryMaEntity maEntity = getCategoryMaEntity("1", productInfoBean, eventId, 1, coreSkuId, "0");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
    }

    @Override
    public void dapeigouDialogProductExposure(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_SkuExpose";
        CategoryMaEntity maEntity = getCategoryMaEntity(null, productInfoBean, eventId, null, coreSkuId, "1");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FExposure(eventId, null, maEntity, null, jdMaPageImp);
    }

    @Override
    public void dapeigouDialogProductClick(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_SkuClick";
        CategoryMaEntity maEntity = getCategoryMaEntity("2", productInfoBean, eventId, null, coreSkuId, "1");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
    }

    @Override
    public void dapeigouDialogProductAddCart(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId) {
        String eventId = "categoryMainPage_MatchProduct_Addcart";
        CategoryMaEntity maEntity = getCategoryMaEntity("1", productInfoBean, eventId, 1, coreSkuId, "1");
        ReplaceParamByRust(eventId, productInfoBean, maEntity, categoryContainerInterface);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
    }

    @Override
    public void dapeigouProductClickMore(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("coreskuId", productInfoBean.getSkuId());
        CategoryMaEntity maEntity = new CategoryMaEntity();
        maEntity.setMa7FextParam(hashMap);
        BaseMaPublicParam publicparam = maEntity.getPublicParam();
        publicparam.FIRSTMODULEID = productContainer.firstMid;
        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
        publicparam.SECONDMODULEID = productContainer.secondMid;
        publicparam.SECONDMODULENAME = productContainer.secondCateName;
        publicparam.THIRDMODULEID = productContainer.thirdMid;
        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
        maEntity.setPublicParam(publicparam);
        maEntity.touchstone_expids = buriedExpLabel;
        JDMaUtils.save7FClick("categoryMainPage_MatchProduct_MoreClick", jdMaPageImp, maEntity);
//        String eventId = "categoryMainPage_MatchProduct_MoreClick";
//        CategoryMaEntity maEntity = getCategoryMaEntity("-1", productInfoBean, eventId, 0, productInfoBean.getSkuId(), "0");
//        maEntity.touchstone_expids = buriedExpLabel;
//        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
    }

    public void ReplaceParamByRust(String eventId, SkuInfoBean productInfoBean, CategoryMaEntity maEntity, CategoryContainerInterface categoryContainerInterface) {
        //判断是否可以用Rust，具体实现在宿主APP里
        if (categoryContainerInterface != null && categoryContainerInterface.isReportCanUseRust()) {
            try {
                NumberOrString.String skuIdNumberOrString = new NumberOrString.String(productInfoBean.getSkuId());
                String skuName = productInfoBean.getSkuName();
                int skuStatus = productInfoBean.getSkuStatus();
                int stockStatus = productInfoBean.getStockStatus();
                ExtMap extMap = new ExtMap(new NumberOrString.String(productInfoBean.getPointStatus()));
                CompatibleGoodsModel goodsModel = new CompatibleGoodsModel(skuIdNumberOrString, skuName, skuStatus, stockStatus, extMap);
                GoodsPointDataRetData goodsPointDataRetData = com.xstore.sevenfresh.commonbusiness.rustmodule.UtilsKt.categoryMatchingPurchaseMakeGoodsPointData(goodsModel);
                if (!goodsPointDataRetData.getSuccess()) {
                    reportSgm("搭配购埋点_rust_exception", goodsPointDataRetData.toString(), jdMaPageImp);
                    return;
                }
                if (goodsPointDataRetData.getData() == null) {
                    reportSgm("搭配购埋点_rust_返回结果为空", goodsModel.toString(), jdMaPageImp);
                    return;
                }
                maEntity.skuId = goodsPointDataRetData.getData().getSkuId() + "";
                maEntity.skuName = goodsPointDataRetData.getData().getSkuName();
                maEntity.skuStatus = goodsPointDataRetData.getData().getSkuStatus() + "";
                maEntity.skuStockStatus = goodsPointDataRetData.getData().getSkuStockStatus();
                maEntity.skuPointStatus = goodsPointDataRetData.getData().getSkuPointStatus() + "";
                maEntity.isUseRust = 1;
            } catch (Exception e) {
                e.printStackTrace();
                reportSgm("搭配购埋点_rust_未被捕获的异常", e.toString(), jdMaPageImp);
            }
        }
    }

    public void reportSgm(String errorCode, String ext1, JDMaUtils.JdMaPageImp jdMaPageImp) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9548;//固定值,也是默认值,SGM预先创建好的类型,表示自定义的业务异常
            errorLog.errorCode = errorCode;
            errorLog.ext1 = ext1;
            errorLog.location = jdMaPageImp.getPageName();
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

}
