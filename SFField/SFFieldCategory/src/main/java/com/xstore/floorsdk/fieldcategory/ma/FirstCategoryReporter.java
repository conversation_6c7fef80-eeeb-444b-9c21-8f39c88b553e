package com.xstore.floorsdk.fieldcategory.ma;


import com.jd.framework.json.JDJSONObject;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryKeyWordItemBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;

/**
 * 分类领域埋点类
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class FirstCategoryReporter {

    private JDMaUtils.JdMaPageImp jdMaPageImp;
    //AB 实验上报 7fNewCategory 中的 buriedExpLabel 埋点上报到 touchstone_expids 这个字段 所有相关的都要
    private String buriedExpLabel;

    public void setBuriedExpLabel(String buriedExpLabel) {
        this.buriedExpLabel = buriedExpLabel;
    }

    public void initData(JDMaUtils.JdMaPageImp jdMaPageImp) {
        this.jdMaPageImp = jdMaPageImp;
    }

    public void firstCategoryExpose(CategoryBean category,int index) {
        if (category != null) {
            CategoryMaEntity maEntity = new CategoryMaEntity();
            BaseMaPublicParam publicparam = maEntity.getPublicParam();
            publicparam.FIRSTMODULEID = category.getMid() + "";
            publicparam.FIRSTMODULENAME = category.getName();
            maEntity.index = index;
            maEntity.setPublicParam(publicparam);
            maEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FExposure("categoryMainPage_firstCategoryExpose", null, maEntity, null, jdMaPageImp);
        }
    }


    /**
     * 分类主页-一级类目点击切换
     *
     * @param category
     */
    public void firstCategoryClick(CategoryBean category,int index) {
        if (category != null) {
            CategoryMaEntity maEntity = new CategoryMaEntity();
            BaseMaPublicParam publicparam = maEntity.getPublicParam();
            publicparam.FIRSTMODULEID = category.getMid() + "";
            publicparam.FIRSTMODULENAME = category.getName();
            maEntity.index = index;
            maEntity.setPublicParam(publicparam);
            maEntity.touchstone_expids = buriedExpLabel;
            JDMaUtils.save7FClick("categoryMainPage_firstCategoryClick", jdMaPageImp, maEntity);
        }
    }

    /**
     * 分类页-顶部一级类目下拉-查看全部
     */
    public void allFirstCategoryClick() {
        JDMaUtils.save7FClick("categoryMainPage_firstCategory_all", jdMaPageImp, null);
    }

    /**
     * 分类页-顶部一级类目下拉-点击收起
     */
    public void foldFirstCategoryClick() {
        JDMaUtils.save7FClick("categoryMainPage_firstCategory_fold", jdMaPageImp, null);
    }

    /**
     * 分类结果页-搜索行点击（点击搜索框）
     */
    public void clickSearchRow() {
        JDMaUtils.save7FClick("categoryListPage_searchComponent_clickSearchRow", jdMaPageImp, null);
    }

    /**
     * 分类结果页-搜索按钮_暗文词曝光
     */
    public void hotWordsExpose(CategoryKeyWordItemBean keyWordItem) {
        if (keyWordItem != null) {
            JDJSONObject jdjsonObject = new JDJSONObject();
            jdjsonObject.put("enkwd", keyWordItem.getSearchWord());
            jdjsonObject.put("hotwords", keyWordItem.getKeyword());
            jdjsonObject.put("type", keyWordItem.getUrl() != null ? 2 : 1);
            JDMaUtils.save7FExposure("categoryListPage_searchComponent_hotWordsExpose", null, null, jdjsonObject.toString(), jdMaPageImp);
        }
    }

}
