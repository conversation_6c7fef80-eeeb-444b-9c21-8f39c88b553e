package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.constant.SpinnerStyle;
import com.xstore.floorsdk.fieldcategory.R;


public class CategoryRefreshHeader extends LinearLayout implements RefreshHeader {

    private TextView tvLoadingText;
    private ProgressBar progressBar;
    private ImageView ivArrow;
    /**
     * 是否下拉到上一个分类
     */
    private boolean pullLastCate = false;
    /**
     * 是否触发一级分类滑动
     */
    private boolean scrollFirstCate = false;

    private StateChanageListener stateChanageListener;

    public CategoryRefreshHeader(Context context) {
        super(context);
        initView();
    }

    public CategoryRefreshHeader(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CategoryRefreshHeader(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        setGravity(Gravity.CENTER_HORIZONTAL | Gravity.BOTTOM);
        LayoutInflater.from(getContext()).inflate(R.layout.sf_field_category_refresh_header, this);

        tvLoadingText = findViewById(R.id.tv_refresh_tip);
        progressBar = findViewById(R.id.header_progressbar);
        ivArrow = findViewById(R.id.header_down_arrow);
    }

    public void setPullLastCate(boolean pullLastCate) {
        this.pullLastCate = pullLastCate;
    }
    public void setScrollFirstCate(boolean scrollFirstCate) {
        this.scrollFirstCate = scrollFirstCate;
    }

    @NonNull
    @Override
    public View getView() {
        return this;
    }

    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    @Override
    public void setPrimaryColors(int... colors) {

    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
        kernel.requestFloorDuration(0);
        kernel.requestRemeasureHeightFor(this);
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        return 0;
    }

    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState,
                               @NonNull RefreshState newState) {
        if (stateChanageListener != null) {
            stateChanageListener.onStateChanged(refreshLayout, oldState, newState, pullLastCate);
        }
        setVisibility(VISIBLE);
        switch (newState) {
            case PullDownToRefresh:
                if (scrollFirstCate) {
                    setVisibility(GONE);
                } else {
                    if (pullLastCate) {
                        tvLoadingText.setText(R.string.sf_field_category_pull_down_to_top_cate);
                        tvLoadingText.setVisibility(VISIBLE);
                        ivArrow.setVisibility(VISIBLE);
                        progressBar.setVisibility(GONE);
                    } else {
                        tvLoadingText.setVisibility(GONE);
                        ivArrow.setVisibility(GONE);
                        progressBar.setVisibility(VISIBLE);
                    }
                }
                break;
            case ReleaseToRefresh:
                if (scrollFirstCate) {
                    setVisibility(GONE);
                } else {
                    if (pullLastCate) {
                        tvLoadingText.setText(R.string.sf_field_category_release_to_top_cate);
                        tvLoadingText.setVisibility(VISIBLE);
                        ivArrow.setVisibility(VISIBLE);
                        progressBar.setVisibility(GONE);
                    } else {
                        tvLoadingText.setVisibility(GONE);
                        ivArrow.setVisibility(GONE);
                        progressBar.setVisibility(VISIBLE);
                    }
                }

                break;
            case Refreshing:
                if (scrollFirstCate) {
                    setVisibility(GONE);
                } else {
                    tvLoadingText.setText(R.string.sf_field_category_loading_now);
                    tvLoadingText.setVisibility(GONE);
                    ivArrow.setVisibility(GONE);
                    progressBar.setVisibility(VISIBLE);
                }
                break;
            case None:
                tvLoadingText.setVisibility(GONE);
                ivArrow.setVisibility(GONE);
                progressBar.setVisibility(GONE);
                break;
            default:
                break;
        }
    }

    public void setStateChanageListener(StateChanageListener stateChanageListener) {
        this.stateChanageListener = stateChanageListener;
    }

    public interface StateChanageListener {
        void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState,
                            @NonNull RefreshState newState, boolean pullLastCate);
    }
}
