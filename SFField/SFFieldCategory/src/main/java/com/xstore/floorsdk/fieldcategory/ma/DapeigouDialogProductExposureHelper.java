package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.DapeigouAdapter;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class DapeigouDialogProductExposureHelper {

    private Map<String, Boolean> exposuredSkuMap = new HashMap<>();

    private CategoryReporterInterface categoryFieldReporter;
    private String coreSkuId;

    public DapeigouDialogProductExposureHelper(CategoryReporterInterface categoryFieldReporter, String coreSkuId) {
        this.categoryFieldReporter = categoryFieldReporter;
        this.coreSkuId = coreSkuId;
    }

    public void updateProductList() {
        if (exposuredSkuMap == null) {
            exposuredSkuMap = new HashMap<>();
        } else {
            exposuredSkuMap.clear();
        }
    }

    public void exposureByHand(CategoryContainerInterface categoryContainerInterface, RecyclerView recyclerView, int newState) {
        if (categoryFieldReporter == null) {
            return;
        }
        if (recyclerView.getAdapter() instanceof DapeigouAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            DapeigouAdapter productAdapter = (DapeigouAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof GridLayoutManager) {
                GridLayoutManager gridLayoutManager = (GridLayoutManager) recyclerView.getLayoutManager();
                int first = gridLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = gridLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    SkuInfoBean productInfoBean = productAdapter.getItem(i);
                    if (productInfoBean == null) {
                        continue;
                    }

                    if (StringUtil.isNullByString(productInfoBean.getSkuId())) {
                        continue;
                    }
                    if (exposuredSkuMap.containsKey(productInfoBean.getSkuId()) && exposuredSkuMap.get(productInfoBean.getSkuId())) {
                        continue;
                    }
                    exposuredSkuMap.put(productInfoBean.getSkuId(), true);
                    if (categoryFieldReporter != null) {
                        // pageIndex 埋点使用 值不正确 设置为正确位置
                        categoryFieldReporter.dapeigouDialogProductExposure(categoryContainerInterface, productInfoBean, coreSkuId);
                    }
                }
            }
        }
    }
}
