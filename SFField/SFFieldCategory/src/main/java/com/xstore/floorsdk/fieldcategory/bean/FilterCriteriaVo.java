package com.xstore.floorsdk.fieldcategory.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/23
 */
public class FilterCriteriaVo implements Serializable {
    /**
     * 选条件id
     */
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否选中
     */
    private boolean selected;
    /**
     * 选中图片
     */
    private String selectedImg;
    /**
     * 未选中图片
     */
    private String notSelectedImg;
    /**
     * 查询条件
     */
    private Object queryCondition;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getSelectedImg() {
        return selectedImg;
    }

    public void setSelectedImg(String selectedImg) {
        this.selectedImg = selectedImg;
    }

    public String getNotSelectedImg() {
        return notSelectedImg;
    }

    public void setNotSelectedImg(String notSelectedImg) {
        this.notSelectedImg = notSelectedImg;
    }

    public Object getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(Object queryCondition) {
        this.queryCondition = queryCondition;
    }
}
