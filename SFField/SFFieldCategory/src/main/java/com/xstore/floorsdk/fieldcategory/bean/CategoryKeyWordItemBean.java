package com.xstore.floorsdk.fieldcategory.bean;

import android.text.TextUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/03/06
 */
public class CategoryKeyWordItemBean implements Serializable {
    /**
     * 别名
     */
    private String keyword;
    /**
     *
     */
    private String icon;
    /**
     *
     */
    private String url;
    /**
     * 暗纹词 ！
     */
    private String searchWord;

    /**
     * 是否曝光
     */
    private boolean exposure;

    public CategoryKeyWordItemBean() {}

    public CategoryKeyWordItemBean(String keyword, String icon, String url, String searchWord, boolean exposure) {
        this.keyword = keyword;
        this.icon = icon;
        this.url = url;
        this.searchWord = searchWord;
        this.exposure = exposure;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSearchWord() {
        return searchWord;
    }

    public void setSearchWord(String searchWord) {
        this.searchWord = searchWord;
    }

    public boolean isExposure() {
        return exposure;
    }

    public void setExposure(boolean exposure) {
        this.exposure = exposure;
    }

    /**
     * @return 获取当前要展示的搜索词  优先展示别名  兜底暗纹词
     */
    public String getDisplayWord() {
        if (!TextUtils.isEmpty(keyword)) {
            return keyword;
        }
        return searchWord;
    }
}
