package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.adapter.CategoryFilterTimeAdapter;
import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2022/12/23
 */
public class DropDownCategoryFilterTimePop extends PopupWindow {

    private RecyclerView rvFilterTime;
    private Context context;
    private View mask;
    private ArrayList<FilterCriteriaVo> firstFilterCriteria = new ArrayList<>();
    private FilterCriteriaVo selectFilter;

    public DropDownCategoryFilterTimePop(Context context, ArrayList<FilterCriteriaVo> firstFilterCriteria, FilterCriteriaVo selectFilter) {
        super(context);
        this.context = context;
        View contentView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_pop_filter_time, null);
        setContentView(contentView);
        initView(contentView);

        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        this.setFocusable(true);
        this.setClippingEnabled(false);
        this.setOutsideTouchable(true);

        rvFilterTime.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        CategoryFilterTimeAdapter filterTimeAdapter = new CategoryFilterTimeAdapter(context, firstFilterCriteria, selectFilter);
        rvFilterTime.setAdapter(filterTimeAdapter);
        filterTimeAdapter.setFilterClickListener(new CategoryFilterTimeAdapter.FilterClickListener() {
            @Override
            public void onItemClick(FilterCriteriaVo selectFilter) {
                dismissDialog();
                if (selectFilterClickListener != null) {
                    selectFilterClickListener.onItemSelect(selectFilter);
                }
            }
        });
    }

    private void initView(View view) {
        rvFilterTime = view.findViewById(R.id.rv_filter_time);
        mask = view.findViewById(R.id.filter_time_mask);
        mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissDialog();
            }
        });
    }

    @Override
    public void showAsDropDown(View anchor) {
        try {
            if (rvFilterTime != null) {
                TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                        Animation.RELATIVE_TO_SELF, -1, Animation.RELATIVE_TO_SELF, 0);
                animation.setDuration(200);
                rvFilterTime.startAnimation(animation);
            }
            this.setWidth(anchor.getWidth());

            if (Build.VERSION.SDK_INT < 24) {
                super.showAsDropDown(anchor);
            } else {
                int[] location = new int[2];
                anchor.getLocationInWindow(location);
                super.showAtLocation(anchor, Gravity.NO_GRAVITY, location[0], location[1] + anchor.getHeight());
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }
    }

    private void dismissDialog() {
        if (rvFilterTime != null) {
            TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                    Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, -1);
            animation.setDuration(200);
            rvFilterTime.startAnimation(animation);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    dismiss();
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        } else {
            dismiss();
        }
    }

    private SelectFilterClickListener selectFilterClickListener;

    public void setFilterClickListener(SelectFilterClickListener listener) {
        selectFilterClickListener = listener;
    }

    public interface SelectFilterClickListener {
        void onItemSelect(FilterCriteriaVo selectFilter);
    }

}
