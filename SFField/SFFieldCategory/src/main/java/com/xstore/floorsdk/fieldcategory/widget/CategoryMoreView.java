package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.xstore.floorsdk.fieldcategory.R;

/**
 * 二级分类的尾部提示
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CategoryMoreView extends RelativeLayout {
    /**
     * 页尾上拉提示
     */
    private TextView tvCateFooterTip;
    /**
     * 页尾上拉提示图标
     */
    private ImageView ivCateFooter;

    public CategoryMoreView(Context context) {
        super(context);
        initView();
    }

    public CategoryMoreView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CategoryMoreView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    /**
     * 初始化
     */
    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.sf_field_category_cate_list_footer, this);
        ivCateFooter = findViewById(R.id.iv_cate_footer);
        tvCateFooterTip = findViewById(R.id.tv_cate_footer_tip);
    }

    /**
     *
     * @param nextSecondCateName 下个二级分类名称，如果为空，则表示没有了
     */
    public void setData(String nextSecondCateName){
        if (TextUtils.isEmpty(nextSecondCateName)) {
            tvCateFooterTip.setText(R.string.sf_field_category_no_more_content);
            ivCateFooter.setVisibility(View.GONE);
        } else {
            tvCateFooterTip.setText(getContext().getResources().getString(R.string.sf_field_category_slide_up_see_next_holder, nextSecondCateName));
            ivCateFooter.setVisibility(View.VISIBLE);
        }
    }
}
