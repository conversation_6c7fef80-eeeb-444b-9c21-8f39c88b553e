//package com.xstore.floorsdk.fieldcategory.ma;
//
//import android.text.TextUtils;
//
//import com.jd.framework.json.JDJSONObject;
//import com.xstore.floorsdk.fieldcategory.CategoryProductContainer;
//import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
//import com.xstore.floorsdk.fieldcategory.bean.CategoryKeyWordItemBean;
//import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
//import com.xstore.sevenfresh.datareport.JDMaUtils;
//import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
//import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
//import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
//import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;
//import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;
//import com.xstore.sevenfresh.productcard.utils.StringUtil;
//
//import org.jetbrains.annotations.NotNull;
//
//import java.util.HashMap;
//
///**
// * 分类领域埋点类
// *
// * <AUTHOR>
// * @date 2022/11/15
// */
//public class CategoryFieldReporter implements CategoryReporterInterface {
//
//    private JDMaUtils.JdMaPageImp jdMaPageImp;
////    private CategoryHomeContainer categoryContainer;
//    private CategoryProductContainer productContainer;
//
//
////    public void initData(JDMaUtils.JdMaPageImp jdMaPageImp, CategoryHomeContainer categoryContainer, CategoryProductContainer productContainer) {
////        this.jdMaPageImp = jdMaPageImp;
////        this.categoryContainer = categoryContainer;
////        this.productContainer = productContainer;
////    }
//
//    /**
//     * 分类主页-一级类目点击切换
//     *
//     * @param category
//     */
//    @Override
//    public void firstCategoryClick(CategoryBean category) {
//        if (category != null) {
//            CategoryMaEntity maEntity = new CategoryMaEntity();
//            BaseMaPublicParam publicparam = maEntity.getPublicParam();
//            publicparam.FIRSTMODULEID = category.getMid() + "";
//            publicparam.FIRSTMODULENAME = category.getName();
//            maEntity.setPublicParam(publicparam);
//            JDMaUtils.save7FClick("categoryMainPage_firstCategoryClick", jdMaPageImp, maEntity);
//        }
//    }
//
//    /**
//     * 分类页-顶部一级类目下拉-查看全部
//     */
//    @Override
//    public void allFirstCategoryClick() {
//        JDMaUtils.save7FClick("categoryMainPage_firstCategory_all", jdMaPageImp, null);
//    }
//
//    /**
//     * 分类页-顶部一级类目下拉-点击收起
//     */
//    @Override
//    public void foldFirstCategoryClick() {
//        JDMaUtils.save7FClick("categoryMainPage_firstCategory_fold", jdMaPageImp, null);
//    }
//
//    /**
//     * 分类主页-二级类目入口点击
//     */
//    @Override
//    public void secondCategoryClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = categoryContainer.firstMid;
//        publicparam.FIRSTMODULENAME = categoryContainer.firstCateName;
//        publicparam.SECONDMODULEID = categoryContainer.secondMid;
//        publicparam.SECONDMODULENAME = categoryContainer.secondCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryMainPage_secondCategoryClick", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类主页-三级类目入口点击
//     */
//    @Override
//    public void thirdCategoryClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = categoryContainer.firstMid;
//        publicparam.FIRSTMODULENAME = categoryContainer.firstCateName;
//        publicparam.SECONDMODULEID = categoryContainer.secondMid;
//        publicparam.SECONDMODULENAME = categoryContainer.secondCateName;
//        publicparam.THIRDMODULEID = categoryContainer.thirdMid;
//        publicparam.THIRDMODULENAME = categoryContainer.thirdCategoryName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryMainPage_thirdCategoryClick", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类页-三级类目展开
//     */
//    @Override
//    public void allThirdCategoryClick() {
//        JDMaUtils.save7FClick("categoryMainPage_thirdCategory_all", jdMaPageImp, null);
//    }
//
//    /**
//     * 分类列表-失效分堆-展开
//     */
//    @Override
//    public void invalidCommodityUnfoldClick(int sinkCount) {
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("num", sinkCount);
//        BaseMaEntity baseMaEntity = new BaseMaEntity();
//        baseMaEntity.setMa7FextParam(hashMap);
//        BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        baseMaEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_invalidCommodityUnfold", jdMaPageImp, baseMaEntity);
//    }
//
//    /**
//     * 分类列表-筛选-综合
//     */
//    @Override
//    public void sortTypeDefaultClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_sort_type_default", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表-筛选-价格升序
//     */
//    @Override
//    public void sortPriceAscClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_sort_price_asc", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表-筛选-价格降序
//     */
//    @Override
//    public void sortPriceDescClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_sort_price_desc", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表-筛选-促销排序
//     */
//    @Override
//    public void sortPromotionClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_promotion_desc", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表-筛选-销量排序
//     */
//    @Override
//    public void sortAmountClick() {
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        maEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_sort_amount_desc", jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类结果页-时效筛选-入口点击
//     */
//    @Override
//    public void promiseSelectEntranceClick() {
//        JDMaUtils.save7FClick("categoryListPage_promiseSelect_entranceClick", jdMaPageImp, null);
//    }
//
//    /**
//     * 分类结果页-时效筛选-入口点击
//     */
//    @Override
//    public void promiseSelectPromiseClick(String filterId) {
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("promiseType", filterId);
//        BaseMaEntity baseMaEntity = new BaseMaEntity();
//        baseMaEntity.setMa7FextParam(hashMap);
//        JDMaUtils.save7FClick("categoryListPage_promiseSelect_promiseClick", jdMaPageImp, baseMaEntity);
//    }
//
//    /**
//     * 分类列表商品点击进商详
//     */
//    @Override
//    public void clickCommodity(SkuInfoBean productInfoBean) {
//        String eventId = "categoryListPage_clickCommodity";
//        CategoryMaEntity maEntity = getCategoryMaEntity("2", productInfoBean, eventId, null);
//        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表商品点击加车
//     */
//    @Override
//    public void clickAddCart(SkuInfoBean productInfoBean) {
//        String eventId = "categoryListPage_addCart";
//        CategoryMaEntity maEntity = getCategoryMaEntity("1", productInfoBean, eventId, 1);
//        JDMaUtils.save7FClick(eventId, jdMaPageImp, maEntity);
//    }
//
//    /**
//     * 分类列表商品曝光
//     */
//    @Override
//    public void productExposure(SkuInfoBean productInfoBean) {
//        String eventId = "Classification_second";
//        CategoryMaEntity maEntity = getCategoryMaEntity(null, productInfoBean, eventId, null);
//        JDMaUtils.save7FExposure(eventId, null, maEntity, null, jdMaPageImp);
//    }
//
//    @NotNull
//    private CategoryMaEntity getCategoryMaEntity(String clickType, SkuInfoBean productInfoBean, String eventId, Integer num) {
//        if (productInfoBean == null) {
//            return null;
//        }
//        CategoryMaEntity maEntity = new CategoryMaEntity();
//        maEntity.skuId = productInfoBean.getSkuId();
//        maEntity.skuName = productInfoBean.getSkuName();
//        if (productInfoBean.getLogicInfo() != null) {
//            maEntity.ifTakeaway = productInfoBean.getLogicInfo().getIsTakeaway();
//            maEntity.productCardType = productInfoBean.getLogicInfo().getProductCardType();
//        }
//        maEntity.listPageIndex = productInfoBean.getPageIndex();
//        maEntity.pos = productInfoBean.getPageIndex();
//        maEntity.listPageNum = productInfoBean.getPage();
//        maEntity.page = productInfoBean.getPage();
//        maEntity.skuStockStatus = productInfoBean.getStatus();
//        maEntity.query = productInfoBean.getMid() + "";
//        maEntity.keyword = productInfoBean.getMid() + "";
//        if(productInfoBean.getLogicInfo() != null){
//            maEntity.skuType = productInfoBean.getLogicInfo().isClearanceFlag() ? 1 : 0;
//        }
//        if (productInfoBean.getCartInfo() != null && productInfoBean.getCartInfo().getType() == SkuEnumInterface.CartBtnType.PRE_SALE) {
//            maEntity.isPreSale = 1;
//        } else {
//            maEntity.isPreSale = 0;
//        }
//
//        maEntity.pvid = productInfoBean.getPvId();
//        maEntity.logid = productInfoBean.getLogId();
//        maEntity.recall_cnt = productInfoBean.getTotalCount();
//        maEntity.refer_id = (!TextUtils.isEmpty(productInfoBean.getPvId())) ? productInfoBean.getPvId() + "|" + eventId + "|" + System.currentTimeMillis() : "-";
//        if (productInfoBean.getSalePrice() != null) {
//            maEntity.price = productInfoBean.getSalePrice().getValue();
//        }
//        maEntity.ori_price = productInfoBean.getOriPrice();
//        if (productContainer != null) {
//            maEntity.sort_type = productContainer.sortType;
//            if (productContainer.mSelectedFilterCriteria != null) {
//                maEntity.promiseType = productContainer.mSelectedFilterCriteria.getId();
//            }
//        }
//        maEntity.caller = "30";
//        maEntity.num = num;
//        if (productContainer != null && productContainer.mCategoryWareInfo != null) {
//            maEntity.hc_cid3 = productContainer.mCategoryWareInfo.getHcCid3() == null ? "-" : productContainer.mCategoryWareInfo.getHcCid3();
//            maEntity.mtest = productContainer.mCategoryWareInfo.getMtest() == null ? "-" : productContainer.mCategoryWareInfo.getMtest();
//            maEntity.is_active_filt = productContainer.mCategoryWareInfo.getIsActiveFilt() == null ? "0" : productContainer.mCategoryWareInfo.getIsActiveFilt();
//            maEntity.firfilter = productContainer.mCategoryWareInfo.getFirfilter() == null ? "-" : productContainer.mCategoryWareInfo.getFirfilter();
//            maEntity.secfilter = productContainer.mCategoryWareInfo.getSecfilter() == null ? "-" : productContainer.mCategoryWareInfo.getSecfilter();
//            maEntity.source = productContainer.mCategoryWareInfo.getSource() == null ? "0" : productContainer.mCategoryWareInfo.getSource();
//        }
//        BaseMaPublicParam publicparam = maEntity.getPublicParam();
//        if (clickType != null) {
//            publicparam.CLICKTYPE = clickType;
//        }
//        publicparam.FIRSTMODULEID = productInfoBean.getFirstMid();
//        publicparam.FIRSTMODULENAME = productInfoBean.getFirstCateName();
//        publicparam.SECONDMODULEID = productInfoBean.getSecondMid();
//        publicparam.SECONDMODULENAME = productInfoBean.getSecondCateName();
//        publicparam.THIRDMODULEID = productInfoBean.getThirdMid();
//        publicparam.THIRDMODULENAME = productInfoBean.getThirdCateName();
//        maEntity.setPublicParam(publicparam);
//        return maEntity;
//    }
//
//
//    /**
//     * 分类结果页-搜索行点击（点击搜索框）
//     */
//    @Override
//    public void clickSearchRow() {
//        JDMaUtils.save7FClick("categoryListPage_searchComponent_clickSearchRow", jdMaPageImp, null);
//    }
//
//    /**
//     * 分类结果页-搜索按钮_暗文词曝光
//     */
//    @Override
//    public void hotWordsExpose(CategoryKeyWordItemBean keyWordItem) {
//        if (keyWordItem != null) {
//            JDJSONObject jdjsonObject = new JDJSONObject();
//            jdjsonObject.put("enkwd", keyWordItem.getSearchWord());
//            jdjsonObject.put("hotwords", keyWordItem.getKeyword());
//            jdjsonObject.put("type", keyWordItem.getUrl() != null ? 2 : 1);
//            JDMaUtils.save7FExposure("categoryListPage_searchComponent_hotWordsExpose", null, null, jdjsonObject.toString(), jdMaPageImp);
//        }
//    }
//
//    /**
//     * 分类结果页-轮播图曝光
//     */
//    @Override
//    public void bannerExpose(String url) {
//        HashMap<String, String> hashMap = new HashMap<>();
//        hashMap.put("url", url);
//        BaseMaEntity baseMaEntity = new BaseMaEntity();
//        BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        baseMaEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FExposure("categoryListPage_bannerExpose", hashMap, baseMaEntity, null, jdMaPageImp);
//    }
//
//    /**
//     * 分类结果页-轮播图点击
//     */
//    @Override
//    public void bannerClick(String url) {
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("url", url);
//        BaseMaEntity baseMaEntity = new BaseMaEntity();
//        baseMaEntity.setMa7FextParam(hashMap);
//        BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//        publicparam.FIRSTMODULEID = productContainer.firstMid;
//        publicparam.FIRSTMODULENAME = productContainer.firstCateName;
//        publicparam.SECONDMODULEID = productContainer.secondMid;
//        publicparam.SECONDMODULENAME = productContainer.secondCateName;
//        publicparam.THIRDMODULEID = productContainer.thirdMid;
//        publicparam.THIRDMODULENAME = productContainer.thirdCateName;
//        baseMaEntity.setPublicParam(publicparam);
//        JDMaUtils.save7FClick("categoryListPage_bannerClick", jdMaPageImp, baseMaEntity);
//    }
//
//    /**
//     * 分类结果页-商品卡片-榜单入口点击
//     */
//    @Override
//    public void clickRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
//        if (productInfoBean != null) {
//            HashMap<String, Object> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            hashMap.put("rankName", marketEntrance != null ? marketEntrance.getText() : "");
//            hashMap.put("rankSortId", marketEntrance != null ? String.valueOf(marketEntrance.getSubType()) : "");
//            BaseMaEntity baseMaEntity = new BaseMaEntity();
//            baseMaEntity.setMa7FextParam(hashMap);
//            JDMaUtils.save7FClick("categoryResultPage_categorySkuList_rankingListEntrance", "", skuId, null, jdMaPageImp, baseMaEntity);
//        }
//    }
//
//    /**
//     * 分类结果页-商品卡片-榜单入口曝光
//     */
//    @Override
//    public void showRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
//        if (productInfoBean != null) {
//            HashMap<String, String> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            hashMap.put("rankName", marketEntrance != null ? marketEntrance.getText() : "");
//            hashMap.put("rankSortId", marketEntrance != null ? String.valueOf(marketEntrance.getSubType()) : "");
//            JDMaUtils.save7FExposure("categoryResultPage_categorySkuList_rankingExpose", hashMap, null, null, jdMaPageImp);
//        }
//    }
//
//    /**
//     * 分类结果页-商品卡片-立即预定点击
//     */
//    @Override
//    public void clickBookNow(SkuInfoBean productInfoBean) {
//        if (productInfoBean != null) {
//            HashMap<String, Object> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            if (productInfoBean.getSalePrice() != null) {
//                hashMap.put("price", productInfoBean.getSalePrice().getValue());
//            }
//            hashMap.put("ori_price", productInfoBean.getOriPrice());
//            hashMap.put("listPageIndex", productInfoBean.getPageIndex());
//
//            BaseMaEntity baseMaEntity = new BaseMaEntity();
//            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//            publicparam.FIRSTMODULEID = productInfoBean.getFirstMid();
//            publicparam.FIRSTMODULENAME = productInfoBean.getFirstCateName();
//            publicparam.SECONDMODULEID = productInfoBean.getSecondMid();
//            publicparam.SECONDMODULENAME = productInfoBean.getSecondCateName();
//            publicparam.THIRDMODULEID = productInfoBean.getThirdMid();
//            publicparam.THIRDMODULENAME = productInfoBean.getThirdCateName();
//            baseMaEntity.setPublicParam(publicparam);
//            baseMaEntity.setMa7FextParam(hashMap);
//            JDMaUtils.save7FClick("categoryResultPage_categorySkuList_preBooking_booknow_addCart", "", skuId, null, jdMaPageImp, baseMaEntity);
//        }
//    }
//
//    /**
//     * 分类结果页-商品卡片-立即预定曝光
//     */
//    @Override
//    public void showBookNow(SkuInfoBean productInfoBean) {
//        if (productInfoBean != null) {
//            HashMap<String, String> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            JDMaUtils.save7FExposure("categoryResultPage_categorySkuList_preBooking_Expose", hashMap, null, null, jdMaPageImp);
//        }
//    }
//    /**
//     * 分类结果页-商品卡片-百科点击
//     */
//    @Override
//    public void clickJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
//        if (productInfoBean != null) {
//            HashMap<String, Object> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            hashMap.put("status", productInfoBean.getStatus() + "");
//            if(marketEntrance != null && StringUtil.isNotEmpty(marketEntrance.getText())){
//                hashMap.put("keyword", marketEntrance.getText());
//            }
//            BaseMaEntity baseMaEntity = new BaseMaEntity();
//            baseMaEntity.setMa7FextParam(hashMap);
//
//            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//            publicparam.FIRSTMODULEID = productInfoBean.firstMid;
//            publicparam.FIRSTMODULENAME = productInfoBean.firstCateName;
//            publicparam.SECONDMODULEID = productInfoBean.secondMid;
//            publicparam.SECONDMODULENAME = productInfoBean.secondCateName;
//            publicparam.THIRDMODULEID = productInfoBean.thirdMid;
//            publicparam.THIRDMODULENAME = productInfoBean.thirdCateName;
//            baseMaEntity.setPublicParam(publicparam);
//
//            JDMaUtils.save7FClick("categoryMainPage_clickHealthChannel", "", skuId, null, jdMaPageImp, baseMaEntity);
//        }
//    }
//
//    /**
//     * 分类结果页-商品卡片-百科曝光
//     */
//    @Override
//    public void showJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
//        if (productInfoBean != null) {
//            HashMap<String, String> hashMap = new HashMap<>();
//            String skuId = productInfoBean.getSkuId();
//            String skuName = productInfoBean.getSkuName();
//            hashMap.put("skuId", skuId);
//            hashMap.put("skuName", skuName);
//            hashMap.put("status", productInfoBean.getStatus() + "");
//            if(marketEntrance != null && StringUtil.isNotEmpty(marketEntrance.getText())){
//                hashMap.put("keyword", marketEntrance.getText());
//            }
//            BaseMaEntity baseMaEntity = new BaseMaEntity();
//            BaseMaPublicParam publicparam = baseMaEntity.getPublicParam();
//            publicparam.FIRSTMODULEID = productInfoBean.firstMid;
//            publicparam.FIRSTMODULENAME = productInfoBean.firstCateName;
//            publicparam.SECONDMODULEID = productInfoBean.secondMid;
//            publicparam.SECONDMODULENAME = productInfoBean.secondCateName;
//            publicparam.THIRDMODULEID = productInfoBean.thirdMid;
//            publicparam.THIRDMODULENAME = productInfoBean.thirdCateName;
//            baseMaEntity.setPublicParam(publicparam);
//
//            JDMaUtils.save7FExposure("categoryMainPage_healthChannelExpose", hashMap, baseMaEntity,null,jdMaPageImp);
//        }
//    }
//
//
//}