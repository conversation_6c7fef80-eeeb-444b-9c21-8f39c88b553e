package com.xstore.floorsdk.fieldcategory.interfaces;

/**
 * 分类领域初始化配置，传入初始化时就要用到的必要分类领域配置
 */
public class SFFieldCategoryConfig {

    private CategoryConfigListener listener;

    private static volatile SFFieldCategoryConfig instance;

    private SFFieldCategoryConfig() {}

    public static SFFieldCategoryConfig getInstance() {
        if (instance == null) {
            synchronized (SFFieldCategoryConfig.class) {
                if (instance == null) {
                    instance = new SFFieldCategoryConfig();
                }
            }
        }
        return instance;
    }

    public interface CategoryConfigListener {
        boolean isOpenCategoryVideo();
    }

    public void setCategoryConfigListener(CategoryConfigListener listener) {
        this.listener = listener;
    }

    public boolean isOpenCategoryVideo() {
        boolean isOpen = false;
        if (null != listener) {
            isOpen = listener.isOpenCategoryVideo();
        }
        return isOpen;
    }

}