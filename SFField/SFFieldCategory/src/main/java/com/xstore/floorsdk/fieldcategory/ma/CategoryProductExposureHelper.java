package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryReporterInterface;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class CategoryProductExposureHelper {

    private Map<String, Boolean> exposuredSkuMap = new HashMap<>();

    private CategoryReporterInterface categoryFieldReporter;

    public CategoryProductExposureHelper(CategoryReporterInterface categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
    }

    public void updateProductList() {
        if (exposuredSkuMap == null) {
            exposuredSkuMap = new HashMap<>();
        } else {
            exposuredSkuMap.clear();
        }
    }

    public void exposureByHand(RecyclerView recyclerView,int newState) {
        if (recyclerView.getAdapter() instanceof CategoryProductAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            CategoryProductAdapter productAdapter = (CategoryProductAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int first = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    SkuInfoBean productInfoBean = productAdapter.getItem(i);
                    if (productInfoBean == null || productInfoBean.getPvId() == null) {
                        continue;
                    }

                    if (productInfoBean.getViewType() == CategoryProductAdapter.VIEW_TYPE_PRODUCT) {
                        if (StringUtil.isNullByString(productInfoBean.getSkuId())) {
                            continue;
                        }
                        if (exposuredSkuMap.containsKey(productInfoBean.getSkuId())
                                && exposuredSkuMap.get(productInfoBean.getSkuId())) {
                            continue;
                        }
                        exposuredSkuMap.put(productInfoBean.getSkuId(), true);
                        if (categoryFieldReporter!=null) {
                            // pageIndex 埋点使用 值不正确 设置为正确位置
                            productInfoBean.setPageIndex(String.valueOf(i+1));
                            categoryFieldReporter.productExposure(productInfoBean);
                        }
                    }
                }
            }
        }
    }
}
