package com.xstore.floorsdk.fieldcategory.interfaces;


import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.bean.CategoryKeyWordItemBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;

public interface CategoryReporterInterface {
//    /**
//     * 分类主页-一级类目点击切换
//     *
//     * @param category
//     */
//    void firstCategoryClick(CategoryBean category);
//
//    /**
//     * 分类页-顶部一级类目下拉-查看全部
//     */
//    void allFirstCategoryClick();
//
//    /**
//     * 分类页-顶部一级类目下拉-点击收起
//     */
//    void foldFirstCategoryClick();

    /**
     * 分类主页-二级类目入口点击
     */
    void secondCategoryClick(CategoryBean currSecCate, String firstCategoryMid, String firstCategoryName, int index);

    /**
     * 分类主页-二级类目曝光
     */
    void secondCategoryExpose(CategoryBean currSecCate, String firstCategoryMid, String firstCategoryName, int index);

    /**
     * 分类主页-三级类目入口点击
     */
    void thirdCategoryClick(int index);

    void thirdCategoryExpose(CategoryBean currSecCate, int index);

    /**
     * 分类页-三级类目展开
     */
    void allThirdCategoryClick();

    /**
     * 分类列表-失效分堆-展开
     */
    void invalidCommodityUnfoldClick(int sinkCount);

    /**
     * 分类列表-筛选-综合
     */
    void sortTypeDefaultClick();

    /**
     * 分类列表-筛选-价格升序
     */
    void sortPriceAscClick();

    /**
     * 分类列表-筛选-价格降序
     */
    void sortPriceDescClick();

    /**
     * 分类列表-筛选-促销排序
     */
    void sortPromotionClick();

    /**
     * 分类列表-筛选-销量排序
     */
    void sortAmountClick();

    /**
     * 分类结果页-时效筛选-入口点击
     */
    void promiseSelectEntranceClick();

    /**
     * 分类结果页-时效筛选-入口点击
     */
    void promiseSelectPromiseClick(String filterId);

    /**
     * 分类列表商品点击进商详
     */
    void clickCommodity(SkuInfoBean productInfoBean);

    /**
     * 分类列表商品点击加车
     */
    void clickAddCart(SkuInfoBean productInfoBean);

    /**
     * 分类列表商品曝光
     */
    void productExposure(SkuInfoBean productInfoBean);


    /**
     * 分类结果页-搜索行点击（点击搜索框）
     */
    void clickSearchRow();

    /**
     * 分类结果页-搜索按钮_暗文词曝光
     */
    void hotWordsExpose(CategoryKeyWordItemBean keyWordItem);

    /**
     * 分类结果页-轮播图曝光
     */
    void bannerExpose(String url, int index);

    /**
     * 分类结果页-轮播图点击
     */
    void bannerClick(String url);

    /**
     * 分类结果页-商品卡片-榜单入口点击
     */
    void clickRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance);

    /**
     * 分类结果页-商品卡片-榜单入口曝光
     */
    void showRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance);

    /**
     * 分类结果页-商品卡片-立即预定点击
     */
    void clickBookNow(SkuInfoBean productInfoBean);

    /**
     * 分类结果页-商品卡片-立即预定曝光
     */
    void showBookNow(SkuInfoBean productInfoBean);

    /**
     * 分类结果页-商品卡片-百科点击
     */
    void clickJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance);

    /**
     * 分类结果页-商品卡片-百科曝光
     */
    void showJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance);

    void dapeigouProductExposure(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouProductClick(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouProductAddCart(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouDialogProductExposure(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouDialogProductClick(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouDialogProductAddCart(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean, String coreSkuId);

    void dapeigouProductClickMore(CategoryContainerInterface categoryContainerInterface, SkuInfoBean productInfoBean);
}
