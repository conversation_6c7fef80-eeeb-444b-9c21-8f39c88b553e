package com.xstore.floorsdk.fieldcategory.request;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
import com.xstore.sdk.floor.floorcore.FloorBaseNetwork;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.CacheConfig;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

import java.util.ArrayList;
import java.util.List;

import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.DEFAULT_EFFECT;
import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.NO_EFFECT;

/**
 * 分类接口工具类
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public class CategoryRequest {

    private static final String CATEGORY = "Category";
    /**
     * 缓存时间 12小时内有效，超过12小时清除缓存
     */
    private static final long DATA_CACHE_TIME = 12 * 60 * 60 * 1000;


    public static final String SDK_VERSION = "1.0.0";

    /**
     * 数据请求时使用的屏幕高度
     * 目前还不止分屏，所以还是写死高度
     */
    public static final int MAX_HEIGHT = 9999;

    /**
     * 请求搜索关键词接口
     *
     * @param context
     * @param callback
     */
    public static void getDefaultKeyWord(Context context, BaseFreshResultCallback callback) {
        List<String> fieldName = new ArrayList<>();
        fieldName.add("getDefaultKeyWord");
        FloorBaseNetwork.requestGql(context, FreshHttpSetting.NO_EFFECT, null, 0, fieldName,
                null, 0, SDK_VERSION, MAX_HEIGHT, callback);
    }

    /**
     * 请求一级类目接口
     *
     * @param context
     * @param callback
     * @param source
     * @param effect
     */
    public static void getFirstCategory(Context context, int source, int effect, CategoryContainerInterface categoryContainerInterface, BaseFreshResultCallback callback) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setEffect(effect);
        if (categoryContainerInterface != null) {
            httpSetting.setFunctionId(categoryContainerInterface.getFirstCategoryUrl());
        }
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setResultCallback(callback);
        httpSetting.putJsonParam("source", source);
        CacheConfig cacheConfig = new CacheConfig()
                .setNeedCache(true)
                .setNeedRequest(true)
                .setLife(DATA_CACHE_TIME)
                .setMaxCacheSize(CacheConfig.CacheAmountLimit.AMOUNT_FIFTEEN)
                .setSceneKey(CATEGORY + "_" + httpSetting.getFunctionId() + "_" + source)
                .setDimenKey(TenantIdUtils.getStoreId());
        httpSetting.setCacheConfig(cacheConfig);
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }

    /**
     * 获取一级分类下子分类
     *
     * @param context
     * @param onlyCache
     * @param effect
     * @param cid1
     * @param cid1Name
     * @param cid2
     * @param mid1
     * @param page
     * @param pageSize
     * @param pvid
     * @param logid
     * @param source
     * @param queryConditions
     * @param categoryContainerInterface
     * @param callback
     */
    public static void getChildCategory(Context context, boolean onlyCache, int effect, long cid1, String cid1Name, long cid2, String mid1, int page, int pageSize, String pvid, String logid, int source, List<Object> queryConditions, CategoryContainerInterface categoryContainerInterface, BaseFreshResultCallback callback) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setEffect(effect);
        if (categoryContainerInterface != null) {
            httpSetting.setFunctionId(categoryContainerInterface.getChildCategoryUrl());
        } else {
            // url容器为null则代表被销毁了 无需再次请求
            return;
        }
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setResultCallback(callback);
        if (cid1 != 0) {//一级类目id
            httpSetting.putJsonParam("cid1", cid1);
        }
        if (!TextUtils.isEmpty(cid1Name)) {//一级类目名称
            httpSetting.putJsonParam("cid1Name", cid1Name);
        }
        if (cid2 > 0) {
            httpSetting.putJsonParam("cid2", cid2);
        }
        if (mid1 != null) {
            httpSetting.putJsonParam("mid1", mid1);
        }
        httpSetting.putJsonParam("page", page);
        httpSetting.putJsonParam("pageSize", pageSize);
        httpSetting.putJsonParam("pvid", pvid);
        httpSetting.putJsonParam("logid", logid);
        setQueryConditionParam(queryConditions, httpSetting);
        if (page == 1) {
            CacheConfig cacheConfig = new CacheConfig()
                    .setNeedCache(page == 1)
                    .setNeedRequest(!onlyCache)
                    .setLife(DATA_CACHE_TIME)
                    .setMaxCacheSize(CacheConfig.CacheAmountLimit.AMOUNT_FIFTEEN)
                    .setSceneKey(CATEGORY + "_" + httpSetting.getFunctionId() + "_" + source)
                    .setDimenKey(TenantIdUtils.getStoreId() + "_" + cid1);
            httpSetting.setCacheConfig(cacheConfig);
        }
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }

    /**
     * 读取二级分类接口缓存数据、
     * 读取商品列表接口缓存数据、
     *
     * @param context
     * @param httpSetting
     */
    public static void getCacheData(Context context, FreshHttpSetting httpSetting) {
        if (httpSetting != null) {
            httpSetting.setEffect(NO_EFFECT);
            if (httpSetting.getCacheConfig() != null) {
                httpSetting.getCacheConfig().setIgnoreCacheSwitch(true);
                httpSetting.getCacheConfig().setNeedCache(true);
                httpSetting.getCacheConfig().setNeedRequest(false);
                FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
            }
        }

    }

    /**
     * 获取分类下商品
     *
     * @param activity
     * @param listener
     * @param effect                     重要不要随意改动
     * @param cid
     * @param mid
     * @param sortType
     * @param pagination
     * @param page
     * @param pageSize
     * @param pvid
     * @param logid
     * @param source
     * @param queryConditions
     * @param categoryContainerInterface
     * @param firstCateName
     * @param secondCateName
     * @param thirdCategoryName
     */
    public static void getWareInfoByCid(Activity activity, BaseFreshResultCallback listener,
                                        int effect, long cid, String mid, String sortType,
                                        boolean pagination, int page, int pageSize, String pvid,
                                        String logid, int source, List<Object> queryConditions, CategoryContainerInterface categoryContainerInterface,
                                        String firstCateName, String secondCateName, String thirdCategoryName) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        if (categoryContainerInterface != null) {
            httpSetting.setFunctionId(categoryContainerInterface.getWareInfoByCidUrl());
        }
        appendParams(httpSetting, listener, effect, cid, mid, sortType, pagination, page, pageSize, pvid, logid, source, queryConditions, "", "", firstCateName, "", "", secondCateName, thirdCategoryName);
        FreshHttpGroupUtils.getHttpGroup().add(activity, httpSetting);
    }

    private static void appendParams(FreshHttpSetting httpSetting, BaseFreshResultCallback listener,
                                     int effect, long cid, String mid, String sortType,
                                     boolean pagination, int page, int pageSize, String pvid,
                                     String logid, int source, List<Object> queryConditions,
                                     String cid1, String mid1, String firstCateName, String cid2, String mid2, String secondCateName, String thirdCategoryName) {
        httpSetting.setEffect(effect);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setResultCallback(listener);
        httpSetting.putJsonParam("cid", cid);
        httpSetting.putJsonParam("mid", mid);
        if (!TextUtils.isEmpty(cid1)) {
            httpSetting.putJsonParam("cid1", cid1);
        }
        if (!TextUtils.isEmpty(mid1)) {
            httpSetting.putJsonParam("mid1", mid1);
        }
        if (!TextUtils.isEmpty(firstCateName)) {
            httpSetting.putJsonParam("cid1Name", firstCateName);
        }
        if (!TextUtils.isEmpty(cid2)) {
            httpSetting.putJsonParam("cid2", cid2);
        }
        if (!TextUtils.isEmpty(mid2)) {
            httpSetting.putJsonParam("mid2", mid2);
        }
        if (!TextUtils.isEmpty(secondCateName)) {
            httpSetting.putJsonParam("cid2Name", secondCateName);
        }
        if (!TextUtils.isEmpty(thirdCategoryName)) {
            httpSetting.putJsonParam("cid3Name", thirdCategoryName);
        }
        httpSetting.putJsonParam("sortType", sortType);
        httpSetting.putJsonParam("canteen", false);
        httpSetting.putJsonParam("pagination", pagination);
        httpSetting.putJsonParam("page", page);
        httpSetting.putJsonParam("pageSize", pageSize);
        httpSetting.putJsonParam("pvid", pvid);
        httpSetting.putJsonParam("logid", logid);
        setQueryConditionParam(queryConditions, httpSetting);
        if (page == 1 && effect == DEFAULT_EFFECT && pagination) {
            CacheConfig cacheConfig = new CacheConfig()
                    .setNeedCache(false)
                    .setNeedRequest(true)
                    .setLife(DATA_CACHE_TIME)
                    .setMaxCacheSize(CacheConfig.CacheAmountLimit.AMOUNT_TWENTY)
                    .setSceneKey(CATEGORY + "_" + httpSetting.getFunctionId() + "_" + source)
                    .setDimenKey(TenantIdUtils.getStoreId() + "_" + cid);
            httpSetting.setCacheConfig(cacheConfig);
        }
    }

    private static void setQueryConditionParam(List<Object> queryConditions, FreshHttpSetting httpSetting) {
        try {
            if (queryConditions != null && queryConditions.size() > 0) {
                Gson gson = new Gson();
                httpSetting.putJsonParam("query", gson.toJson(queryConditions));
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(new Exception("分类 query参数有问题：" + e.getMessage()));
        }
    }

    public static void getWareInfoByCid3s(Activity activity, BaseFreshResultCallback listener, List<CategoryBean> cate3List,
                                          int effect, long cid, String mid, String sortType,
                                          boolean pagination, int page, int pageSize, String pvid,
                                          String logid, int source, List<Object> queryConditions, CategoryContainerInterface categoryContainerInterface,
                                          String cid1, String mid1, String firstCateName, String cid2, String mid2, String secondCateName, String thirdCategoryName) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        if (categoryContainerInterface != null) {
            httpSetting.setFunctionId(categoryContainerInterface.getWareInfoByCid3s());
        }
        if (cate3List != null && cate3List.size() > 0) {
            JDJSONArray jsonArray = new JDJSONArray();
            for (CategoryBean cate : cate3List) {
                if (cate == null) {
                    continue;
                }
                JDJSONObject jdjsonObject = new JDJSONObject();
                jdjsonObject.put("id", cate.getId());
                jdjsonObject.put("name", cate.getName());
                jdjsonObject.put("mid", cate.getMid());
                jsonArray.add(jdjsonObject);
            }
            if (jsonArray.size() > 0) {
                httpSetting.putJsonParam("thirdCategoryIds", jsonArray);
            }
        }
        appendParams(httpSetting, listener, effect, cid, mid, sortType, pagination, page, pageSize, pvid, logid, source, queryConditions, cid1, mid1, firstCateName, cid2, mid2, secondCateName, thirdCategoryName);
        FreshHttpGroupUtils.getHttpGroup().add(activity, httpSetting);
    }

    /**
     * 获取分类下所有无货商品
     *
     * @param activity
     * @param listener
     * @param cid
     * @param mid
     * @param sortType
     * @param pagination
     * @param page
     * @param pageSize
     * @param filterSkuId                需要过滤的skuId数组
     * @param pvid
     * @param logid
     * @param queryConditions
     * @param categoryContainerInterface
     * @param firstCateName
     * @param secondCateName
     * @param thirdCategoryName
     */
    public static void getSinkWareInfo(Activity activity, BaseFreshResultCallback listener,
                                       long cid, String mid, String sortType, boolean pagination,
                                       int page, int pageSize, List<String> filterSkuId, String pvid, String logid,
                                       List<Object> queryConditions, CategoryContainerInterface categoryContainerInterface,
                                       String firstCateName, String secondCateName, String thirdCategoryName) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setType(FreshHttpSetting.RequestType.POST);
        httpSetting.setEffect(DEFAULT_EFFECT);
        if (categoryContainerInterface != null) {
            httpSetting.setFunctionId(categoryContainerInterface.getSinkWareInfoUrl());
            httpSetting.setBackString(categoryContainerInterface.getSinkWareInfoUrl());
        }
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setResultCallback(listener);
        httpSetting.putJsonParam("cid", cid);
        httpSetting.putJsonParam("mid", mid);
        if (!TextUtils.isEmpty(firstCateName)) {
            httpSetting.putJsonParam("cid1Name", firstCateName);
        }
        if (!TextUtils.isEmpty(secondCateName)) {
            httpSetting.putJsonParam("cid2Name", secondCateName);
        }
        if (!TextUtils.isEmpty(thirdCategoryName)) {
            httpSetting.putJsonParam("cid3Name", thirdCategoryName);
        }
        httpSetting.putJsonParam("sortType", sortType);
        httpSetting.putJsonParam("canteen", false);
        httpSetting.putJsonParam("pagination", pagination);
        httpSetting.putJsonParam("page", page);
        httpSetting.putJsonParam("pageSize", pageSize);
        if (filterSkuId != null && filterSkuId.size() > 0) {
            JDJSONArray jsonArray = new JDJSONArray();
            jsonArray.addAll(filterSkuId);
            if (jsonArray.size() > 0) {
                httpSetting.putJsonParam("filterSkuId", jsonArray);
            }
        }
        httpSetting.putJsonParam("pvid", pvid);
        httpSetting.putJsonParam("logid", logid);
        setQueryConditionParam(queryConditions, httpSetting);
        FreshHttpGroupUtils.getHttpGroup().add(activity, httpSetting);
    }


}
