package com.xstore.floorsdk.fieldcategory.bean;

import java.io.Serializable;
import java.util.List;

public class CategoryBean implements Serializable {

    /**
     * 积理分类ID
     */
    private Long id;
    /**
     * 中台分类ID
     */
    private String mid;
    /**
     * 分类logo
     */
    private String imageUrl;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 分类父类id
     */
    private long parentId;
    /**
     * 分类等级
     */
    private int level;
    /**
     * 排序
     */
    private int sort;
    /**
     * 一级类目是否可以进行点击
     */
    private boolean isClick;
    /**
     * 三级分类列表(只有二级分类下有此字段)
     */
    private List<CategoryBean> cid3;
    /**
     * 二级分类的图片-(分类运营位)
     */
    private List<BannerBean> images;
    /**
     * 一级类目打标
     */
    private String tagIcon;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getParentId() {
        return parentId;
    }

    public void setParentId(long parentId) {
        this.parentId = parentId;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public boolean isClick() {
        return isClick;
    }

    public void setClick(boolean click) {
        isClick = click;
    }

    public List<CategoryBean> getCid3() {
        return cid3;
    }

    public void setCid3(List<CategoryBean> cid3) {
        this.cid3 = cid3;
    }

    public List<BannerBean> getImages() {
        return images;
    }

    public void setImages(List<BannerBean> images) {
        this.images = images;
    }

    public String getTagIcon() {
        return tagIcon;
    }

    public void setTagIcon(String tagIcon) {
        this.tagIcon = tagIcon;
    }
}