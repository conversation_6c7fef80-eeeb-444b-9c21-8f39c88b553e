package com.xstore.floorsdk.fieldcategory.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.TranslateAnimation;
import android.widget.LinearLayout;

import androidx.core.view.NestedScrollingParent;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

/**
 * OverScrollLayout简介
 * 反弹
 *
 * <AUTHOR>
 * @date 2020-6-16 20:42:04
 */
public class OverScrollLayout extends LinearLayout implements NestedScrollingParent {

    /**
     * ANIM_TIME 动画时长
     */
    private static final int ANIM_TIME = 400;

    private RecyclerView childView;

    /**
     * original 原始布局位置
     */
    private Rect original = new Rect();

    /**
     * isMoved 是否在弹性动画
     */
    private boolean isMoved = false;

    /**
     * startYpos 起始滑动位置
     */
    private float startYpos;

    /**
     * 阻尼系数
     */
    private static final float DAMPING_COEFFICIENT = 0.3f;

    /**
     * isSuccess 是否执行成功
     */
    private boolean isSuccess = false;

    /**
     * mScrollListener 扫码
     */
    private ScrollListener mScrollListener;
    /**
     * needConsumed
     */
    private boolean needConsumed;
    /**
     * offset
     */
    private int offset;

    private int[] viewLocation;
    /**
     * keepOverState
     */
    private boolean keepOverState;
    /**
     * topRange 在屏幕中的上边界
     */
    private int topRange;
    /**
     * bottomRange 在屏幕中的下边界
     */
    private int bottomRange;
    /**
     * needReplay 需要重新分发滑动
     */
    private boolean needReplay;
    /**
     * location 位置信息
     */
    private int[] location = new int[2];
    /**
     * RecyclerView最后一条Item底部间距
     */
    private int lastItemBottomMargin = 0;


    public OverScrollLayout(Context context) {
        this(context, null);
    }

    public OverScrollLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OverScrollLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        childView = (RecyclerView) getChildAt(0);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        original.set(childView.getLeft(), childView.getTop(), childView.getRight(), childView.getBottom());
        getLocationOnScreen(location);
        topRange = location[1];
        bottomRange = location[1] + getHeight();
    }

    public void setScrollListener(ScrollListener listener) {
        mScrollListener = listener;
    }

    @Override
    public boolean onStartNestedScroll(View child, View target, int nestedScrollAxes) {
        return nestedScrollAxes == ViewCompat.SCROLL_AXIS_VERTICAL;
    }

    @Override
    public void onNestedPreScroll(View target, int dx, int dy, int[] consumed) {
        if (needConsumed) {
            consumed[0] = dx;
            consumed[1] = dy;
        } else {
            //no thing
        }
    }

    @Override
    public void onNestedScrollAccepted(View child, View target, int axes) {
    }

    @Override
    public void onStopNestedScroll(View child) {
    }

    @Override
    public void onNestedScroll(View target, int dxConsumed, int dyConsumed, int dxUnconsumed, int dyUnconsumed) {
    }

    @Override
    public boolean onNestedFling(View target, float velocityX, float velocityY, boolean consumed) {
        return true;
    }

    @Override
    public boolean onNestedPreFling(View target, float velocityX, float velocityY) {
        if (needConsumed) {
            return true;
        }
        return false;
    }

    public void setNeedConsumed(boolean b) {
        needConsumed = b;
    }

    @Override
    public int getNestedScrollAxes() {
        return 0;
    }

    public void setLastItemBottomMargin(int lastItemBottomMargin) {
        this.lastItemBottomMargin = lastItemBottomMargin;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        float touchYpos = ev.getRawY();
        //  touchYpos >= original.bottom || touchYpos <= original.top ||

        if (touchYpos >= bottomRange || touchYpos <= topRange || keepOverState) {
            if (isMoved) {
                recoverLayout();
            }
            startYpos = ev.getRawY();
            return true;
        }
        if (needReplay) {
            needReplay = false;
            replayEvent(ev);
        }
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startYpos = ev.getRawY();
            case MotionEvent.ACTION_MOVE:
                int scrollYpos = (int) (ev.getRawY() - startYpos);
                boolean pullDown = scrollYpos > 2 && canPullDown();
                boolean pullUp = scrollYpos < -2 && canPullUp();
                if (pullDown || pullUp) {
//                    cancelChild(ev);
                    offset = (int) (scrollYpos * DAMPING_COEFFICIENT);
                    childView.layout(original.left, original.top + offset, original.right, original.bottom + offset);
                    if (mScrollListener != null) {
                        mScrollListener.onScroll(scrollYpos);
                    }
                    isMoved = true;
                    isSuccess = false;
                    return true;
                } else {
                    startYpos = ev.getRawY();
                    isMoved = false;
                    isSuccess = true;
                    return super.dispatchTouchEvent(ev);
                }
            case MotionEvent.ACTION_UP:
                if (isMoved) {
                    recoverLayout();
                }
                return !isSuccess || super.dispatchTouchEvent(ev);
            default:
                return true;
        }
    }

    public boolean isOverScroll() {
//        return childView.getTop() != original.top;
        return isMoved;
    }

    /**
     * 取消子view已经处理的事件
     *
     * @param ev event
     */
    private void cancelChild(MotionEvent ev) {
        ev.setAction(MotionEvent.ACTION_CANCEL);
        super.dispatchTouchEvent(ev);
    }

    private void replayEvent(MotionEvent ev) {
        ev.setAction(MotionEvent.ACTION_DOWN);
        super.dispatchTouchEvent(ev);
    }

    /**
     * 位置还原
     */
    private void recoverLayout() {
        TranslateAnimation anim = new TranslateAnimation(0, 0, childView.getTop() - original.top, 0);
        anim.setDuration(ANIM_TIME);
        childView.startAnimation(anim);
        childView.layout(original.left, original.top, original.right, original.bottom);
        isMoved = false;
        offset = 0;
    }

    /**
     * 判断是否可以下拉
     *
     * @return true：可以，false:不可以
     */
    private boolean canPullDown() {
        Adapter adapter = childView.getAdapter();
        RecyclerView.LayoutManager manager = childView.getLayoutManager();
        if (adapter == null || manager == null) {
            return false;
        }
        final int firstVisiblePosition = ((LinearLayoutManager) manager).findFirstVisibleItemPosition();
        if (firstVisiblePosition != 0 && adapter.getItemCount() != 0) {
            return false;
        }
        int mostTop = (childView.getChildCount() > 0) ? childView.getChildAt(0).getTop() : 0;
        return mostTop >= 0;
    }

    /**
     * 判断是否可以上拉
     *
     * @return true：可以，false:不可以
     */
    private boolean canPullUp() {
        Adapter adapter = childView.getAdapter();
        RecyclerView.LayoutManager manager = childView.getLayoutManager();
        if (adapter == null || manager == null) {
            return false;
        }
        final int lastItemPosition = adapter.getItemCount() - 1;
        final int lastVisiblePosition = ((LinearLayoutManager) manager).findLastVisibleItemPosition();
        if (lastVisiblePosition >= lastItemPosition) {
            final int childIndex = lastVisiblePosition - ((LinearLayoutManager) manager).findFirstVisibleItemPosition();
            final int childCount = childView.getChildCount();
            final int index = Math.min(childIndex, childCount - 1);
            final View lastVisibleChild = childView.getChildAt(index);
            if (lastVisibleChild != null) {
                return lastVisibleChild.getBottom() <= childView.getBottom() - childView.getTop() - lastItemBottomMargin;
            }
        }
        return false;
    }

    /**
     * @param keep 是否保持弹性滑动状态
     */
    public void keepCurState(boolean keep) {
        if (keepOverState != keep && !keep) {
            needReplay = true;
        }
        this.keepOverState = keep;

    }


    public interface ScrollListener {
        /**
         * 滚动事件回调
         *
         * @param scrollYpos 弹性滑动距离
         */
        void onScroll(int scrollYpos);
    }

}
