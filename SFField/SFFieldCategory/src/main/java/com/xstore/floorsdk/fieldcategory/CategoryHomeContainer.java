//package com.xstore.floorsdk.fieldcategory;
//
//import android.animation.Animator;
//import android.animation.ValueAnimator;
//import android.annotation.SuppressLint;
//import android.app.Activity;
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.content.IntentFilter;
//import android.graphics.Rect;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Message;
//import android.text.TextUtils;
//import android.util.AttributeSet;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.FrameLayout;
//
//import androidx.annotation.NonNull;
//
//import androidx.localbroadcastmanager.content.LocalBroadcastManager;
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.boredream.bdcodehelper.utils.DisplayUtils;
//import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
//import com.gyf.barlibrary.ImmersionBar;
//import com.jingdong.sdk.jdcrashreport.JdCrashReport;
//import com.xstore.floorsdk.fieldcategory.adapter.SecondCategoryAdapter;
//import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
//import com.xstore.floorsdk.fieldcategory.bean.BannerBean;
//import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
//import com.xstore.floorsdk.fieldcategory.bean.CategoryWareInfoResult;
//import com.xstore.floorsdk.fieldcategory.bean.ChildCategoryResult;
//import com.xstore.floorsdk.fieldcategory.bean.CustomizeFilterCriteriaVo;
//import com.xstore.floorsdk.fieldcategory.bean.FilterCriteriaVo;
//import com.xstore.floorsdk.fieldcategory.bean.FirstCategoryResult;
//import com.xstore.floorsdk.fieldcategory.interfaces.ActionState;
//import com.xstore.floorsdk.fieldcategory.interfaces.CategoryContainerInterface;
//import com.xstore.floorsdk.fieldcategory.interfaces.OnProductRefreshOrLoadMoreListener;
//import com.xstore.floorsdk.fieldcategory.ma.CategoryFieldReporter;
//import com.xstore.floorsdk.fieldcategory.request.CategoryRequest;
//import com.xstore.floorsdk.fieldcategory.widget.CategoryRelativeLayout;
//import com.xstore.floorsdk.fieldcategory.widget.CategorySearchView;
//import com.xstore.floorsdk.fieldcategory.widget.DropDownFirstCategoryPop;
//import com.xstore.floorsdk.fieldcategory.widget.OverScrollLayout;
//import com.xstore.sdk.floor.floorcore.bean.ResponseData;
//import com.xstore.sdk.floor.floorcore.interfaces.FloorLifecycle;
//import com.xstore.sdk.floor.floorcore.utils.StringUtil;
//import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;
//import com.xstore.sevenfresh.addressstore.constants.LbsBroadcastConstants;
//import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
//import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
//import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
//import com.xstore.sevenfresh.fresh_network_business.FreshResultCallback;
//import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
//import com.xstore.sevenfresh.service.sflog.SFLogCollector;
//import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;
//
//import org.jetbrains.annotations.NotNull;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Random;
//
//import static com.xstore.sdk.floor.floorcore.utils.DPIUtil.getWidthByDesignValueFitFold;
//import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.DEFAULT_EFFECT;
//import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.NO_EFFECT;
//
///**
// * 一级分类接口和二级分类接口现在是串行的
// *
// * 分类页面逻辑：
// * 1、切一级分类：重置排序、重置二级分类、重置三级分类、刷新商品列表；
// * 2、切二级分类（手动点击切/滑动切）：重置排序、重置三级分类、刷新商品列表；
// * a、如果此二级分类没有三级分类，则请求二级分类下商品列表；
// * b、如果此二级分类有三级分类，则请求第一个三级分类下商品列表；
// * 3、手动点击切三级分类：（沿用当前的排序类型）
// * a、如果当前已展示此三级分类商品，则商品列表自动滚动到此三级分类节点；
// * b、如果当前未展示此三级分类商品，则请求此三级分类下的商品数据并沿用当前的排序类型，刷新商品列表
// * 4、滑动切三级分类：（沿用当前的排序类型）
// * a、上拉加载下一页，请求当前三级分类下一页商品数据，拼接到当前列表尾部；
// * b、上拉加载下一三级分类，请求此分类商品数据，拼接到当前列表尾部；
// * c、上拉加载下一二级分类，重走第2步切二级分类逻辑；
// * d、下拉到上一个三级分类，请求此三级分类全部商品数据（不分页）并插入到当前列表头部；
// * e、下拉到上一个二级分类，重走第2步切二级分类逻辑；
// * 5、切换排序方式（从二级分类维度）：切换时清除数据；
// * a、如果有三级分类按此排序方式请求当前二级分类下的第一个三级分类商品数据；
// * b、如果没有三级分类，则按此排序方式请求当前二级分类下的商品数据。
// *
// * <AUTHOR>
// * @date 2022/10/24
// */
//public class CategoryHomeContainer extends FrameLayout implements FloorLifecycle, View.OnClickListener, OnProductRefreshOrLoadMoreListener {
//
//    private static final String TAG = "CategoryHomeContainer";
//
//    //二级分类页下拉切换一级分类
//    public static final int CHANGE_FIRST_CATE = 222;
//
//
//    private Activity mActivity;
//
//    /**
//     * 左侧二级分类列表
//     */
//    private RecyclerView secondCateListView;
//    private RecyclerView.ItemDecoration secondCateDecoration;
//    private CategoryProductContainer productContainer;
//    /**
//     * 二级分类列表适配器
//     */
//    private SecondCategoryAdapter secondAdapter;
//    /**
//     * 二级分类
//     */
//    private List<CategoryBean> secondCategory = new ArrayList<>();
//    /**
//     * 二级分类名
//     */
//    public String secondCateName;
//    /**
//     * 选中的二级类目Id
//     */
//    public String secondCateId;
//    public String secondMid;
//    /**
//     * 当前选中的二级分类位置
//     */
//    private int currSecondCateIndex;
//    /**
//     * 下个二级类目的名称
//     */
//    private String nextSecondCateName;
//    /**
//     * 当前分类Id（二级或者三级）
//     */
//    private long currCId;
//    private String currMid;
//
//    /**
//     * 三级分类
//     */
//    private List<CategoryBean> thirdCategory = new ArrayList<>();
//    /**
//     * 当前选中的三级分类位置
//     */
//    private int currThirdCateIndex;
//    /**
//     * 三级分类名（埋点用）
//     */
//    public String thirdCategoryName;
//    /**
//     * 三级分类名
//     */
//    public String thirdCateId;
//    public String thirdMid;
//    /**
//     * 门店Id
//     */
//    private String storeId;
//    /**
//     * 围栏id
//     */
//    private String fenceId;
//    /**
//     * 是否destroy
//     */
//    private boolean isDestroy = false;
//    /**
//     * 是否切换二级分类
//     */
//    public boolean isChangeSecondCate = true;
//    /**
//     * 当前操作状态
//     */
//    private ActionState actionState;
//    /**
//     * rvFirstCate
//     */
//    private RecyclerView rvFirstCate;
//    /**
//     * firstCateAdapter
//     */
//    private FirstCategoryAdapter firstCategoryAdapter;
//    /**
//     * isAnimating 是否正在执行动画
//     */
//    public static boolean isAnimating;
//    /**
//     * lastFold 上次的折叠状态
//     */
//    private boolean lastFold = false;
//    /**
//     * srlSencondCate 二级类目包裹
//     */
//    private OverScrollLayout oslSencondCate;
//    /**
//     * skipNextFold
//     */
//    private boolean skipNextFold;
//    /**
//     * secNoDataLayout 二级类目无数据 空白页
//     */
//    private View secNoDataLayout;
//    /**
//     * firstCateManager
//     */
//    private CenterLayoutManager firstCateManager;
//    /**
//     * 全部按钮
//     */
//    private View llFoldAll;
//    /**
//     * 折叠全部的阴影
//     */
//    private View foldAllShadow;
//    /**
//     * 全部分类 下拉展示
//     */
//    private DropDownFirstCategoryPop dropDownFirstCategoryPop;
//
//    private View rootView;
//    /**
//     * 容器
//     */
//    private CategoryRelativeLayout cmrl;
//    /**
//     * 轮播搜索词
//     */
//    private CategorySearchView searchView;
//
//    /**
//     * categoryId 用户点击的一级类目id
//     */
//    public long categoryId;
//    public String mid;
//
//    /**
//     * 上一个页面的二级类目id，只有第一次请求的时候使用，之后都是用户自己的点击行为
//     */
//    public long beforeCategoryId2;
//
//    /**
//     * 选中的一级类目名称
//     */
//    public String firstCateName;
//
//    /**
//     * 选中的一级类目Id
//     */
//    public String firstCateId;
//    public String firstMid;
//
//    /**
//     * 排序类型 1:综合排序, 2:价格升序 , 3:价格降序, 4:总销量升序,
//     * 5:总销量降序 (如果此字段为空， 则走默认排序：销量降序)
//     */
//    public String sortType;
//    /**
//     * 页面来源 {@link CategoryConstant}
//     */
//    private int source = 0;
//
//    /**
//     * firstCateList 一级类目数据
//     */
//    public List<CategoryBean> firstCateList;
//    /**
//     * 当次分类筛选的随机序号,每次重新搜索都会变化
//     */
//    public String pvId;
//    public String logId;
//
//    private boolean mHidden;
//    /**
//     * 自定义筛选条件
//     */
//    private ArrayList<FilterCriteriaVo> firstFilterCriteria = new ArrayList<>();
//    /**
//     * 选中的筛选条件
//     */
//    private FilterCriteriaVo mSelectedFilterCriteriaVo;
//
//    private CategoryFieldReporter categoryFieldReporter;
//    /**
//     * 通过外部开关控制 1级分类是否可以折叠
//     */
//    private boolean isCanFoldFirstCate = true;
//    public void setCanFoldFirstCate(boolean isCan){
//        isCanFoldFirstCate = isCan;
//    }
//    private CategoryContainerInterface categoryContainerInterface;
//
//    public CategoryHomeContainer(Context context) {
//        super(context);
//        initRootView(context);
//    }
//
//    public CategoryHomeContainer(Context context, AttributeSet attrs) {
//        super(context, attrs);
//        initRootView(context);
//    }
//
//    public CategoryHomeContainer(Context context, AttributeSet attrs, int defStyleAttr) {
//        super(context, attrs, defStyleAttr);
//        initRootView(context);
//    }
//
//    /**
//     * 初始化视图
//     *
//     * @param context
//     */
//    private void initRootView(Context context) {
//        mActivity = (Activity) context;
//        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_category_container, this, true);
//        initView();
//        //注册
//        IntentFilter filter1 = new IntentFilter();
//        filter1.addAction(LbsBroadcastConstants.ACTION_UPDATE_ADDRESS);
////        filter1.addAction(Constant.ADDRESS_RED_PACKET);
//        filter1.setPriority(Integer.MAX_VALUE);
//        LocalBroadcastManager.getInstance(context).registerReceiver(addressChangeReceiver, filter1);
//        categoryFieldReporter = new CategoryFieldReporter();
//
//    }
//
//    /**
//     * 初始化view
//     */
//    private void initView() {
//        llFoldAll = rootView.findViewById(R.id.ll_fold_all);
//        foldAllShadow = rootView.findViewById(R.id.view_all_shadow);
//        searchView = rootView.findViewById(R.id.search_view);
//
//        rvFirstCate = rootView.findViewById(R.id.rv_first_cate);
//        firstCateManager = new CenterLayoutManager(mActivity, LinearLayoutManager.HORIZONTAL, false);
//        rvFirstCate.setLayoutManager(firstCateManager);
//
//        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) rvFirstCate.getLayoutParams();
//        params.topMargin = getWidthByDesignValueFitFold(mActivity, 44, 375);
//        rvFirstCate.setLayoutParams(params);
//
//        llFoldAll.setOnClickListener(this);
//
//        cmrl = rootView.findViewById(R.id.container);
//
//        //二级分类
//        oslSencondCate = (OverScrollLayout) rootView.findViewById(R.id.osl_second_cate);
//        secondCateListView = rootView.findViewById(R.id.rv_second_category);
//        CenterLayoutManager secondCateLayoutManager = new CenterLayoutManager(mActivity);
//        secondCateLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
//        secondCateListView.setLayoutManager(secondCateLayoutManager);
//        oslSencondCate.setScrollListener(new OverScrollLayout.ScrollListener() {
//            @Override
//            public void onScroll(int scrollYpos) {
//                SFLogCollector.d("lsp", "scrollYpos:" + scrollYpos);
//                if (cmrl.getDirection() == CategoryRelativeLayout.ScrollDirection.UP) {
//                    //如果发生了线上拖动, 那么可能当前是不满一屏 不能滚动，所以在这里出发一下收起
//                    if (isAnimating) {
//                        SFLogCollector.d("lsp", "skippp  anim");
//                        return;
//                    }
//                    if (skipNextFold) {
//                        skipNextFold = false;
//                        SFLogCollector.d("lsp", "skippp  fold");
//                        return;
//                    }
//
//                    //up
//                    foldFirstCate(true, true);
//                }
//                if (scrollYpos > DisplayUtils.dp2px(mActivity, 25)) {
//                    if (cmrl.getDirection() != CategoryRelativeLayout.ScrollDirection.DOWN) {
//                        SFLogCollector.d("lsp", "skippp aa upppppp=");
//                        return;
//                    }
//
//                    if (isAnimating) {
//                        SFLogCollector.d("lsp", "skippp aa anim");
//                        return;
//                    }
//                    if (skipNextFold) {
//                        skipNextFold = false;
//                        SFLogCollector.d("lsp", "skippp aa fold");
//                        return;
//                    }
//                    foldFirstCate(false, true);
//                }
//            }
//        });
//
//        cmrl.setRangeView(secondCateListView);
//        cmrl.setListener(new CategoryRelativeLayout.Listener() {
//            @Override
//            public void scrollUpInRange() {
//                if (isAnimating) {
//                    SFLogCollector.d("lsp", "skippp  anim");
//                    return;
//                }
//
//                if (oslSencondCate.isOverScroll()) {
//                    return;
//                }
//
//                if (skipNextFold) {
//                    skipNextFold = false;
//                    SFLogCollector.d("lsp", "skippp  fold");
//                    return;
//                }
//
//                //down
//                foldFirstCate(true, true);
//            }
//        });
//        secNoDataLayout = rootView.findViewById(R.id.sec_no_data);
//        initSecondCateDecoration();
//
//        productContainer = rootView.findViewById(R.id.sku_container);
//    }
//
//    private void initSecondCateDecoration() {
//        secondCateDecoration = new RecyclerView.ItemDecoration() {
//            @Override
//            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
//                int childPosition = parent.getChildAdapterPosition(view);
//                if (childPosition == secondAdapter.getItemCount() - 1) {
//                    outRect.bottom = DisplayUtils.dp2px(mActivity, 64);
//                } else {
//                    outRect.bottom = 0;
//                }
//            }
//        };
//    }
//
//    /**
//     * 距离底部的距离
//     *
//     * @param distance
//     */
//    public void setBottomDistance(int distance) {
//        if (distance > 0) {
//            secondCateListView.addItemDecoration(secondCateDecoration);
//            oslSencondCate.setLastItemBottomMargin(DisplayUtils.dp2px(mActivity, distance));
//        } else {
//            secondCateListView.removeItemDecoration(secondCateDecoration);
//            oslSencondCate.setLastItemBottomMargin(0);
//        }
//    }
//
//    /**
//     * 设置数据
//     *
//     * @param bundle
//     */
//    public void setData(Bundle bundle,CategoryContainerInterface categoryContainerInterface) {
//        this.categoryContainerInterface = categoryContainerInterface;
//        if (categoryContainerInterface != null) {
//            categoryFieldReporter.initData(categoryContainerInterface.getJdMaPageImp(),this,productContainer);
//        }
//
//        if (bundle != null) {
//            source = bundle.getInt(CategoryConstant.Key.FROM_SOURCE);
//            categoryId = bundle.getLong(CategoryConstant.Key.FIRST_CATEGORY_ID);
//            beforeCategoryId2 = bundle.getLong(CategoryConstant.Key.SECOND_CATEGORY_ID);
//        }
//
//        if (source == CategoryConstant.Value.SOURCE_CATEGORY_PAGE && cmrl.getLayoutParams() instanceof FrameLayout.LayoutParams) {
//            FrameLayout.LayoutParams layoutParams1 = (FrameLayout.LayoutParams) cmrl.getLayoutParams();
//            layoutParams1.topMargin = ImmersionBar.getStatusBarHeight(mActivity);
//            cmrl.setLayoutParams(layoutParams1);
//        }
//        getFirstCategoryData();
//    }
//
//    private void getFirstCategoryData() {
//        if (searchView != null) {
//            searchView.setSearchStyle(source, categoryFieldReporter);
//        }
//        CategoryRequest.getFirstCategory(mActivity, source, DEFAULT_EFFECT, categoryContainerInterface,new FirstCategoryListener());
//    }
//
//    @SuppressLint("HandlerLeak")
//    private Handler handler = new Handler() {
//
//        @Override
//        public void handleMessage(Message msg) {
//            super.handleMessage(msg);
//            switch (msg.what) {
//                case CHANGE_FIRST_CATE:/** 切换一级分类 */
//                    isChangeSecondCate = true;
//                    currCId = 0;
//                    currMid = null;
//                    getChildCategory(false);
//                    break;
//                default:
//                    break;
//            }
//        }
//    };
//
//    @Override
//    public void onResume(boolean hidden) {
//        mHidden = hidden;
//        if (hidden) {
//            return;
//        }
//        if (searchView != null) {
//            searchView.onResume(hidden);
//        }
//        compareStoreId();
//    }
//
//    @Override
//    public void onPause() {
//        if (searchView != null) {
//            searchView.onPause();
//        }
//        mHidden = false;
//    }
//
//    /**
//     * 比较判断storeId是否发生变化
//     * 追加围栏id的判断
//     *
//     * @return true 门店id发生了变化
//     */
//    private boolean compareStoreId() {
//        //如果门店id发生变化，或者门店id相同但是围栏id发生变化
//        if (!TextUtils.isEmpty(storeId) && !storeId.equals(TenantIdUtils.getStoreId()) ||
//                (TextUtils.equals(storeId, TenantIdUtils.getStoreId()) && !TextUtils.equals(fenceId, TenantIdUtils.getFenceId()))) {
//            storeIdChanged();
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 门店发生变化后进行刷新
//     */
//    private void storeIdChanged() {
//        //他再首页的时候不要关闭 需要刷新 !!!!
//        if (source == CategoryConstant.Value.SOURCE_CATEGORY_PAGE) {
//            //重置切换二级分类标识
//            isChangeSecondCate = true;
//            //重置选中的时效
//            mSelectedFilterCriteriaVo = null;
//            //重置类目id
//            categoryId = 0;
//            mid = null;
//            //这里重置storeId,代表还未请求过二级类目的数据
//            storeId = "";
//            fenceId = "";
//            getFirstCategoryData();
//        } else {
//            mActivity.finish();
//        }
//    }
//
//
//    @Override
//    public void onClick(View v) {
//        if (NoDoubleClickUtils.isDoubleClick()) {
//            return;
//        }
//        int id = v.getId();
//        if (id == R.id.ll_fold_all) {
//            categoryFieldReporter.allFirstCategoryClick();
//            if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
//                dropDownFirstCategoryPop.dismiss();
//            }
//            if (mHidden) {
//                return;
//            }
//            if (dropDownFirstCategoryPop == null) {
//                dropDownFirstCategoryPop = new DropDownFirstCategoryPop(mActivity, firstCateList, categoryId,new DropDownFirstCategoryPop.PopClickListener() {
//                    @Override
//                    public void onItemClick(@NotNull View view, int position, @NotNull CategoryBean cate) {
//                        clickFirstCategory(view, position, cate);
//                        //这里需要同步选中状态给外面的adapter
//                        firstCategoryAdapter.setSelectedCategoryId(cate);
//                    }
//                    @Override
//                    public void onFoldClick() {
//                        //收起
//                        categoryFieldReporter.foldFirstCategoryClick();
//                    }
//                });
//            } else {
//                dropDownFirstCategoryPop.updateSelectCate(categoryId);
//            }
//            dropDownFirstCategoryPop.showAsDropDown(searchView);
//        }
//    }
//
//    /**
//     * 获取一级分类下子分类
//     */
//    private void getChildCategory(boolean needCache) {
//        actionState = ActionState.PRODUCT_REFRESH;
//        generateNewSearchPvId(1);
//        CategoryRequest.getChildCategory(mActivity,false,1, categoryId, beforeCategoryId2, mid,1,CategoryProductContainer.PAGE_SIZE,pvId,logId,source,getCategoryQueryConditions(),needCache,categoryContainerInterface,new NewChildCategoryListener(categoryId));
//        if (beforeCategoryId2 > 0) {
//            beforeCategoryId2 = 0;
//        }
//    }
//
//    /**
//     * 处理筛选条件
//     */
//    private void handleQueryCondition(CustomizeFilterCriteriaVo customizeFilterCriteriaVo){
//        if (firstFilterCriteria == null) {
//            firstFilterCriteria = new ArrayList<>();
//        }
//        firstFilterCriteria.clear();
//        if (customizeFilterCriteriaVo != null && customizeFilterCriteriaVo.getFirstFilterCriteria()!=null) {
//            firstFilterCriteria.addAll(customizeFilterCriteriaVo.getFirstFilterCriteria());
//        }
//        if (firstFilterCriteria != null && firstFilterCriteria.size() > 0) {
//            mSelectedFilterCriteriaVo = firstFilterCriteria.get(0);
//        } else {
//            mSelectedFilterCriteriaVo = null;
//        }
//    }
//
//    /**
//     * 一级分类请求回调
//     */
//    public class FirstCategoryListener extends FreshResultCallback<ResponseData<FirstCategoryResult>> {
//
//        @Override
//        public void onEnd(ResponseData<FirstCategoryResult> object, FreshHttpSetting httpSetting) {
//            SFLogCollector.i("yyyyyy","====FirstCategoryListener======"+httpSetting.isFromCache()+"=====");
//            if (object != null && ResponseData.CODE_SUCC.equals(object.getCode()) && object.getData() != null) {
//                //接口请求成功且有数据
//                handleQueryCondition(object.getData().getCustomizeFilterCriteriaVo());
//                //处理一级分类数据
//                firstCateList = object.getData().getAllCategoryList();
//                if (firstCateList != null && firstCateList.size() > 0) {
//                    getFirstCateSuc(httpSetting.isFromCache());
//                } else {
//                    getFirstCateFail(httpSetting.isFromCache());
//                }
//            } else {
//                getFirstCateFail(false);
//            }
//
//            if (!httpSetting.isFromCache()) {
//                if (firstCateList == null || firstCateList.size() == 0) {
//                    SgmReportBusinessErrorLog.reportFirstCateEmpty();
//                }
//                if (firstCateList != null && firstCateList.size() < 5) {
//                    SgmReportBusinessErrorLog.reportFirstCateLess5();
//                }
//            }
//
//        }
//
//        @Override
//        public void onError(FreshHttpException error) {
////            getFirstCateFail(false);
//            if ((firstCateList == null || firstCateList.size() == 0) && error != null && error.getHttpSetting().getCacheConfig()!=null) {
//                CategoryRequest.getCacheData(mActivity, error.getHttpSetting());
//            }
//        }
//    }
//
//    public class NewChildCategoryListener extends FreshResultCallback<ResponseData<ChildCategoryResult>> {
//
//        private final long cid;
//        private boolean hasData = false;
//
//        public NewChildCategoryListener(long categoryId) {
//            this.cid = categoryId;
//        }
//
//        @Override
//        public void onEnd(ResponseData<ChildCategoryResult> o, FreshHttpSetting httpSetting) {
//            SFLogCollector.i("yyyyyy","====NewChildCategoryListener======"+httpSetting.isFromCache()+"======");
//            //首次判断id是否相同
//            if (cid != 0 && cid != categoryId) {
//                SFLogCollector.i(TAG, "NewChildCategoryListener cid not same");
//                int itemCount = 0;
//                if (secondAdapter != null) {
//                    itemCount = secondAdapter.getItemCount();
//                }
//                JdCrashReport.postCaughtException(new Exception("new cate cid not same:" + cid + " c:" + categoryId + " 是否有数据:" + itemCount));
//                return;
//            }
//            if (o != null && ResponseData.CODE_SUCC.equals(o.getCode()) && o.getData() != null) {
//                hasData = true;
//                parseChildCategoryData(o.getData(),httpSetting.isFromCache());
//            } else {
//                hasData = false;
//                getChildCategoryFail(false);
//            }
//        }
//
//        @Override
//        public void onError(FreshHttpException httpError) {
//            //首次判断id是否相同
//            if (cid != categoryId) {
//                SFLogCollector.i(TAG, "NewChildCategoryListener cid not same err");
//                int itemCount = 0;
//                if (secondAdapter != null) {
//                    itemCount = secondAdapter.getItemCount();
//                }
//                JdCrashReport.postCaughtException(new Exception("new cate cid not same:" + cid + " c:" + categoryId + " 是否有数据:" + itemCount));
//                return;
//            }
//            if (!hasData && httpError != null && httpError.getHttpSetting().getCacheConfig()!=null) {
//                secondCategory = null;
//                CategoryRequest.getCacheData(mActivity, httpError.getHttpSetting());
//            }
//        }
//    }
//
//
//    /**
//     * 解析子分类数据
//     *
//     * @param fromCache
//     */
//    private void parseChildCategoryData(ChildCategoryResult childCategoryResult, boolean fromCache) {
//        try {
//            if (childCategoryResult != null) {
//                for (int i = firstCateList.size() - 1; i >= 0; i--) {
//                    CategoryBean c = firstCateList.get(i);
//                    if (c.getId() == null) {
//                        firstCateList.remove(i);
//                        continue;
//                    }
//                    if (c.getId() != null && c.getId() == categoryId) {
//                        firstCateName = c.getName();
//                        firstCateId = c.getId() + "";
//                        firstMid = c.getMid() + "";
//                    }
//                }
//
//                secondCategory = childCategoryResult.getChildCidList();
//                if (secondCategory != null && secondCategory.size() > 0) {
//                    //当前默认选中的二级分类 TODO
//                    long currSecCateId = childCategoryResult.getCid() != null ? childCategoryResult.getCid() : 0;
//                    long currThirCateId = childCategoryResult.getCid() != null ? childCategoryResult.getCid() : 0;
//                    if (currSecCateId == 0) {
//                        //当前没有默认选中的二级分类，则选中第一个
//                        showSecondAndThirdCate(0, !fromCache);
//                    } else {
//                        boolean findSecondCate = false;
//                        for (CategoryBean secCate : secondCategory) {
//                            boolean selectedCid3 = false;
//                            if (secCate.getCid3() != null && secCate.getCid3().size() > 0) {
//                                for (CategoryBean c3 : secCate.getCid3()) {
//                                    if (c3.getId() == currSecCateId) {
//                                        //纠正为当前选中的二级类目id
//                                        currSecCateId = c3.getParentId();
//                                        currThirCateId = c3.getId();
//                                        selectedCid3 = true;
//                                    }
//                                }
//                            }
//                            if (selectedCid3 || (secCate != null && secCate.getId() != null
//                                    && currSecCateId == secCate.getId())) {
//                                currSecondCateIndex = secondCategory.indexOf(secCate);
//                                //如果已经返回第一页商品数据，则不用再请求，直接展示
//                                if (childCategoryResult.getCategorySkuInfoDto() != null
//                                        && childCategoryResult.getCategorySkuInfoDto().getProductCardVoList() != null
//                                        && childCategoryResult.getCategorySkuInfoDto().getProductCardVoList().size() > 0) {
//                                    showSecondAndThirdCate(currSecondCateIndex, false);
//                                    //兼容里层取不到的mid的情况
//                                    if (StringUtil.isNullByString(childCategoryResult.getCategorySkuInfoDto().getMid())) {
//                                        childCategoryResult.getCategorySkuInfoDto().setCid(childCategoryResult.getCid());
//                                        childCategoryResult.getCategorySkuInfoDto().setMid(childCategoryResult.getMid());
//                                    }
//                                    showCateProducts(currSecCateId, currThirCateId, childCategoryResult.getCategorySkuInfoDto(), pvId, logId, fromCache);
//                                } else {
//                                    showSecondAndThirdCate(currSecondCateIndex, !fromCache);
//                                }
//                                findSecondCate = true;
//                                break;
//                            }
//                        }
//                        if (!findSecondCate) {
//                            //没有找到默认的二级分类，则选中第一个
//                            showSecondAndThirdCate(0, !fromCache);
//                        }
//                    }
//                    secNoDataLayout.setVisibility(View.GONE);
//                    return;
//                } else {
//                    //二级分类是空的情况
//                    showSecondAndThirdCate(0, false);
//                    showCateProducts(0, 0, new CategoryWareInfoResult(), pvId, logId, fromCache);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            JdCrashReport.postCaughtException(e);
//        }
//        //没数据 空白页
//        getChildCategoryFail(fromCache);
//
//    }
//
//    private void getChildCategoryFail(boolean fromCache) {
//        if (!fromCache) {
//            //如果二级类目数据获取失败，那么整个下面都是空白页
//            if (secondCategory == null || secondCategory.size() == 0) {
//                secNoDataLayout.setVisibility(View.VISIBLE);
//                SgmReportBusinessErrorLog.reportSecondCateEmpty(firstCateId,firstCateName);
//            }
//        }
//    }
//
//    /**
//     * 子类数据请求成功，展示二级、三级分类列表
//     *
//     * @param selectSecondIndex
//     * @param needRequestWare
//     */
//    private void showSecondAndThirdCate(int selectSecondIndex, boolean needRequestWare) {
//        if (secondCategory == null || secondCategory.size() <= 0) {
//            secondCateListView.setVisibility(View.GONE);
//            return;
//        }
//        if (selectSecondIndex >= secondCategory.size()) {
//            return;
//        }
//        actionState = ActionState.PRODUCT_REFRESH;
//        currSecondCateIndex = selectSecondIndex;
//        CategoryBean currSecCate = secondCategory.get(currSecondCateIndex);
//        secondCateName = currSecCate.getName();
//        secondCateId = currSecCate.getId() + "";
//        secondMid = currSecCate.getMid() + "";
//        //如果存在下一个二级分类 那么就赋值
//        if (secondCategory.size() > currSecondCateIndex + 1) {
//            nextSecondCateName = secondCategory.get(currSecondCateIndex + 1).getName();
//        }
//        if (currSecCate.getCid3() != null && currSecCate.getCid3().size() > 0) {
//            thirdCategory = currSecCate.getCid3();
//            currThirdCateIndex = 0;
//            currCId = thirdCategory.get(0).getId();
//            thirdCategoryName = thirdCategory.get(0).getName();
//            thirdCateId = thirdCategory.get(0).getId() + "";
//            thirdMid = thirdCategory.get(0).getMid() + "";
//            currMid = thirdCategory.get(0).getMid();
//        } else { //当前二级分类没有三级分类
//            thirdCategory = null;
//            currThirdCateIndex = 0;
//            thirdCategoryName = "";
//            thirdCateId = "";
//            thirdMid = "";
//            currCId = currSecCate.getId();
//            currMid = currSecCate.getMid();
//        }
//        //重置排序方式为综合
//        sortType = CategoryConstant.Value.SORT_DEFAULT;
//
//        //显示二级分类view
//        secondCateListView.setVisibility(View.VISIBLE);
//        if (secondAdapter == null) {
//            secondAdapter = new SecondCategoryAdapter(mActivity, secondCategory, currSecondCateIndex, SecondCategoryAdapter.Source.CATEGORY_PAGE);
//            secondAdapter.setOnItemClickListener(position -> changeSecondCate(position, true, false));
//            secondCateListView.setAdapter(secondAdapter);
//        } else {
//            secondAdapter.setSecondCategory(secondCategory, currSecondCateIndex);
//        }
//
//        secondCateListView.postDelayed(() -> {
//            try {
//                secondCateListView.smoothScrollToPosition(currSecondCateIndex);
//            } catch (Exception e) {
//                JdCrashReport.postCaughtException(e);
//            }
//        }, 5);
//
//        if (needRequestWare) {
//            //请求商品数据
//            handler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    if (isDestroy) {
//                        return;
//                    }
//                    getWareInfoByCid(1, CategoryProductContainer.PAGE_SIZE, true);
//                }
//            }, 40);
//        }
//    }
//
//    /**
//     * 点击二级类目
//     *
//     * @param position       点击的二级分类索引
//     * @param userClick      是否是用户点击引起的切换
//     * @param selectNextCate 是否滑动商品列表加载下一分类
//     * @return
//     */
//    private boolean changeSecondCate(int position, boolean userClick, boolean selectNextCate) {
//        if (position < 0 || currSecondCateIndex == position) {/** 点击当前选中的不响应*/
//            return false;
//        }
//        if (userClick) {
//            foldFirstCate(true, false);
//        }
//        isChangeSecondCate = true;
//        actionState = ActionState.PRODUCT_REFRESH;
//        showSecondAndThirdCate(position, true);
//
//        if (userClick) {
//            categoryFieldReporter.secondCategoryClick();
//        } else if (selectNextCate) {
//            //上拉到下一分类
//        } else {
//            //下拉到下一分类
//        }
//        return false;
//    }
//
//    public void getFirstCateFail(boolean fromCache) {
//        if (!fromCache) {
//            if (firstCateList == null || firstCateList.size() == 0) {
//                rvFirstCate.setVisibility(View.GONE);
//                secNoDataLayout.setVisibility(VISIBLE);
//            }
//        }
//
//    }
//
//    public void getFirstCateSuc(boolean fromCache) {
//        if (firstCateList == null) {
//            return;
//        }
//        rvFirstCate.setVisibility(View.VISIBLE);
//        //第一次进入，还没有一级类目id storeId为空代表着还没没请求过二级类目数据
//        if (TextUtils.isEmpty(storeId)) {
//            secondCateListView.setVisibility(View.GONE);
//            storeId = TenantIdUtils.getStoreId();
//            //判断第一次进入  围栏id就足以判断
//            fenceId = TenantIdUtils.getFenceId();
//            PreferenceUtil.getString(CategorySearchView.HOT_WORDS_INFO + storeId, "");
//            if (categoryId == 0) {
//                //通过读取一级缓存走到这里 无一级缓存的网络请求走到这里
//                categoryId = firstCateList.get(0).getId();
//                mid = firstCateList.get(0).getMid();
//                getChildCategory(true);
//            } else {
//                //通过首页点击分类和读取一级缓存走到这里
//                getChildCategory(true);
//            }
//        }
//
//
//        //走到这里代表一开始进入类目页没有传入一级类目数据，没有正确上报pv 重报pv
//        boolean checkFirstCateIndex = false;
//        int firstCateIndex = 0;
//        for (int i = firstCateList.size() - 1; i >= 0; i--) {
//            CategoryBean c = firstCateList.get(i);
//            if (c.getId() == null) {
//                firstCateList.remove(i);
//                continue;
//            }
//            if (c.getId() != null && c.getId() == categoryId) {
//                firstCateName = c.getName();
//                firstCateId = c.getId() + "";
//                firstMid = c.getMid() + "";
//                firstCateIndex = i;
//                checkFirstCateIndex = true;
//            }
//        }
//        //确认当前的categoryId一级分类列表中，如果不在且一级列表是从网络返回的，则默认取第一个，再重新请求二级接口
//        if (!checkFirstCateIndex && !fromCache) {
//            CategoryBean category = firstCateList.get(0);
//            beforeCategoryId2 = 0;
//            if (category != null) {
//                categoryId = category.getId();
//                mid = category.getMid();
//                firstCateId = category.getId() + "";
//                firstMid = category.getMid() + "";
//                firstCateName = category.getName();
//                firstCateIndex = 0;
//            }
//            getChildCategory(false);
//        }
//        firstCategoryAdapter = new FirstCategoryAdapter(mActivity, firstCateList, categoryId, false);
//        firstCategoryAdapter.setClickListener(new FirstCategoryAdapter.ClickListener() {
//            @Override
//            public void onItemClick(View view, int position, CategoryBean cate) {
//                clickFirstCategory(view, position, cate);
//            }
//        });
//        rvFirstCate.setAdapter(firstCategoryAdapter);
//        scrollFirstCate(firstCateIndex, true);
//        //如果一级类目数量大于5，则可以滚动，并且展示全部，否则的话，不可以滚动，不展示全部
//        if (firstCateList != null && firstCateList.size() > 5) {
//            llFoldAll.setVisibility(View.VISIBLE);
//            foldAllShadow.setVisibility(View.VISIBLE);
//        } else {
//            llFoldAll.setVisibility(View.GONE);
//            foldAllShadow.setVisibility(View.GONE);
//        }
//        if (rvFirstCate.getItemDecorationCount() > 0) {
//            rvFirstCate.removeItemDecorationAt(0);
//        }
//        rvFirstCate.addItemDecoration(new RecyclerView.ItemDecoration() {
//
//            @Override
//            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
//
//                if (parent.getChildLayoutPosition(view) == firstCategoryAdapter.getItemCount() - 1 && firstCategoryAdapter.getItemCount() > 5) {
//                    outRect.right = DisplayUtils.dp2px(mActivity, 40);
//                } else if (parent.getChildLayoutPosition(view) == 0) {
//                    outRect.left = getWidthByDesignValueFitFold(mActivity, 7, 375);
//                }
//
//            }
//        });
//        //更新弹框里的一级类目数据
//        if (dropDownFirstCategoryPop != null) {
//            dropDownFirstCategoryPop.updateCategories(firstCateList);
//        }
//    }
//
//    @Override
//    public void onHiddenChange(boolean hidden) {
//        mHidden = hidden;
//        if (!hidden) {
//            if(TextUtils.isEmpty(storeId)){
//                getFirstCategoryData();
//            }
//            //判断storeId是否发生变化
//            compareStoreId();
//        } else {
//            if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
//                dropDownFirstCategoryPop.dismiss();
//            }
//        }
//        if (searchView != null) {
//            searchView.onHiddenChanged(hidden);
//        }
//    }
//
//    /**
//     *
//     * @param view
//     * @param position
//     * @param cate
//     */
//    private void clickFirstCategory(@NotNull View view, int position, @NotNull CategoryBean cate) {
//        currSecondCateIndex = 0;
//        categoryId = cate.getId();
//        mid = cate.getMid();
//        firstCateName = cate.getName();
//        firstCateId = cate.getId() + "";
//        firstMid = cate.getMid();
//        Message msg = handler.obtainMessage();
//        msg.obj = cate;
//        msg.what = CHANGE_FIRST_CATE;
//        handler.sendMessage(msg);
//
//        categoryFieldReporter.firstCategoryClick(cate);
//
//        scrollFirstCate(position, false);
//    }
//
//    private void scrollFirstCate(int index, boolean first) {
//        firstCateManager.setFast(first);
//        if (firstCategoryAdapter != null && firstCategoryAdapter.getSelectCategoryIndex() != index) {
//            handler.postDelayed(() -> {
//                if (index < 0) {
//                    return;
//                }
//                rvFirstCate.smoothScrollToPosition(index);
//            }, 0);
//        }
//    }
//
//    /**
//     * 切换三级分类
//     *
//     * @param userClick       是否用户手动点击切换
//     * @param position
//     * @param needRequestWare
//     */
//    @Override
//    public void changeThirdCate(boolean userClick, int position, boolean needRequestWare) {
//        if (position < 0) {
//            return;
//        }
//        if (thirdCategory == null || thirdCategory.size() <= 0 || position >= thirdCategory.size()) {
//            return;
//        }
//        currThirdCateIndex = position;
//        CategoryBean selectThirdCate = thirdCategory.get(position);
//        currCId = selectThirdCate.getId();
//        currMid = selectThirdCate.getMid();
//        thirdCategoryName = selectThirdCate.getName();
//        thirdCateId = selectThirdCate.getId() + "";
//        thirdCateId = selectThirdCate.getMid() + "";
//        if (userClick) {
//            if (productContainer != null && productContainer.isThirdCateExist(currCId)) {
//                //如果列表里已经有了此三级分类，则不需要请求数据，直接滚动到此三级分类节点
//                needRequestWare = false;
//                productContainer.scrollToExistThirdCate(currCId, currMid);
//            } else {
//                actionState = ActionState.PRODUCT_REFRESH;
//                needRequestWare = true;
//            }
//        }
//        if (needRequestWare) {
//            getWareInfoByCid(1, CategoryProductContainer.PAGE_SIZE, actionState != ActionState.PRODUCT_INSERT);
//        }
//
//        //更新选中的状态
//        if (productContainer!=null) {
//            productContainer.updateThirdCatePos(currThirdCateIndex);
//        }
//
//        if (userClick) {
//            categoryFieldReporter.thirdCategoryClick();
//        }
//    }
//
//
//    /**
//     * 展示商品数据
//     *
//     * @param cateId
//     * @param categoryWareInfoResult
//     */
//    private void showCateProducts(long secondCateId, long cateId, CategoryWareInfoResult categoryWareInfoResult,String pvId,String logId, boolean fromCache) {
//        if (isDestroy) {
//            return;
//        }
//        //这里需要判断一下 如果数据不匹配不是同一个二级类目下的数据 那么就不请求了
//        if (secondCateId != secondAdapter.getSelectCid()) {
//            //继续等
//            SFLogCollector.i(TAG, "showCateProducts cid not same");
//            return;
//        }
//
//        //商品列表绑定埋点所需数据
//        handleWareInfoListMaData(categoryWareInfoResult,pvId,logId);
//
//        //需要补全下一页的场景：
//        //1.第一次进入这个二级分类，加载的是当前的第一个三级分类
//        //2.需要清空三级分类的情景
//        boolean needAutoLoadMore = false;
//
//        productContainer.setActionListener(this,categoryContainerInterface,categoryFieldReporter);
//        if (isChangeSecondCate) {
//            needAutoLoadMore = true;
//            productContainer.showProductList(getArgumentsBundle(),currThirdCateIndex,cateId,thirdCategory,categoryWareInfoResult);
//            if (!fromCache && (categoryWareInfoResult == null || categoryWareInfoResult.getProductCardVoList() == null || categoryWareInfoResult.getProductCardVoList().size() == 0)) {
//                SgmReportBusinessErrorLog.reportFirstPageDataEmpty(firstCateId,firstCateName,secondCateId+"",secondCateName,thirdCateId,thirdCategoryName);
//            }
//        } else {
//            if (actionState == ActionState.PRODUCT_LOAD_MORE) {
//                productContainer.loadMoreProduct(cateId, categoryWareInfoResult);
//            } else if (actionState == ActionState.PRODUCT_INSERT) {
//                productContainer.insertProduct(cateId, categoryWareInfoResult);
//            } else {
//                needAutoLoadMore = true;
//                productContainer.refreshProductList(getArgumentsBundle(),currThirdCateIndex, cateId, thirdCategory, categoryWareInfoResult);
//            }
//        }
//
//        actionState = ActionState.NO_ACTION;
//
//        //在需要补足的场景下判断 是否满足一屏（10个）展示
//        if (!fromCache && needAutoLoadMore) {
//            if (categoryWareInfoResult.getProductCardVoList() == null || categoryWareInfoResult.getProductCardVoList().size() < 10 || categoryWareInfoResult.getSinkPageCount() > 1) {
//                onLoadThirdCate(currThirdCateIndex + 1);
//            }
//        }
//        //移动到这个位置
//        isChangeSecondCate = false;
//    }
//
//    /**
//     * 商品列表绑定埋点数据、cid、mid
//     * @param categoryWareInfoResult
//     */
//    private void handleWareInfoListMaData(CategoryWareInfoResult categoryWareInfoResult,String pvId,String logId){
//        if (categoryWareInfoResult != null && categoryWareInfoResult.getProductCardVoList() != null && categoryWareInfoResult.getProductCardVoList().size() > 0) {
//            for (int i = 0; i < categoryWareInfoResult.getProductCardVoList().size(); i++) {
//                SkuInfoBean wareInfoBean = categoryWareInfoResult.getProductCardVoList().get(i);
//                if (wareInfoBean != null) {
//                    if (!StringUtil.isNullByString(pvId)) {
//                        wareInfoBean.setPvId(pvId);
//                        wareInfoBean.setLogId(logId);
//                    }
//                    wareInfoBean.setPage(categoryWareInfoResult.getPage());
//                    wareInfoBean.setPageIndex((i + 1) + "");
//                    wareInfoBean.setTotalCount(categoryWareInfoResult.getTotalCount());
//                    wareInfoBean.setCateId(currCId);
//                    wareInfoBean.setMid(currMid);
//                    wareInfoBean.setFirstMid(firstMid);
//                    wareInfoBean.setFirstCateName(firstCateName);
//                    wareInfoBean.setSecondMid(secondMid);
//                    wareInfoBean.setSecondCateName(secondCateName);
//                    wareInfoBean.setThirdMid(thirdMid);
//                    wareInfoBean.setThirdCateName(thirdCategoryName);
//                }
//            }
//        }
//    }
//
//
//    /**
//     * 获取参数集合
//     *
//     * @return
//     */
//    private Bundle getArgumentsBundle() {
//        Bundle bundle = new Bundle();
//        bundle.putInt("currSecondCateIndex", currSecondCateIndex);
//        bundle.putString("nextSecondCateName", nextSecondCateName);
//        bundle.putInt("totalSecondCateSize", secondCategory == null ? 0 : secondCategory.size());
//        bundle.putString("sortType", sortType);
////        bundle.putString("firstCateId", firstCateId);
//        bundle.putString("firstMid", firstMid);
//        bundle.putString("firstCateName", firstCateName);
////        bundle.putString("secondCateId", secondCateId);
//        bundle.putString("secondMid", secondMid);
//        bundle.putString("secondCateName", secondCateName);
////        bundle.putString("thirdCateId", thirdCateId);
//        bundle.putString("thirdMid", thirdMid);
//        bundle.putString("thirdCategoryName", thirdCategoryName);
//
//        CategoryBean curSecondCate = null;
//        if (secondCategory != null) {
//            curSecondCate = secondCategory.get(currSecondCateIndex);
//        }
//        ArrayList<BannerBean> banners = null;
//        if (curSecondCate != null) {
//            banners = (ArrayList<BannerBean>) curSecondCate.getImages();
//        }
//        bundle.putSerializable("banners", banners);
//        bundle.putSerializable("firstFilterCriteria",firstFilterCriteria);
//        bundle.putSerializable("selectTimeFiler",mSelectedFilterCriteriaVo);
//
//        return bundle;
//    }
//
//    /**
//     * 切换排序，刷新数据
//     * 切换排序时需要清空数据列表，按照此排序请求当前二级分类下的第一个三级分类商品数据
//     *
//     * @param sortType
//     */
//    @Override
//    public void changeSortType(String sortType) {
//        actionState = ActionState.PRODUCT_REFRESH;
//        this.sortType = sortType;
//        changeSortTimeType();
//    }
//
//    /**
//     * 切换综合、销量、价格、促销和时效筛选的
//     */
//    private void changeSortTimeType() {
//        if (thirdCategory != null && thirdCategory.size() > 0) {
//            //有三级分类，切换三级分类，刷新数据
//            changeThirdCate(false, productContainer.getCurrentThirdCate(), true);
//        } else {
//            generateNewSearchPvId(1);
//            //没有三级分类，刷新当前二级分类的数据
//            CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(), pvId, logId),
//                    DEFAULT_EFFECT, currCId, currMid, sortType, true,
//                    1, CategoryProductContainer.PAGE_SIZE, pvId, logId,source, getCategoryQueryConditions(),categoryContainerInterface);
//        }
//    }
//
//    @Override
//    public void changeTimeFilter(FilterCriteriaVo selectFilter,boolean needRefresh) {
//        this.mSelectedFilterCriteriaVo = selectFilter;
//        if (needRefresh) {
//            actionState = ActionState.PRODUCT_REFRESH;
//            changeSortTimeType();
//        }
//    }
//    @NotNull
//    private List<Object> getCategoryQueryConditions() {
//        List<Object> queryConditions = new ArrayList<>();
//        if (mSelectedFilterCriteriaVo != null && mSelectedFilterCriteriaVo.getQueryCondition() != null) {
//            queryConditions.add(mSelectedFilterCriteriaVo.getQueryCondition());
//        }
//        return queryConditions;
//    }
//
//    /**
//     * 获取分类下商品
//     *
//     * @param currPage
//     * @param pageSize
//     * @param pagination 是否分页
//     */
//    private void getWareInfoByCid(int currPage, int pageSize, boolean pagination) {
//        int effect = DEFAULT_EFFECT;
//        if (actionState == ActionState.PRODUCT_LOAD_MORE || actionState == ActionState.PRODUCT_INSERT) {
//            effect = NO_EFFECT;
//        }
//        generateNewSearchPvId(currPage);
//        CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(),pvId,logId),
//                effect, currCId, currMid, sortType, pagination, currPage, pageSize,pvId,logId,source,getCategoryQueryConditions(),categoryContainerInterface);
//    }
//
//    @Override
//    public void onProductLoadMore(int currPage, int pageSize) {
//        actionState = ActionState.PRODUCT_LOAD_MORE;
//        getWareInfoByCid(currPage, pageSize, true);
//    }
//
//    @Override
//    public void onSinkProductLoadMore(long cateId,String mid, int page, int pageSize, List<String> filterSkuId) {
//        actionState = ActionState.PRODUCT_INSERT;
//        generateNewSearchPvId(page);
//        CategoryRequest.getSinkWareInfo(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(),pvId,logId),
//                cateId, mid, sortType, false, page, pageSize, filterSkuId,pvId,logId,getCategoryQueryConditions(),categoryContainerInterface);
//    }
//
//    @Override
//    public void onPullThirdCate(int thirdIndex) {
//        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
//            return;
//        }
//        actionState = ActionState.PRODUCT_INSERT;
//        changeThirdCate(false, thirdIndex, true);
//    }
//
//    @Override
//    public void onLoadThirdCate(int thirdIndex) {
//        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
//            return;
//        }
//        generateNewSearchPvId(1);
//        currThirdCateIndex = thirdIndex;
//        actionState = ActionState.PRODUCT_LOAD_MORE;
//        //为防止三级分类锚点闪动，上拉加载三级分类时暂不切换三级分类选中位置，当滚动到顶部时再切换
//        long tempThirdCateId = thirdCategory.get(thirdIndex).getId();
//        currCId = tempThirdCateId;
//        currMid = thirdCategory.get(thirdIndex).getMid();
//        thirdCateId = tempThirdCateId+"";
//        thirdMid = currMid;
//        thirdCategoryName = thirdCategory.get(thirdIndex).getName();
//        CategoryRequest.getWareInfoByCid(mActivity, new CateWareInfoListener(secondAdapter.getSelectCid(),pvId,logId),
//                NO_EFFECT, tempThirdCateId,currMid, sortType, true,
//                1, CategoryProductContainer.PAGE_SIZE,pvId,logId,source,getCategoryQueryConditions(),categoryContainerInterface);
//    }
//
//    @Override
//    public void onScrollToThirdCate(int thirdIndex) {
//        if (thirdCategory == null || thirdIndex >= thirdCategory.size()) {
//            return;
//        }
//        changeThirdCate(false, thirdIndex, false);
//    }
//
//    @Override
//    public void onChangeSecondCate(int secondIndex) {
//        actionState = ActionState.PRODUCT_REFRESH;
//        if (secondIndex == currSecondCateIndex) {
//            return;
//        }
//        changeSecondCate(secondIndex, false, secondIndex > currSecondCateIndex);
//    }
//
//    @Override
//    public void foldFirstCate(boolean fold, boolean scroll) {
//        if(!isCanFoldFirstCate){
//            return;
//        }
//        // 是否可以折叠
//        if (rvFirstCate == null || rvFirstCate.getVisibility() != View.VISIBLE) {
//            return;
//        }
//        if (isAnimating) {
//            return;
//        }
//        if (fold == lastFold) {
//            return;
//        }
//        isAnimating = true;
//        lastFold = fold;
//        skipNextFold = true;
//
//        final int[] lastOffset = {0};
//        final int[] lastPosition = {0};
//        SFLogCollector.d("lsp", " ===============  fold ============" + fold);
//        int unFoldMarginTop = getWidthByDesignValueFitFold(mActivity, 47, 375);
//        int foldMarginTop = getWidthByDesignValueFitFold(mActivity, -5, 375);
//        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) rvFirstCate.getLayoutParams();
//        int curMargin = params.topMargin;
//        ValueAnimator animator = null;
//        if (fold) {
//            animator = ValueAnimator.ofInt(curMargin, foldMarginTop);
//        } else {
//            animator = ValueAnimator.ofInt(curMargin, unFoldMarginTop);
//        }
//        animator.setDuration(150);
//        animator.addListener(new Animator.AnimatorListener() {
//            @Override
//            public void onAnimationStart(Animator animation) {
//                if (oslSencondCate != null) {
//                    oslSencondCate.setNeedConsumed(true);
//                    oslSencondCate.keepCurState(true);
//                }
//                secondCateListView.setLayoutFrozen(true);
//                if (fold) {
//                    //折叠的时候恢复顶部滚动位置
//                    LinearLayoutManager manager = (LinearLayoutManager) secondCateListView.getLayoutManager();
//                    //获取可视的第一个view
//                    View topView = manager.getChildAt(0);
//                    if (topView != null) {
//                        //获取与该view的顶部的偏移量
//                        lastOffset[0] = topView.getTop();
//                        //得到该View的数组位置
//                        lastPosition[0] = manager.getPosition(topView);
//                    }
//                } else {
//                    lastOffset[0] = 0;
//                    //得到该View的数组位置
//                    lastPosition[0] = 0;
//                }
//            }
//
//            @Override
//            public void onAnimationEnd(Animator animation) {
//                secondCateListView.setLayoutFrozen(false);
//                //折叠的时候恢复顶部滚动位置
//                if (secondCateListView.getLayoutManager() != null && lastPosition[0] >= 0) {
//                    ((LinearLayoutManager) secondCateListView.getLayoutManager()).scrollToPositionWithOffset(lastPosition[0], lastOffset[0]);
//                }
//                //展开时要恢复弹性位置
//
//                handler.postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        isAnimating = false;
//                        if (oslSencondCate != null) {
//                            oslSencondCate.setNeedConsumed(false);
//                            oslSencondCate.keepCurState(false);
//                        }
//                    }
//                }, 40);
//            }
//
//            @Override
//            public void onAnimationCancel(Animator animation) {
//
//            }
//
//            @Override
//            public void onAnimationRepeat(Animator animation) {
//
//            }
//        });
//        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
//            @Override
//            public void onAnimationUpdate(ValueAnimator animation) {
//                //ugly
////                if (fold) {
//                //折叠的时候恢复顶部滚动位置
//                if (secondCateListView.getLayoutManager() != null && lastPosition[0] >= 0) {
//                    ((LinearLayoutManager) secondCateListView.getLayoutManager()).scrollToPositionWithOffset(lastPosition[0], lastOffset[0]);
//                }
////                }
//                int value = (int) animation.getAnimatedValue();
//                params.topMargin = value;
//                rvFirstCate.setLayoutParams(params);
//            }
//        });
//        animator.start();
//    }
//
//    /**
//     * 分类下商品请求回调
//     */
//    public class CateWareInfoListener extends FreshResultCallback<ResponseData<CategoryWareInfoResult>> {
//
//        private final long secId;
//        private String pvId;
//        private String logId;
//        private boolean hasData = false;
//        public CateWareInfoListener(long secId,String pvId,String logId) {
//            this.secId = secId;
//            this.logId = logId;
//            this.pvId = pvId;
//        }
//
//        @Override
//        public void onEnd(ResponseData<CategoryWareInfoResult> object, FreshHttpSetting httpSetting) {
//            try {
//                SFLogCollector.i("yyyyyy","二级分类："+secondCateName+" 三级分类："+thirdCategoryName+"====CateWareInfoListener======"+httpSetting.isFromCache()+"=======");
//                if (object != null && ResponseData.CODE_SUCC.equals(object.getCode()) && object.getData() != null) {
//                    CategoryWareInfoResult wareInfoResult = object.getData();
//                    if (wareInfoResult != null) {
//                        hasData = true;
//                        showCateProducts(secId, wareInfoResult.getCid(), wareInfoResult,pvId,logId,httpSetting.isFromCache());
//                    }
//                } else {
//                    hasData = false;
//                    showCateProducts(secId, currCId, new CategoryWareInfoResult(),pvId,logId,true);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                JdCrashReport.postCaughtException(e);
//            }
//        }
//
//        @Override
//        public void onError(FreshHttpException error) {
//            try {
//                if (actionState == ActionState.PRODUCT_REFRESH) {
//                    if (!hasData && error!=null && error.getHttpSetting()!=null && error.getHttpSetting().getCacheConfig()!=null) {
//                        CategoryRequest.getCacheData(mActivity,error.getHttpSetting());
//                    }
//                } else {
//                    if (productContainer!=null) {
//                        productContainer.finishRefresh();
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                JdCrashReport.postCaughtException(e);
//            }
//        }
//    }
//
//    /**
//     * 地址修改后接收者
//     */
//    private BroadcastReceiver addressChangeReceiver = new BroadcastReceiver() {
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            //这个地方原有的广播发送位置只有1个，其他场景没有发送该广播，应该属于历史逻辑没有维护更新，页面目前主要靠onResume刷新
//            if (intent != null && LbsBroadcastConstants.ACTION_UPDATE_ADDRESS.equals(intent.getAction())) {
//                if (!StringUtil.safeEqualsAndNotNull(storeId, TenantIdUtils.getStoreId()) && !mHidden) {
//                    storeIdChanged();
//                }
//            }
//        }
//    };
//
//
//    @Override
//    public void onDestroy() {
//        isDestroy = true;
//        handler.removeCallbacksAndMessages(null);
//        if (dropDownFirstCategoryPop != null && dropDownFirstCategoryPop.isShowing()) {
//            dropDownFirstCategoryPop.dismiss();
//        }
//        if (productContainer != null) {
//            productContainer.onDestroy();
//        }
//        LocalBroadcastManager.getInstance(mActivity).unregisterReceiver(addressChangeReceiver);
//    }
//
//
//    @Override
//    public void onActivityResult(int requestCode, int resultCode, Intent data) {
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//
//    }
//
//    /**
//     * 新的搜索 要生成一个新的pvId，都是从第一页开始的，后续翻页不变
//     * 新的搜索 要生成一个新的logId，第一页的logId等于pvId，后续分页再随机生成
//     */
//    private void generateNewSearchPvId(int currentPage) {
//        if (currentPage == 1) {
//            pvId = getRandomString(32);
//            logId = pvId;
//        } else {
//            //当前页码是其他页 需要生成新的logId
//            logId = getRandomString(32);
//        }
//    }
//
//    /**
//     * 获取一个随机值
//     *
//     * @param length 随机字符串的长度
//     * @return 返回一个随机值
//     */
//    private String getRandomString(int length) {
//        String str = "abcdefghijklmnopqrstuvwxyz0123456789";
//        Random random = new Random();
//        StringBuffer sb = new StringBuffer();
//        for (int i = 0; i < length; i++) {
//            int number = random.nextInt(36);
//            sb.append(str.charAt(number));
//        }
//        return sb.toString();
//    }
//}
