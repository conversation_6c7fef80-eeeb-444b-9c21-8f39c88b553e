package com.xstore.floorsdk.fieldcategory;

import com.jd.framework.json.JDJSON;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

/**
 * sgm业务异常上报
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
public class SgmReportBusinessErrorLog {
    public static int ERROR_TYPE = 9528;
    public static String ERROR_LOCATION = "分类页";

    public static void reportFirstCateEmpty() {
        try {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("一级分类数据为空，租户id：");
            stringBuffer.append(TenantIdUtils.getTenantId());
            stringBuffer.append("，门店id：");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_一级类目数据为空" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportFirstCateLess5() {
        try {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("一级分类数据小于5条，租户id：");
            stringBuffer.append(TenantIdUtils.getTenantId());
            stringBuffer.append("，门店id：");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_一级分类数据小于5条" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportSecondCateEmpty(String firstCateId, String firstCateName) {
        try {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("二级分类数据为空，对应的一级分类：");
            stringBuffer.append(firstCateName);
            stringBuffer.append(" ");
            stringBuffer.append(firstCateId);
            stringBuffer.append("，租户id：");
            stringBuffer.append(TenantIdUtils.getTenantId());
            stringBuffer.append("，门店id：");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_二级类目数据为空" + "_" + firstCateName + "下" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportProductNoDataLayout(String firstCateId, String firstCateName, String secondCateId, String secondCateName, String thirdCateId, String thirdCateName) {
        try {
//            分类_兜底页_一级分类id_二级分类id_三级分类id_storeName_storeId
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("分类_兜底页_");
            stringBuffer.append(firstCateName);
            stringBuffer.append("_");
            stringBuffer.append(firstCateId);
            stringBuffer.append("_");
            stringBuffer.append(secondCateId);
            stringBuffer.append("_");
            stringBuffer.append(secondCateName);
            stringBuffer.append("_");
            stringBuffer.append(thirdCateId);
            stringBuffer.append("_");
            stringBuffer.append(thirdCateName);
            stringBuffer.append("_");
            stringBuffer.append(AddressStoreHelper.getAddressStoreBean().getStoreName());
            stringBuffer.append("_");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = stringBuffer.toString();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void reportFirstPageDataEmpty(String firstCateId, String firstCateName, String secondCateId, String secondCateName, String thirdCateId, String thirdCateName) {
        try {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("第一页商品数据为空，对应的一级分类：");
            stringBuffer.append(firstCateName);
            stringBuffer.append(" ");
            stringBuffer.append(firstCateId);
            stringBuffer.append("，二级分类：");
            stringBuffer.append(secondCateName);
            stringBuffer.append(" ");
            stringBuffer.append(secondCateId);
            stringBuffer.append("，三级分类：");
            stringBuffer.append(thirdCateName);
            stringBuffer.append(" ");
            stringBuffer.append(thirdCateId);
            stringBuffer.append("，租户id：");
            stringBuffer.append(TenantIdUtils.getTenantId());
            stringBuffer.append("，门店id：");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_第一页商品数据为空" + "_" + firstCateName + "下" + "_" + secondCateName + "下" + "_" + thirdCateName + "下" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportFirstCateIconUrlEmpty(String firstCateId, String firstCateName) {
        try {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("一级分类：");
            stringBuffer.append(firstCateName);
            stringBuffer.append(" ");
            stringBuffer.append(firstCateId);
            stringBuffer.append(", icon链接为空，租户id：");
            stringBuffer.append(TenantIdUtils.getTenantId());
            stringBuffer.append("，门店id：");
            stringBuffer.append(TenantIdUtils.getStoreId());
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_一级分类icon链接未下发" + "_" + firstCateName + "下" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = stringBuffer.toString();
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reporCateCrash(String data) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_crash" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.ext1 = data;
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
        }
    }

    public static void reporCateCrashViewType(int viewType) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_crash" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId() + "_viewType: " + viewType;
            errorLog.ext1 = "viewType: " + viewType;
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
        }
    }

    public static void reporCateCrashItem(int viewType, SkuInfoBean skuInfoBean,int position) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = ERROR_TYPE;
            errorLog.errorCode = "分类_crash" + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId() + "_viewType: " + viewType+"_position: "+position;
            errorLog.ext1 = skuInfoBean == null ? "skuInfoBean is null" : JDJSON.toJSONString(skuInfoBean);
            errorLog.location = ERROR_LOCATION;
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
        }
    }
}
