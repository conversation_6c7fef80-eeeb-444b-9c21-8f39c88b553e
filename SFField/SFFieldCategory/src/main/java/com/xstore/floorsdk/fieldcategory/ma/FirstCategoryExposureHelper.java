package com.xstore.floorsdk.fieldcategory.ma;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.floorsdk.fieldcategory.adapter.FirstCategoryAdapter;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 分类商品列表的曝光帮助类
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public class FirstCategoryExposureHelper {

    private Map<String, Boolean> exposuredCategoryMap = new HashMap<>();

    private FirstCategoryReporter categoryFieldReporter;

    public FirstCategoryExposureHelper(FirstCategoryReporter categoryFieldReporter) {
        this.categoryFieldReporter = categoryFieldReporter;
        if (exposuredCategoryMap == null) {
            exposuredCategoryMap = new HashMap<>();
        }
    }

    public void exposureByHand(RecyclerView recyclerView, int newState) {
        if (recyclerView == null) {
            return;
        }
        if (recyclerView.getAdapter() instanceof FirstCategoryAdapter && newState == RecyclerView.SCROLL_STATE_IDLE) {
            FirstCategoryAdapter firstCategoryAdapter = (FirstCategoryAdapter) recyclerView.getAdapter();
            if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int first = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                int last = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                for (int i = first; i <= last; i++) {
                    CategoryBean itemData = firstCategoryAdapter.getItemData(i);
                    if (itemData == null) {
                        continue;
                    }
                    if (StringUtil.isNullByString(itemData.getMid())) {
                        continue;
                    }
                    if (exposuredCategoryMap.containsKey(itemData.getMid())
                            && exposuredCategoryMap.get(itemData.getMid())) {
                        continue;
                    }
                    exposuredCategoryMap.put(itemData.getMid(), true);
                    if (categoryFieldReporter != null) {
                        categoryFieldReporter.firstCategoryExpose(itemData, i + 1);
                    }
                }
            }
        }
    }
}
