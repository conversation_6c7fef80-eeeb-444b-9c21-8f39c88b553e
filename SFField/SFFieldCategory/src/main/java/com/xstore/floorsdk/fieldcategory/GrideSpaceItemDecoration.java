package com.xstore.floorsdk.fieldcategory;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class GrideSpaceItemDecoration extends RecyclerView.ItemDecoration {

    private final int defaultSpaceWidth;
    private final int lineSpace;
    private final int bottomSpace;
    private final int growCount;

    public GrideSpaceItemDecoration(int defaultSpaceWidth, int lineSpace, int bottomSpace, int growCount) {
        this.defaultSpaceWidth = defaultSpaceWidth;
        this.lineSpace = lineSpace;
        this.bottomSpace = bottomSpace;
        this.growCount = growCount;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int itemCount = state.getItemCount();
        int column = position % growCount; // 当前项所在的列
        int row = position / growCount; // 当前项所在的行
        int totalRows = (itemCount + growCount - 1) / growCount; // 总行数

        // 设置左右间距
//        if (column == 0) {
//            // 第一列
//            outRect.left = firstItemLeftSpace;
//            outRect.right = defaultSpaceWidth / 2;
//        } else if (column == growCount - 1) {
//            // 最后一列
//            outRect.left = defaultSpaceWidth / 2;
//            outRect.right = lastItemRightSpace;
//        } else {
            // 中间列
            outRect.left = defaultSpaceWidth / 2;
            outRect.right = defaultSpaceWidth / 2;
//        }

        // 设置上下间距
        if (row == 0) {
            // 第一行
            outRect.top = 0;
        } else {
            outRect.top = lineSpace;
        }

        // 设置底部间距
        if (row == totalRows - 1) {
            // 最后一行
            outRect.bottom = bottomSpace;
        } else {
            outRect.bottom = 0;
        }
    }
}