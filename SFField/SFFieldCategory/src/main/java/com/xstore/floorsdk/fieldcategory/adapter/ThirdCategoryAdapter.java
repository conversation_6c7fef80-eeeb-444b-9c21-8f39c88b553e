package com.xstore.floorsdk.fieldcategory.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldcategory.R;
import com.xstore.floorsdk.fieldcategory.bean.CategoryBean;

import java.util.List;


/**
 * ThirdCategoryAdapter简介
 * 分类页  三级分类adapter
 *
 * <AUTHOR>
 * @date 2019-12-2 15:04:47
 */
public class ThirdCategoryAdapter extends RecyclerView.Adapter<ThirdCategoryAdapter.Holder> {

    private Context context;
    private List<CategoryBean> categories;
    private int curSelectPos;
    private ItemClickListener itemClickListener;
    private boolean matchParent;
    private int left;
    private int top;
    private int right;
    private int bottom;


    public ThirdCategoryAdapter(Context context, List<CategoryBean> categories, int thirdPosition, boolean matchParent) {
        this.context = context;
        this.categories = categories;
        this.curSelectPos = thirdPosition;
        this.matchParent = matchParent;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_category_third_cate_label, null);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int i) {
        holder.itemView.setPadding(left, top, right, bottom);
        CategoryBean cate = categories.get(i);
        holder.tvLabel.setText(cate.getName());
        ViewGroup.LayoutParams layoutParams = holder.tvLabel.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        if (matchParent) {
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        } else {
            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        holder.tvLabel.setLayoutParams(layoutParams);

        ViewGroup.LayoutParams layoutParams2 = holder.llBg.getLayoutParams();
        if (layoutParams2 == null) {
            layoutParams2 = new RecyclerView.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        if (matchParent) {
            layoutParams2.width = ViewGroup.LayoutParams.MATCH_PARENT;
        } else {
            layoutParams2.width = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        holder.llBg.setLayoutParams(layoutParams);

        if (i == curSelectPos) {
            holder.tvLabel.setTextColor(context.getResources().getColor(R.color.sf_theme_color_level_1));
            holder.llBg.setBackgroundResource(R.drawable.sf_field_category_thrid_cate_selected);
        } else {
            holder.tvLabel.setTextColor(context.getResources().getColor(R.color.sf_field_category_color_1D1F2B));
            holder.llBg.setBackgroundResource(R.drawable.sf_field_category_thrid_cate_unselect);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (itemClickListener != null && curSelectPos != i) {
                    itemClickListener.clickItem(i);
                }
            }
        });
    }
    public CategoryBean getItem(int i) {
        if(i < 0 || categories == null || categories.size() == 0 || i >= categories.size()) {
            return null;
        }
        return categories.get(i);
    }
    @Override
    public int getItemCount() {
        if (categories == null) {
            return 0;
        }
        return categories.size();
    }

    public void setSetSelectPos(int pos) {
        curSelectPos = pos;
        notifyDataSetChanged();
    }

    public void setOnItemClickListener(ItemClickListener listener) {
        itemClickListener = listener;
    }

    public List<CategoryBean> getData() {
        return categories;
    }

    public int getSelectPos() {
        return curSelectPos;
    }

    public void setPadding(int left, int top, int right, int bottom) {
        this.left = left;
        this.top = top;
        this.right = right;
        this.bottom = bottom;
    }

    public class Holder extends RecyclerView.ViewHolder {

        LinearLayout llBg;
        TextView tvLabel;

        public Holder(@NonNull View itemView) {
            super(itemView);
            llBg = itemView.findViewById(R.id.ll_bg);
            tvLabel = itemView.findViewById(R.id.tv_label);
        }
    }

    public interface ItemClickListener {
        void clickItem(int pos);
    }
}
