package com.xstore.floorsdk.fieldcategory.bean;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/24
 */
public class BannerBean implements Serializable {
    private String image;
    private ActionBean action;

    /**
     * 是否曝光过
     */
    private boolean hasShowView = false;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public ActionBean getAction() {
        return action;
    }

    public void setAction(ActionBean action) {
        this.action = action;
    }

    public boolean isHasShowView() {
        return hasShowView;
    }

    public void setHasShowView(boolean hasShowView) {
        this.hasShowView = hasShowView;
    }

    public static class ActionBean implements Serializable {
        private String toUrl;
        private String clsTag;
        private int urlType;

        public String getToUrl() {
            return toUrl;
        }

        public void setToUrl(String toUrl) {
            this.toUrl = toUrl;
        }

        public String getClsTag() {
            return clsTag;
        }

        public void setClsTag(String clsTag) {
            this.clsTag = clsTag;
        }

        public int getUrlType() {
            return urlType;
        }

        public void setUrlType(int urlType) {
            this.urlType = urlType;
        }
    }
}
