package androidx.recyclerview.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.framework.json.JDJSON;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldcategory.SgmReportBusinessErrorLog;
import com.xstore.floorsdk.fieldcategory.adapter.CategoryProductAdapter;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.List;


/**
 * 楼层列表容器
 */
public class CategoryRecyclerView extends RecyclerView {

    /**
     * 伪造的系统功能回调
     */
    private FakeCallback fakeCallback;

    public CategoryRecyclerView(@NonNull Context context) {
        super(context);
    }

    public CategoryRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CategoryRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void attachViewToParent(View child, int index, ViewGroup.LayoutParams params) {
        try {
            super.attachViewToParent(child, index, params);
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e, "CategoryRecyclerView");
            if (getAdapter() != null && (getAdapter() instanceof CategoryProductAdapter)) {
                CategoryProductAdapter productAdapter = (CategoryProductAdapter) getAdapter();
                List<SkuInfoBean> listData = productAdapter.getProductList();
                String sgmMsg = "列表数据为空";
                try {
                    if (listData != null && listData.size() != 0) {
                        sgmMsg = JDJSON.toJSONString(listData);
                    }
                } catch (Exception e1) {
                    sgmMsg = "json 转换异常:" + e1.getMessage();
                }
                SgmReportBusinessErrorLog.reporCateCrash(sgmMsg);
            }
        }
    }

    /**
     * 绕过recyclerView 自身机制
     * 获取到每个item定义的特殊id，并不影响原本的itemId机制
     *
     * @param fakeId
     * @return
     */
    public ViewHolder findViewHolderForFakeItemId(long fakeId) {
        if (mAdapter == null || !mAdapter.hasStableIds()) {
            return null;
        }
        final int childCount = mChildHelper.getUnfilteredChildCount();
        ViewHolder hidden = null;
        for (int i = 0; i < childCount; i++) {
            final ViewHolder holder = getChildViewHolderInt(mChildHelper.getUnfilteredChildAt(i));
            if (fakeCallback != null) {
                if (holder != null && !holder.isRemoved() && fakeCallback.getFakeItemId(holder) == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            } else {
                if (holder != null && !holder.isRemoved() && holder.getItemId() == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            }
        }
        return hidden;
    }

    /**
     * 伪造的系统功能回调
     */
    public interface FakeCallback {
        /**
         * 获取伪造的itemId
         * 由于holder类型不定，将此实现暴露出去实现
         *
         * @param holder 对应的holder
         * @return 当前holder绑定数据对应的itemid
         */
        long getFakeItemId(ViewHolder holder);
    }

    /**
     * 设置回调
     *
     * @param fakeCallback
     */
    public void setFakeCallback(FakeCallback fakeCallback) {
        this.fakeCallback = fakeCallback;
    }
}
