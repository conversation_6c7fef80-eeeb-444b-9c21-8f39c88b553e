<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_expand_tip"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_ware_expand_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/sf_field_category_color_1D1F2B"
            android:textSize="12dp" />

        <ImageView
            android:layout_width="10dp"
            android:layout_height="6dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/sf_field_category_black_down_arrow_5_3" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/ll_expand_tip"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/sf_field_category_color_08000000" />

</RelativeLayout>