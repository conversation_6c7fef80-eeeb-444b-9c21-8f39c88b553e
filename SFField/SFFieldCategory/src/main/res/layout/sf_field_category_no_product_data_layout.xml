<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@color/sf_field_category_white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="50dp">

        <ImageView
            android:id="@+id/no_product_image_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/sf_theme_image_search_empty" />

        <TextView
            android:id="@+id/no_product_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/sf_field_category_cate_no_goods_see_other"
            android:textColor="@color/sf_field_category_color_898989"
            android:textSize="13dp" />

    </LinearLayout>
</RelativeLayout>