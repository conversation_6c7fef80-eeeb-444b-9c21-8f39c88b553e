<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/sf_card_color_transparent"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <View
        android:id="@+id/mask_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sf_field_category_dialog_white_top_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="48dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="推荐搭配"
                android:textStyle="bold"
                android:textColor="#1A1A1A"
                android:textSize="14dp" />

            <ImageView
                android:id="@+id/close_image"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:padding="5dp"
                android:src="@drawable/sf_card_similar_list_close" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/product_recycler_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal" />
    </LinearLayout>
</LinearLayout>