<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="22dp"
        android:paddingBottom="100dp">

        <ImageView
            android:id="@+id/iv_cate_footer"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginRight="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/sf_field_category_next_category" />

        <TextView
            android:id="@+id/tv_cate_footer_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/sf_field_category_color_999999"
            android:textSize="13dp"
            tools:text="上滑继续浏览 产地/品牌" />
    </LinearLayout>

</RelativeLayout>