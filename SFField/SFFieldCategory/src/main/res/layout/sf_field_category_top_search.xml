<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_top_bar"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/sf_field_category_white"
    android:gravity="center_vertical">

    <!--返回按钮-->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginLeft="5dp"
        android:padding="5dp"
        android:src="@drawable/sf_field_category_img_back" />

    <!--搜索框-->
    <RelativeLayout
        android:id="@+id/search_rl"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginLeft="6dp"
        android:layout_marginRight="14dp"
        android:background="@drawable/sf_field_category_shape_search">

        <ImageView
            android:id="@+id/iv_my_order_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:scaleType="fitXY"
            android:src="@drawable/sf_field_category_img_search" />

        <TextView
            android:id="@+id/tv_hot_words"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="5dp"
            android:layout_toRightOf="@id/iv_my_order_search"
            android:background="@null"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/sf_field_category_color_8A8A8A"
            android:textColorHint="@color/font_C_secondary_info_color_black_gray"
            android:textSize="13dp" />

        <!--轮播区域-->
        <com.xstore.sdk.floor.floorcore.widget.FixViewFlipper
            android:id="@+id/vf_hot_search_word"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="5dp"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_my_order_search"
            android:visibility="gone" />
    </RelativeLayout>

</LinearLayout>