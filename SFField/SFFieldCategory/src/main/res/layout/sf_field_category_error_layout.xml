<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/sf_field_category_white"
    android:gravity="center">


    <ImageView
        android:id="@+id/tv_nodata_image_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:src="@drawable/sf_theme_image_no_net" />
    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_nodata_image_item"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:text="网络异常,请检查您的网络设置"
        android:textColor="@color/sf_field_category_color_898989"
        android:textSize="13dp" />
    <TextView
        android:id="@+id/refresh_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/text"
        android:layout_centerHorizontal="true"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/sf_field_category_shape_refresh_bg"
        android:text="刷新试试"
        android:textColor="#0A665E"
        android:textSize="15dp" />
    
    <View
        android:layout_below="@id/refresh_text"
        android:layout_width="match_parent"
        android:layout_height="100dp"></View>
</RelativeLayout>