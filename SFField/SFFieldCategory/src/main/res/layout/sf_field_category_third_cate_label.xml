<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sf_field_category_thrid_cate_selected">

        <TextView
            android:id="@+id/tv_label"
            android:layout_width="wrap_content"
            android:layout_height="21dp"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:includeFontPadding="false"
            android:paddingRight="10dp"
            android:singleLine="true"
            android:textSize="12dp"
            tools:text="sss" />
    </LinearLayout>

</LinearLayout>