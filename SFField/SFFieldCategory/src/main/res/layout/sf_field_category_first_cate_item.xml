<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">


    <com.xstore.sdk.floor.floorcore.widget.YLCircleImageView
        android:id="@+id/iv_cate_logo"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="1dp"
        android:background="@color/sf_field_category_white"
        app:sf_floor_core_borderColorYL="@color/sf_theme_color_level_1"
        app:sf_floor_core_borderSpaceYL="1.5dp"
        app:sf_floor_core_borderWidthYL="1.5dp"
        app:sf_floor_core_radiusYL="20dp" />


    <TextView
        android:id="@+id/tv_cate_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="6dp"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxEms="6"
        android:paddingLeft="6dp"
        android:paddingTop="1dp"
        android:paddingRight="6dp"
        android:paddingBottom="1dp"
        android:singleLine="true"
        android:textColor="@color/sf_field_category_color_252525"
        android:textSize="11dp"
        tools:text="测试档口哈哈" />

</LinearLayout>