<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#b0000000" />

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:background="@drawable/sf_field_category_shape_drop_down_menu"
        android:clickable="true"
        android:orientation="vertical">

        <!--顶部下拉菜单-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:text="@string/sf_field_category_all_category"
                android:textColor="@color/sf_field_category_color_1D1F2B"
                android:textSize="14dp"
                android:textStyle="bold" />

        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPullDownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:background="@color/sf_field_category_white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_marginTop="5dp"
            android:background="@color/sf_field_category_color_F6F6F6" />

        <LinearLayout
            android:id="@+id/tv_fold_up"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableRight="@drawable/sf_field_category_img_arrow_up"
                android:drawablePadding="6dp"
                android:padding="10dp"
                android:text="@string/sf_field_category_click_collapse"
                android:textColor="@color/sf_field_category_color_666666"
                android:textSize="12dp" />
        </LinearLayout>


    </LinearLayout>

</FrameLayout>