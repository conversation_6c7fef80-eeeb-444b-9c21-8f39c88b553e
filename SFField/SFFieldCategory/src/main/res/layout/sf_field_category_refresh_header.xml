<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/sf_field_category_white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/header_down_arrow"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:scaleType="center"
        android:src="@drawable/sf_field_category_gray_arrow_down"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_refresh_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:textColor="@color/sf_field_category_color_999999"
        android:textSize="13dp" />

    <ProgressBar
        android:id="@+id/header_progressbar"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginBottom="5dp"
        android:indeterminate="false"
        android:indeterminateDrawable="@drawable/bdcodehelper_refresh_progress_style"
        android:visibility="gone" />

</LinearLayout>