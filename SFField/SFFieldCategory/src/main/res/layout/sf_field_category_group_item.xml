<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/icon"
        android:layout_width="4dp"
        android:layout_height="18dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:background="@color/sf_theme_color_level_1" />

    <LinearLayout
        android:id="@+id/ll_second_cate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/hot_icon"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_marginRight="4dp"
            android:scaleType="centerInside"
            android:visibility="visible"
            tools:src="@drawable/sf_theme_image_placeholder_square" />

        <TextView
            android:id="@+id/group_name"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/sf_field_category_color_252525"
            android:textSize="12dp" />
    </LinearLayout>
</RelativeLayout>