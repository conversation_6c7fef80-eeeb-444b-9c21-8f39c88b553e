<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_third_category"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_third_arrow"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_second_cate_name"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_third_arrow"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/sf_field_category_white"
        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:textColor="@color/sf_theme_color_level_1"
        android:textSize="12dp"
        android:textStyle="bold"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_third_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="40dp"
        android:layout_height="0dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:paddingLeft="15dp"
        android:paddingTop="9dp"
        android:paddingRight="15dp"
        android:paddingBottom="9dp"
        android:scaleType="centerInside"
        android:src="@drawable/sf_field_category_arrow_third_cate"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>