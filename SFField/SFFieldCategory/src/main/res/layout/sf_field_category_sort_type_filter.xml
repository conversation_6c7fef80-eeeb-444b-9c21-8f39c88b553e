<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/filter_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.5"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/sf_field_category_color_252525"
                android:textSize="12dp"
                tools:text="全部时效" />

            <ImageView
                android:id="@+id/iv_filter"
                android:layout_width="66dp"
                android:layout_height="13dp" />

            <ImageView
                android:id="@+id/iv_fold"
                android:layout_width="6dp"
                android:layout_height="9dp"
                android:layout_marginLeft="2dp"
                android:src="@drawable/sf_field_category_filter_time_down" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_all"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_weight="1"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/sf_field_category_comprehensive"
            android:textColor="@color/sf_field_category_color_252525"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tv_sold_count"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_weight="1"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/sf_field_category_sales_volume"
            android:textColor="@color/sf_field_category_color_252525"
            android:textSize="12dp" />

        <LinearLayout
            android:id="@+id/price_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/sf_field_category_price"
                android:textColor="@color/sf_field_category_color_252525"
                android:textSize="12dp" />

            <ImageView
                android:id="@+id/price_img"
                android:layout_width="7.2dp"
                android:layout_height="10.8dp"
                android:layout_marginLeft="5dp"
                android:src="@drawable/sf_field_category_search_arrow_normal" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_promotion"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_weight="1"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/sf_field_category_promotion"
            android:textColor="@color/sf_field_category_color_252525"
            android:textSize="12dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/sf_field_category_color_F6F6F6" />
</LinearLayout>