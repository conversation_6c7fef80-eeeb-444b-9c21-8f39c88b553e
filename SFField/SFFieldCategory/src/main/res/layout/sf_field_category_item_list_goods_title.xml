<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="10dp"
        android:drawableLeft="@drawable/sf_field_category_third_title_left"
        android:drawableRight="@drawable/sf_field_category_third_title_right"
        android:drawablePadding="24dp"
        android:gravity="center"
        android:padding="1dp"
        android:textColor="@color/sf_field_category_color_252525"
        android:textSize="12dp"
        android:textStyle="bold" />
<!--    <TextView-->
<!--        android:id="@+id/title"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="1dp"-->
<!--        android:layout_centerInParent="true" />-->
</RelativeLayout>