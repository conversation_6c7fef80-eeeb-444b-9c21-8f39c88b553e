<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="ResourceName">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <com.xstore.floorsdk.fieldcategory.widget.OverScrollLayout
            android:id="@+id/osl_second_cate"
            android:layout_width="84dp"
            android:layout_height="match_parent"
            android:background="@color/sf_card_color_F7F7F7"
            app:srlEnableOverScrollBounce="true"
            app:srlEnableOverScrollDrag="true"
            app:srlEnableRefresh="false">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_second_category"
                android:layout_width="84dp"
                android:layout_height="match_parent"
                android:scrollbars="none" />

        </com.xstore.floorsdk.fieldcategory.widget.OverScrollLayout>
        <!--列表-->
        <com.xstore.floorsdk.fieldcategory.CategoryProductContainer
            android:id="@+id/sku_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
        <include
            android:id="@+id/product_no_data"
            layout="@layout/sf_field_category_no_data_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </LinearLayout>

    <!--空白页-->
    <include
        android:id="@+id/sec_no_data"
        layout="@layout/sf_field_category_no_data_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</FrameLayout>