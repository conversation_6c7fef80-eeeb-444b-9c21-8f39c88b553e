<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sf_field_category_white">


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/cdl_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">

            <!--  banner轮播图  -->
            <com.xstore.floorsdk.fieldcategory.widget.CategoryBannerPager
                android:id="@+id/banner_pager"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|enterAlways|enterAlwaysCollapsed" />

            <!--  三级分类筛选  -->
            <com.xstore.floorsdk.fieldcategory.widget.ThirdCategoryFilter
                android:id="@+id/third_cate_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <!--  筛选条件  -->
            <com.xstore.floorsdk.fieldcategory.widget.CategorySortTypeFilter
                android:id="@+id/sort_type_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"/>

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/ll_smart_refresh_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/smart_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.xstore.floorsdk.fieldcategory.widget.CategoryRefreshHeader
                    android:id="@+id/layout_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />


                <androidx.recyclerview.widget.CategoryRecyclerView
                    android:id="@+id/rv_products"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:cacheColorHint="@android:color/transparent"
                    android:clipToPadding="false"
                    android:divider="@null"
                    android:dividerHeight="0dp"
                    android:fadingEdge="none"
                    android:fastScrollEnabled="false"
                    android:footerDividersEnabled="false"
                    android:headerDividersEnabled="false"
                    android:scrollbars="none"
                    android:smoothScrollbar="true" />

                <com.xstore.floorsdk.fieldcategory.widget.CategoryRefreshFooter
                    android:id="@+id/layout_footer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />


            </com.scwang.smart.refresh.layout.SmartRefreshLayout>


            <include
                android:id="@+id/no_data_layout"
                layout="@layout/sf_field_category_no_product_data_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </LinearLayout>


        <View
            android:id="@+id/product_list_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/sf_field_category_color_66000000"
            android:visibility="gone"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"/>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</RelativeLayout>