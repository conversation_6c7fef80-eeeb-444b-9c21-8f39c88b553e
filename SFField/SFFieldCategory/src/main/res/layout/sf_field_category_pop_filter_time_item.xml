<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="10dp">


    <TextView
        android:id="@+id/tv_filter_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/sf_field_category_color_252525"
        android:textSize="12dp"
        android:visibility="visible"
        android:textStyle="bold"
        tools:text="农夫山泉" />

    <ImageView
        android:id="@+id/iv_filter_key"
        android:layout_width="66dp"
        android:layout_height="13dp"
        android:layout_alignParentLeft="true"
        android:visibility="gone" />


    <ImageView
        android:id="@+id/iv_filter_selected"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_alignParentRight="true"
        android:layout_marginLeft="10dp"
        android:src="@drawable/sf_field_category_filter_selected"
        android:visibility="gone" />

</RelativeLayout>