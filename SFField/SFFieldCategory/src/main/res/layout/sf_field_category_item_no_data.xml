<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_cate_nodata_tip"
        android:layout_width="match_parent"
        android:layout_height="110dp"
        android:gravity="center"
        android:text="@string/sf_field_category_no_data_tip"
        android:textColor="@color/sf_field_category_color_95969F"
        android:textSize="14dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/tv_cate_nodata_tip"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/sf_field_category_color_08000000" />

</RelativeLayout>