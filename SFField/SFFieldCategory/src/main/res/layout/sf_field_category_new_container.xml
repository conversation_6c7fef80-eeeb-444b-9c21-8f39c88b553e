<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <!--  一级类目  -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_first_cate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:background="@color/sf_field_category_white"
        android:minHeight="1dp" />

    <!--  展开全部一级分类按钮  -->
    <LinearLayout
        android:id="@+id/ll_fold_all"
        android:layout_width="36dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/rv_first_cate"
        android:layout_alignBottom="@+id/rv_first_cate"
        android:layout_alignParentRight="true"
        android:background="@color/sf_field_category_white"
        android:gravity="bottom|center_horizontal"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ems="1"
            android:text="@string/sf_field_category_all"
            android:textColor="@color/sf_field_category_color_1D1F2B"
            android:textSize="12dp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="36dp"
            android:layout_height="35dp"
            android:paddingTop="13dp"
            android:paddingBottom="15dp"
            android:src="@drawable/sf_field_category_solid_arrow_down" />

    </LinearLayout>

    <View
        android:id="@+id/view_all_shadow"
        android:layout_width="8dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/rv_first_cate"
        android:layout_alignBottom="@+id/rv_first_cate"
        android:layout_toLeftOf="@+id/ll_fold_all"
        android:background="@drawable/sf_field_category_shape_gradient_shadow" />

    <!-- 顶部搜索 -->
    <com.xstore.floorsdk.fieldcategory.widget.CategorySearchView
        android:id="@+id/search_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rv_first_cate">
        <androidx.viewpager.widget.ViewPager
            android:id="@+id/product_view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />
        <!--空白页-->
        <include
            android:id="@+id/sec_first_no_data"
            layout="@layout/sf_field_category_error_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_float_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|bottom"
            android:layout_marginRight="15dp"
            android:orientation="vertical" />


    </FrameLayout>

    <!--  一级类目下面的阴影  -->
    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_below="@+id/rv_first_cate"
        android:background="@drawable/sf_field_category_shape_hor_gradient_shadow" />

</RelativeLayout>