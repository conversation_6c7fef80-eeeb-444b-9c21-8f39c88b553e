<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:background="@color/sf_field_category_white">

    <TextView
        android:id="@+id/tv_load_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:textColor="@color/sf_field_category_color_999999"
        android:textSize="13dp" />

    <ProgressBar
        android:id="@+id/footer_progressbar"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="5dp"
        android:indeterminate="false"
        android:indeterminateDrawable="@drawable/bdcodehelper_refresh_progress_style"
        android:visibility="gone" />

</LinearLayout>