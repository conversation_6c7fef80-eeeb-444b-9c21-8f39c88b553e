<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/sf_field_category_white"
    android:gravity="center">


    <ImageView
        android:id="@+id/tv_nodata_image_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:src="@drawable/sf_theme_image_search_empty" />

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_nodata_image_item"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:text="@string/sf_field_category_cate_no_goods_see_other"
        android:textColor="@color/sf_field_category_color_898989"
        android:textSize="13dp" />
</RelativeLayout>