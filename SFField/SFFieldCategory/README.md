## 模块说明
分类领域

三个场景进入分类页
1、底部Tab进入分类页
2、首页分类icon球进入分类页
3、H5自有品牌页点击分类icon球进入分类页

## 组件更新日志

### 3.1.4~3.1.5
1. 接入新商卡，新商品模型
2. 类目支持横滑
3. 类目支持视频播放

### 3.1.1
1. 分类商卡支持外卖品

### 3.1.0
1. 分类支持围栏维度刷新

### 3.0.2
1. vi改版

### 3.0.1
1. 分类加载问题

### 2.1.3
1. 业务异常上报

### 2.1.1
1. 跳转商详 新增榜单排名字段

### com.xstore.floorsdk:SFFieldCategory:2.1.0
1. 【新增】分类-一行一个不定高商品卡片增加健康百科的入口点击事件和曝光的回调
【影响】分类一行一个不定高商卡
【回归】分类一行一个不定高商卡
2. 预售立即预订 埋点调整
3. 【修改】当存在连续售罄的三级类目id时，打开折叠的售罄商品有重复
【影响】打开售罄商品数据展示情况，分页加载数据展示是否正确

### com.xstore.floorsdk:SFFieldCategory:2.0.1
1. 【修复】还原二级分类id重置逻辑

### com.xstore.floorsdk:SFFieldCategory:2.0.0
1. 【新增】分类的商品卡片的对比价在售卖价上面展示
【影响】价格展示
【回归】各种价格展示逻辑

2.

### com.xstore.floorsdk:SFFieldCategory:1.0.13
1. 分类商品新增天天低价埋点

### com.xstore.floorsdk:SFFieldCategory:1.0.12
1.解除分类领域对SFloors的依赖
2.使用商品卡片库中一行一个不定高的商品卡片


### com.xstore.floorsdk:SFFieldCategory:1.0.9-1.0.11
1.分类搜索框数据来源用接口返回的，解除对首页搜索楼层的依赖
2.分类删除自己做的缓存，直接用网络缓存器（缓存一级分类接口、二级分类接口、商品列表第一页数据）
  a.一级和二级接口首次进入会读缓存，再网络请求刷新数据
  b.在分类页切换二级接口、商品接口只存不读，异常情况下重新请求只读缓存，不请求接口，防止二刷图片闪动
  c.分类三个接口，请求正常时，缓存开关有效；请求异常时，忽略缓存开关，强制用缓存做兜底数据
3.跳转分类页使用转场动画

### com.xstore.floorsdk:SFFieldCategory:1.0.8
1.图片加载完成渐现动画
2.更换商品模型的路径

### com.xstore.floorsdk:SFFieldCategory:1.0.7
1. 分类列表数据重复、缺少问题

### com.xstore.floorsdk:SFFieldCategory:1.0.6
1. 地址门店服务包路径修改，取数逻辑修改

### com.xstore.floorsdk:SFFieldCategory:1.0.5
1. 分类增加时效筛选（切换门店重置时效筛选和综合筛选）
2、商品曝光、加车、进商详埋点增加promiseType
3、兼容接口请求失败的时没有storeId的情况

### com.xstore.floorsdk:SFFieldCategory:1.0.0-1.0.4
1. 分类独立领域化
