apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'AuraAar'
apply plugin: 'AuraConfig'

android {
    compileSdk = rootProject.ext.android.compileSdkVersion
    //buildToolsVersion = rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        javaCompileOptions {
            annotationProcessorOptions {
                //includeCompileClasspath = true
                arguments = [AROUTER_MODULE_NAME: project.getName(), "project_dir": project.getProjectDir().toString()]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    resourcePrefix 'sf_field_search_'
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    //Android扩展库依赖
    implementation DepAndroid.appcompat
    implementation DepAndroid.material
    implementation DepAndroid.eventbus

    //功能组件层service依赖
    implementation DepSF.SFServiceImageModule
    implementation DepSF.SFServiceMTAManagerModule
    implementation DepSF.SFServiceNetWorkModule
    implementation DepSF.SFServiceSFLogCollectorModule
    implementation DepSF.SFServiceStorageModule
    implementation DepSF.SFServicePlaybase
    implementation DepSF.SFServiceUiKit


    implementation DepSFCB.SFCBThemeResourceModule
    implementation DepSFCB.SFCBBeanModule
    implementation DepSFCB.SFCBCartService
    implementation DepSFCB.SFCBProductCardModule
    implementation DepSFCB.SFCBLbs


    //中台依赖
    implementation DepJD.android_sdk_jdjson
    implementation DepJD.bdcodehelper
    implementation DepJD.JDCrashReport

    //外部三方依赖
    implementation DepEXT.smartRefreshLayout
    implementation DepEXT.secondFloorHeader
    implementation DepEXT.Gson
    kapt DepEXT.arouter_compiler
    implementation DepEXT.arouter_api
    implementation DepEXT.videoCache

    if (fieldSdkProjectDep_floorCore.toBoolean()) {
        implementation project(path: ':HomeFloorSdk:SFFloorCore')
    } else {
        implementation DepSFFieldSDK.SFFloorCore
    }

    if (fieldSdkProjectDep_RecommendGoodFloor.toBoolean()) {
        implementation project(path: ':HomeFloorSdk:SFRangeFloor:RecommendGoodFloor')
    } else {
        implementation DepSFFieldSDK.SFRecommendGoodFloor
    }



}