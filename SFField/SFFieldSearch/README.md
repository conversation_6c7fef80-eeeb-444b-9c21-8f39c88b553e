## 模块说明
搜索领域。


## 组件更新日志

### 3.3.2~3.3.3
1. 搜索主页改版

### 3.1.3~3.1.4
1. 接入新商卡，新商品模型

### 3.1.1
1. 搜索商卡支持外卖品

### 3.1.0 
1. 搜索支持围栏维度刷新


### 3.0.3
1. vi改版

### 3.0.2 
1. 修改推荐无数据异常上报时机


### 3.0.1
1. 推荐数据业务异常上报修改


### 2.1.4
1、业务异常上报

### 2.1.1
1.【新增】搜索领域Graphql接口改为color接口，防止频权
【影响】搜索主页联想词、搜索结果页各级筛选项、大促标、搜索商品、推荐商品、促销信息、券信息、无货推荐
【回归】主搜、促销搜、券搜、加价购凑单、领货码搜索、运费凑单

2跳转商详 新增榜单排名字段

### com.xstore.floorsdk:SFFieldSearch:2.1.0
1. 【新增】搜索-一行一个不定高、瀑布流商品卡片增加健康百科的入口点击事件和曝光的回调
【影响】搜索一行一个不定高、瀑布流商卡
【回归】搜索一行一个不定高、瀑布流商卡
2. 瀑布流新增预售按钮 点击相应以及埋点

### com.xstore.floorsdk:SFFieldSearch:2.0.0
1. 【新增】搜索-云卖场替换使用商品卡片库中一行一个定高的卡片
          搜索-常购清单价格判空
【影响】搜索中云卖场的商品卡片
【回归】云卖场卡片功能


### com.xstore.floorsdk:SFFieldSearch:1.0.14
1. 修复搜索主页getActivity空指针问题

### com.xstore.floorsdk:SFFieldSearch:1.0.13
1. 修复促销搜赠品条点击无跳转问题

### com.xstore.floorsdk:SFFieldSearch:1.0.12
1. 搜索主页PaaS化封装
2. 搜索商品卡片动态控制找相似按钮显示隐藏
3. 搜索云卖场UI使用style实现
4. 搜索结果页商品新增天天低价埋点

### com.xstore.floorsdk:SFFieldSearch:1.0.11
1. 搜索结果使用商品卡片库中一行一个不定高的卡片
2. 云卖场使用商品卡片库中一行多个定高的卡片

### com.xstore.floorsdk:SFFieldSearch:1.0.10
1. 类目平铺宽度修改，展示扩展至5.5个
2. 券搜增加使用限制提示
3. 券提示语样式修改
4. 图片加载完成渐现动画
5. 更换商品模型的路径
6. 使用商品卡片组件库中瀑布流卡片

### com.xstore.floorsdk:SFFieldSearch:1.0.9
1. 地址门店服务包路径修改，取数逻辑修改

### com.xstore.floorsdk:SFFieldSearch:1.0.8
1. 修改搜索结果页大促活动标被裁剪问题。

### com.xstore.floorsdk:SFFieldSearch:1.0.7
1. 促销信息接口入参promotionId改为long型。

### com.xstore.floorsdk:SFFieldSearch:1.0.0-1.0.6
1. 创建仓库。
