<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="77dp"
    android:layout_margin="2.5dp"
    android:background="@drawable/sf_field_search_corner_8_white_bg"
    android:padding="2dp">

    <com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1
        android:id="@+id/iv_yun_mult_product_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop">

    </com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1>

    <!--<com.xstore.sevenfresh.floor.modules.floor.recommend.good.ProductTagViewV2
        android:id="@+id/product_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/iv_yun_mult_product_img"
        android:layout_alignTop="@id/iv_yun_mult_product_img"
        android:layout_alignRight="@id/iv_yun_mult_product_img"
        android:layout_alignBottom="@id/iv_yun_mult_product_img" />-->

</RelativeLayout>
