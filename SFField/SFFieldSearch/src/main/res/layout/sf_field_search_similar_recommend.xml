<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/sf_field_search_similar_recommend_bg"
    android:minHeight="137dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="137dp"
        android:layout_marginBottom="8dp">

        <com.xstore.floorsdk.fieldsearch.widget.HorizontalMoreRecyclerView
            android:id="@+id/hmrv_recyclerview"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:fastScrollEnabled="false"
            android:overScrollMode="never"
            android:scrollbars="none" />

        <include
            android:id="@+id/ll_bottom_right_more"
            layout="@layout/sf_field_search_home_recycle_foot_content"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            android:layout_marginRight="-40dp" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="64dp"
        android:layout_height="19dp"
        android:background="@drawable/sf_field_search_similar_recommend_title_bg"
        android:gravity="center"
        android:paddingBottom="2dp"
        android:text="@string/sf_field_search_similar_good_tag"
        android:textColor="@color/sf_floor_core_white"
        android:textSize="12dp" />

</FrameLayout>