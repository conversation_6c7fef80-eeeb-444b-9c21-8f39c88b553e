<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_product_card_fixed_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="6dp"
    android:background="@drawable/sf_card_bg_corner_8_white"
    android:paddingStart="2dp"
    android:paddingTop="2dp"
    android:paddingEnd="2dp"
    android:paddingBottom="2dp">


    <!-- 商品图片及打标信息 -->
    <RelativeLayout
        android:id="@+id/rl_product_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_alignParentStart="true"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="false">

        <com.xstore.sevenfresh.productcard.widget.RoundCornerImageViewV3
            android:id="@+id/iv_product_img"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:contentDescription="@null"
            android:scaleType="fitXY"
            tools:src="@drawable/sf_theme_image_app_launcher" />

        <!-- 商品打标 -->
        <com.xstore.sevenfresh.productcard.widget.ProductImageTagView
            android:id="@+id/product_img_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@id/iv_product_img"
            android:layout_alignTop="@id/iv_product_img"
            android:layout_alignEnd="@id/iv_product_img"
            android:layout_alignBottom="@id/iv_product_img" />

    </RelativeLayout>


    <!-- 商品名称 -->
    <TextView
        android:id="@+id/tv_product_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="2dp"
        android:layout_marginRight="4dp"
        android:layout_toEndOf="@id/rl_product_img"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:lineSpacingExtra="0dp"
        android:maxLines="2"
        android:textColor="@color/sf_card_color_252525"
        android:textSize="12dp"
        tools:text="海南贵妃芒果" />

    <TextView
        android:id="@+id/image_tag_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_product_name"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:layout_toEndOf="@id/rl_product_img"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:textColor="@color/sf_card_color_0A665E"
        android:textSize="10dp"
        android:visibility="gone"
        tools:text="好评率100%"
        tools:visibility="visible" />


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/image_tag_txt"
        android:layout_marginTop="-5dp"
        android:layout_marginEnd="4dp"
        android:layout_toEndOf="@id/rl_product_img">

        <com.xstore.sevenfresh.productcard.widget.SfCardPriceView
            android:id="@+id/tv_price_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="4dp" />

        <!--加车按钮-->
        <com.xstore.sevenfresh.cart.widget.AddCartViewV3
            android:id="@+id/acv_addcart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:sf_cart_icon_height="18dp"
            app:sf_cart_icon_margin_bottom="8dp"
            app:sf_cart_icon_margin_right="4dp"
            app:sf_cart_icon_width="18dp"
            app:sf_cart_rush_height="25dp"
            app:sf_cart_rush_width="28dp"
            app:sf_cart_takeaway_height="16dp"
            app:sf_cart_takeaway_width="39dp"
            app:sf_cart_type_small_normal="true" />

        <!--找相似/去预定-->
        <TextView
            android:id="@+id/tv_find_similar"
            android:layout_width="44dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/sf_card_corner_20_stroke_0a665e_bg"
            android:gravity="center"
            android:text="@string/sf_card_find_similar"
            android:textColor="@color/sf_card_color_0A665E"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone" />
    </RelativeLayout>

</RelativeLayout>

