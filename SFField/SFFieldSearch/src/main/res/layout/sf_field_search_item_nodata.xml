<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/sf_field_search_white">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:paddingTop="25dp"
        android:paddingBottom="35dp">

        <TextView
            android:id="@+id/tv_nodata_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:shadowRadius="3.0"
            android:text="@string/sf_field_search_no_data_tips"
            android:textColor="@color/sf_field_search_color_898989"
            android:textSize="13dp" />


        <TextView
            android:id="@+id/tv_nodata_feedback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingVertical="2dp"
            android:text="去反馈"
            android:textColor="@color/sf_theme_color_level_1"
            android:textSize="13dp" />


    </LinearLayout>


</RelativeLayout>