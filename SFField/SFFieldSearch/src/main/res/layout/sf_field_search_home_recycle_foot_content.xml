<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="1"
        android:gravity="center"
        android:text="@string/sf_field_search_more_goods"
        android:textColor="@color/sf_field_search_main_new_recommend_bottom_content_av_color"
        android:textSize="13dp" />

    <ImageView
        android:id="@+id/iv_load_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:scaleType="centerInside"
        android:src="@drawable/sf_field_search_icon_main_theme_load_more" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_load_more"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="18.5dp"
        android:lines="2"
        android:text="@string/sf_field_search_slide_view_more"
        android:textColor="@color/sf_field_search_color_301d1d"
        android:textSize="10dp"
        android:textStyle="bold"
        android:visibility="gone" />
</LinearLayout>
