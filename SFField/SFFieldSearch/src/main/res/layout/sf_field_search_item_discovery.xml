<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_search_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="15dp"
        android:gravity="center_vertical"
        android:text="搜索发现"
        android:textColor="@color/sf_field_search_color_252525"
        android:textSize="16dp"
        android:textStyle="bold" />


    <com.xstore.floorsdk.fieldsearch.widget.FlowLayout
        android:id="@+id/flow_discovery_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        app:maxLinesCount="3"
        app:horizontalSpacing="8dp"
        app:verticalSpacing="8dp" />


</LinearLayout>