<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants">

    <TextView
        android:id="@+id/tv_promotion_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginRight="2dp"
        android:layout_toLeftOf="@+id/tv_num"
        android:ellipsize="end"
        android:textColor="@color/sf_field_search_color_363636"
        android:textSize="13dp" />

    <TextView
        android:id="@+id/tv_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="5dp"
        android:layout_toLeftOf="@id/iv_arrow"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/sf_field_search_color_363636"
        android:textSize="13dp" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="5.5dp"
        android:layout_height="10dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/sf_field_search_more_arrow_icon" />
</RelativeLayout>