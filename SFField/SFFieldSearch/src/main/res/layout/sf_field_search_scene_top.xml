<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="30dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="14dp">

    <ImageView
        android:id="@+id/iv_rank"
        android:layout_width="63dp"
        android:layout_height="30dp"
        android:src="@drawable/sf_field_search_scene_rank_icon" />

    <ImageView
        android:layout_width="13dp"
        android:layout_height="15dp"
        android:src="@drawable/sf_field_search_scene_title_tag" />

    <TextView
        android:id="@+id/tv_scene_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FFFFFF"
        android:textSize="20dp" />

    <ImageView
        android:layout_width="13dp"
        android:layout_height="15dp"
        android:src="@drawable/sf_field_search_scene_title_tag" />

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tv_more_rank"
        android:layout_width="58dp"
        android:layout_height="17dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@drawable/sf_field_search_scene_more_rank_bg"
        android:gravity="center"
        android:text="@string/sf_field_search_more_rank"
        android:textColor="#FFFFFF"
        android:textSize="12dp"
        android:visibility="gone" />
</LinearLayout>