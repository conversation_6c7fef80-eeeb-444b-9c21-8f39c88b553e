<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="43dp">

    <ImageView
        android:id="@+id/iv_filter_action"
        android:layout_width="96dp"
        android:layout_height="43dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:layout_centerVertical="true"
        android:scaleType="fitCenter"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_filter_item"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_filter_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/sf_field_search_color_1a1a1a"
            android:textSize="14dp"
            tools:text="综合" />

        <ImageView
            android:id="@+id/iv_filter_expand"
            android:layout_width="6dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:scaleType="centerCrop"
            android:src="@drawable/sf_field_search_filter_feature_unselect_expanded"
            android:visibility="gone"/>

    </LinearLayout>

</RelativeLayout>