<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="38dp"
    android:background="@color/sf_field_search_app_background"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <RelativeLayout
        android:id="@+id/rl_filter_all"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_filter_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/sf_field_search_filter_all"
            android:textColor="@color/sf_theme_color_level_1"
            android:textSize="15dp"
            android:textStyle="bold" />

        <View
            android:id="@+id/v_filter_all_indicator"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@drawable/sf_field_search_filter_indicator" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_filter_min"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <ImageView
            android:id="@+id/iv_filter_min"
            android:layout_width="76dp"
            android:layout_height="15dp"
            android:layout_centerInParent="true" />

        <View
            android:id="@+id/v_filter_min_indicator"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@drawable/sf_field_search_filter_indicator"
            android:visibility="gone" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_filter_yun"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <ImageView
            android:id="@+id/iv_filter_yun"
            android:layout_width="76dp"
            android:layout_height="15dp"
            android:layout_centerInParent="true" />

        <View
            android:id="@+id/v_filter_yun_indicator"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@drawable/sf_field_search_filter_indicator"
            android:visibility="gone" />

    </RelativeLayout>
</LinearLayout>


