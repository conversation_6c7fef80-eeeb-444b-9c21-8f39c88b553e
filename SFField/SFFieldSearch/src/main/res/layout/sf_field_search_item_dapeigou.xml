<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/new_white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="184dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:background="@drawable/sf_field_search_dapeigou_bg"
        android:orientation="vertical">


        <RelativeLayout
            android:id="@+id/more_layout_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="6dp">

            <ImageView
                android:layout_width="56dp"
                android:layout_height="16dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="4dp"
                android:src="@drawable/sf_field_search_icon_dapeigou" />

            <LinearLayout
                android:id="@+id/more_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="2dp"
                    android:text="更多"
                    android:textColor="#0A665E"
                    android:textSize="11sp" />

                <ImageView
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:src="@drawable/sf_card_right_arr"
                    android:tint="#0A665E"
                    tools:ignore="UseAppTint" />
            </LinearLayout>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/dapeigou_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="6dp"
            android:overScrollMode="never" />

    </LinearLayout>

</FrameLayout>