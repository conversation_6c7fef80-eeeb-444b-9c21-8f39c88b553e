<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/try_search_root"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <View
        android:visibility="gone"
        android:id="@+id/try_search_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />
    <FrameLayout
        android:id="@+id/try_search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/sf_card_color_transparent"
        android:paddingLeft="10dp"
        android:paddingTop="12dp"
        android:paddingRight="10dp"
        android:paddingBottom="12dp">
        <ImageView
            android:layout_width="92dp"
            android:layout_height="20dp"
            android:src="@drawable/sf_field_search_image_try_search_all" />

        <com.xstore.floorsdk.fieldsearch.widget.FlowLayout
            android:layout_marginTop="30dp"
            android:id="@+id/try_search_flow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:maxLinesCount="6"
            app:horizontalSpacing="8dp"
            app:verticalSpacing="8dp" />
</FrameLayout>
</FrameLayout>