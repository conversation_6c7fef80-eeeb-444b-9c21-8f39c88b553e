<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="28dp"
    android:background="@drawable/sf_field_search_corner_6_white_bg"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="2dp">

    <ImageView
        android:id="@+id/iv_word"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/sf_field_search_corner_4_f6f6f6_bg" />

    <TextView
        android:id="@+id/tv_word"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/sf_field_search_color_1a1a1a"
        android:textSize="13dp"
        tools:text="红富士" />
</LinearLayout>