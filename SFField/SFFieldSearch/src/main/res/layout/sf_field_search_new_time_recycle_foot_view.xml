<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sf_field_search_transparent"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/more_blank_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="1dp" />

    <View
        android:layout_width="1dp"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/iv_more_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="0dp"
        android:layout_marginRight="0dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ems="1"
            android:gravity="center"
            android:text="@string/sf_field_search_more_goods"
            android:textColor="@color/sf_floor_core_white"
            android:textSize="13dp" />

        <ImageView
            android:id="@+id/iv_load_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:scaleType="centerInside"
            android:src="@drawable/sf_field_search_icon_main_theme_load_more"
            android:visibility="invisible" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_load_more"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="18.5dp"
            android:lines="2"
            android:text="@string/sf_field_search_slide_view_more"
            android:textColor="@color/sf_field_search_color_301d1d"
            android:textSize="10dp"
            android:textStyle="bold"
            android:visibility="gone" />
    </LinearLayout>

    <TextView
        android:id="@+id/more_blank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/iv_more_container"
        android:textColor="@color/sf_floor_core_white"
        android:textSize="1dp" />

</LinearLayout>
