<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="95dp"
    android:minHeight="137dp"
    android:orientation="vertical"
    android:paddingTop="10dp">

    <ImageView
        android:id="@+id/iv_goods_icon"
        android:layout_width="79dp"
        android:layout_height="79dp"
        android:contentDescription="@null"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/tv_goods_name"
        android:layout_width="82dp"
        android:layout_height="18dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/sf_field_search_color_1d1f2b"
        android:textSize="12dp"
        tools:text="海口本土龙虾" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:lines="1"
            android:textColor="@color/sf_theme_color_price"
            android:textSize="12dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="1.5dp"
            android:layout_weight="1"
            android:includeFontPadding="false"
            android:lines="1"
            android:textColor="@color/sf_floor_core_app_gray"
            android:textSize="12dp" />

        <com.xstore.sevenfresh.cart.widget.AddCartView
            android:id="@+id/acv_addcart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="1dp"
            app:sf_cart_icon_height="20dp"
            app:sf_cart_icon_width="20dp"
            app:sf_cart_type_small="true" />
    </LinearLayout>
</LinearLayout>