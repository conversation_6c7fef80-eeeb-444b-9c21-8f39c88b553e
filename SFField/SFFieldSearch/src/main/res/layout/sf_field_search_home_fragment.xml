<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sf_field_search_home_background"
    android:orientation="vertical">

    <com.xstore.floorsdk.fieldsearch.widget.SearchView
        android:id="@+id/view_search"
        android:layout_width="match_parent"
        android:layout_height="48dp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/view_search">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/hot_wordsgrid"
                android:layout_marginTop="4dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />


            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/hot_wordsgrid"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/search_page_container"
                    android:layout_width="wrap_content"
                    android:layout_height="555dp"
                    android:orientation="horizontal">
                </LinearLayout>
            </HorizontalScrollView>

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_centerInParent="true"
                android:indeterminate="false"
                android:indeterminateDrawable="@drawable/sf_floor_core_new_refresh_progress_style"
                android:visibility="gone" />

        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- 搜索关键字下来页面 -->
    <LinearLayout
        android:id="@+id/ll_relate_key"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/view_search"
        android:orientation="vertical"
        android:visibility="gone">

        <ListView
            android:id="@+id/lv_relate_key"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/sf_field_search_corner_top_8_ffffff_bg"
            android:divider="@color/sf_field_search_color_f5f6fa"
            android:dividerHeight="0.5dp"
            android:focusableInTouchMode="false"
            android:listSelector="@android:color/transparent"
            android:paddingLeft="15dp"
            android:paddingRight="15dp" />
    </LinearLayout>
</RelativeLayout>
