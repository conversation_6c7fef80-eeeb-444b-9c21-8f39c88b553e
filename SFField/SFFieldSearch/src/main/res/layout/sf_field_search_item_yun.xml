<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/sf_field_search_white"
    android:orientation="vertical">

    <com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1
        android:id="@+id/iv_yun_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:scaleType="fitXY"
        android:src="@drawable/sf_field_search_yun_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:orientation="vertical"
        android:paddingTop="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:gravity="bottom"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_yun_title"
                style="@style/sf_field_search_ItemYunTitleStyle"
                android:layout_height="16dp" />

            <TextView
                android:id="@+id/tv_yun_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/sf_field_search_yun_desc"
                android:textColor="@color/sf_field_search_color_ff5454"
                android:textSize="12dp" />

            <ImageView
                android:id="@+id/iv_yun_arrow"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_marginLeft="2dp"
                android:src="@drawable/sf_field_search_yun_arrow" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_yun_product_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="11dp"
            android:layout_marginRight="5dp"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_yun_product_horizontal"
            android:layout_width="match_parent"
            android:layout_height="190dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="9dp"
            android:layout_marginBottom="5dp"
            android:fastScrollEnabled="false"
            android:overScrollMode="never"
            android:scrollbars="none" />
    </LinearLayout>

</FrameLayout>