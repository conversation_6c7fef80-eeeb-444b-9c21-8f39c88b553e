<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_header_bg"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/sf_field_search_corner_top_8_ffffff_bg"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:text="七鲜热搜"
        android:textColor="#F37C00"
        android:textSize="16dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="5dp"
        android:layout_toLeftOf="@+id/iv_arrow"
        android:ellipsize="end"
        android:lines="1"
        android:text="查看全部"
        android:textColor="#898989"
        android:textSize="12dp" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="9dp"
        android:layout_height="9dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="5dp"
        android:src="@drawable/sf_field_search_page_arrow" />

</RelativeLayout>

