<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_search_cate"
    android:layout_width="60dp"
    android:layout_height="wrap_content"
    android:layout_marginLeft="4dp"
    android:layout_marginRight="4dp"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.xstore.sdk.floor.floorcore.widget.YLCircleImageView
        android:id="@+id/iv_cate_logo"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:background="@color/sf_field_search_white"
        app:sf_floor_core_borderColorYL="@color/sf_theme_color_level_1"
        app:sf_floor_core_borderSpaceYL="1.4dp"
        app:sf_floor_core_borderWidthYL="1dp"
        app:sf_floor_core_radiusYL="20dp" />

    <TextView
        android:id="@+id/tv_cate_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginBottom="7dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/sf_field_search_color_999999"
        android:textSize="12dp"
        tools:text="测试档口123456" />

</LinearLayout>