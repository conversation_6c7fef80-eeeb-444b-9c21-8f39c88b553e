<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_coupon_prepare"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/sf_field_search_color_898989"
            android:textSize="15dp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="62dp"
            android:layout_height="18dp"
            android:layout_marginLeft="10dp"
            android:src="@drawable/sf_theme_image_coupon_no_begin" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_coupon_begin_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="15dp"
        android:includeFontPadding="false"
        android:textColor="@color/sf_field_search_color_898989"
        android:textSize="13dp" />
</LinearLayout>
