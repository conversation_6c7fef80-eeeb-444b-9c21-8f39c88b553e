<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_filter_feature_item"
        android:layout_width="match_parent"
        android:layout_height="39dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/sf_field_search_fragment_filter_item_bg"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="@drawable/sf_field_search_fragment_filter_item_color"
        android:textSize="13dp" />

</LinearLayout>