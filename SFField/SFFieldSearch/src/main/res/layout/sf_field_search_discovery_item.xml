<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="28dp"
    android:background="@drawable/sf_field_search_corner_6_white_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="2dp"
    android:paddingEnd="10dp">

    <com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1
        android:id="@+id/iv_discovery"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="-4dp"
        android:scaleType="fitXY"
        android:src="@drawable/sf_field_search_corner_4_f6f6f6_bg" />

    <TextView
        android:id="@+id/tv_discovery"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginLeft="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/sf_field_search_color_1a1a1a"
        android:textSize="13dp" />

</LinearLayout>