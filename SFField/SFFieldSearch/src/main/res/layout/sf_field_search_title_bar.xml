<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:paddingLeft="8dp"
    android:paddingRight="8dp">

    <ImageView
        android:id="@+id/iv_bar_back"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_centerVertical="true"
        android:onClick="onClick"
        android:scaleType="centerInside"
        android:src="@drawable/sf_field_search_back_black" />

    <RelativeLayout
        android:id="@+id/rl_bar_search"
        android:layout_width="wrap_content"
        android:layout_height="31dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="4dp"
        android:layout_toLeftOf="@id/iv_change_mode"
        android:layout_toRightOf="@id/iv_bar_back"
        android:background="@drawable/sf_field_search_corner_16_ffffff_bg"
        android:paddingRight="7dp">

        <ImageView
            android:id="@+id/iv_search_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="13dp"
            android:src="@drawable/sf_field_search_search_icon" />

        <TextView
            android:id="@+id/tv_search_tips"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:layout_toRightOf="@id/iv_search_icon"
            android:gravity="center_vertical"
            android:hint="@string/sf_field_search_edittext_hint"
            android:singleLine="true"
            android:textColor="@color/sf_field_search_hint_color"
            android:textColorHint="@color/sf_field_search_hint_color"
            android:textSize="14dp" />

        <!--  新版搜索词显示  -->
        <TextView
            android:id="@+id/tv_search_keyword"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:layout_toRightOf="@id/iv_search_icon"
            android:background="@drawable/sf_field_search_corner_10_9d9d9d_bg"
            android:drawableRight="@drawable/sf_field_search_close_white"
            android:drawablePadding="5dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingLeft="7dp"
            android:paddingTop="1dp"
            android:paddingRight="6dp"
            android:textColor="#FFFFFF"
            android:textSize="12dp"
            android:visibility="gone" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_change_mode"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/sf_field_search_change_to_card_icon" />

</RelativeLayout>
