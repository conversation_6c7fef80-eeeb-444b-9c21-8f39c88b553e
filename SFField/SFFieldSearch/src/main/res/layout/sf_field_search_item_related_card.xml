<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">


    <LinearLayout
        android:id="@+id/tv_search_related_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="18dp"
        android:paddingBottom="18dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp">

        <View
            android:layout_width="60dp"
            android:layout_height="1dp"
            android:background="@color/color_e6e6e6" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="@string/sf_field_search_related_tips1"
            android:textColor="@color/sf_field_search_color_999999"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tv_search_related_keyword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:maxWidth="60dp"
            android:textColor="@color/sf_field_search_color_999999"
            android:textSize="12dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:text="@string/sf_field_search_related_tips2"
            android:textColor="@color/sf_field_search_color_999999"
            android:textSize="12dp" />


        <View
            android:layout_width="60dp"
            android:layout_height="1dp"
            android:background="@color/color_e6e6e6" />

    </LinearLayout>


</LinearLayout>