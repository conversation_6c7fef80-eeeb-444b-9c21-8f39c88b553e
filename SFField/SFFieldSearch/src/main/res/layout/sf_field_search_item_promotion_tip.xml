<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp">

        <TextView
            android:id="@+id/tv_promotion_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sf_field_search_promotion_discount_with_colon"
            android:textColor="@color/sf_field_search_color_363636"
            android:textSize="13dp" />

        <LinearLayout
            android:id="@+id/ll_promotion_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_promotion_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:includeFontPadding="false"
        android:paddingLeft="15dp"
        android:textColor="@color/sf_field_search_color_898989"
        android:textSize="13dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="10dp"
        android:background="@color/sf_field_search_color_16000000" />
</LinearLayout>