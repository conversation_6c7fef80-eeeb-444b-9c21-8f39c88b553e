<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/sf_field_search_product_card_shadow"
    android:orientation="vertical"
    app:cardElevation="2dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@drawable/sf_field_search_similar_recommend_card_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_marginTop="17dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="81dp"
                android:layout_height="24dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:background="@drawable/sf_field_search_similar_recommend_title_bg"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingBottom="2dp"
                android:text="@string/sf_field_search_similar_good_tag"
                android:textColor="@color/sf_floor_core_white"
                android:textSize="14dp" />

            <LinearLayout
                android:id="@+id/ll_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    android:layout_toLeftOf="@+id/iv_arrow"
                    android:text="@string/sf_field_search_all"
                    android:textColor="@color/sf_floor_core_black"
                    android:textSize="12dp" />

                <ImageView
                    android:id="@+id/iv_arrow"
                    android:layout_width="4dp"
                    android:layout_height="7dp"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/sf_field_search_ic_black_arrow_5_9" />

            </LinearLayout>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</FrameLayout>