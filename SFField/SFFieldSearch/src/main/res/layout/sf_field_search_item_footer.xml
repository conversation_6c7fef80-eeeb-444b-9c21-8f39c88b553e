<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/root_footer"
    android:layout_height="195dp">

    <View
        android:id="@+id/v_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/sf_field_search_color_16000000"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_footer_nomore"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/sf_field_search_no_more"
        android:textColor="@color/sf_field_search_color_898989"
        android:textSize="12dp" />

    <ImageView
        android:id="@+id/iv_footer_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:src="@drawable/sf_theme_image_bottom_logo"
        android:visibility="gone" />
</RelativeLayout>