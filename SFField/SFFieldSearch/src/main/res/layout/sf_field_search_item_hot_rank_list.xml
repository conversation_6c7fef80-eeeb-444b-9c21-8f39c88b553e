<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:clickable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_rank"
            android:layout_width="17dp"
            android:layout_height="19dp"
            android:background="@drawable/sf_field_search_rank_first_bg"
            android:gravity="center"
            android:paddingBottom="2dp"
            android:textColor="@color/sf_field_search_white"
            android:textSize="11dp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_rank_pic"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginLeft="8dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_rank_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/sf_field_search_color_1d1f2b"
            android:textSize="14dp" />

        <ImageView
            android:id="@+id/iv_rank_top_icon"
            android:layout_width="11dp"
            android:layout_height="14dp"
            android:layout_marginLeft="5dp" />
    </LinearLayout>

</LinearLayout>