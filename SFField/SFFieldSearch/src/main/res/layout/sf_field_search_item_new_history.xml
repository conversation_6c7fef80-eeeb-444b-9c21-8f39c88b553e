<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_search_history"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/history_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="4dp"
        android:layout_marginLeft="15dp">

        <TextView
            android:id="@+id/tv_search_history"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="@string/sf_field_search_history"
            android:textColor="@color/sf_field_search_color_1a1a1a"
            android:textSize="16dp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/delete_history"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:src="@drawable/sf_field_search_history_delete"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/tagflow_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/history_wordsgrid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:orientation="vertical">

        </LinearLayout>

    </RelativeLayout>

    <View
        android:id="@+id/history_hot_line"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:background="@color/sf_field_search_app_background" />

    <LinearLayout
        android:id="@+id/ll_hot_words"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_hot_words"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/sf_field_search_hot_search"
            android:textColor="@color/sf_field_search_color_1a1a1a"
            android:textSize="16dp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>