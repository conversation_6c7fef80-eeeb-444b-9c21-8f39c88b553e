<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/rl_feature_title"
        android:layout_width="match_parent"
        android:layout_height="46dp">

        <TextView
            android:id="@+id/tv_feature_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:textColor="@color/sf_field_search_color_252525"
            android:textSize="14dp"
            android:textStyle="bold"
            tools:text="品牌" />

        <TextView
            android:id="@+id/tv_feature_all"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawableRight="@drawable/sf_field_search_arrow_down_icon"
            android:drawablePadding="5dp"
            android:ellipsize="middle"
            android:gravity="right|center_vertical"
            android:paddingRight="15dp"
            android:singleLine="true"
            android:text="@string/sf_field_search_all"
            android:textColor="@color/sf_theme_color_level_1"
            android:textSize="12dp" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_feature"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_feature_title"
        android:paddingLeft="10dp"
        android:paddingRight="10dp" />

</RelativeLayout>