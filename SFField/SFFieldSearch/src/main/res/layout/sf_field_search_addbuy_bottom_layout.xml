<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:background="@drawable/sf_field_search_addbuy_bg"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="14dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingRight="15dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="1.5dp"
                android:text="合计:"
                android:textColor="#111111"
                android:textSize="11dp"
                android:textStyle="bold" />

            <com.xstore.sevenfresh.productcard.widget.SfCardPriceView
                android:id="@+id/tv_addbuy_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_addbuy_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/sf_field_search_color_888B94"
            android:textSize="11dp"
            tools:text="还差10元, 才可享5元低价购买" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_addbuy_cart"
        android:layout_width="108dp"
        android:layout_height="38dp"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="12dp"
        android:background="@drawable/sf_field_search_corner_20_green_white_bg"
        android:gravity="center"
        android:textColor="@color/sf_theme_color_level_1"
        android:textSize="13dp"
        tools:text="查看加价购商品" />

    <ImageView
        android:id="@+id/iv_promotion_cart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="25dp"
        android:background="@drawable/sf_field_search_cart_icon"
        android:visibility="gone" />
</LinearLayout>