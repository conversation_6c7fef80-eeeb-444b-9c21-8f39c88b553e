<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sf_field_search_white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_nodata"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:src="@drawable/sf_theme_image_search_empty" />

    <TextView
        android:id="@+id/tv_nodata_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/sf_field_search_no_data"
        android:textColor="@color/sf_field_search_color_898989"
        android:textSize="13dp" />

    <TextView
        android:id="@+id/tv_nodata_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/sf_field_search_corner_20_gray_white_bg"
        android:gravity="center"
        android:paddingLeft="30dp"
        android:paddingTop="9dp"
        android:paddingRight="30dp"
        android:paddingBottom="9dp"
        android:text="@string/sf_field_search_goto_firstpage"
        android:textColor="@color/sf_field_search_color_252525"
        android:textSize="13dp" />

</LinearLayout>