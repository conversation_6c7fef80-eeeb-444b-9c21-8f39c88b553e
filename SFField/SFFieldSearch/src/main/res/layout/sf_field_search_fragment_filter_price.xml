<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingTop="10dp"
    android:paddingRight="15dp"
    android:paddingBottom="10dp">

    <TextView
        android:id="@+id/tv_price_range"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:text="@string/sf_field_search_price_range"
        android:textColor="@color/sf_field_search_color_252525"
        android:textSize="14dp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        android:weightSum="11">

        <EditText
            android:id="@+id/et_min_price"
            android:layout_width="0dp"
            android:layout_height="39dp"
            android:layout_weight="5"
            android:background="@drawable/sf_field_search_fragment_filter_price_bg"
            android:gravity="center"
            android:hint="@string/sf_field_search_min_price"
            android:inputType="number"
            android:maxLength="9"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textColor="@color/sf_field_search_color_252525"
            android:textColorHint="@color/sf_field_search_color_c2c2c2"
            android:textSize="13dp" />

        <TextView
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="39dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/sf_field_search_price_divider" />

        <EditText
            android:id="@+id/et_max_price"
            android:layout_width="0dp"
            android:layout_height="39dp"
            android:layout_gravity="bottom"
            android:layout_weight="5"
            android:background="@drawable/sf_field_search_fragment_filter_price_bg"
            android:gravity="center"
            android:hint="@string/sf_field_search_max_price"
            android:inputType="number"
            android:maxLength="9"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textColor="@color/sf_field_search_color_252525"
            android:textColorHint="@color/sf_field_search_color_c2c2c2"
            android:textSize="13dp" />
    </LinearLayout>

</LinearLayout>