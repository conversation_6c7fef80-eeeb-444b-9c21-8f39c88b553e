<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/sf_field_search_corner_left_8_ffffff_bg"
    android:clickable="true"
    android:orientation="vertical"
    android:paddingTop="25dp">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_fragment_filter"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <View
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:background="@drawable/sf_field_search_fragment_filter_btn_shadow" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="15dp">

        <TextView
            android:id="@+id/tv_reset"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/sf_field_search_corner_20_gray_white_bg"
            android:gravity="center"
            android:text="@string/sf_field_search_reset"
            android:textColor="@color/sf_field_search_color_1d1f2b"
            android:textSize="15dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="10dp"
            android:layout_weight="2.2"
            android:background="@drawable/sf_field_search_corner_20_green_btn_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/sf_field_search_confirm"
            android:textColor="@color/sf_field_search_white"
            android:textSize="15dp"
            android:textStyle="bold" />
    </LinearLayout>

</LinearLayout>