<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sf_field_search_app_background">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_scene_bg"
            android:layout_width="match_parent"
            android:layout_height="316dp"
            android:scaleType="centerCrop"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_search_result_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/layout_addbuy_bottom"
            android:orientation="vertical">

            <include layout="@layout/sf_field_search_title_bar" />

            <View
                android:id="@+id/v_search_bar_bottom_divider"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/sf_floor_core_divide_color"
                android:visibility="gone" />

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appbar_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/sf_field_search_transparent"
                    app:elevation="0dp">

                    <LinearLayout
                        android:id="@+id/ll_scene_and_filter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_scrollFlags="scroll|enterAlways|enterAlwaysCollapsed">

                        <include
                            android:id="@+id/scene_layout"
                            layout="@layout/sf_field_search_scene_layout"
                            android:visibility="gone" />

                        <com.xstore.floorsdk.fieldsearch.widget.deliveryfilter.SearchResultDeliveryFilter
                            android:id="@+id/search_delivery_filter"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone" />


                    </LinearLayout>

                    <com.xstore.floorsdk.fieldsearch.widget.filter.SearchResultFilter
                        android:id="@+id/search_filter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <!--  类目平铺  图片和文本-->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_cate_filter"
                            android:layout_width="match_parent"
                            android:layout_height="82dp"
                            android:background="@color/sf_field_search_white"
                            android:visibility="gone" />
                        <!--  类目平铺  纯文本-->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_cate_text_filter"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@color/sf_field_search_white"
                            android:visibility="gone" />
                    </LinearLayout>


                    <!--  加价购筛选栏  -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_addbuy_filter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/sf_field_search_white"
                        android:paddingLeft="15dp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_coupon_limit_tip"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/sf_field_search_color_fff1d3"
                        android:gravity="center"
                        android:paddingLeft="10dp"
                        android:paddingTop="6dp"
                        android:paddingRight="10dp"
                        android:paddingBottom="6dp"
                        android:textColor="@color/sf_field_search_color_b47e17"
                        android:textSize="11dp"
                        android:visibility="gone" />

                </com.google.android.material.appbar.AppBarLayout>

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/refreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

                    <androidx.recyclerview.widget.SearchRecyclerView
                        android:id="@+id/rv_products"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/sf_field_search_app_background"
                        android:cacheColorHint="@android:color/transparent"
                        android:clipToPadding="false"
                        android:divider="@null"
                        android:dividerHeight="0dp"
                        android:fadingEdge="none"
                        android:fastScrollEnabled="false"
                        android:footerDividersEnabled="true"
                        android:headerDividersEnabled="false"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        android:smoothScrollbar="true" />

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:id="@+id/cf_recycler_footer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:srlClassicsSpinnerStyle="Translate"
                        app:srlDrawableProgress="@drawable/bdcodehelper_refresh_progress_style"
                        app:srlFinishDuration="0"
                        app:srlTextFinish=""
                        app:srlTextLoading="加载中..." />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <include
                    android:id="@+id/layout_nodata"
                    layout="@layout/sf_field_search_layout_nodata"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />
            </androidx.coordinatorlayout.widget.CoordinatorLayout>
        </LinearLayout>

        <include
            android:id="@+id/layout_addbuy_bottom"
            layout="@layout/sf_field_search_addbuy_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:visibility="gone" />
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/fl_filter_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="end" />

</androidx.drawerlayout.widget.DrawerLayout>