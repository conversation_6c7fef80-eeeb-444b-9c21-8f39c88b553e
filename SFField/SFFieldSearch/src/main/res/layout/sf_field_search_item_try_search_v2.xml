<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="10dp"
    android:background="@color/sf_field_search_white"
    android:id="@+id/try_search_root"
    >
    <View
        android:visibility="gone"
        android:id="@+id/try_search_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sf_field_search_bg_try_search_horizontal"
        />
    <FrameLayout
        android:id="@+id/try_search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/sf_card_color_transparent"
        android:paddingLeft="12dp"
        android:paddingTop="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="12dp">
        <ImageView
            android:layout_width="92dp"
            android:layout_height="20dp"
            android:src="@drawable/sf_field_search_image_try_search_all" />

        <com.xstore.floorsdk.fieldsearch.widget.FlowLayout
            android:id="@+id/try_search_flow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            app:horizontalSpacing="8dp"
            app:maxLinesCount="2"
            app:verticalSpacing="8dp" />
    </FrameLayout>
</FrameLayout>