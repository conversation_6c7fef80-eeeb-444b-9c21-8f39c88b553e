<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/sf_field_search_color_66000000" />

    <LinearLayout
        android:id="@+id/ll_drop_down_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:background="@drawable/sf_field_search_filter_draw_down_bg"
        android:clickable="true"
        android:orientation="vertical"
        android:paddingLeft="5dp"
        android:paddingTop="10dp"
        android:paddingRight="5dp"
        android:paddingBottom="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_drop_down_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/sf_field_search_white"
            android:maxHeight="180dp" />

        <LinearLayout
            android:id="@+id/ll_drop_down_action"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="5dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_reset"
                android:layout_width="90dp"
                android:layout_height="match_parent"
                android:background="@drawable/sf_field_search_corner_20_gray_white_bg"
                android:gravity="center"
                android:text="@string/sf_field_search_reset"
                android:textColor="@color/sf_field_search_color_1d1f2b"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="190dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:background="@drawable/sf_field_search_corner_20_green_btn_bg"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/sf_field_search_confirm"
                android:textColor="@color/sf_field_search_white"
                android:textSize="15dp"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

</FrameLayout>