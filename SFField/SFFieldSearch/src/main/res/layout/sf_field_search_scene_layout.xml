<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="10dp">

    <include layout="@layout/sf_field_search_scene_top" />

    <LinearLayout
        android:id="@+id/ll_single_container"
        android:layout_width="match_parent"
        android:layout_height="128dp"
        android:orientation="horizontal"
        android:paddingLeft="14dp"
        android:paddingTop="10dp"
        android:paddingRight="14dp"
        android:paddingBottom="10dp">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/sf_field_search_content_left_tag" />

            <TextView
                android:id="@+id/tv_desc_single"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="9dp"
                android:layout_marginBottom="11dp"
                android:ellipsize="end"
                android:lineSpacingMultiplier="1.35"
                android:maxLines="3"
                android:textColor="#FFFFFF"
                android:textSize="12dp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|bottom"
                android:src="@drawable/sf_field_search_content_right_tag" />

        </FrameLayout>

        <ImageView
            android:id="@+id/iv_img_single"
            android:layout_width="108dp"
            android:layout_height="108dp"
            android:layout_marginLeft="14dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_multi_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="14dp"
            android:paddingTop="5dp"
            android:paddingRight="14dp"
            android:paddingBottom="5dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/sf_field_search_content_left_tag" />

            <TextView
                android:id="@+id/tv_desc_multi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:lineSpacingMultiplier="1.1"
                android:maxLines="3"
                android:textColor="@color/sf_field_search_white"
                android:textSize="12dp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|bottom"
                android:src="@drawable/sf_field_search_content_right_tag" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_img_two"
            android:layout_width="match_parent"
            android:layout_height="123dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:paddingBottom="10dp">

            <ImageView
                android:id="@+id/iv_img_two_l"
                android:layout_width="163dp"
                android:layout_height="108dp"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/iv_img_two_r"
                tools:ignore="MissingConstraints" />

            <ImageView
                android:id="@+id/iv_img_two_r"
                android:layout_width="163dp"
                android:layout_height="108dp"
                app:layout_constraintLeft_toRightOf="@id/iv_img_two_l"
                app:layout_constraintRight_toRightOf="parent"
                tools:ignore="MissingConstraints" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_img_three"
            android:layout_width="match_parent"
            android:layout_height="123dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:paddingBottom="10dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_img_three_l"
                android:layout_width="108dp"
                android:layout_height="108dp"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/iv_img_three_m"
                tools:ignore="MissingConstraints" />

            <ImageView
                android:id="@+id/iv_img_three_m"
                android:layout_width="108dp"
                android:layout_height="108dp"
                app:layout_constraintLeft_toRightOf="@id/iv_img_three_l"
                app:layout_constraintRight_toLeftOf="@id/iv_img_three_r"
                app:layout_constraintRight_toRightOf="parent"
                tools:ignore="MissingConstraints" />

            <ImageView
                android:id="@+id/iv_img_three_r"
                android:layout_width="108dp"
                android:layout_height="108dp"
                app:layout_constraintLeft_toRightOf="@id/iv_img_three_m"
                app:layout_constraintRight_toRightOf="parent"
                tools:ignore="MissingConstraints" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</LinearLayout>