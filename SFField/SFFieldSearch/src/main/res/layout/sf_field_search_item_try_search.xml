<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/sf_field_search_white"
    android:orientation="vertical"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <View
            android:layout_width="60dp"
            android:layout_height="1dp"
            android:background="@color/sf_field_search_color_e6e6e6" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="@string/sf_field_search_try_search"
            android:textColor="@color/sf_theme_color_level_1"
            android:textSize="15dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:text="这些"
            android:textColor="@color/sf_field_search_color_1d1f2b"
            android:textSize="15dp" />

        <View
            android:layout_width="60dp"
            android:layout_height="1dp"
            android:background="@color/sf_field_search_color_e6e6e6" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_try_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="5dp" />

</LinearLayout>