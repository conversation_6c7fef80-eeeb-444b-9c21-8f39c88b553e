<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="43dp"
    android:background="#FFFFFF">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_filter"
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginLeft="16dp"
        android:layout_toLeftOf="@id/ll_filter"
        android:scrollbars="none" />


    <TextView
        android:id="@+id/tv_equal_filter"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/sf_field_search_filter"
        android:textColor="@color/sf_field_search_color_1a1a1a"
        android:textSize="14dp" />


    <LinearLayout
        android:id="@+id/ll_filter"
        android:layout_width="68dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/sf_field_search_filter"
            android:textColor="@color/sf_field_search_color_313131"
            android:textSize="14dp" />

        <ImageView
            android:id="@+id/iv_filter"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginLeft="5dp"
            android:src="@drawable/sf_field_search_filter_icon_unselect" />
    </LinearLayout>

    <View
        android:id="@+id/v_filter_shadow"
        android:layout_width="20dp"
        android:layout_height="match_parent"
        android:layout_toLeftOf="@id/ll_filter"
        android:background="@drawable/sf_field_search_filter_shadow" />

</RelativeLayout>
