<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1
        android:id="@+id/iv_yun_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/sf_field_search_yun_card_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="13dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_yun_title"
                style="@style/sf_field_search_ItemYunCardTitleStyle"
                android:layout_height="14dp" />

            <ImageView
                android:id="@+id/iv_yun_arrow"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_marginLeft="5dp"
                android:src="@drawable/sf_field_search_yun_arrow" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_yun_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:text="@string/sf_field_search_yun_desc"
            android:textColor="@color/sf_field_search_color_ff5454"
            android:textSize="11dp" />

        <RelativeLayout
            android:id="@+id/rl_yun_single_product"
            android:layout_width="match_parent"
            android:layout_height="159dp"
            android:layout_margin="5dp"
            android:background="@drawable/sf_field_search_corner_8_white_bg"
            android:padding="4dp"
            android:visibility="gone">

            <com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1
                android:id="@+id/iv_yun_single_product_img"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop">

            </com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1>

            <!--<com.xstore.sevenfresh.floor.modules.floor.recommend.good.ProductTagViewV2
                android:id="@+id/product_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@id/iv_yun_single_product_img"
                android:layout_alignTop="@id/iv_yun_single_product_img"
                android:layout_alignRight="@id/iv_yun_single_product_img"
                android:layout_alignBottom="@id/iv_yun_single_product_img" />-->

        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_yun_mult_product"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2.5dp"
            android:layout_marginTop="6.5dp"
            android:layout_marginRight="2.5dp"
            android:layout_marginBottom="2.5dp" />

    </LinearLayout>

</FrameLayout>