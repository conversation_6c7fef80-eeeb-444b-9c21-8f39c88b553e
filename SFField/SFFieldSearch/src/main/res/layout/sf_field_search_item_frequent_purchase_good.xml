<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="71.5dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <RelativeLayout
        android:id="@+id/rl_pic"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp">

        <ImageView
            android:id="@+id/tv_goods_image"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:src="@drawable/sf_theme_image_placeholder_square" />

        <!--    商品达标    -->
        <com.xstore.sevenfresh.productcard.widget.ProductTagViewV3
            android:id="@+id/product_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/tv_goods_image"
            android:layout_alignTop="@+id/tv_goods_image"
            android:layout_alignRight="@+id/tv_goods_image"
            android:layout_alignBottom="@+id/tv_goods_image" />
    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_toLeftOf="@+id/acv_addcart"
        android:layout_toRightOf="@+id/rl_pic"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_goods_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/sf_field_search_color_1d1f2b"
            android:textSize="14dp"
            tools:text="新疆无籽绿葡萄" />

        <TextView
            android:id="@+id/tv_purchase_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:singleLine="true"
            android:textColor="@color/sf_theme_color_level_1"
            android:textSize="10dp"
            tools:text="买过7次" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">


            <TextView
                android:id="@+id/tv_goods_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/sf_theme_color_price"
                android:textSize="16dp"
                android:textStyle="bold"
                tools:text="￥20.80" />

            <TextView
                android:id="@+id/tv_goods_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/sf_field_search_base_black_95969F"
                android:textSize="12dp"
                tools:text="/盒" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:singleLine="true"
                android:textColor="@color/sf_field_search_color_cccccc"
                android:textSize="12dp"
                tools:text="￥16.8" />

        </LinearLayout>

    </LinearLayout>

    <com.xstore.sevenfresh.cart.widget.AddCartView
        android:id="@+id/acv_addcart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:paddingRight="2dp"
        app:sf_cart_icon_height="20dp"
        app:sf_cart_icon_width="20dp"
        app:sf_cart_takeaway_width="48dp"
        app:sf_cart_takeaway_height="21dp" />
</RelativeLayout>