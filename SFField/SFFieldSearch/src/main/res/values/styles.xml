<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="sf_field_search_ItemYunTitleStyle">
        <item name="android:layout_width">90dp</item>
        <item name="android:src">@drawable/sf_field_search_yun_title_icon</item>
    </style>

    <style name="sf_field_search_ItemYunCardTitleStyle">
        <item name="android:layout_width">79dp</item>
        <item name="android:src">@drawable/sf_field_search_yun_title_icon</item>
    </style>
    <style name="sf_field_search_DapeigouDialogStyle" parent="@android:style/Theme.Dialog">

        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 浮于Activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- Dialog以外的区域模糊效果 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- Dialog进入及退出动画 -->
        <item name="android:windowAnimationStyle">@style/sf_field_search_DapeigouActionDialogAnimation</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>
    <!-- ActionSheet进出动画 -->
    <style name="sf_field_search_DapeigouActionDialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/sf_field_search_bottom_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/sf_field_search_bottom_dialog_out</item>
    </style>

</resources>