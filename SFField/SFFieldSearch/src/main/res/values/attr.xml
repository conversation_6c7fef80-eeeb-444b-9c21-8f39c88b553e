<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="sf_field_search_FreshShadowLayout">
        <attr name="sf_field_search_shadowColor" format="color" />
        <attr name="sf_field_search_shadowRadius" format="dimension" />
        <attr name="sf_field_search_shadowDx" format="dimension" />
        <attr name="sf_field_search_shadowDy" format="dimension" />
        <attr name="sf_field_search_shadowTopMargin" format="dimension" />
        <attr name="sf_field_search_shadowBottomMargin" format="dimension" />
        <attr name="sf_field_search_shadowShapes">
            <flag name="rectangle" value="0x0001" />
            <flag name="oval" value="0x0010" />
        </attr>
        <attr name="sf_field_search_shadowSide">
            <flag name="all" value="0x1111" />
            <flag name="left" value="0x0001" />
            <flag name="top" value="0x0010" />
            <flag name="right" value="0x0100" />
            <flag name="bottom" value="0x1000" />
        </attr>
    </declare-styleable>

    <declare-styleable name="sf_field_search_FlowLayout">
        <attr name="horizontalSpacing" format="dimension" />
        <attr name="verticalSpacing" format="dimension" />
        <attr name="maxLinesCount" format="integer" />
        <attr name="supportFold" format="boolean" />
    </declare-styleable>
</resources>