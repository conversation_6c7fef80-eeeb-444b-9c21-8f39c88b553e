package com.xstore.floorsdk.fieldsearch.widget.deliveryfilter;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterBean;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.List;

import androidx.annotation.Nullable;

/**
 * 搜索结果页履约时效筛选条
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
public class SearchResultDeliveryFilter extends LinearLayout implements View.OnClickListener {

    private View allLayout;
    private TextView tvAll;
    private View allIndicator;
    private View minLayout;
    private ImageView ivMin;
    private View minIndicator;
    private View yunLayout;
    private ImageView ivYun;
    private View yunIndicator;

    private SearchFilterBean allFilterBean;
    private SearchFilterBean minFilterBean;
    private SearchFilterBean yunFilterBean;
    private SearchResultDataManager searchResultDataManager;

    public SearchResultDeliveryFilter(Context context) {
        super(context);
        init(context);
    }

    public SearchResultDeliveryFilter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public SearchResultDeliveryFilter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    /**
     * 初始化
     *
     * @param context
     */
    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.sf_field_search_widget_delivery_filter, this, true);
        allLayout = findViewById(R.id.rl_filter_all);
        tvAll = findViewById(R.id.tv_filter_all);
        allIndicator = findViewById(R.id.v_filter_all_indicator);
        minLayout = findViewById(R.id.rl_filter_min);
        ivMin = findViewById(R.id.iv_filter_min);
        minIndicator = findViewById(R.id.v_filter_min_indicator);
        yunLayout = findViewById(R.id.rl_filter_yun);
        ivYun = findViewById(R.id.iv_filter_yun);
        yunIndicator = findViewById(R.id.v_filter_yun_indicator);

        allLayout.setOnClickListener(this);
        minLayout.setOnClickListener(this);
        yunLayout.setOnClickListener(this);
    }

    /**
     * 绑定履约时效筛选数据
     *
     * @param searchResultDataManager
     * @param searchFilterBeans
     */
    public void setDeliveryFilterData(SearchResultDataManager searchResultDataManager, List<SearchFilterBean> searchFilterBeans) {
        if (searchFilterBeans == null || searchFilterBeans.size() < 2) {
            this.setVisibility(GONE);
            return;
        }
        this.searchResultDataManager = searchResultDataManager;
        allFilterBean = searchFilterBeans.get(0);
        setAllTab(allFilterBean != null && allFilterBean.isSelected());
        minFilterBean = searchFilterBeans.get(1);
        setMinTab(minFilterBean != null && minFilterBean.isSelected());
        if (searchFilterBeans.size() >= 3 && searchFilterBeans.get(2) != null) {
            yunFilterBean = searchFilterBeans.get(2);
            yunLayout.setVisibility(VISIBLE);
            setYunTab(yunFilterBean != null && yunFilterBean.isSelected());
        } else {
            yunFilterBean = null;
            yunLayout.setVisibility(GONE);
        }
    }

    private void setAllTab(boolean selected) {
        if (allFilterBean != null && !StringUtil.isNullByString(allFilterBean.getTitle())) {
            tvAll.setText(allFilterBean.getTitle());
        }
        if (selected) {
            tvAll.setTextColor(getResources().getColor(R.color.sf_theme_color_level_1));
            tvAll.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            allIndicator.setVisibility(View.VISIBLE);
        } else {
            tvAll.setTextColor(getResources().getColor(R.color.sf_field_search_color_1d1f2b));
            tvAll.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            allIndicator.setVisibility(View.GONE);
        }
    }

    private void setMinTab(boolean selected) {
        if (minFilterBean == null) {
            return;
        }
        if (selected) {
            if (!StringUtil.isNullByString(minFilterBean.getSelectedImg())) {
                ImageloadUtils.loadImage(getContext(), ivMin, minFilterBean.getSelectedImg());
            }
            minIndicator.setVisibility(View.VISIBLE);
        } else {
            if (!StringUtil.isNullByString(minFilterBean.getNotSelectedImg())) {
                ImageloadUtils.loadImage(getContext(), ivMin, minFilterBean.getNotSelectedImg());
            }
            minIndicator.setVisibility(View.GONE);
        }
    }

    private void setYunTab(boolean selected) {
        if (yunFilterBean == null) {
            return;
        }
        if (selected) {
            if (!StringUtil.isNullByString(yunFilterBean.getSelectedImg())) {
                ImageloadUtils.loadImage(getContext(), ivYun, yunFilterBean.getSelectedImg());
            }
            yunIndicator.setVisibility(View.VISIBLE);
        } else {
            if (!StringUtil.isNullByString(yunFilterBean.getNotSelectedImg())) {
                ImageloadUtils.loadImage(getContext(), ivYun, yunFilterBean.getNotSelectedImg());
            }
            yunIndicator.setVisibility(View.GONE);
        }
    }

    /**
     * 获取履约时效筛选类型，埋点用
     *
     * @return
     */
    public String getDeliveryType() {
        if (allFilterBean != null && allFilterBean.isSelected()) {
            return "1";
        }
        if (minFilterBean != null && minFilterBean.isSelected()) {
            return "2";
        }
        if (yunFilterBean != null && yunFilterBean.isSelected()) {
            return "3";
        }
        return "";
    }

    @Override
    public void onClick(View v) {
        if (NoDoubleClickUtils.isDoubleClick()) {
            return;
        }
        int id = v.getId();
        if (id == R.id.rl_filter_all) {
            if (allFilterBean == null || allFilterBean.isSelected()) {
                return;
            }
            selectAll();
        } else if (id == R.id.rl_filter_min) {
            if (minFilterBean == null || minFilterBean.isSelected()) {
                return;
            }
            selectMin();
        } else if (id == R.id.rl_filter_yun) {
            if (yunFilterBean == null || yunFilterBean.isSelected()) {
                return;
            }
            selectYun();
        }
    }

    private void selectAll() {
        if (searchResultDataManager == null) {
            return;
        }
        setAllTab(true);
        setMinTab(false);
        setYunTab(false);
        searchResultDataManager.clearDeliveryFilter(true);
        if (searchResultDataManager.searchResultReporter != null) {
            searchResultDataManager.searchResultReporter.clickDelivery("1");
        }
    }

    private void selectMin() {
        if (searchResultDataManager == null) {
            return;
        }
        setAllTab(false);
        setMinTab(true);
        setYunTab(false);
        if (minFilterBean != null && minFilterBean.getQueryCondition() != null) {
            searchResultDataManager.updateDeliveryFilter(minFilterBean.getQueryCondition().getFilterKey(), minFilterBean.getQueryCondition().getFilterValue());
        }
        if (searchResultDataManager.searchResultReporter != null) {
            searchResultDataManager.searchResultReporter.clickDelivery("2");
        }
    }

    private void selectYun() {
        if (searchResultDataManager == null) {
            return;
        }
        setAllTab(false);
        setMinTab(false);
        setYunTab(true);
        if (yunFilterBean != null && yunFilterBean.getQueryCondition() != null) {
            searchResultDataManager.updateDeliveryFilter(yunFilterBean.getQueryCondition().getFilterKey(), yunFilterBean.getQueryCondition().getFilterValue());
        }
        if (searchResultDataManager.searchResultReporter != null) {
            searchResultDataManager.searchResultReporter.clickDelivery("3");
        }
    }

    public void jumpYunTab() {
        if (searchResultDataManager == null) {
            return;
        }
        if (yunFilterBean != null && yunLayout.getVisibility() == VISIBLE) {
            selectYun();
        } else {
            selectMin();
        }
    }
}
