package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 筛选查询条件实体类
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class SearchFilterQuery implements Serializable {

    public final static int TYPE_FLAG = 1;
    public final static int TYPE_ENUM = 2;
    /**
     * id
     */
    private Long id;
    /**
     * 筛选条件类型 1 打标， 2 枚举
     */
    private int type;
    /**
     * 客户端显示标签,
     */
    private String filterLable;
    /**
     * 筛选的标签，例如：价格、产地、重量等
     */
    private String filterKey;
    /**
     * 筛选或者查询条件值(只有加价购筛选用)
     */
    private String filterValue;
    /**
     * 选项是否默认展开
     */
    private boolean expand = true;
    /**
     * 属性筛选展示的子项名称
     */
    private String name;
    /**
     * 分类Id
     */
    private String catId;
    /**
     * 分类名
     */
    private String catName;
    /**
     * 父Id
     */
    private String parentId;
    /**
     * 筛选条件值,数组
     */
    private List<SearchFilterQuery> filterValues;


    /***** 以下为本地字段 *****/
    /**
     * 是否排序筛选
     */
    private boolean isSortFilter = false;
    /**
     * 最低价格
     */
    private String minPrice;
    /**
     * 最高价格
     */
    private String maxPrice;
    /**
     * 是否选中
     */
    private boolean selected;
    /**
     * 是否已展开下拉框
     */
    private boolean expanded;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getFilterLable() {
        return filterLable;
    }

    public void setFilterLable(String filterLable) {
        this.filterLable = filterLable;
    }

    public String getFilterKey() {
        return filterKey;
    }

    public void setFilterKey(String filterKey) {
        this.filterKey = filterKey;
    }

    public String getFilterValue() {
        return filterValue;
    }

    public void setFilterValue(String filterValue) {
        this.filterValue = filterValue;
    }

    public boolean isExpand() {
        return expand;
    }

    public void setExpand(boolean expand) {
        this.expand = expand;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCatId() {
        return catId;
    }

    public void setCatId(String catId) {
        this.catId = catId;
    }

    public String getCatName() {
        return catName;
    }

    public void setCatName(String catName) {
        this.catName = catName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<SearchFilterQuery> getFilterValues() {
        return filterValues;
    }

    public void setFilterValues(List<SearchFilterQuery> filterValues) {
        this.filterValues = filterValues;
    }

    public boolean isSortFilter() {
        return isSortFilter;
    }

    public void setSortFilter(boolean sortFilter) {
        isSortFilter = sortFilter;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public String getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(String maxPrice) {
        this.maxPrice = maxPrice;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public boolean isExpanded() {
        return expanded;
    }

    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }

    public boolean canExpand() {
        return type == TYPE_ENUM;
    }
}
