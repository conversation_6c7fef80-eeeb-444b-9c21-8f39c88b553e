package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sdk.floor.floorcore.bean.BaseData;

import java.io.Serializable;
import java.util.List;

public class DiscoveryResultData extends BaseData {
    private String baseExt;


    private List<DiscoveryDataBean> recQueryList;

    private AbTestResultData abTestResult;


    public void setBaseExt(String baseExt) {
        this.baseExt = baseExt;
    }

    public String getBaseExt() {
        return baseExt;
    }

    public List<DiscoveryDataBean> getRecQueryList() {
        return recQueryList;
    }

    public AbTestResultData getAbTestResult() {
        return abTestResult;
    }

    public void setRecQueryList(List<DiscoveryDataBean> recQueryList) {
        this.recQueryList = recQueryList;
    }


    public void setAbTestResult(AbTestResultData abTestResult) {
        this.abTestResult = abTestResult;
    }

    public static class AbTestResultData implements Serializable {
        public String userConfigKey;
        public String userConfigValue;
        public String buriedExpLabel;
        public int abTestSource;
    }
}
