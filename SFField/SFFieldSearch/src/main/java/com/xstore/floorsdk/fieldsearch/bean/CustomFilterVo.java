package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 筛选条件集合实体类
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class CustomFilterVo implements Serializable {

    /**
     * 第一级别筛选条件(履约时效筛选)
     */
    private List<SearchFilterBean> firstFilterCriteria;
    /**
     * 第二级别筛选条件(排序筛选)
     */
    private List<SearchFilterQuery> secondFilterCriteria;

    public List<SearchFilterBean> getFirstFilterCriteria() {
        return firstFilterCriteria;
    }

    public void setFirstFilterCriteria(List<SearchFilterBean> firstFilterCriteria) {
        this.firstFilterCriteria = firstFilterCriteria;
    }

    public List<SearchFilterQuery> getSecondFilterCriteria() {
        return secondFilterCriteria;
    }

    public void setSecondFilterCriteria(List<SearchFilterQuery> secondFilterCriteria) {
        this.secondFilterCriteria = secondFilterCriteria;
    }
}
