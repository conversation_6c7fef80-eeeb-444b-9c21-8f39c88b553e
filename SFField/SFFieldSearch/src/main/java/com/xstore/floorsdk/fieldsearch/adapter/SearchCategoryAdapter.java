package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchCategory;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;
import com.xstore.sdk.floor.floorcore.widget.YLCircleImageView;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 搜索类目平铺适配器
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
public class SearchCategoryAdapter extends RecyclerView.Adapter<SearchCategoryAdapter.CategoryHolder> {

    private Context context;
    private SearchResultDataManager searchResultDataManager;
    private List<SearchCategory> categoryList;
    private int cateItemWidth;

    public SearchCategoryAdapter(Context context, SearchResultDataManager searchResultDataManager,
                                 List<SearchCategory> categoryList) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
        this.categoryList = categoryList;
        this.cateItemWidth = DPIUtil.getWidthByDesignValue(context, 60, 375);
    }

    public void setCategoryList(List<SearchCategory> categoryList) {
        this.categoryList = categoryList;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public CategoryHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_cate_item, parent, false);
        return new CategoryHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryHolder holder, int position) {
        SearchCategory searchCategory = categoryList.get(position);
        if (searchCategory == null) {
            return;
        }
        ViewGroup.LayoutParams lp = holder.llSearchCate.getLayoutParams();
        lp.width = this.cateItemWidth;
        holder.llSearchCate.setLayoutParams(lp);
        ImageloadUtils.loadImage(context, holder.ivCateLogo, searchCategory.getImageUrl(), true);
        holder.tvCateName.setText(searchCategory.getCategoryName());
        if (searchCategory.isSelected()) {
            holder.tvCateName.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
            holder.tvCateName.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            holder.ivCateLogo.setBorderColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
        } else {
            holder.tvCateName.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_999999));
            holder.tvCateName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            holder.ivCateLogo.setBorderColor(ContextCompat.getColor(context, R.color.sf_field_search_white));
        }
        holder.itemView.setOnClickListener(v -> {
            if (searchCategory.isSelected()) {
                searchResultDataManager.updateCategoryFilter("", "", "");
            } else {
                searchResultDataManager.updateCategoryFilter(searchCategory.getTileCategoryId(), searchCategory.getCategoryName(), searchCategory.getType());
            }
            searchResultDataManager.searchResultReporter.clickCategory(searchCategory.getCategoryName(), position);
        });
    }

    public SearchCategory getItem(int position) {
        if (categoryList == null) {
            return null;
        }
        if (position < 0 || categoryList.size() <= position) {
            return null;
        }
        return categoryList.get(position);
    }

    @Override
    public int getItemCount() {
        return categoryList == null ? 0 : categoryList.size();
    }

    public class CategoryHolder extends RecyclerView.ViewHolder {

        LinearLayout llSearchCate;
        YLCircleImageView ivCateLogo;
        TextView tvCateName;

        public CategoryHolder(@NonNull View itemView) {
            super(itemView);
            llSearchCate = itemView.findViewById(R.id.ll_search_cate);
            ivCateLogo = itemView.findViewById(R.id.iv_cate_logo);
            tvCateName = itemView.findViewById(R.id.tv_cate_name);
        }
    }
}
