package com.xstore.floorsdk.fieldsearch.widget.fragmentfilter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.config.SearchActivityConfig;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 搜索结果页筛选面板适配器
 *
 * <AUTHOR>
 * @date 2022/09/24
 */
public class SearchFragmentFilterAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    public final static int FILTER_TYPE_ACTION = 1;
    public final static int FILTER_TYPE_PRICE_RANGE = 2;
    public final static int FILTER_TYPE_FEATURE = 3;

    public static final String FILTER_KEY_PRICE_RANGE = "priceRange";

    private Context context;
    private SearchResultDataManager searchResultDataManager;
    private LayoutInflater inflater;
    private List<SearchFilterQuery> filterQueryList;
    private Map<String, List<String>> selectedFilterMap;
    private SearchActivityConfig activitySearchConfig;
    /**
     * 记录展开状态
     */
    private Map<String, Boolean> expandStatus = new HashMap<>();
    private EditText cachedEtMinPrice;
    private EditText cachedEtMaxPrice;
    private RecyclerView rvFragmentFilter;

    /**
     * 构造方法
     *
     * @param context
     * @param searchResultDataManager
     * @param rvFragmentFilter
     * @param filterQueryList
     */
    public SearchFragmentFilterAdapter(Context context, SearchResultDataManager searchResultDataManager,
                                       RecyclerView rvFragmentFilter, List<SearchFilterQuery> filterQueryList) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
        this.rvFragmentFilter = rvFragmentFilter;
        this.inflater = LayoutInflater.from(context);
        this.filterQueryList = filterQueryList;
        this.selectedFilterMap = searchResultDataManager.selectedFilterMap;
        this.activitySearchConfig = searchResultDataManager.getActivitySearchConfig();
    }

    public void setFilterQueryList(List<SearchFilterQuery> filterQueryList) {
        this.filterQueryList = filterQueryList;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case FILTER_TYPE_ACTION:
                View actionView = inflater.inflate(R.layout.sf_field_search_fragment_filter_action, parent, false);
                return new ActionHolder(actionView);
            case FILTER_TYPE_PRICE_RANGE:
                View priceView = inflater.inflate(R.layout.sf_field_search_fragment_filter_price, parent, false);
                return new PriceHolder(priceView);
            case FILTER_TYPE_FEATURE:
                View featureView = inflater.inflate(R.layout.sf_field_search_fragment_filter_feature, parent, false);
                return new FeatureHolder(featureView);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof ActionHolder) {
            ActionHolder actionHolder = (ActionHolder) holder;
            setActivityFilter(actionHolder.ivFilterAction, searchResultDataManager.isActionFilterSelected());
            actionHolder.ivFilterAction.setOnClickListener(v -> {
                if (searchResultDataManager.isActionFilterSelected()) {
                    updatePriceRangeFilter(false);
                    searchResultDataManager.clearActionFilter();
                    setActivityFilter(actionHolder.ivFilterAction, false);
                    searchResultDataManager.searchResultReporter.clickFragmentActivity("2");
                } else {
                    updatePriceRangeFilter(false);
                    searchResultDataManager.selectAction();
                    setActivityFilter(actionHolder.ivFilterAction, true);
                    searchResultDataManager.searchResultReporter.clickFragmentActivity("1");
                }
            });
        } else if (holder instanceof PriceHolder) {
            PriceHolder priceHolder = (PriceHolder) holder;
            if (!StringUtil.isNullByString(searchResultDataManager.filterMinPrice)) {
                priceHolder.etMinPrice.setText(searchResultDataManager.filterMinPrice);
            } else {
                priceHolder.etMinPrice.setText("");
            }
            if (!StringUtil.isNullByString(searchResultDataManager.filterMaxPrice)) {
                priceHolder.etMaxPrice.setText(searchResultDataManager.filterMaxPrice);
            } else {
                priceHolder.etMaxPrice.setText("");
            }
            priceHolder.etMinPrice.setOnKeyListener(onKeyListener);
            priceHolder.etMaxPrice.setOnKeyListener(onKeyListener);
            cachedEtMinPrice = priceHolder.etMinPrice;
            cachedEtMaxPrice = priceHolder.etMaxPrice;

            // 创建一个 InputFilter 实例，用于过滤输入内容
            InputFilter filterN = new InputFilter() {
                @Override
                public CharSequence filter(CharSequence charSequence, int i, int i1, Spanned spanned, int i2, int i3) {
                    if(StringUtil.isEmpty(getMaxPrice()) && charSequence.equals("0")){//只有没有值的时候，才判断
                        return ""; // 如果输入的是数字 0，则返回空字符串，表示不允许输入
                    }
                    return null; // 如果输入的不是数字 0，则不做任何处理
                }
            };

            cachedEtMaxPrice.setFilters(new InputFilter[]{filterN});

        } else if (holder instanceof FeatureHolder) {
            int realPosition = position;
            if (searchResultDataManager.canShowActivity()) {
                realPosition -= 1;
            }
            SearchFilterQuery filterQuery = filterQueryList.get(realPosition);
            if (filterQuery == null) {
                return;
            }
            FeatureHolder featureHolder = (FeatureHolder) holder;
            featureHolder.tvFeatureTitle.setText(filterQuery.getFilterLable());
            if (filterQuery.isSelected()) {
                featureHolder.tvFeatureAll.setText(getFeatureFilterValue(filterQuery.getFilterValues()));
                featureHolder.tvFeatureAll.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
            } else {
                if (filterQuery.getFilterValues() != null && filterQuery.getFilterValues().size() > 9) {
                    featureHolder.tvFeatureAll.setText(R.string.sf_field_search_filter_all);
                } else {
                    featureHolder.tvFeatureAll.setText("");
                }
                featureHolder.tvFeatureAll.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_898989));
            }
            if (filterQuery.getFilterValues() == null || filterQuery.getFilterValues().size() <= 9) {
                featureHolder.tvFeatureAll.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            } else {
                if (expandStatus.containsKey(filterQuery.getFilterKey()) && expandStatus.get(filterQuery.getFilterKey())) {
                    Drawable arrDrawable = context.getResources().getDrawable(R.drawable.sf_field_search_arrow_up_icon);
                    featureHolder.tvFeatureAll.setCompoundDrawablesWithIntrinsicBounds(null, null, arrDrawable, null);
                } else {
                    Drawable arrDrawable = context.getResources().getDrawable(R.drawable.sf_field_search_arrow_down_icon);
                    featureHolder.tvFeatureAll.setCompoundDrawablesWithIntrinsicBounds(null, null, arrDrawable, null);
                }
            }
            featureHolder.rvFeature.setLayoutManager(new GridLayoutManager(context, 3));
            FeatureFilterItemAdapter filterItemAdapter = new FeatureFilterItemAdapter(context, this,
                    searchResultDataManager, filterQuery, expandStatus.containsKey(filterQuery.getFilterKey()) && expandStatus.get(filterQuery.getFilterKey()));
            featureHolder.rvFeature.setAdapter(filterItemAdapter);
            featureHolder.rvFeature.setHasFixedSize(true);
            featureHolder.rvFeature.setNestedScrollingEnabled(false);
            filterItemAdapter.setOnItemClickListener(new FeatureFilterItemAdapter.OnItemClickListener() {
                @Override
                public void clickItem(int position) {
                    searchResultDataManager.searchResultReporter.exposureFragmentFilterClick(filterItemAdapter.getFilterQuery(),filterItemAdapter.getItem(position));
                }
            });
            featureHolder.tvFeatureAll.setOnClickListener(v -> {
                if (expandStatus.containsKey(filterQuery.getFilterKey()) && expandStatus.get(filterQuery.getFilterKey())) {
                    filterItemAdapter.setExpanded(false);
                    Drawable arrDrawable = context.getResources().getDrawable(R.drawable.sf_field_search_arrow_down_icon);
                    featureHolder.tvFeatureAll.setCompoundDrawablesWithIntrinsicBounds(null, null, arrDrawable, null);
                    expandStatus.remove(filterQuery.getFilterKey());
                    searchResultDataManager.searchResultReporter.clickFragmentFilterExpand(filterQuery.getFilterLable(), "1");
                } else {
                    filterItemAdapter.setExpanded(true);
                    Drawable arrDrawable = context.getResources().getDrawable(R.drawable.sf_field_search_arrow_up_icon);
                    featureHolder.tvFeatureAll.setCompoundDrawablesWithIntrinsicBounds(null, null, arrDrawable, null);
                    expandStatus.put(filterQuery.getFilterKey(), true);
                    searchResultDataManager.searchResultReporter.clickFragmentFilterExpand(filterQuery.getFilterLable(), "2");
                }
            });
        }
    }

    /**
     * 校验价格是否变动
     *
     * @param needRefresh
     */
    public void updatePriceRangeFilter(boolean needRefresh) {
        String minPrice = getMinPrice();
        String maxPrice = getMaxPrice();
        if (!TextUtils.equals(minPrice, searchResultDataManager.filterMinPrice)
                || !TextUtils.equals(maxPrice, searchResultDataManager.filterMaxPrice)) {
            searchResultDataManager.updatePriceRangeFilter(SearchFragmentFilterAdapter.FILTER_KEY_PRICE_RANGE, minPrice, maxPrice, needRefresh);
        }
    }

    public String getMinPrice() {
        if (cachedEtMinPrice != null && null != cachedEtMinPrice.getText() && !TextUtils.isEmpty(cachedEtMinPrice.getText().toString())) {
            return cachedEtMinPrice.getText().toString();
        }
        return "";
    }

    public String getMaxPrice() {
        if (cachedEtMaxPrice != null && null != cachedEtMaxPrice.getText() && !TextUtils.isEmpty(cachedEtMaxPrice.getText().toString())) {
            return cachedEtMaxPrice.getText().toString();
        }
        return "";
    }

    /**
     * 获取筛选项拼接字符串
     *
     * @param childQuery
     * @return
     */
    private String getFeatureFilterValue(List<SearchFilterQuery> childQuery) {
        if (childQuery == null || childQuery.isEmpty()) {
            return "";
        }
        int i = 0;
        StringBuilder querySb = new StringBuilder();
        for (SearchFilterQuery query : childQuery) {
            if (query != null && query.isSelected() && StringUtil.isNotEmpty(query.getName())) {
                if (i > 0) {
                    querySb.append(",");
                }
                querySb.append(query.getName());
                i++;
            }
        }
        return querySb.toString();
    }

    /**
     * 设置活动筛选按钮
     *
     * @param ivFilterAction
     * @param selected
     */
    private void setActivityFilter(ImageView ivFilterAction, boolean selected) {
        if (selected) {
            ImageloadUtils.loadImage(context, ivFilterAction, activitySearchConfig.getScreenSelectedImg(), R.drawable.sf_field_search_filter_fragment_action_select, R.drawable.sf_field_search_filter_fragment_action_select);
        } else {
            ImageloadUtils.loadImage(context, ivFilterAction, activitySearchConfig.getScreenNoSelectedImg(), R.drawable.sf_field_search_filter_fragment_action_unselect, R.drawable.sf_field_search_filter_fragment_action_unselect);
        }
    }

    @Override
    public int getItemCount() {
        int count = filterQueryList == null ? 0 : filterQueryList.size();
        if (searchResultDataManager.canShowActivity()) {
            count++;
        }
        return count;
    }

    @Override
    public int getItemViewType(int position) {
        int realPosition = position;
        if (searchResultDataManager.canShowActivity()) {
            if (position == 0) {
                return FILTER_TYPE_ACTION;
            }
            realPosition -= 1;
        }
        if (filterQueryList == null || filterQueryList.size() <= realPosition) {
            return -1;
        }
        if (FILTER_KEY_PRICE_RANGE.equals(filterQueryList.get(realPosition).getFilterKey())) {
            return FILTER_TYPE_PRICE_RANGE;
        } else {
            return FILTER_TYPE_FEATURE;
        }
    }

    View.OnKeyListener onKeyListener = new View.OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (keyCode == KeyEvent.KEYCODE_ENTER && KeyEvent.ACTION_UP == event.getAction()) {
                //强制隐藏键盘
                InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                return true;
            }
            return false;
        }
    };

    class ActionHolder extends RecyclerView.ViewHolder {
        ImageView ivFilterAction;

        public ActionHolder(@NonNull View itemView) {
            super(itemView);
            ivFilterAction = itemView.findViewById(R.id.iv_filter_action);
            ViewGroup.LayoutParams lp = ivFilterAction.getLayoutParams();
            lp.width = rvFragmentFilter.getWidth() - ScreenUtils.dip2px(context, 30);
            lp.height = lp.width * 34 / 290;
            ivFilterAction.setLayoutParams(lp);
        }
    }

    class PriceHolder extends RecyclerView.ViewHolder {

        EditText etMinPrice;
        EditText etMaxPrice;

        public PriceHolder(@NonNull View itemView) {
            super(itemView);
            etMinPrice = itemView.findViewById(R.id.et_min_price);
            etMaxPrice = itemView.findViewById(R.id.et_max_price);
        }
    }

    class FeatureHolder extends RecyclerView.ViewHolder {

        TextView tvFeatureTitle;
        TextView tvFeatureAll;
        RecyclerView rvFeature;

        public FeatureHolder(@NonNull View itemView) {
            super(itemView);
            tvFeatureTitle = itemView.findViewById(R.id.tv_feature_title);
            tvFeatureAll = itemView.findViewById(R.id.tv_feature_all);
            rvFeature = itemView.findViewById(R.id.rv_feature);
        }
    }
}
