package com.xstore.floorsdk.fieldsearch;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.google.android.material.appbar.AppBarLayout;
import com.gyf.barlibrary.ImmersionBar;
import com.jd.framework.json.JDJSONArray;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.xstore.floorsdk.fieldsearch.adapter.SearchAddBuyFilterAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.SearchCategoryAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.SearchProductAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.SearchTextCategoryAdapter;
import com.xstore.floorsdk.fieldsearch.bean.CouponInfo;
import com.xstore.floorsdk.fieldsearch.bean.CustomFilterVo;
import com.xstore.floorsdk.fieldsearch.bean.SearchCategory;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.bean.SearchRecommendResult;
import com.xstore.floorsdk.fieldsearch.bean.SearchRelateWordRecQuery;
import com.xstore.floorsdk.fieldsearch.bean.SearchResultInfo;
import com.xstore.floorsdk.fieldsearch.bean.SearchResultResponse;
import com.xstore.floorsdk.fieldsearch.bean.SearchSceneBean;
import com.xstore.floorsdk.fieldsearch.interfaces.SearchResultContainerInterface;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultListExposureHelper;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.floorsdk.fieldsearch.request.SearchResultCallback;
import com.xstore.floorsdk.fieldsearch.video.HomeFloorPlayHelper;
import com.xstore.floorsdk.fieldsearch.video.ShareAnimationPlayer;
import com.xstore.floorsdk.fieldsearch.widget.SearchItemDecoration;
import com.xstore.floorsdk.fieldsearch.widget.deliveryfilter.SearchResultDeliveryFilter;
import com.xstore.floorsdk.fieldsearch.widget.filter.SearchResultFilter;
import com.xstore.floorsdk.fieldsearch.widget.fragmentfilter.SearchResultFragmentFilter;
import com.xstore.sdk.floor.floorcore.FloorActionConstants;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.utils.ImageHelper;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.modules.productdetail.bean.PromotionTypeInfo;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.holder.ProductListViewHolder;
import com.xstore.sevenfresh.productcard.widget.SfCardPriceView;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SearchRecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

/**
 * 搜索结果页fragment
 *
 * <AUTHOR>
 * @date 2022/09/15
 */
public class SearchResultFragment extends Fragment implements SearchResultContract.View, View.OnClickListener, SearchResultCallback {

    public static final int REQUEST_CODE_SEARCH_HOME = 1;

    /**
     * 搜索商品渲染后切换卡片模式
     */
    private static final int CHANGE_TO_CART_AFTER_SKU = 1;
    /**
     * 推荐商品渲染后切换卡片模式
     */
    private static final int CHANGE_TO_CART_AFTER_RECOMMEND = 2;

    private DrawerLayout drawerLayout;
    private ImageView ivSceneBg;
    private LinearLayout llSearchResultContainer;
    private ImageView ivBarBack;
    private ImageView ivSearchIcon;
    private RelativeLayout rlBarSearch;
    private TextView tvSearchTips;
    private TextView tvSearchKeyword;
    private ImageView ivChangeMode;
    private AppBarLayout appBarLayout;
    private LinearLayout llSceneAndFilter;
    private View sceneLayout;
    private ImageView ivRank;
    private TextView tvSceneTitle;
    private TextView tvMoreRank;
    private LinearLayout llSingleContainer;
    private TextView tvDescSingle;
    private ImageView ivImgSingle;
    private LinearLayout llMultiContainer;
    private TextView tvDescMulti;
    private ConstraintLayout clImgTwo;
    private ImageView ivImgTwoL;
    private ImageView ivImgTwoR;
    private ConstraintLayout clImgThree;
    private ImageView ivImgThreeL;
    private ImageView ivImgThreeM;
    private ImageView ivImgThreeR;
    private SearchResultDeliveryFilter searchDeliveryFilter;
    private RecyclerView rvCateFilter;
    //类目平铺纯文本
    private RecyclerView rvCateTextFilter;
    private SearchResultFilter searchFilter;
    private RecyclerView rvAddbuyFilter;
    private TextView tvCouponLimitTip;
    private SmartRefreshLayout refreshLayout;
    private SearchRecyclerView rvProducts;
    private ClassicsFooter cfRecyclerFooter;
    private View layoutAddbuyBottom;
    private SfCardPriceView tvAddbuyTotal;
    private TextView tvAddbuyTips;
    private TextView tvAddbuyCart;
    private ImageView ivPromotionCart;
    private View layoutNodata;
    private ImageView ivNodata;
    private TextView tvNodataDesc;
    private TextView tvNodataAction;

    private SearchResultContract.Presenter presenter;
    private CenterLayoutManager cateFilterManager;
    //类目平铺纯文本
    private CenterLayoutManager cateTextFilterManager;
    private SearchCategoryAdapter categoryAdapter;
    private SearchTextCategoryAdapter textCategoryAdapter;
    private SearchResultFragmentFilter fragmentFilter;
    private CenterLayoutManager addBuyFilterManager;
    private SearchAddBuyFilterAdapter addBuyFilterAdapter;
    private StaggeredGridLayoutManager searchProductManager;
    private SearchProductAdapter searchProductAdapter;
    /**
     * 搜索结果页容器接口
     */
    public SearchResultContainerInterface searchResultContainerInterface;
    private SearchResultDataManager searchResultDataManager;
    private SearchResultListExposureHelper searchResultListExposureHelper;
    private SearchResultReporter searchResultReporter;

    /**
     * 来源 1 主搜 2 券搜 3 促销搜 4 加价购 5 领货码 6 搜索列表 7 直播
     */
    private int fromType = 0;
    private String searchKeyword = "";
    private String keywordClickFrom;
    private String couponId;
    private String promotionId;
    private String batchId;
    private String batchKey;
    private String couponIdIsToday;
    private String tipsContent;
    private String pickUpCodeBrandId;
    private boolean sourceFromCart;
    private int rankSortIndex;
    private ProductDetailBean.WareInfoBean.PromotionTypesBean promotionTypesBean;

    private String storeId;
    private String fenceId;
    private int cardIconID = R.drawable.sf_field_search_change_to_card_icon;
    private int listIconID = R.drawable.sf_field_search_change_to_list;
    /**
     * 是否是卡片模式
     */
    private boolean showCard;
    /**
     * 切换到卡片模式的时机
     */
    private int changeToCardTime;
    /**
     * 场景词数据
     */
    private SearchSceneBean searchSceneBean;
    /**
     * 第二级别筛选条件(排序筛选)
     */
    private List<SearchFilterQuery> secondFilterCriteria;
    /**
     * 外露的属性筛选条件
     */
    private List<SearchFilterQuery> showSearchFilerQuery;
    /**
     * 搜索条件数组
     */
    private List<SearchFilterQuery> filterQueries;
    /**
     * 价格带筛选条件
     */
    private List<SearchFilterQuery> addBuyFilterList;

    private SearchResultInfo searchResultInfo;
    private List<String> exposureCate = new ArrayList<>();
    private Handler handler = new Handler();

    private int padding5dpInPx = 0;
    /**
     * 是否展示 SearchResultLightInfoView
     */
    private boolean needShuangDuanShow = false;
    public SearchResultFragment() {
        searchResultDataManager = new SearchResultDataManager();

        searchResultListExposureHelper = new SearchResultListExposureHelper(searchResultDataManager);
        searchResultDataManager.setSearchResultListExposureHelper(searchResultListExposureHelper);

        searchResultReporter = new SearchResultReporter(searchResultDataManager);
        searchResultDataManager.setSearchResultReporter(searchResultReporter);

        searchResultDataManager.setSearchResultCallback(this);
    }

    public void setSearchResultContainerInterface(SearchResultContainerInterface searchResultContainerInterface) {
        this.searchResultContainerInterface = searchResultContainerInterface;
        searchResultDataManager.setSearchResultContainerInterface(searchResultContainerInterface);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.sf_field_search_result_fragment, container, false);
        searchResultDataManager.setActivity(getActivity());
        presenter = new SearchResultPresenter(getActivity(), this, this);
        padding5dpInPx = DisplayUtils.dp2px(getActivity(), 5);
        initView(view);
        initData();
        storeId = TenantIdUtils.getStoreId();
        fenceId = TenantIdUtils.getFenceId();
        searchResultDataManager.refreshData(fromType == SearchConstant.Value.FROM_TYPE_SEARCH);
        if (fromType == SearchConstant.Value.FROM_TYPE_COUPON) {
//            searchResultDataManager.queryCouponDetail(getActivity());
        } else if (isPromotion()) {
            searchResultDataManager.queryPromotionDesc(getActivity());
        }
        return view;
    }

    private void initView(View rootView) {
        drawerLayout = rootView.findViewById(R.id.drawer_layout);
        ivSceneBg = rootView.findViewById(R.id.iv_scene_bg);
        llSearchResultContainer = rootView.findViewById(R.id.ll_search_result_container);
        ivBarBack = rootView.findViewById(R.id.iv_bar_back);
        rlBarSearch = rootView.findViewById(R.id.rl_bar_search);
        ivSearchIcon = rootView.findViewById(R.id.iv_search_icon);
        tvSearchTips = rootView.findViewById(R.id.tv_search_tips);
        tvSearchKeyword = rootView.findViewById(R.id.tv_search_keyword);
        ivChangeMode = rootView.findViewById(R.id.iv_change_mode);
        appBarLayout = rootView.findViewById(R.id.appbar_layout);
        llSceneAndFilter = rootView.findViewById(R.id.ll_scene_and_filter);
        sceneLayout = rootView.findViewById(R.id.scene_layout);
        ivRank = rootView.findViewById(R.id.iv_rank);
        tvSceneTitle = rootView.findViewById(R.id.tv_scene_title);
        tvMoreRank = rootView.findViewById(R.id.tv_more_rank);
        llSingleContainer = rootView.findViewById(R.id.ll_single_container);
        tvDescSingle = rootView.findViewById(R.id.tv_desc_single);
        ivImgSingle = rootView.findViewById(R.id.iv_img_single);
        llMultiContainer = rootView.findViewById(R.id.ll_multi_container);
        tvDescMulti = rootView.findViewById(R.id.tv_desc_multi);
        clImgTwo = rootView.findViewById(R.id.cl_img_two);
        ivImgTwoL = rootView.findViewById(R.id.iv_img_two_l);
        ivImgTwoR = rootView.findViewById(R.id.iv_img_two_r);
        clImgThree = rootView.findViewById(R.id.cl_img_three);
        ivImgThreeL = rootView.findViewById(R.id.iv_img_three_l);
        ivImgThreeM = rootView.findViewById(R.id.iv_img_three_m);
        ivImgThreeR = rootView.findViewById(R.id.iv_img_three_r);
        searchDeliveryFilter = rootView.findViewById(R.id.search_delivery_filter);
        rvCateFilter = rootView.findViewById(R.id.rv_cate_filter);
        rvCateTextFilter = rootView.findViewById(R.id.rv_cate_text_filter);
        searchFilter = rootView.findViewById(R.id.search_filter);
        rvAddbuyFilter = rootView.findViewById(R.id.rv_addbuy_filter);
        tvCouponLimitTip = rootView.findViewById(R.id.tv_coupon_limit_tip);
        refreshLayout = rootView.findViewById(R.id.refreshLayout);
        rvProducts = rootView.findViewById(R.id.rv_products);
        cfRecyclerFooter = rootView.findViewById(R.id.cf_recycler_footer);
        layoutAddbuyBottom = rootView.findViewById(R.id.layout_addbuy_bottom);
        tvAddbuyTotal = rootView.findViewById(R.id.tv_addbuy_total);
        tvAddbuyTotal.setStyle(SfCardPriceView.PRICE_STYLE_NOMAL);
        tvAddbuyTips = rootView.findViewById(R.id.tv_addbuy_tips);
        tvAddbuyCart = rootView.findViewById(R.id.tv_addbuy_cart);
        ivPromotionCart = rootView.findViewById(R.id.iv_promotion_cart);
        layoutNodata = rootView.findViewById(R.id.layout_nodata);
        ivNodata = rootView.findViewById(R.id.iv_nodata);
        tvNodataDesc = rootView.findViewById(R.id.tv_nodata_desc);
        tvNodataAction = rootView.findViewById(R.id.tv_nodata_action);

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) llSearchResultContainer.getLayoutParams();
        layoutParams.topMargin = ImmersionBar.getStatusBarHeight(getActivity());
        llSearchResultContainer.setLayoutParams(layoutParams);

        ivBarBack.setOnClickListener(this);
        rlBarSearch.setOnClickListener(this);
        tvSearchKeyword.setOnClickListener(this);
        ivChangeMode.setOnClickListener(this);
        tvMoreRank.setOnClickListener(this);
        tvAddbuyCart.setOnClickListener(this);
        ivPromotionCart.setOnClickListener(this);
        tvNodataAction.setOnClickListener(this);
        searchFilter.setFilterActionListener(() -> {
            if (filterQueries == null || filterQueries.isEmpty() || !searchResultDataManager.canOpenFragmentFilter()) {
                FloorInit.getFloorConfig().showToast(getString(R.string.sf_field_search_no_goods_to_filter));
                return;
            }
            if (drawerLayout != null) {
                drawerLayout.openDrawer(Gravity.RIGHT);
                if (searchResultContainerInterface != null) {
                    searchResultContainerInterface.onDrawerLayoutChange(true);
                }
            }
            if (fragmentFilter == null) {
                fragmentFilter = getSearchFilterFragment();
            }
            fragmentFilter.setFragmentFilterQuery(filterQueries, searchResultInfo.getTotalCount());
            searchResultReporter.openFragmentFilter();

            JDJSONArray sortArea = new JDJSONArray();
            JDJSONArray sortValue = new JDJSONArray();
            fragmentFilter.getFragmentFeatureFilter(sortArea, sortValue);
            searchResultReporter.exposureFragmentFilter(sortArea, sortValue);
        });
        rvProducts.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (searchProductManager == null) {
                    return;
                }
                int[] position = searchProductManager.findFirstVisibleItemPositions(null);
                int firstVisibleItemPosition = position[0];
                if (searchResultContainerInterface != null) {
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        if (firstVisibleItemPosition == 0) {
                            searchResultContainerInterface.showGotoTop(false);
                        } else {
                            searchResultContainerInterface.showGotoTop(true);
                        }
                        searchResultContainerInterface.slideAi7HalfHide(false);
                        // 因为有搭配购,item的高低会动态变化,即使不滑到顶,也有必要刷新,否则可能左右错位,暂时没有更好办法结解决,因为SearchItemDecoration设置的第一列和第二列左右间距不一样,有问题的话再考虑打开注释强刷一下
                        //searchProductManager.invalidateSpanAssignments();
                        //rvProducts.invalidateItemDecorations();
                    } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                        searchResultContainerInterface.showGotoTop(false);
                        searchResultContainerInterface.slideAi7HalfHide(true);
                    }
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (searchProductManager == null || searchProductAdapter == null) {
                    return;
                }
                int[] position = searchProductManager.findFirstVisibleItemPositions(null);
                int firstVisibleItemPosition = position[0];
                if (changeToCardTime > 0 && dy < 0) {
                    if (changeToCardTime == CHANGE_TO_CART_AFTER_RECOMMEND && searchProductAdapter.recommendSkuPosition > 0
                            && firstVisibleItemPosition == searchProductAdapter.recommendSkuPosition - 1) {
                        //如果切换了卡片模式且已加载了推荐商品，那么在上滑到推荐第一条的时候需要重绘，防止留白
                        searchProductManager.invalidateSpanAssignments();
                        rvProducts.invalidateItemDecorations();
                        if (searchProductAdapter.skuStartPosition >= 0) {
                            changeToCardTime = CHANGE_TO_CART_AFTER_SKU;
                        } else {
                            changeToCardTime = 0;
                        }
                    }
                    if (changeToCardTime == CHANGE_TO_CART_AFTER_SKU && searchProductAdapter.skuStartPosition >= 0
                            && firstVisibleItemPosition <= searchProductAdapter.skuStartPosition + 1) {
                        //如果切换了卡片模式且已加载了搜索商品，那么在上滑到第一条的时候需要重绘，防止留白
                        searchProductManager.invalidateSpanAssignments();
                        rvProducts.invalidateItemDecorations();
                        //这里只需要重绘一次，重绘完后重置状态
//                        changeToCardTime = 0;
                    }
                }
            }
        });
        rvCateFilter.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (cateFilterManager == null || categoryAdapter == null) {
                    return;
                }
                int first = cateFilterManager.findFirstCompletelyVisibleItemPosition();
                int last = cateFilterManager.findLastCompletelyVisibleItemPosition();
                if (first == -1 || last == -1) {
                    return;
                }
                for (int i = first; i <= last; i++) {
                    SearchCategory searchCategory = categoryAdapter.getItem(i);
                    if (searchCategory == null) {
                        continue;
                    }
                    if (!exposureCate.contains(searchCategory.getTileCategoryId())) {
                        searchResultReporter.exposureCategory(searchCategory.getCategoryName(), i);
                        exposureCate.add(searchCategory.getTileCategoryId());
                    }
                }
            }
        });

        initDrawerLayout();
        initSmartRefreshLayout();
    }

    private void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            fromType = bundle.getInt(SearchConstant.Key.FROM_TYPE);
            searchKeyword = bundle.getString(SearchConstant.Key.SEARCH_KEYWORD, "");
            keywordClickFrom = bundle.getString(SearchConstant.Key.KEYWORD_CLICKFROM);
            batchId = bundle.getString(SearchConstant.Key.BATCH_ID, "");
            batchKey = bundle.getString(SearchConstant.Key.BATCH_KEY, "");
            couponId = bundle.getString(SearchConstant.Key.COUPON_ID);
            promotionTypesBean = (ProductDetailBean.WareInfoBean.PromotionTypesBean) bundle.getSerializable(SearchConstant.Key.PROMOTION_BEAN);
            promotionId = bundle.getString(SearchConstant.Key.PROMOTION_ID, "");
            tipsContent = bundle.getString(SearchConstant.Key.TIPS_CONTENT);
            sourceFromCart = bundle.getBoolean(SearchConstant.Key.SOURCE_FROM_CART, false);
            pickUpCodeBrandId = bundle.getString(SearchConstant.Key.PICKING_CODE_BRANDID);
            rankSortIndex = bundle.getInt(SearchConstant.Key.RANK_SORT_INDEX);
            needShuangDuanShow = bundle.getBoolean(SearchConstant.Key.SHUANG_DUAN_NEW_OLD_KEY,false);

            searchResultDataManager.setExtraBundle(bundle);
            searchResultReporter.initData(fromType, promotionTypesBean);
            searchResultListExposureHelper.initData(this);
            setSearchBarData();
//            setPromotionBottomView();
        }
        showCard = PreferenceUtil.getBoolean(SearchConstant.Key.SEARCH_CARD_MODE, false);
        refreshTopBarIcon(false);
        initProductRecyclerView();
    }

    /**
     * 设置顶部搜索框数据
     */
    private void setSearchBarData() {
        if (StringUtil.isNotEmpty(searchKeyword)) {
            tvSearchKeyword.setText(searchKeyword.trim());
            tvSearchKeyword.setVisibility(View.VISIBLE);
            tvSearchTips.setVisibility(View.INVISIBLE);
        } else if (fromType == SearchConstant.Value.FROM_TYPE_COUPON) {
            tvSearchTips.setText(R.string.sf_field_search_coupon_goods);
        } else if (isPromotion()) {
            tvSearchTips.setText(R.string.sf_field_search_pomotion_goods);
        } else if (fromType == SearchConstant.Value.FROM_TYPE_PICKING_CODE) {
            tvSearchTips.setText(R.string.sf_field_search_picking_code_goods);
        } else {
            String bizName = !StringUtil.isNullByString(FloorInit.getFloorConfig().getBizName()) ? FloorInit.getFloorConfig().getBizName() : "";
            tvSearchTips.setText(getString(R.string.sf_field_search_edittext_hint, bizName));
        }
    }

    /**
     * 设置促销底部合计view
     * <p>
     * 促销子类型 100 单品直降  101 单品折扣  200 赠品
     * 300 满额直降  301 满额打折  302 满额赠品
     * 304 满额加价购 305 满减加价购 306 满件折 307 满件额 308 满件加价购 1001 订单加价购
     */
    private void setPromotionBottomView() {
        if (promotionTypesBean != null) {
            layoutAddbuyBottom.setVisibility(View.VISIBLE);
            if (isAddBuy()) {
//                boolean isShowCartV2 = PreferenceUtil.getBoolean("isShowCartV2",true);
//                if(isShowCartV2){
//                    tvAddbuyCart.setVisibility(View.GONE);
//                } else {
                    tvAddbuyCart.setVisibility(View.VISIBLE);
//                }
                ivPromotionCart.setVisibility(View.GONE);
                refreshAddBuyCart(promotionTypesBean.getAddMoneyLevel());
            } else {
                tvAddbuyCart.setVisibility(View.GONE);
                ivPromotionCart.setVisibility(View.VISIBLE);
            }
        } else if(needShuangDuanShow){
            layoutAddbuyBottom.setVisibility(View.VISIBLE);
            tvAddbuyCart.setVisibility(View.GONE);
            ivPromotionCart.setVisibility(View.VISIBLE);
        } else {
            layoutAddbuyBottom.setVisibility(View.GONE);
        }
    }

    /**
     * 重新刷新加价购底部购物车按钮
     *
     * @param addMoneyLevel
     */
    public void refreshAddBuyCart(int addMoneyLevel) {
        if (addMoneyLevel == 2) {
            tvAddbuyCart.setText(R.string.sf_field_search_to_repurchase);
            tvAddbuyCart.setTextColor(ContextCompat.getColor(getActivity(), R.color.sf_field_search_white));
            tvAddbuyCart.setBackgroundResource(R.drawable.sf_field_search_corner_20_green_btn_bg);
        } else if (addMoneyLevel == 3) {
            tvAddbuyCart.setText(R.string.sf_field_search_re_repurchase);
            tvAddbuyCart.setTextColor(ContextCompat.getColor(getActivity(), R.color.sf_field_search_white));
            tvAddbuyCart.setBackgroundResource(R.drawable.sf_field_search_corner_20_green_btn_bg);
        } else {
            tvAddbuyCart.setText(R.string.sf_field_search_look_repurchase_product);
            tvAddbuyCart.setTextColor(ContextCompat.getColor(getActivity(), R.color.sf_theme_color_level_1));
            tvAddbuyCart.setBackgroundResource(R.drawable.sf_field_search_corner_20_green_white_bg);
        }
    }

    /**
     * 是否加价购
     *
     * @return
     */
    private boolean isAddBuy() {
        //addMoneyLevel：0 未命中加价购， 1 去凑单， 2 去换购， 3 重新换购
        return fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE && promotionTypesBean != null && promotionTypesBean.getAddMoneyLevel() > 0;
    }

    /**
     * 是否来自促销搜，包括普通促销及加价购
     *
     * @return
     */
    private boolean isPromotion() {
        return fromType == SearchConstant.Value.FROM_TYPE_PROMOTION || fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!TextUtils.equals(storeId, TenantIdUtils.getStoreId()) ||
                (TextUtils.equals(storeId, TenantIdUtils.getStoreId()) && !TextUtils.equals(fenceId, TenantIdUtils.getFenceId()))) {
            searchResultDataManager.secondSearch(searchKeyword);
            storeId = TenantIdUtils.getStoreId();
            fenceId = TenantIdUtils.getFenceId();
        }
        updatePromotionBottomPrice();
    }

    /**
     * 更新促销底部价格
     */
    public void updatePromotionBottomPrice() {
        if (isPromotion()) {
            if (StringUtil.isNotEmpty(promotionId) && promotionTypesBean != null && tvAddbuyTotal != null && tvAddbuyTips != null
                    && searchResultContainerInterface != null) {
                searchResultContainerInterface.promotionUpdateTotalPrice(promotionId, promotionTypesBean, tvAddbuyTotal, tvAddbuyTips, isAddBuy());
            }
        }else if(needShuangDuanShow){
            if (tvAddbuyTotal != null && tvAddbuyTips != null && searchResultContainerInterface != null) {
                searchResultContainerInterface.promotionUpdateTotalPrice(promotionId, promotionTypesBean, tvAddbuyTotal, tvAddbuyTips, isAddBuy());
            }
        }
    }

    /**
     * 初始化刷新加载控件
     */
    private void initSmartRefreshLayout() {
        refreshLayout.setEnableRefresh(false);
        refreshLayout.setOnLoadMoreListener(refreshLayout -> {
            if (searchResultDataManager.hasNextPage()) {
                searchResultDataManager.loadMore(getActivity());
            } else {
                searchResultDataManager.loadMoreRecommend(getActivity());
            }
        });
    }

    private HomeFloorPlayHelper recyclerviewPlayHelper;

    /**
     * 初始化商品列表view
     */
    private void initProductRecyclerView() {
        rvProducts.setFakeCallback(new SearchRecyclerView.FakeCallback() {
            @Override
            public long getFakeItemId(RecyclerView.ViewHolder holder) {
                if (holder instanceof ProductListViewHolder) {
                    return ((ProductListViewHolder) holder).getFakeItemId();
                }
                return holder.getItemId();
            }
        });
        rvProducts.setPadding(showCard ? padding5dpInPx : 0, 0, showCard ? padding5dpInPx : 0, 0);
        rvProducts.setClipToPadding(false);
        searchProductAdapter = new SearchProductAdapter((AppCompatActivity) getActivity(), searchResultDataManager, fromType, showCard);
        searchProductAdapter.setMPaasShowDaPeiGou(searchResultContainerInterface == null ? true : searchResultContainerInterface.isShowSearchDaPeiGou());
        rvProducts.setAdapter(searchProductAdapter);
        recyclerviewPlayHelper = new HomeFloorPlayHelper();
        recyclerviewPlayHelper.init(rvProducts, ShareAnimationPlayer.PlayType.SEARCH_LIST);
        searchProductAdapter.setTipsContent(tipsContent);
        searchProductAdapter.setOnItemClickListener(new SearchProductAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(int position) {
            }

            @Override
            public void onYunClick() {
                if (searchDeliveryFilter != null) {
                    scrollTopAndExpandAppBar();
                    searchDeliveryFilter.jumpYunTab();
                }
            }

            @Override
            public void secondSearch(String searchKeyword) {
                searchResultDataManager.secondSearch(searchKeyword);
            }

            @Override
            public void openFeedback() {
                if(searchResultDataManager!=null && searchResultDataManager.searchResultContainerInterface!=null) {
                    searchResultDataManager.searchResultContainerInterface.openFeedback(true);
                }

            }
        });
        searchProductManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        rvProducts.setLayoutManager(searchProductManager);
//        searchProductManager.setGapStrategy(StaggeredGridLayoutManager.GAP_HANDLING_NONE);
        rvProducts.addItemDecoration(new SearchItemDecoration(getActivity()));
        searchResultListExposureHelper.exposure(rvProducts);
    }

    /**
     * 更新筛选头最小高度，用于滑动折叠展开动效
     */
    private void updateFilterMinHeight() {
        if (llSceneAndFilter != null) {
            int filterHeight = 0;
            if (searchDeliveryFilter.getVisibility() == View.VISIBLE) {
                filterHeight += ScreenUtils.dip2px(getActivity(), 38);
            }
//            if (rvCateFilter.getVisibility() == View.VISIBLE) {
//                int finalFilterHeight = filterHeight;
//                rvCateFilter.post(new Runnable() {
//                    @Override
//                    public void run() {
//                        llSceneAndFilter.setMinimumHeight(rvCateFilter.getHeight() + finalFilterHeight);
//                    }
//                });
//            } else {
                llSceneAndFilter.setMinimumHeight(filterHeight);
//            }
        }
    }

    /**
     * 初始化侧边抽屉
     */
    private void initDrawerLayout() {
        fragmentFilter = getSearchFilterFragment();
        drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        drawerLayout.addDrawerListener(new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {
            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(drawerLayout.getWindowToken(), 0); //强制隐藏键盘
                if (searchResultContainerInterface != null) {
                    searchResultContainerInterface.onDrawerLayoutChange(false);
                }
            }

            @Override
            public void onDrawerStateChanged(int newState) {
            }
        });
    }

    public void closeDrawerLayout() {
        drawerLayout.closeDrawer(Gravity.RIGHT);
        InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(drawerLayout.getWindowToken(), 0); //强制隐藏键盘
        if (searchResultContainerInterface != null) {
            searchResultContainerInterface.onDrawerLayoutChange(false);
        }
    }

    public boolean isDrawerOpen() {
        return drawerLayout != null && drawerLayout.isDrawerOpen(Gravity.RIGHT);
    }

    public SearchResultFragmentFilter getSearchFilterFragment() {
        SearchResultFragmentFilter fragmentFilter = SearchResultFragmentFilter.getInstance(searchResultDataManager, fromType);
        getActivity().getSupportFragmentManager().beginTransaction().replace(R.id.fl_filter_container, fragmentFilter).commitAllowingStateLoss();
        fragmentFilter.setSearchFragmentFilterCallback(() -> closeDrawerLayout());
        return fragmentFilter;
    }

    /**
     * 初始化类目平铺
     *
     * @param searchResultInfo
     */
    private void initCateFilter(SearchResultInfo searchResultInfo) {
        if (searchResultInfo == null || searchResultInfo.getTileCategoryList() == null || searchResultInfo.getTileCategoryList().isEmpty()) {
            rvCateFilter.setVisibility(View.GONE);
            rvCateTextFilter.setVisibility(View.GONE);
            searchResultDataManager.clearCategoryFilter();
            updateFilterMinHeight();
            return;
        }
        List<SearchCategory> searchCategoryList = searchResultInfo.getTileCategoryList();
        int scrollPosition = 0;
        for (int i = 0; i < searchCategoryList.size(); i++) {
            SearchCategory searchCategory = searchCategoryList.get(i);
            if (searchCategory == null) {
                continue;
            }
            if (StringUtil.safeEqualsAndNotNull(searchCategory.getTileCategoryId(), searchResultDataManager.tileCategoryId)
                    || StringUtil.safeEqualsAndNotNull(searchCategory.getCategoryName(), searchResultDataManager.tileCategoryName)) {
                searchCategory.setSelected(true);
                scrollPosition = i;
            } else {
                searchCategory.setSelected(false);
            }
        }
        rvCateFilter.setVisibility(View.VISIBLE);
        if (cateFilterManager == null) {
            cateFilterManager = new CenterLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
            rvCateFilter.setLayoutManager(cateFilterManager);
        }
        if (categoryAdapter == null) {
            categoryAdapter = new SearchCategoryAdapter(getActivity(), searchResultDataManager, searchCategoryList);
            rvCateFilter.setAdapter(categoryAdapter);
            scrollToCate(scrollPosition, true);
        } else {
            categoryAdapter.setCategoryList(searchCategoryList);
            scrollToCate(scrollPosition, false);
        }
        //类目平铺纯文本初始化
        rvCateTextFilter.setVisibility(View.GONE);
        if (cateTextFilterManager == null) {
            cateTextFilterManager = new CenterLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
            rvCateTextFilter.setLayoutManager(cateTextFilterManager);
        }
        if (textCategoryAdapter == null) {
            textCategoryAdapter = new SearchTextCategoryAdapter(getActivity(), searchResultDataManager, searchCategoryList);
            rvCateTextFilter.setAdapter(textCategoryAdapter);
            scrollToTextCate(scrollPosition, true);
        } else {
            textCategoryAdapter.setCategoryList(searchCategoryList);
            scrollToTextCate(scrollPosition, false);
        }
        updateFilterMinHeight();
    }

    /**
     * 类目滚动
     *
     * @param position
     * @param first
     */
    private void scrollToCate(int position, boolean first) {
        if (cateFilterManager != null) {
            cateFilterManager.setFast(first);
        }
        rvCateFilter.smoothScrollToPosition(position);
    }

    private void scrollToTextCate(int position, boolean first) {
        if (cateTextFilterManager != null) {
            cateTextFilterManager.setFast(first);
        }
        rvCateTextFilter.smoothScrollToPosition(position);
    }

    /**
     * 初始化筛选条
     *
     * @param customFilterVo
     * @param showSearchFilerQuery
     */
    private void initSearchFilter(CustomFilterVo customFilterVo, List<SearchFilterQuery> showSearchFilerQuery) {
        if (secondFilterCriteria == null || secondFilterCriteria.isEmpty()) {
            if (customFilterVo != null) {
                secondFilterCriteria = customFilterVo.getSecondFilterCriteria();
            }
            this.showSearchFilerQuery = showSearchFilerQuery;
            if (secondFilterCriteria != null && !secondFilterCriteria.isEmpty()) {
                searchFilter.setVisibility(View.VISIBLE);
                searchFilter.setFilterData(searchResultDataManager, customFilterVo.getSecondFilterCriteria(), showSearchFilerQuery);
            } else {
                searchFilter.setVisibility(View.GONE);
                SgmBusinessErrorUtil.reportSecondFilterCriteriaEmpty(searchKeyword);
            }
        } else {
            //如果本地已经有排序筛选数据，不更新，只同步选中态
            searchFilter.syncFeatureFilterSelectStatus(searchResultDataManager.selectedFilterMap);
        }
    }

    /**
     * 获取排序类型（埋点用）
     * sortType 1 综合 2 价格升序 3 价格降序  4 销量降序
     *
     * @return
     */
    public int getSortType() {
        int sortType = 0;
        if (secondFilterCriteria != null && !secondFilterCriteria.isEmpty()) {
            for (int i = 0; i < secondFilterCriteria.size(); i++) {
                if (secondFilterCriteria.get(i).isSelected()) {
                    if (i == 1) {
                        sortType = 4;
                    } else if (i == 2 || i == 3) {
                        sortType = i;
                    } else {
                        sortType = i + 1;
                    }
                    break;
                }
            }
        }
        return sortType;
    }

    /**
     * 获取履约时效筛选类型，埋点用
     *
     * @return
     */
    public String getDeliveryType() {
        if (searchDeliveryFilter != null && searchDeliveryFilter.getVisibility() == View.VISIBLE) {
            return searchDeliveryFilter.getDeliveryType();
        }
        return "";
    }

    /**
     * 获取属性筛选条件，埋点用
     *
     * @param sortArea
     * @param sortValue
     */
    public void getFeatureFilter(JDJSONArray sortArea, JDJSONArray sortValue) {
        if (sortArea == null || sortValue == null) {
            return;
        }
        if (showSearchFilerQuery != null && !showSearchFilerQuery.isEmpty()) {
            for (SearchFilterQuery filterQuery : showSearchFilerQuery) {
                if (filterQuery == null) {
                    continue;
                }
                sortArea.add(filterQuery.getFilterLable());
                if (filterQuery.isSelected()) {
                    if (filterQuery.canExpand() && filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
                        for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                            if (query != null && query.isSelected()) {
                                sortValue.add(filterQuery.getFilterLable() + "#" + query.getName());
                            }
                        }
                    } else {
                        sortValue.add(filterQuery.getFilterLable());
                    }
                }
            }
        }
    }

    /**
     * 初始化加价购筛选条
     *
     * @param addBuyFilters
     */
    private void initAddBuyFilter(List<SearchFilterQuery> addBuyFilters) {
        if (this.addBuyFilterList == null || this.addBuyFilterList.isEmpty()) {
            this.addBuyFilterList = addBuyFilters;
            if (addBuyFilterList == null || addBuyFilterList.isEmpty()) {
                rvAddbuyFilter.setVisibility(View.GONE);
                searchResultDataManager.clearAddBuyFilter();
                return;
            } else {
                rvAddbuyFilter.setVisibility(View.VISIBLE);
                if (addBuyFilterList.get(0) != null) {
                    //默认选中全部
                    addBuyFilterList.get(0).setSelected(true);
                }
                if (addBuyFilterManager == null) {
                    addBuyFilterManager = new CenterLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false);
                    rvAddbuyFilter.setLayoutManager(addBuyFilterManager);
                }
                if (addBuyFilterAdapter == null) {
                    addBuyFilterAdapter = new SearchAddBuyFilterAdapter(getActivity(), searchResultDataManager, addBuyFilterList, rvAddbuyFilter);
                    rvAddbuyFilter.setAdapter(addBuyFilterAdapter);
                } else {
                    addBuyFilterAdapter.setAddBugFilterList(addBuyFilterList);
                }
            }
        } else {
            if (addBuyFilterAdapter != null) {
                addBuyFilterAdapter.notifyDataSetChanged();
            }
        }
    }

    /**
     * 初始化面板筛选器
     *
     * @param searchResultInfo
     */
    private void initFragmentFilter(SearchResultInfo searchResultInfo) {
        if (filterQueries == null || filterQueries.isEmpty()) {
            filterQueries = searchResultInfo.getFilterQueries();
        } else {
            //如果本地已经有筛选数据，不更新，只同步选中态
            if (isDrawerOpen()) {
                fragmentFilter.setFragmentFilterQuery(filterQueries, searchResultInfo.getTotalCount());
            }
        }
        if (filterQueries == null || filterQueries.isEmpty() || !searchResultDataManager.canOpenFragmentFilter()) {
            searchFilter.setFilterStyle(SearchResultFilter.FILTER_STYLE_UNENABLE);
        } else {
            searchFilter.setFilterStyle(searchResultDataManager.hasSelectedFilter() ? SearchResultFilter.FILTER_STYLE_SELECTED : SearchResultFilter.FILTER_STYLE_UNSELECT);
        }
    }

    @Override
    public void onClick(View v) {
        if (NoDoubleClickUtils.isDoubleClick()) {
            return;
        }
        int id = v.getId();
        if (id == R.id.iv_bar_back) {
            getActivity().finish();
        } else if (id == R.id.rl_bar_search || id == R.id.tv_search_keyword) {
            Bundle bundle = new Bundle();
            bundle.putInt(FloorJumpManager.URL_TYPE, FloorActionConstants.URL_TYPE_SEARCH_HOME);
            bundle.putString(FloorJumpManager.TO_URL, searchKeyword);
            bundle.putInt(SearchConstant.Key.FROM_TYPE, fromType);
            bundle.putBoolean(SearchConstant.Key.FROM_SEARCH_RESULT, true);
            bundle.putSerializable(FloorJumpManager.REQUEST_CODE, REQUEST_CODE_SEARCH_HOME);
            FloorJumpManager.getInstance().jumpAction(getActivity(), bundle);
        } else if (id == R.id.iv_change_mode) {
            if (searchProductAdapter != null) {
                showCard = !showCard;
                if (showCard && searchProductAdapter != null) {
                    if (searchProductAdapter.recommendSkuPosition > 0) {
                        changeToCardTime = CHANGE_TO_CART_AFTER_RECOMMEND;
                    } else if (searchProductAdapter.skuStartPosition >= 0) {
                        changeToCardTime = CHANGE_TO_CART_AFTER_SKU;
                    } else {
                        changeToCardTime = 0;
                    }
                } else {
                    changeToCardTime = 0;
                }
                searchProductAdapter.changeShowMode(showCard);
                rvProducts.setPadding(showCard ? padding5dpInPx : 0, 0, showCard ? padding5dpInPx : 0, 0);
                ivChangeMode.setImageResource(showCard ? listIconID : cardIconID);
                PreferenceUtil.saveBoolean(SearchConstant.Key.SEARCH_CARD_MODE, showCard);
                postDelayAutoPlay();
            }
        } else if (id == R.id.tv_more_rank) {
        } else if (id == R.id.tv_addbuy_cart) {
            boolean isShowCartV2 = PreferenceUtil.getBoolean("isShowCartV2",true);
            if (isShowCartV2) {
                if (searchResultContainerInterface != null) {
                    searchResultContainerInterface.showRepurchase(promotionTypesBean);
                }
            } else {
                FloorJumpManager.getInstance().jumpRepurchase(getActivity(), promotionTypesBean);
            }
            searchResultReporter.clickRepurchase(tvAddbuyCart.getText().toString());
        } else if (id == R.id.iv_promotion_cart) {
            searchResultReporter.clickGotoCart();
            if (sourceFromCart) {
                getActivity().finish();
            } else {
                FloorJumpManager.getInstance().jumpCart(getActivity());
            }
        } else if (id == R.id.tv_nodata_action) {
            FloorJumpManager.getInstance().backHomePage(getActivity());
        }
    }

    @Override
    public void setSearchResult(SearchResultResponse searchResult, boolean isRefesh, int source) {
        refreshLayout.setVisibility(View.VISIBLE);
        layoutNodata.setVisibility(View.GONE);
        if (!isRefesh) {
            refreshLayout.finishLoadMore();
        } else {
            if (rvProducts != null) {
                rvProducts.scrollToPosition(0);
            }
        }
        if (searchResult == null) {
            return;
        }
        searchResultInfo = searchResult.getSearchResultInfoVo();
        if (isRefesh) {
            setSceneData(searchResult.getSceneWordVo());
            CustomFilterVo customFilterVo = searchResult.getCustomizeFilterCriteriaVo();
            if (customFilterVo != null && customFilterVo.getFirstFilterCriteria() != null && !customFilterVo.getFirstFilterCriteria().isEmpty()) {
                searchDeliveryFilter.setVisibility(View.VISIBLE);
                searchDeliveryFilter.setDeliveryFilterData(searchResultDataManager, customFilterVo.getFirstFilterCriteria());
            } else {
                searchDeliveryFilter.setVisibility(View.GONE);
                searchResultDataManager.clearDeliveryFilter(false);
                SgmBusinessErrorUtil.reportFirstFilterCriteriaEmpty(searchKeyword, source);
            }
            initCateFilter(searchResultInfo);
            initSearchFilter(customFilterVo, searchResult.getShowSearchFilerQuery());
        }
        if (searchResultInfo != null) {
            if (isRefesh) {
                initAddBuyFilter(searchResultInfo.getPriceRangeList());
                initFragmentFilter(searchResultInfo);
                searchFilter.setTotalCount(searchResultInfo.getTotalCount());
                if (!StringUtil.isNullByString(searchResultInfo.getCouponLimitDesc())) {
                    tvCouponLimitTip.setText(searchResultInfo.getCouponLimitDesc());
                    tvCouponLimitTip.setVisibility(View.VISIBLE);
                } else {
                    tvCouponLimitTip.setVisibility(View.GONE);
                }
            }
//            List<String> relatedWords = searchResult.getRelatedWords() == null ? null : searchResult.getRelatedWords().getWords();
            List<SearchRelateWordRecQuery> relatedWordRecQueryList = searchResult.getRelatedWords() == null ? null : searchResult.getRelatedWords().getRecQueryList();
            setProductData(searchResultInfo, relatedWordRecQueryList,searchResult.getBottomProductCardInfo(), isRefesh);
        } else {
            if (isRefesh && (searchResult.getBottomProductCardInfo() == null || searchResult.getBottomProductCardInfo().isEmpty())) {
                //刷新操作，无搜索结果且无试试搜且无托底推荐，展示空数据托底
                showNoData();
            }
        }
        handleLoadMore(searchResult.getBottomProductCardInfo());
    }

    /**
     * 处理是否可加载更多
     *
     * @param bottomRecommendWares
     */
    private void handleLoadMore(List<SkuInfoBean> bottomRecommendWares) {
        if (searchResultDataManager.hasNextPage()) {
            refreshLayout.setEnableLoadMore(true);
            refreshLayout.setEnableAutoLoadMore(true);
        } else if (bottomRecommendWares != null && bottomRecommendWares.size() >= searchResultDataManager.recommendPageSize) {
            refreshLayout.setEnableLoadMore(true);
            refreshLayout.setEnableAutoLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
            refreshLayout.setEnableAutoLoadMore(false);
        }
    }

    @Override
    public void finishLoadMore() {
        refreshLayout.finishLoadMore();
    }

    /**
     * 绑定商品数据
     *
     * @param searchResultInfo
     * @param relatedWordsRecQueryList
     * @param bottomRecommendWares
     * @param isRefesh
     */
    private void setProductData(SearchResultInfo searchResultInfo, List<SearchRelateWordRecQuery> relatedWordsRecQueryList, List<SkuInfoBean> bottomRecommendWares, boolean isRefesh) {
        if (isRefesh && (searchResultInfo.getProductCardVoList() == null || searchResultInfo.getProductCardVoList().isEmpty())
                && (searchResultInfo.getTrySearchWords() == null || searchResultInfo.getTrySearchWords().isEmpty())
                && (bottomRecommendWares == null || bottomRecommendWares.isEmpty())) {
            //刷新操作，无搜索结果且无试试搜且无托底推荐，展示空数据托底
            showNoData();
            return;
        }
        if ((needShuangDuanShow ||isPromotion()) && layoutAddbuyBottom.getVisibility() == View.GONE) {
            setPromotionBottomView();
        }
        if (searchProductAdapter != null) {
            if (isRefesh) {
                searchProductAdapter.setData(searchResultInfo.getProductCardVoList(), searchResultInfo.getWordSearchVo(),
                        searchResultInfo.getExpandQueryVo(), searchResultInfo.getTrySearchWords(),relatedWordsRecQueryList,
                        searchResultInfo.getPrescriptionCloudStoreVo(), bottomRecommendWares);
                //数据请求回来，列表未滚动时，需要手动执行一次曝光
                if (handler != null && searchResultListExposureHelper != null) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            searchResultListExposureHelper.exposureByHand(rvProducts);
                        }
                    });
                }
            } else {
                searchProductAdapter.addData(searchResultInfo.getProductCardVoList(), bottomRecommendWares);
            }
            postDelayAutoPlay();
        }
    }

    /**
     * 绑定场景词数据
     *
     * @param sceneBean
     */
    private void setSceneData(SearchSceneBean sceneBean) {
        if (searchSceneBean != null) {
            //已经设置过场景词，不再重复设置
            return;
        }
        if (sceneBean == null || sceneBean.getSceneWordImageVos() == null || sceneBean.getSceneWordImageVos().size() == 0) {
            setDarkStatusBar(false);
            if (ivSceneBg.getVisibility() == View.VISIBLE) {
                refreshTopBarIcon(false);
            }
            sceneLayout.setVisibility(View.GONE);
            ivSceneBg.setVisibility(View.GONE);
            if (sceneBean != null && (sceneBean.getSceneWordImageVos() == null || sceneBean.getSceneWordImageVos().size() == 0)) {
                SgmBusinessErrorUtil.reportSceneWordImageEmpty(searchKeyword);
            }
            return;
        }
        searchSceneBean = sceneBean;
        List<String> imgList = new ArrayList<>();
        for (SearchSceneBean.SceneImage sceneImage : sceneBean.getSceneWordImageVos()) {
            if (sceneImage != null && !StringUtil.isNullByString(sceneImage.getImage())) {
                imgList.add(sceneImage.getImage());
            }
        }
        if (imgList.size() <= 0) {
            setDarkStatusBar(false);
            if (ivSceneBg.getVisibility() == View.VISIBLE) {
                refreshTopBarIcon(false);
            }
            sceneLayout.setVisibility(View.GONE);
            ivSceneBg.setVisibility(View.GONE);
            SgmBusinessErrorUtil.reportSceneWordImageEmpty(searchKeyword);
            return;
        }
        sceneLayout.setVisibility(View.VISIBLE);
        ivSceneBg.setVisibility(View.VISIBLE);
        ImageloadUtils.loadImage(getActivity(), ivSceneBg, sceneBean.getBgImg(), 0, R.drawable.sf_field_search_scene_bg_default);
        ImageloadUtils.loadImage(getActivity(), ivRank, sceneBean.getRankImg());
        tvSceneTitle.setText(sceneBean.getTitle());
        if (imgList.size() == 1) {
            llSingleContainer.setVisibility(View.VISIBLE);
            llMultiContainer.setVisibility(View.GONE);
            tvDescSingle.setText(sceneBean.getSceneWordDesc());
            ImageHelper.loadRoundImage(getActivity(), imgList.get(0), ivImgSingle, 10f);
            ivImgSingle.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(0)));
        } else {
            llSingleContainer.setVisibility(View.GONE);
            llMultiContainer.setVisibility(View.VISIBLE);
            tvDescMulti.setText(sceneBean.getSceneWordDesc());
            if (imgList.size() == 2) {
                clImgTwo.setVisibility(View.VISIBLE);
                clImgThree.setVisibility(View.GONE);
                ImageHelper.loadRoundImage(getActivity(), imgList.get(0), ivImgTwoL, 8f, 0, 8f, 0);
                ImageHelper.loadRoundImage(getActivity(), imgList.get(1), ivImgTwoR, 0, 8f, 0, 8f);
                ivImgTwoL.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(0)));
                ivImgTwoR.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(1)));
            } else {
                clImgTwo.setVisibility(View.GONE);
                clImgThree.setVisibility(View.VISIBLE);
                ImageHelper.loadRoundImage(getActivity(), imgList.get(0), ivImgThreeL, 8f, 0, 8f, 0);
                ImageHelper.loadRoundImage(getActivity(), imgList.get(1), ivImgThreeM, 0);
                ImageHelper.loadRoundImage(getActivity(), imgList.get(2), ivImgThreeR, 0, 8f, 0, 8f);
                ivImgThreeL.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(0)));
                ivImgThreeM.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(1)));
                ivImgThreeR.setOnClickListener(v -> doAction(sceneBean.getSceneWordImageVos().get(2)));
            }
        }
        setDarkStatusBar(true);
        refreshTopBarIcon(true);
        searchResultReporter.exposureScene();
    }

    /**
     * 是否有场景词
     *
     * @return
     */
    public boolean showScene() {
        return sceneLayout != null && sceneLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 场景图跳转
     *
     * @param sceneImage
     */
    private void doAction(SearchSceneBean.SceneImage sceneImage) {
        if (sceneImage == null) {
            return;
        }
        if (!StringUtil.isNullByString(sceneImage.getLink())) {
            Bundle bundle = new Bundle();
            bundle.putString(FloorJumpManager.TO_URL, sceneImage.getLink());
            bundle.putInt(FloorJumpManager.URL_TYPE, sceneImage.getLinkType());
            bundle.putBoolean(FloorJumpManager.NEED_LOGIN, false);
            FloorInit.getFloorConfig().startPage(getActivity(), bundle);
        }
    }

    /**
     * 展示搜索无结果托底
     */
    @Override
    public void showNoData() {
        refreshLayout.setVisibility(View.GONE);
        layoutNodata.setVisibility(View.VISIBLE);
        switch (fromType) {
            case SearchConstant.Value.FROM_TYPE_SEARCH:
                tvNodataDesc.setText(R.string.sf_field_search_no_data_tips);
                tvNodataAction.setVisibility(View.VISIBLE);
                break;
            case SearchConstant.Value.FROM_TYPE_COUPON:
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
            case SearchConstant.Value.FROM_TYPE_LIVE:
                tvNodataDesc.setText(R.string.sf_field_search_no_data);
                tvNodataAction.setVisibility(View.GONE);
                break;
            default:
                break;
        }
        searchResultReporter.searchNoData();
    }

    @Override
    public void setRecommendResult(SearchRecommendResult searchBottomWareInfo) {
        if (searchBottomWareInfo == null || searchBottomWareInfo.getProductCardVoList() == null
                || searchBottomWareInfo.getProductCardVoList().size() < searchResultDataManager.recommendPageSize) {
            refreshLayout.finishLoadMore();
            refreshLayout.setEnableLoadMore(false);
            refreshLayout.setEnableAutoLoadMore(false);
        } else {
            refreshLayout.finishLoadMore();
            refreshLayout.setEnableLoadMore(true);
            refreshLayout.setEnableAutoLoadMore(true);
        }
        searchProductAdapter.addRecommendData(searchBottomWareInfo != null ? searchBottomWareInfo.getProductCardVoList() : null);
    }

    @Override
    public void setPromotionResult(PromotionTypeInfo promotionTypeInfo) {
        if (promotionTypeInfo == null) {
            SgmBusinessErrorUtil.reportGetPromotionDescEmpty(promotionId);
            return;
        }
        if (searchProductAdapter != null) {
            searchProductAdapter.setPromotionInfo(promotionTypeInfo);
        }
    }

    @Override
    public void setCouponInfoResult(CouponInfo couponInfo) {
        if (couponInfo == null) {
            return;
        }
        if (searchProductAdapter != null) {
            searchProductAdapter.setCouponInfo(couponInfo);
        }
    }

    @Override
    public void setUnStockRecommend() {
        if (!showCard && searchProductAdapter != null) {
            searchProductAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void secondSearchToClearCache(String searchKeyword) {
        searchSceneBean = null;
        secondFilterCriteria = null;
        filterQueries = null;
        addBuyFilterList = null;
        this.searchKeyword = searchKeyword;
        changeToCardTime = 0;
        setSearchBarData();
        scrollTopAndExpandAppBar();
        if (searchResultContainerInterface != null) {
            searchResultContainerInterface.showGotoTop(false);
        }
        if (searchFilter != null) {
            searchFilter.scrollToPosition(0);
        }
        if (rvAddbuyFilter != null) {
            rvAddbuyFilter.scrollToPosition(0);
        }
    }

    /**
     * 滑动到顶部并展开折叠的AppBar
     */
    public void scrollTopAndExpandAppBar() {
        if (rvProducts != null) {
            rvProducts.scrollToPosition(0);
        }
        if (appBarLayout != null) {
            appBarLayout.setExpanded(true);
        }
    }

    /**
     * 更新回退及切换模式图标
     *
     * @param showWhite
     */
    private void refreshTopBarIcon(boolean showWhite) {
        if (showWhite) {
            cardIconID = R.drawable.sf_field_search_change_to_card_white;
            listIconID = R.drawable.sf_field_search_change_to_list_white;
        } else {
            cardIconID = R.drawable.sf_field_search_change_to_card_icon;
            listIconID = R.drawable.sf_field_search_change_to_list;
        }
        ivChangeMode.setImageResource(showCard ? listIconID : cardIconID);
        ivBarBack.setImageResource(showWhite ? R.drawable.sf_field_search_back_white : R.drawable.sf_field_search_back_black);
    }

    /**
     * 设置状态栏
     *
     * @param darkStatusBar
     */
    public void setDarkStatusBar(boolean darkStatusBar) {
        if (searchResultContainerInterface != null) {
            searchResultContainerInterface.setDarkStatusBar(darkStatusBar);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_SEARCH_HOME:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    searchKeyword = data.getStringExtra(SearchConstant.Key.SEARCH_KEYWORD);
                    if (!StringUtil.isNotEmpty(searchKeyword)) {
                        searchKeyword = "";
                    }
                    keywordClickFrom = data.getStringExtra(SearchConstant.Key.KEYWORD_CLICKFROM);
                    searchResultDataManager.keywordClickFrom = keywordClickFrom;
                    searchResultDataManager.secondSearch(searchKeyword);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (autoPlayRunnable != null) {
            handler.removeCallbacks(autoPlayRunnable);
        }
    }

    // 列表内容刚刚填充或刷新时，手动触发可视区域第一个高光视频并播放
    private Runnable autoPlayRunnable = new Runnable() {
        @Override
        public void run() {
            if (recyclerviewPlayHelper != null) {
                recyclerviewPlayHelper.autoPlay();
            }
        }
    };

    /**
     * 先移除，在增加，延迟1秒，确保1秒内不会被多次执行高光时刻视频的自动播放检测
     */
    private void postDelayAutoPlay() {
        handler.removeCallbacks(autoPlayRunnable);
        handler.postDelayed(autoPlayRunnable, 1000);
    }
}
