package com.xstore.floorsdk.fieldsearch.config;

import com.xstore.sdk.floor.floorcore.utils.StringUtil;

import java.io.Serializable;

/**
 * 搜索ducc配置
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
public class SearchActivityConfig implements Serializable {
    private boolean ifShowActivitySelect;
    private String searchBarNoSelectedImg;
    private String searchBarSelectedImg;
    private String screenSelectedImg;
    private String screenNoSelectedImg;
    private String activitySelectValue;

    public boolean isIfShowActivitySelect() {
        return ifShowActivitySelect;
    }

    public void setIfShowActivitySelect(boolean ifShowActivitySelect) {
        this.ifShowActivitySelect = ifShowActivitySelect;
    }

    public String getSearchBarNoSelectedImg() {
        return searchBarNoSelectedImg;
    }

    public void setSearchBarNoSelectedImg(String searchBarNoSelectedImg) {
        this.searchBarNoSelectedImg = searchBarNoSelectedImg;
    }

    public String getSearchBarSelectedImg() {
        return searchBarSelectedImg;
    }

    public void setSearchBarSelectedImg(String searchBarSelectedImg) {
        this.searchBarSelectedImg = searchBarSelectedImg;
    }

    public String getScreenSelectedImg() {
        return screenSelectedImg;
    }

    public void setScreenSelectedImg(String screenSelectedImg) {
        this.screenSelectedImg = screenSelectedImg;
    }

    public String getScreenNoSelectedImg() {
        return screenNoSelectedImg;
    }

    public void setScreenNoSelectedImg(String screenNoSelectedImg) {
        this.screenNoSelectedImg = screenNoSelectedImg;
    }

    public String getActivitySelectValue() {
        return activitySelectValue;
    }

    public void setActivitySelectValue(String activitySelectValue) {
        this.activitySelectValue = activitySelectValue;
    }

    /**
     * 是否展示活动图标
     *
     * @return
     */
    public boolean showActivityImg() {
        return ifShowActivitySelect && !StringUtil.isNullByString(activitySelectValue);
    }
}
