package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchCategory;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;

import java.util.List;

public class SearchTextCategoryAdapter extends RecyclerView.Adapter<SearchTextCategoryAdapter.TextCategoryHolder> {

    private Context context;
    private SearchResultDataManager searchResultDataManager;
    private List<SearchCategory> categoryList;

    public SearchTextCategoryAdapter(Context context, SearchResultDataManager searchResultDataManager,
                                 List<SearchCategory> categoryList) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
        this.categoryList = categoryList;
    }

    public void setCategoryList(List<SearchCategory> categoryList) {
        this.categoryList = categoryList;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public TextCategoryHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_text_cate_item, parent, false);
        return new TextCategoryHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TextCategoryHolder holder, int position) {
        SearchCategory searchCategory = categoryList.get(position);
        if (searchCategory == null) {
            return;
        }

        holder.tvCateName.setText(searchCategory.getCategoryName());
        // 动态设置margin
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) holder.tvCateName.getLayoutParams();
        int marginLeft = position == 0 ? ScreenUtils.dip2px(context,12) : 0;
        int marginRight = ScreenUtils.dip2px(context,8);
        layoutParams.leftMargin = marginLeft;
        layoutParams.rightMargin = marginRight;
        holder.tvCateName.setLayoutParams(layoutParams);
        if (searchCategory.isSelected()) {
            holder.tvCateName.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
            holder.tvCateName.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            holder.tvCateName.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_999999));
            holder.tvCateName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
        holder.itemView.setOnClickListener(v -> {
            if (searchCategory.isSelected()) {
                searchResultDataManager.updateCategoryFilter("", "", "");
            } else {
                searchResultDataManager.updateCategoryFilter(searchCategory.getTileCategoryId(), searchCategory.getCategoryName(), searchCategory.getType());
            }
            searchResultDataManager.searchResultReporter.clickCategory(searchCategory.getCategoryName(), position);
        });
    }

    public SearchCategory getItem(int position) {
        if (categoryList == null) {
            return null;
        }
        if (position < 0 || categoryList.size() <= position) {
            return null;
        }
        return categoryList.get(position);
    }

    @Override
    public int getItemCount() {
        return categoryList == null ? 0 : categoryList.size();
    }

    public class TextCategoryHolder extends RecyclerView.ViewHolder {

        TextView tvCateName;

        public TextCategoryHolder(@NonNull View itemView) {
            super(itemView);
            tvCateName = itemView.findViewById(R.id.tv_cate_name);
        }
    }
}

