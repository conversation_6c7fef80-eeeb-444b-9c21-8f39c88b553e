package com.xstore.floorsdk.fieldsearch.widget.dropdownfilter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 下拉展开筛选面板适配器
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
public class DropDownFilterAdapter extends RecyclerView.Adapter<DropDownFilterAdapter.FilterHolder> {
    private Context context;
    private SearchResultDataManager searchResultDataManager;
    private OnItemClickListener onItemClickListener;
    private SearchFilterQuery filterQuery;
    private List<SearchFilterQuery> childFilterQueryList;

    public DropDownFilterAdapter(Context context, SearchResultDataManager searchResultDataManager) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
    }

    public SearchFilterQuery getFilterQuery() {
        return filterQuery;
    }

    /**
     * 绑定数据
     *
     * @param filterQuery
     */
    public void setFilterQuery(SearchFilterQuery filterQuery) {
        this.filterQuery = filterQuery;
        if (filterQuery != null) {
            childFilterQueryList = filterQuery.getFilterValues();
        } else {
            childFilterQueryList = null;
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public FilterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_drop_down_filter_item, parent, false);
        return new FilterHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FilterHolder holder, int position) {
        SearchFilterQuery query = childFilterQueryList.get(position);
        if (query == null) {
            return;
        }
        holder.tvFilterKey.setText(query.getName());
        if (query.isSelected()) {
            holder.tvFilterKey.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
            holder.ivFilterSelected.setVisibility(View.VISIBLE);
        } else {
            holder.tvFilterKey.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_999999));
            holder.ivFilterSelected.setVisibility(View.GONE);
        }
        holder.itemView.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (filterQuery.isSortFilter()) {
                if (!query.isSelected()) {
                    clearSortFilterSelectStatus();
                    query.setSelected(true);
                    filterQuery.setFilterLable(query.getName());
                    if (StringUtil.isNullByString(query.getFilterKey())) { //综合
                        searchResultDataManager.clearSortFilter(true);
                    } else {
                        searchResultDataManager.updateSortFilter(query.getFilterKey(), query.getFilterValue());
                    }
                }
                if (onItemClickListener != null) {
                    onItemClickListener.dismissPop();
                }
            } else {
                if (query.isSelected()) {
                    query.setSelected(false);
                    clearParentWhenNoChildSelect();
                    searchResultDataManager.removeFeatureFilterQuery(filterQuery.getFilterKey(), query.getFilterValue());
                } else {
                    if (searchResultDataManager.getFeatureFilterQueryCount(filterQuery.getFilterKey()) >= 5) {
                        FloorInit.getFloorConfig().showToast(context.getString(R.string.sf_field_search_max_select_5));
                        return;
                    }
                    query.setSelected(true);
                    filterQuery.setSelected(true);
                    searchResultDataManager.addFeatureFilterQuery(filterQuery.getFilterKey(), query.getFilterValue());
                }
                searchResultDataManager.searchResultReporter.clickFilterQuery(filterQuery.getFilterLable(), query.getName());
            }
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(position);
            }
        });
    }

    /**
     * 排序是单选，因此点击时需要先清除其他item选中态
     */
    private void clearSortFilterSelectStatus() {
        for (SearchFilterQuery filterQuery : childFilterQueryList) {
            filterQuery.setSelected(false);
        }
    }

    /**
     * 当所有子选项都未选中时，清除父选项选中态
     */
    private void clearParentWhenNoChildSelect() {
        boolean hasChildSelected = false;
        for (SearchFilterQuery query : childFilterQueryList) {
            if (query.isSelected()) {
                hasChildSelected = true;
            }
        }
        if (!hasChildSelected) {
            filterQuery.setSelected(false);
        }
    }

    @Override
    public int getItemCount() {
        return childFilterQueryList == null ? 0 : childFilterQueryList.size();
    }

    public SearchFilterQuery getItem(int position) {
        if (childFilterQueryList == null || position >= childFilterQueryList.size() || position < 0) {
            return null;
        }
        return childFilterQueryList.get(position);
    }

    class FilterHolder extends RecyclerView.ViewHolder {

        TextView tvFilterKey;
        ImageView ivFilterSelected;

        public FilterHolder(@NonNull View itemView) {
            super(itemView);
            tvFilterKey = itemView.findViewById(R.id.tv_filter_key);
            ivFilterSelected = itemView.findViewById(R.id.iv_filter_selected);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    interface OnItemClickListener {
        void onItemClick(int position);

        void dismissPop();
    }

}
