package com.xstore.floorsdk.fieldsearch;

/**
 * 搜索常量
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
public interface SearchConstant {

    interface Key {
        //来源
        String FROM_TYPE = "fromType";
        //关键字
        String SEARCH_KEYWORD = "searchKeyword";
        //关键字点击来源
        String KEYWORD_CLICKFROM = "keywordClickFrom";
        //是否来自购物车
        String SOURCE_FROM_CART = "sourceFromCart";
        //优惠券批次Id
        String BATCH_ID = "batchId";
        //优惠券批次号
        String BATCH_KEY = "batchKey";
        //优惠券Id
        String COUPON_ID = "couponId";
        //促销Id
        String PROMOTION_ID = "promotionId";
        //促销信息
        String PROMOTION_BEAN = "promotionBean";
        //顶部提示语，用于券搜和促销搜
        String TIPS_CONTENT = "tipsContent";
        //领货码Id
        String PICKING_CODE_BRANDID = "pickingCodeBrandId";
        //热搜榜单排序
        String RANK_SORT_INDEX = "rankSortIndex";
        //是否从搜索结果页跳转过来的(结果页二次搜索跳转搜索页用)
        String FROM_SEARCH_RESULT = "fromSearchResult";
        //是否卡片模式
        String SEARCH_CARD_MODE = "search_card_mode";
        // 双端新人、双端老友 ： 购物车 凑单 去主搜 的底部悬浮view
        String SHUANG_DUAN_NEW_OLD_KEY = "SHUANG_DUAN_NEW_OLD_KEY";

    }

    interface Value {
        //主搜
        int FROM_TYPE_SEARCH = 1;
        //券搜
        int FROM_TYPE_COUPON = 2;
        //促销搜
        int FROM_TYPE_PROMOTION = 3;
        //加价购
        int FROM_TYPE_INCREASE_PRICE = 4;
        //领货码
        int FROM_TYPE_PICKING_CODE = 5;
        //运费凑单
        int FROM_TYPE_FREIGHT = 6;
        //直播
        int FROM_TYPE_LIVE = 7;

        //联想词
        String KEYWORD_FROM_RELATE_KEY = "1";
        //热搜词
        String KEYWORD_FROM_HOT_WORD = "2";
        //暗纹词
        String KEYWORD_FROM_HINT_WORD = "3";
        //历史记录
        String KEYWORD_FROM_HISTORY = "4";
        //输入关键字
        String KEYWORD_FROM_INPUT = "5";

        // 双端新人、双端老友 ： 购物车 凑单 去主搜 的底部悬浮view
        String SHUANG_DUAN_NEW_OLD_VALUE = "SHUANG_DUAN_NEW_OLD_VALUE";
    }

}
