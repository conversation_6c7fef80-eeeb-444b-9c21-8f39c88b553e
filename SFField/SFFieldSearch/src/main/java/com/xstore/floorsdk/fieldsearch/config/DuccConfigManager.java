package com.xstore.floorsdk.fieldsearch.config;

import android.content.Context;

import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.xstore.floorsdk.fieldsearch.request.SearchResultNetwork;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

/**
 * Ducc配置
 * 设计原则 不经常变得配置 只有搞活动的时候才会配置 跟登录态无关，接口原则不建议增加很多逻辑 只是简单的接受ducc配置
 * 首页请求一次配置即可 内存级别
 *
 * <AUTHOR>
 * @date 2022/10/02
 */
public class DuccConfigManager {

    private static DuccConfigManager configManager;
    private SearchActivityConfig activitySearchConfig;

    private DuccConfigManager() {
    }

    public static DuccConfigManager getInstance() {
        if (configManager == null) {
            configManager = new DuccConfigManager();
        }
        return configManager;
    }

    /**
     * 请求配置
     *
     * @param context
     */
    public void requestConfig(Context context, String functionId) {
        SearchResultNetwork.requestPost(context, functionId, FreshHttpSetting.NO_EFFECT, null, new BaseFreshResultCallback<String, ResponseData<DuccConfigBean>>() {

            @Override
            public ResponseData<DuccConfigBean> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<DuccConfigBean> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<DuccConfigBean>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<DuccConfigBean> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    activitySearchConfig = null;
                    return;
                }
                String config = responseData.getData().getActivitySearchConfig();
                if (!StringUtil.isNullByString(config)) {
                    activitySearchConfig = JDJSON.parseObject(config, new TypeReference<SearchActivityConfig>() {
                    }.getType());
                }
            }
        });
    }

    public SearchActivityConfig getActivitySearchConfig() {
        return activitySearchConfig;
    }
}
