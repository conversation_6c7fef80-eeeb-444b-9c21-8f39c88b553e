package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

/**
 * 搜索类目平铺实体类
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
public class SearchCategory implements Serializable {

    /**
     * 平铺类目id
     */
    private String tileCategoryId;
    /**
     * 平铺类目图片url
     */
    private String imageUrl;
    /**
     * 平铺类目名称
     */
    private String categoryName;
    /**
     * 平铺类目类型
     */
    private String type;

    /**
     * 过滤条件下商品数量
     */
    private String count;
    /**
     * 本地字段：是否被选中
     */
    private boolean selected;

    public String getTileCategoryId() {
        return tileCategoryId;
    }

    public void setTileCategoryId(String tileCategoryId) {
        this.tileCategoryId = tileCategoryId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
