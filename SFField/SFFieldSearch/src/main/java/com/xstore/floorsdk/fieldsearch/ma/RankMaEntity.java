package com.xstore.floorsdk.fieldsearch.ma;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;

/**
 * 榜单埋点参数
 *
 * <AUTHOR>
 * @date 2022/01/17
 */
public class RankMaEntity extends BaseMaEntity {

    /**
     * 商详页-算法榜单楼层点击
     */
    public final static String DETAILPAGE_RANKINGLISTEXPOSE = "detailPage_rankingList_rankingListExpose";
    /**
     * 商详页-算法榜单楼层点击
     */
    public final static String COMMODITYDETAILPAGE_RANKINGLISTCLICK = "commodityDetailPage_rankingListClick";
    /**
     * 首页推荐流榜单入口
     */
    public final static String RECOMMENDFIRSTPAGE_RANKINGLISTENTRANCE = "recommendFirstPage_recommendSkuFlow_rankingListEntrance";
    /**
     * 搜索主页榜单卡片-查看全部榜单
     */
    public final static String SEARCHPAGE_RANKINGLISTCARD_LOOKALL = "searchPage_hotList_all";
    /**
     * 搜索结果页榜单入口
     */
    public final static String SEARCHRESULTPAGE_RANKINGLISTENTRANCE = "searchResultPage_searchSkuList_rankingListEntrance";
    /**
     * 券搜索结果页榜单入口
     */
    public final static String SEARCHLISTPAGE_COUPON_RANKINGLIST = "searchCouponPage_normalPromotionSkuList_rankingList";
    /**
     * 促销搜索列表页榜单入口
     */
    public final static String SEARCHLISTPAGE_PROMO_RANKINGLIST = "searchListPage_normalPromotionSkuList_rankingList";
    /**
     * 分类列表页榜单入口
     */
    public final static String CATEGORYRESULTPAGE_RANKINGLISTENTRANCE = "categoryResultPage_categorySkuList_rankingListEntrance";
    /**
     * 商详、订单详情、订单列表、常购清单等等推荐流榜单入口
     */
    public final static String FEEDLIST_RANKINGLISTENTRANCE = "feedList_cardEntrance_rankingList";
    /**
     * 商详推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_PRODUCT_DETAIL = "商详推荐流";
    /**
     * 个人中心推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_PERSONAL_CENTER = "个人中心推荐流";
    /**
     * 订单列表推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_ORDER_LIST = "订单列表推荐流";
    /**
     * 订单详情推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_ORDER_DETAIL = "订单详情推荐流";
    /**
     * 支付结果推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_PAY = "支付结果推荐流";
    /**
     * 购物车推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_SHOPCART = "购物车推荐流";
    /**
     * 常购清单推荐流 feedLocation
     */
    public final static String FEEDLOCATION_FROM_FREQUENT_PURCHASE = "常购清单推荐流";

    /**
     * 搜索推荐 feedLocation
     */
//    public final static String FEEDLOCATION_FROM_SEARCH_RECOMMEND = "搜索推荐";
    /**
     * 搜索榜单入口曝光
     */
    public final static String SEARCHLIST_SKULIST_RANGKINGEXPOSE = "Searchlist_SkuList_rankingExpose";

    /**
     * 搜索推荐流里的榜单入口曝光
     */
    public final static String FEEDLIST_CARDENTRANCE_RANKING_EXPOSE = "feedList_cardEntrance_rankingExpose";




    /**
     * skuId
     */
    public String skuId;
    /**
     * sku名称
     */
    public String skuName;
    /**
     * 榜单名称
     */
    public String rankName;
    /**
     * sku在榜单内的排名
     */
    public String rankSortId;
    /**
     * XX推荐流
     */
    public String feedLocation;

    // salePrice：红字价，枚举值
    public int salePriceType;
    // comparePrice：对比价
    public int comparePriceType;
    // discountPrice：预估到手价
    public int discountPriceType;
}
