package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 加价购筛选条适配器(价格带)
 *
 * <AUTHOR>
 * @date 2022/09/24
 */
public class SearchAddBuyFilterAdapter extends RecyclerView.Adapter<SearchAddBuyFilterAdapter.AddbuyFilterHolder> {

    private Context context;
    private RecyclerView rvAddbuyFilter;
    private SearchResultDataManager searchResultDataManager;
    private List<SearchFilterQuery> addBuyFilterList;

    public SearchAddBuyFilterAdapter(Context context, SearchResultDataManager searchResultDataManager,
                                     List<SearchFilterQuery> addBuyFilterList, RecyclerView rvAddbuyFilter) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
        this.rvAddbuyFilter = rvAddbuyFilter;
        this.addBuyFilterList = addBuyFilterList;
    }

    public void setAddBugFilterList(List<SearchFilterQuery> addBuyFilterList) {
        this.addBuyFilterList = addBuyFilterList;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public AddbuyFilterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_price_range_item, parent, false);
        return new AddbuyFilterHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AddbuyFilterHolder holder, int position) {
        SearchFilterQuery filterQuery = addBuyFilterList.get(position);
        if (filterQuery == null) {
            return;
        }
        holder.tvLabel.setText(filterQuery.getFilterLable());
        if (filterQuery.isSelected()) {
            //TODO:是否统一展示新样式
//            if (searchResultDataManager.isFreightSearch()) {
            holder.llContainer.setBackgroundResource(R.drawable.sf_field_search_corner_12_green_btn_bg);
            holder.tvLabel.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_white));
//            } else {
//                holder.llContainer.setBackgroundResource(R.drawable.sf_field_search_corner_4_ebfaf2_bg);
//                holder.tvLabel.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
//            }
        } else {
//            if (searchResultDataManager.isFreightSearch()) {
            holder.llContainer.setBackgroundResource(R.drawable.sf_field_search_corner_12_f7f7f7_bg);
            holder.tvLabel.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_1d1f2b));
//            } else {
//                holder.llContainer.setBackgroundResource(R.drawable.sf_field_search_corner_4_0d000000_bg);
//                holder.tvLabel.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_1d1f2b));
//            }
        }
        holder.itemView.setOnClickListener(v -> {
            if (filterQuery.isSelected()) {
                rvAddbuyFilter.smoothScrollToPosition(position);
                return;
            }
            clearOtherSelectStatus();
            filterQuery.setSelected(true);
            searchResultDataManager.updateAddBuyFilter(filterQuery.getFilterKey(), filterQuery.getFilterValue());
            rvAddbuyFilter.smoothScrollToPosition(position);
            searchResultDataManager.searchResultReporter.clickPriceRange(filterQuery.getFilterLable());
        });
    }

    /**
     * 清除掉其他项选中态
     */
    private void clearOtherSelectStatus() {
        if (addBuyFilterList != null && !addBuyFilterList.isEmpty()) {
            for (SearchFilterQuery filterQuery : addBuyFilterList) {
                if (filterQuery != null) {
                    filterQuery.setSelected(false);
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        return addBuyFilterList == null ? 0 : addBuyFilterList.size();
    }

    class AddbuyFilterHolder extends RecyclerView.ViewHolder {

        LinearLayout llContainer;
        TextView tvLabel;

        public AddbuyFilterHolder(@NonNull View itemView) {
            super(itemView);
            llContainer = itemView.findViewById(R.id.ll_container);
            tvLabel = itemView.findViewById(R.id.tv_label);
        }
    }
}
