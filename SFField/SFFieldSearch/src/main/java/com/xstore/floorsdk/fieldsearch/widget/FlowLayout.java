package com.xstore.floorsdk.fieldsearch.widget;


import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldsearch.R;

import java.util.ArrayList;
import java.util.List;

public class FlowLayout extends ViewGroup {

    public int defaultSpacing = 2;
    // 横纵向间隔
    private int mHorizontalSpacing = defaultSpacing;
    private int mVerticalSpacing = defaultSpacing;

    boolean mNeedLayout = true;
    private boolean supportFold = false;

    private int mUsedWidth = 0;// 当前行已用的宽度，由子View宽度加上横向间隔

    private final List<Line> mLines = new ArrayList<Line>();// 存放着每一行的集合
    private Line mLine = null;

    private int mMaxLinesCount = Integer.MAX_VALUE;
    private int limitLine = mMaxLinesCount;
    private View foldView;//折叠view
    private boolean isFold = true;
    private View ivFoldArrow;
    private TextView tvFoldTitle;
    private BaseAdapter mAdapter;
    private AdapterDataSetObserver mDataSetObserver;

    public FlowLayout(Context context) {
        super(context);
        init(context, null);
    }

    public FlowLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public FlowLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (context != null && attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.sf_field_search_FlowLayout);
            mVerticalSpacing = typedArray.getDimensionPixelOffset(R.styleable.sf_field_search_FlowLayout_verticalSpacing, defaultSpacing);
            mHorizontalSpacing = typedArray.getDimensionPixelOffset(R.styleable.sf_field_search_FlowLayout_horizontalSpacing, defaultSpacing);
            mMaxLinesCount = typedArray.getInt(R.styleable.sf_field_search_FlowLayout_maxLinesCount, Integer.MAX_VALUE);
            supportFold = typedArray.getBoolean(R.styleable.sf_field_search_FlowLayout_supportFold, false);
            typedArray.recycle();
            LayoutInflater inflate = LayoutInflater.from(context);
            foldView = inflate.inflate(R.layout.sf_field_search_item_fold_flow, this, false);
            ivFoldArrow = foldView.findViewById(R.id.iv_fold_arrow);
            tvFoldTitle = foldView.findViewById(R.id.tv_fold_title);
            if (foldView != null) {
                foldView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (isFold) {
                            limitLine = mMaxLinesCount;
                            setMaxLines(Integer.MAX_VALUE);
                            isFold = false;
                            ivFoldArrow.setRotation(180);
                            tvFoldTitle.setText("收起");
                        } else {
                            setMaxLines(limitLine);
                            isFold = true;
                            ivFoldArrow.setRotation(0);
                            tvFoldTitle.setText("展开");
                        }
                    }
                });
            }
            addView(foldView);
        }

    }

    public void setHorizontalSpacing(int spacing) {
        if (mHorizontalSpacing != spacing) {
            mHorizontalSpacing = spacing;
            requestLayout();
        }
    }

    public void setVerticalSpacing(int spacing) {
        if (mVerticalSpacing != spacing) {
            mVerticalSpacing = spacing;
            requestLayout();
        }
    }

    public void setMaxLines(int count) {
        if (mMaxLinesCount != count) {
            mMaxLinesCount = count;
            requestLayout();
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

//        SFLogCollector.d("createView===", "onMeasure");
        int sizeWidth = MeasureSpec.getSize(widthMeasureSpec) - getPaddingRight() - getPaddingLeft();
        int sizeHeight = MeasureSpec.getSize(heightMeasureSpec) - getPaddingTop() - getPaddingBottom();

        int modeWidth = MeasureSpec.getMode(widthMeasureSpec);
        int modeHeight = MeasureSpec.getMode(heightMeasureSpec);

        restoreLine();// 还原数据，以便重新记录
        final int count = getChildCount();
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);

            if (child.getVisibility() == GONE) {
                continue;
            }

            int childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(sizeWidth,
                    modeWidth == MeasureSpec.EXACTLY ? MeasureSpec.AT_MOST : modeWidth);
            int childHeightMeasureSpec = MeasureSpec.makeMeasureSpec(sizeHeight,
                    modeHeight == MeasureSpec.EXACTLY ? MeasureSpec.AT_MOST : modeHeight);
            // 测量child
            child.measure(childWidthMeasureSpec, childHeightMeasureSpec);

            if (child == foldView) {
                continue;
            }

            int childWidth = child.getMeasuredWidth();
            mUsedWidth += childWidth;// 增加使用的宽度
            if (mLine == null) {
                mLine = new Line();
            }

            if (mUsedWidth <= sizeWidth) {// 使用宽度小于总宽度，该child属于这一行。
                mLine.addView(child);// 添加child
                mUsedWidth += mHorizontalSpacing;// 加上间隔
                if (mUsedWidth >= sizeWidth) {// 加上间隔后如果大于等于总宽度，需要换行
                    if (!newLine()) {
                        break;
                    }
                }
            } else {// 使用宽度大于总宽度。需要换行
                if (mLine.getViewCount() == 0) {// 如果这行一个child都没有，即使占用长度超过了总长度，也要加上去，保证每行都有至少有一个child
                    mLine.addView(child);// 添加child
                    if (!newLine()) {// 换行
                        break;
                    }
                } else {// 如果该行有数据了，就直接换行
                    if (!newLine()) {// 换行
                        break;
                    }
                    // 在新的一行，不管是否超过长度，先加上去，因为这一行一个child都没有，所以必须满足每行至少有一个child
                    mLine.addView(child);
                    mUsedWidth += childWidth + mHorizontalSpacing;
                }
            }
        }

        if (mLine != null && mLine.getViewCount() > 0 && !mLines.contains(mLine)) {
            // 由于前面采用判断长度是否超过最大宽度来决定是否换行，则最后一行可能因为还没达到最大宽度，所以需要验证后加入集合中
            mLines.add(mLine);
        }

        int totalWidth = MeasureSpec.getSize(widthMeasureSpec);
        int totalHeight = 0;
        final int linesCount = mLines.size();
        for (int i = 0; i < linesCount; i++) {// 加上所有行的高度
            totalHeight += mLines.get(i).mHeight;
        }
        totalHeight += mVerticalSpacing * (linesCount - 1);// 加上所有间隔的高度
        totalHeight += getPaddingTop() + getPaddingBottom();// 加上padding
        // 设置布局的宽高，宽度直接采用父view传递过来的最大宽度，而不用考虑子view是否填满宽度，因为该布局的特性就是填满一行后，再换行
        // 高度根据设置的模式来决定采用所有子View的高度之和还是采用父view传递过来的高度

        //加上折叠高度
        if (supportFold) {
            if (hasMoreLine) {
                totalHeight += DisplayUtils.dp2px(getContext(), 35);
            } else {
                totalHeight += DisplayUtils.dp2px(getContext(), 15);
            }
        }

        setMeasuredDimension(totalWidth, resolveSize(totalHeight, heightMeasureSpec));
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        if (!mNeedLayout || changed) {
            mNeedLayout = false;
            int left = getPaddingLeft();
            int top = getPaddingTop();
            final int linesCount = mLines.size();
            for (int i = 0; i < linesCount; i++) {
                final Line oneLine = mLines.get(i);
//                SFLogCollector.d("createView===", "top===" + top);
                oneLine.layoutView(left, top, i);
                top += oneLine.mHeight + mVerticalSpacing;
            }
            if (supportFold && hasMoreLine && foldView != null) {
                top -= mVerticalSpacing;
                foldView.layout(0, top, 0 + getMeasuredWidth(), top + DisplayUtils.dp2px(getContext(), 35));
            }
        }
    }

    private void restoreLine() {
        mLines.clear();
        mLine = new Line();
        mUsedWidth = 0;
        int childCount = getChildCount();
        for (int i = 0; i < childCount; i++) {
            View v = getChildAt(i);
            v.layout(0, 0, 0, 0);
        }
    }

    private boolean hasMoreLine = false;

    private boolean newLine() {
        mLines.add(mLine);
        if (mLines.size() < mMaxLinesCount) {
            mLine = new Line();
            mUsedWidth = 0;
            return true;
        } else {
            hasMoreLine = true;
        }
        return false;
    }

    @Override
    protected boolean drawChild(Canvas canvas, View child, long drawingTime) {
        boolean flag = super.drawChild(canvas, child, drawingTime);
        return flag;
    }


    /**
     * 复用baseAdapter
     * @param adapter
     */
    public void setAdapter(BaseAdapter adapter) {
        if (mAdapter != null && mDataSetObserver != null) {
            mAdapter.unregisterDataSetObserver(mDataSetObserver);
        }
        removeAllViews();

        if (adapter == null) {
            return;
        }
        mAdapter = adapter;

        for (int i = 0; i < adapter.getCount(); i++) {
            //不会复用 所以 convertView 是空
            addView(adapter.getView(i, null, this));
        }

        mDataSetObserver = new AdapterDataSetObserver();
        mAdapter.registerDataSetObserver(mDataSetObserver);
    }

    /**
     * AdapterDataSetObserver简介
     * 数据刷新
     * <AUTHOR>
     * @date 2020-4-29 10:12:03
     */
    private class AdapterDataSetObserver extends DataSetObserver {

        @Override
        public void onChanged() {
            super.onChanged();
            removeAllViews();
            if (mAdapter == null) {
                return;
            }
            for (int i = 0; i < mAdapter.getCount(); i++) {
                //不会复用 所以 convertView 是空
                addView(mAdapter.getView(i, null, FlowLayout.this));
            }
        }

        @Override
        public void onInvalidated() {
            super.onInvalidated();
            removeAllViews();
            if (mAdapter == null) {
                return;
            }
            for (int i = 0; i < mAdapter.getCount(); i++) {
                //不会复用 所以 convertView 是空
                addView(mAdapter.getView(i, null, FlowLayout.this));
            }
        }
    }


    // ==========================================================================
    // Inner/Nested Classes
    // ==========================================================================

    /**
     * 代表着一行，封装了一行所占高度，该行子View的集合，以及所有View的宽度总和
     */
    class Line {
        int mWidth = 0;// 该行中所有的子View累加的宽度
        int mHeight = 0;// 该行中所有的子View中高度的那个子View的高度
        List<View> views = new ArrayList<View>();

        public void addView(View view) {// 往该行中添加一个
            views.add(view);
            mWidth += view.getMeasuredWidth();
            int childHeight = view.getMeasuredHeight();
            mHeight = mHeight < childHeight ? childHeight : mHeight;
        }

        public int getViewCount() {
            return views.size();
        }

        public void layoutView(int l, int t, int position) {// 布局
            int left = l;
            int top = t;
            int count = getViewCount();
            int layoutWidth = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();

            int surplusWidth = layoutWidth - mWidth - mHorizontalSpacing * (count - 1);

            if (surplusWidth >= 0) {// 剩余空间

                // 采用float类型数据计算后四舍五入能减少int类型计算带来的误差
                int splitSpacing = (int) (surplusWidth / count + 0.5);
                for (int i = 0; i < count; i++) {
                    final View view = views.get(i);
                    int childWidth = view.getMeasuredWidth();
                    int childHeight = view.getMeasuredHeight();

                    int topOffset = (int) ((mHeight - childHeight) / 2.0 + 0.5);
                    if (topOffset < 0) {
                        topOffset = 0;
                    }
                    // childWidth = childWidth + splitSpacing;
                    view.getLayoutParams().width = childWidth;
                    if (splitSpacing > 0) {
                        int widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidth, MeasureSpec.EXACTLY);
                        int heightMeasureSpec = MeasureSpec.makeMeasureSpec(childHeight, MeasureSpec.EXACTLY);
                        view.measure(widthMeasureSpec, heightMeasureSpec);
                    }
                    view.layout(left, top + topOffset, left + childWidth, top + topOffset + childHeight);
                    left += childWidth + mHorizontalSpacing;
                }
                if (surplusWidth == 0) {
                    post(new Runnable() {
                        @Override
                        public void run() {
                            requestLayout();
                        }
                    });
                }
            } else {
                if (count == 1) {
                    View view = views.get(0);
                    view.layout(left, top, left + view.getMeasuredWidth(), top + view.getMeasuredHeight());
                } else {
                    // 走到这里来，应该是代码出问题了，目前按照逻辑来看，是不可能走到这一步
                }
                //  }
            }
        }
    }


}
