package com.xstore.floorsdk.fieldsearch.adapter;


import static com.xstore.sevenfresh.image.ImageloadUtils.checkIsSafe;
import static com.xstore.sevenfresh.image.ImageloadUtils.reformUrl;

import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sdk.floor.floorcore.adapter.BaseQuickAdapter;
import com.xstore.sdk.floor.floorcore.adapter.BaseViewHolder;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.image.utils.GlideRoundTransform;
import com.xstore.floorsdk.fieldsearch.bean.HotSearchWordBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class HotWordSearchAdapter extends BaseQuickAdapter<HotSearchWordBean, BaseViewHolder> {


    public HotWordSearchAdapter(@Nullable List<HotSearchWordBean> data) {
        super(R.layout.sf_field_search_item_hot_word, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, HotSearchWordBean item) {

        helper.setText(R.id.tv_search_word, StringUtil.isNullByString(item.getShowWord())?item.getHotWord():item.getShowWord());

        if (StringUtil.isEmpty(item.getIcon())) {
            helper.setVisibleGone(R.id.iv_search_label, false);
        } else {
            helper.setVisibleGone(R.id.iv_search_label, true);
            ImageView ivLabel = helper.getView(R.id.iv_search_label);
            ImageloadUtils.loadImage(mContext, ivLabel, item.getIcon());
        }

        if (StringUtil.isEmpty(item.getPicurl())) {
            helper.setVisibleGone(R.id.iv_search_icon, false);
        } else {
            helper.setVisibleGone(R.id.iv_search_icon, true);
            ImageView ivIcon = helper.getView(R.id.iv_search_icon);

            if (checkIsSafe(mContext)) {
                final String url = reformUrl(item.getPicurl());
                Glide.with(mContext)
                        .load(url)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .error(R.drawable.sf_theme_image_placeholder_square)
                        .transform(new CenterCrop(mContext), new GlideRoundTransform(mContext,3.8f))
                        .into(ivIcon);
            }
        }

        TextView tvRank = helper.getView(R.id.tv_rank);
        tvRank.setText(String.valueOf(helper.getAdapterPosition()));
        tvRank.setBackground(mContext.getResources().getDrawable(getDrawable(helper.getAdapterPosition())));
    }

    private int getDrawable(int pos) {
        if (pos == 1) {
            return R.drawable.sf_field_search_rank_first_bg;
        }

        if (pos == 2) {
            return R.drawable.sf_field_search_rank_second_bg;
        }

        if (pos == 3) {
            return R.drawable.sf_field_search_rank_third_bg;
        }
        return R.drawable.sf_field_search_rank_other_bg;
    }
}
