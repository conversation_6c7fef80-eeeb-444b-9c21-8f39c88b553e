package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;

public class SearchCardClickEvent extends BaseMaEntity {

    private String skuId;
    private String skuName;
    private String url;

    public SearchCardClickEvent(String skuId, String skuName, String url) {
        this.skuId = skuId;
        this.skuName = skuName;
        this.url = url;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
