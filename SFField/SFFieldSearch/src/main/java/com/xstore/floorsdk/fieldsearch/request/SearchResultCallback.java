package com.xstore.floorsdk.fieldsearch.request;

import com.xstore.floorsdk.fieldsearch.bean.CouponInfo;
import com.xstore.floorsdk.fieldsearch.bean.SearchRecommendResult;
import com.xstore.floorsdk.fieldsearch.bean.SearchResultResponse;
import com.xstore.sevenfresh.modules.productdetail.bean.PromotionTypeInfo;

/**
 * 搜索结果数据回调
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public interface SearchResultCallback {

    void setSearchResult(SearchResultResponse searchResult, boolean isRefresh, int source);

    void showNoData();

    void finishLoadMore();

    void setRecommendResult(SearchRecommendResult searchBottomWareInfo);

    void setPromotionResult(PromotionTypeInfo promotionTypeInfo);

    void setCouponInfoResult(CouponInfo couponInfo);

    void setUnStockRecommend();

    void secondSearchToClearCache(String searchKeyword);
}
