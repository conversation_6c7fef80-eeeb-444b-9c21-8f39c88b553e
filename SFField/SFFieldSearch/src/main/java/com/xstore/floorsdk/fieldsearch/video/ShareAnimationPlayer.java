package com.xstore.floorsdk.fieldsearch.video;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import com.kk.taurus.playerbase.DataInter;
import com.kk.taurus.playerbase.assist.AssistPlay;
import com.kk.taurus.playerbase.assist.OnAssistPlayEventHandler;
import com.kk.taurus.playerbase.assist.RelationAssist;
import com.kk.taurus.playerbase.entity.DataSource;
import com.kk.taurus.playerbase.event.OnErrorEventListener;
import com.kk.taurus.playerbase.event.OnPlayerEventListener;
import com.kk.taurus.playerbase.player.IPlayer;
import com.kk.taurus.playerbase.provider.IDataProvider;
import com.kk.taurus.playerbase.receiver.IReceiverGroup;
import com.kk.taurus.playerbase.receiver.OnReceiverEventListener;
import com.kk.taurus.playerbase.render.AspectRatio;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页视频播放类，保护级别
 */
public class ShareAnimationPlayer {
    private static final String TAG = "ShareAnimationPlayer";

    private RelationAssist mRelationAssist;

    private static ShareAnimationPlayer clubPlayer;

    private static ShareAnimationPlayer homePlayer;

    private static ShareAnimationPlayer categoryPlayer;

    private Context mAppContext;

    private DataSource mDataSource;

    /**
     * 播放类型
     */
    public enum PlayType {
        //首页列表
        HOME_LIST,
        //7club列表
        CLUB_LIST,
        //视频播放详情
        VIDEO_DETAIL,
        //分类列表
        CATEGORY_LIST,
        //分类列表
        SEARCH_LIST,
    }


    private PlayType playType;

    public ShareAnimationPlayer(Context context, PlayType playtype) {
        mAppContext = context.getApplicationContext();
        mRelationAssist = new RelationAssist(mAppContext, null, false);
        mRelationAssist.setAspectRatio(AspectRatio.AspectRatio_FIT_PARENT);
        mRelationAssist.setLooping(true);
        mRelationAssist.setEventAssistHandler(mInternalEventAssistHandler);
        mOnPlayerEventListeners = new ArrayList<>();
        mOnErrorEventListeners = new ArrayList<>();
        mOnReceiverEventListeners = new ArrayList<>();

        playType = playtype;
        setAspectRatio(AspectRatio.AspectRatio_FILL_PARENT);
    }

//    public static ShareAnimationPlayer get(Context context, PlayType type) {
//        if(type == PlayType.HOME_LIST) {
//            if (null == homePlayer) {
//                synchronized (ShareAnimationPlayer.class) {
//                    if (null == homePlayer) {
//                        homePlayer = new ShareAnimationPlayer(context);
//                        homePlayer.setAspectRatio(AspectRatio.AspectRatio_FILL_PARENT);
//                    }
//                }
//            }
//            return homePlayer;
//        } else if(type == PlayType.CATEGORY_LIST) {
//            if (null == categoryPlayer) {
//                synchronized (ShareAnimationPlayer.class) {
//                    if (null == categoryPlayer) {
//                        categoryPlayer = new ShareAnimationPlayer(context);
//                        categoryPlayer.setAspectRatio(AspectRatio.AspectRatio_FILL_PARENT);
//                    }
//                }
//            }
//            return categoryPlayer;
//        } else {
//            if (null == clubPlayer) {
//                synchronized (ShareAnimationPlayer.class) {
//                    if (null == clubPlayer) {
//                        clubPlayer = new ShareAnimationPlayer(context);
//                    }
//                }
//            }
//            return clubPlayer;
//        }
//    }

    private List<OnPlayerEventListener> mOnPlayerEventListeners;
    private List<OnErrorEventListener> mOnErrorEventListeners;
    private List<OnReceiverEventListener> mOnReceiverEventListeners;

    private OnErrorEventListener singleErrorEventListeners;
    private OnPlayerEventListener singlePlayerEventListener;

    public void addOnPlayerEventListener(OnPlayerEventListener onPlayerEventListener) {
        if (mOnPlayerEventListeners.contains(onPlayerEventListener)) {
            return;
        }
        mOnPlayerEventListeners.add(onPlayerEventListener);
    }

    public boolean removePlayerEventListener(OnPlayerEventListener onPlayerEventListener) {
        return mOnPlayerEventListeners.remove(onPlayerEventListener);
    }

    public void addOnErrorEventListener(OnErrorEventListener onErrorEventListener) {
        if (mOnErrorEventListeners.contains(onErrorEventListener)) {
            return;
        }
        mOnErrorEventListeners.add(onErrorEventListener);
    }

    public boolean removeErrorEventListener(OnErrorEventListener onErrorEventListener) {
        return mOnErrorEventListeners.remove(onErrorEventListener);
    }

    public void addOnReceiverEventListener(OnReceiverEventListener onReceiverEventListener) {
        if (mOnReceiverEventListeners.contains(onReceiverEventListener)) {
            return;
        }
        mOnReceiverEventListeners.add(onReceiverEventListener);
    }

    public boolean removeReceiverEventListener(OnReceiverEventListener onReceiverEventListener) {
        return mOnReceiverEventListeners.remove(onReceiverEventListener);
    }

    private OnPlayerEventListener mInternalPlayerEventListener =
            new OnPlayerEventListener() {
                @Override
                public void onPlayerEvent(int eventCode, Bundle bundle) {
                    callBackPlayerEventListeners(eventCode, bundle);
                }
            };

    private void callBackPlayerEventListeners(int eventCode, Bundle bundle) {
        for (OnPlayerEventListener listener : mOnPlayerEventListeners) {
            listener.onPlayerEvent(eventCode, bundle);
        }
        if(singlePlayerEventListener != null) {
            singlePlayerEventListener.onPlayerEvent(eventCode, bundle);
        }
    }

    private OnErrorEventListener mInternalErrorEventListener =
            new OnErrorEventListener() {
                @Override
                public void onErrorEvent(int eventCode, Bundle bundle) {
                    callBackErrorEventListeners(eventCode, bundle);
                }
            };

    private void callBackErrorEventListeners(int eventCode, Bundle bundle) {
        for (OnErrorEventListener listener : mOnErrorEventListeners) {
            listener.onErrorEvent(eventCode, bundle);
        }
        if (singleErrorEventListeners != null) {
            singleErrorEventListeners.onErrorEvent(eventCode, bundle);
        }
    }

    private OnReceiverEventListener mInternalReceiverEventListener =
            new OnReceiverEventListener() {
                @Override
                public void onReceiverEvent(int eventCode, Bundle bundle) {
                    callBackReceiverEventListeners(eventCode, bundle);
                }
            };

    private OnAssistPlayEventHandler mInternalEventAssistHandler =
            new OnAssistPlayEventHandler() {
                @Override
                public void onAssistHandle(AssistPlay assistPlay, int eventCode, Bundle bundle) {
                    super.onAssistHandle(assistPlay, eventCode, bundle);
                    switch (eventCode) {
                        case DataInter.Event.EVENT_CODE_ERROR_SHOW:
                            reset();
                            break;
                        default:
                            break;
                    }
                }
            };

    private void callBackReceiverEventListeners(int eventCode, Bundle bundle) {
        for (OnReceiverEventListener listener : mOnReceiverEventListeners) {
            listener.onReceiverEvent(eventCode, bundle);
        }
    }

    private void attachListener() {
        mRelationAssist.setOnPlayerEventListener(mInternalPlayerEventListener);
        mRelationAssist.setOnErrorEventListener(mInternalErrorEventListener);
        mRelationAssist.setOnReceiverEventListener(mInternalReceiverEventListener);
    }

    public void setReceiverGroup(IReceiverGroup receiverGroup) {
        mRelationAssist.setReceiverGroup(receiverGroup);
    }

    public IReceiverGroup getReceiverGroup() {
        return mRelationAssist.getReceiverGroup();
    }

    public DataSource getDataSource() {
        return mDataSource;
    }


    /**
     * 用于记录上一个播放的视频容器，做视频恢复逻辑
     */
    private WeakReference<ViewGroup> oldUserContainerRef = null;


    public void setNeedRecovery(Boolean needRecovery, ViewGroup oldUserContainer) {
        SFLogCollector.d(TAG, "setNeedRecovery needRecovery:" + needRecovery + " oldUserContainer:" + oldUserContainer);
        if (needRecovery != null) {
            this.needRecovery = needRecovery;
            oldUserContainerRef = new WeakReference<>(oldUserContainer);
        }
    }

    /**
     * @param userContainer
     * @param dataSource
     */
    public void play(ViewGroup userContainer, DataSource dataSource, PlayType playType) {
        SFLogCollector.d(TAG, "play:" + playType + " UserContainer:" + userContainer + " " +dataSource );

        if (dataSource != null) {
            this.mDataSource = dataSource;
        }

        this.playType = playType;

        attachListener();
        IReceiverGroup receiverGroup = getReceiverGroup();
        if (receiverGroup != null && dataSource != null) {
            receiverGroup.getGroupValue().putBoolean(DataInter.Key.KEY_COMPLETE_SHOW, false);
        }
        mRelationAssist.attachContainer(userContainer);
        if (dataSource != null) {
            mRelationAssist.setDataSource(dataSource);
        }
        if (receiverGroup != null
                && receiverGroup.getGroupValue().getBoolean(DataInter.Key.KEY_ERROR_SHOW)) {
            return;
        }
        //再设置一次
        mRelationAssist.setLooping(true);

        if (dataSource != null) {
            mRelationAssist.play(true);
        } else if (mDataSource != null) {
            if (getState() == IPlayer.STATE_PAUSED) {
                resume();
            } else if (!isInPlaybackState()) {
                mRelationAssist.play(true);
            }
        }

        if(playType != PlayType.VIDEO_DETAIL) {
            mRelationAssist.setVolume(0, 0);
        }
        //mRelationAssist.attachContainer(userContainer);
    }

    public void makeSureAttach(ViewGroup userContainer) {
        SFLogCollector.d(TAG, "makesure attach UserContainer:" + userContainer);
        mRelationAssist.attachContainer(userContainer);
    }

    public void setDataProvider(IDataProvider dataProvider) {
        mRelationAssist.setDataProvider(dataProvider);
    }

    public boolean isInPlaybackState() {
        int state = getState();
        SFLogCollector.d(TAG, "isInPlaybackState : state = " + state);
        return state != IPlayer.STATE_END
                && state != IPlayer.STATE_ERROR
                && state != IPlayer.STATE_IDLE
                && state != IPlayer.STATE_INITIALIZED
                && state != IPlayer.STATE_STOPPED;
    }

    public boolean isPlaying() {
        return mRelationAssist.isPlaying();
    }

    public int getState() {
        return mRelationAssist.getState();
    }

    public void resume() {
        SFLogCollector.d(TAG, "resume");
        mRelationAssist.resume();
    }

    public void pause() {
        SFLogCollector.d(TAG, "pause");
        mRelationAssist.pause();
    }

    public void stop() {
        SFLogCollector.d(TAG, "stop");
        mRelationAssist.stop();
    }

    public void reset() {
        SFLogCollector.d(TAG, "reset");
        mRelationAssist.reset();
    }

    public void destroy() {
        SFLogCollector.d(TAG, "destroy");
        mOnPlayerEventListeners.clear();
        mOnErrorEventListeners.clear();
        mOnReceiverEventListeners.clear();
        mRelationAssist.destroy();
        clubPlayer = null;
        homePlayer = null;
        categoryPlayer = null;
    }

    public void clearSingleErrorListener() {
        singleErrorEventListeners = null;
    }

    public void setSingleErrorEventListeners(OnErrorEventListener s) {
        this.singleErrorEventListeners = s;
    }

    public void setSinglePlayerEventListener(OnPlayerEventListener singlePlayerEventListener) {
        this.singlePlayerEventListener = singlePlayerEventListener;
    }

    public void clearSinglePlayerEventListener() {
        singlePlayerEventListener = null;
    }


    /**
     * 当前页面已经onResume
     *
     * @param hidden 如果是在fragment中使用的话，需要传入当前fragment的展示状态
     */
    public void onResume(boolean hidden) {
        SFLogCollector.d(TAG, "onResume isHidden:" + hidden + " state:" + getState());
        if (hidden) {
            //当前fragment还没有展示 不用恢复
            return;
        }
        if(getState() == IPlayer.STATE_PAUSED) {
            resume();
        } else if (playType == PlayType.HOME_LIST || playType == PlayType.CATEGORY_LIST || playType == PlayType.SEARCH_LIST) {
            mRelationAssist.play();
        }
    }

    /**
     * 页面onPause的时候
     */
    public void onPause() {
        SFLogCollector.d(TAG, "onPause playType" + playType + " needRecovery:" + needRecovery);
        if(playType == PlayType.VIDEO_DETAIL) {
            pause();
            return;
        }
        if (needRecovery != null && needRecovery) {
            //尝试恢复播放视频
            mRelationAssist.setAspectRatio(AspectRatio.AspectRatio_FIT_PARENT);
            return;
        }
        pause();
    }

    /**
     * 判断是否在hidden的时候停止了播放
     */
    private boolean hiddenStop = false;
    /**
     * @param hidden fragment的展示状态变化的时候
     */
    public void onHiddenChanged(boolean hidden) {
        SFLogCollector.d(TAG, "onHiddenChanged playType" + playType + " hidden:" + hidden);
        if (hidden) {
            if(playType == PlayType.HOME_LIST || playType == PlayType.CATEGORY_LIST|| playType == PlayType.SEARCH_LIST) {
                //首页直接停止播放
                if(isPlaying()) {
                    hiddenStop = true;
                    stop();
                    return;
                }
            } else {
                pause();
                hiddenStop = false;
            }
        } else {
            //首页要求重新播放
            if (hiddenStop) {
                hiddenStop = false;
                mRelationAssist.play();
            } else {
                if(getState() == IPlayer.STATE_PAUSED) {
                    resume();
                } else {
                    mRelationAssist.play();
                }
            }
        }
    }


    /**
     *
     */
    private Boolean needRecovery = null;

    /**
     * 当页面destory时调用, 目前暂时不用销毁此单例
     */
    public void onDestroy(Context context) {
        SFLogCollector.d(TAG, "destroy");
        mOnPlayerEventListeners.clear();
        mOnErrorEventListeners.clear();
        mOnReceiverEventListeners.clear();
        mRelationAssist.stop();
        mRelationAssist.destroy();
        categoryPlayer = null;
//        SFLogCollector.d(TAG, "onDestroy playType" + playType + " needRecovery:" + needRecovery);
//
//        if (needRecovery != null && needRecovery) {
//            if (oldUserContainerRef == null) {
//                return;
//            }
//            final ViewGroup oldContainer = oldUserContainerRef.get();
//            if (oldContainer != null) {
//                //尝试恢复播放视频
//                mRelationAssist.setAspectRatio(AspectRatio.AspectRatio_FIT_PARENT);
////                mRelationAssist.getRender().updateVideoWidthWithAnim(SevenClubMediaHolder.width, SevenClubMediaHolder.height, AspectRatio.AspectRatio_FILL_PARENT);
//                this.setNeedRecovery(false, oldContainer);
//                DataSource dataSource = null;
//                if(!isPlaying()) {
//                    dataSource = getDataSource();
//                }
//                play(oldContainer, dataSource, PlayType.CLUB_LIST);
//               new Handler().postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        //兼容系统共享元素动画，确保可以正确完成
//                        oldContainer.setAlpha(1);
//                    }
//                }, 1000);
//            }
//        }
    }

    public void seekTo(int i) {
        mRelationAssist.seekTo(i);
    }

    public void setVolume(int i) {
        mRelationAssist.setVolume(i, i);
    }

    public void start() {

    }

    public int getDuration() {
        return mRelationAssist.getDuration();
    }

    public int getCurrentPosition() {
        return mRelationAssist.getCurrentPosition();
    }

    public void setLooping(boolean isLoopPlay) {
        mRelationAssist.setLooping(isLoopPlay);
    }

    public int getBufferPercentage() {
        return mRelationAssist.getBufferPercentage();
    }

    private void setAspectRatio(AspectRatio aspectRatio_fill_parent) {
        mRelationAssist.setAspectRatio(aspectRatio_fill_parent);
    }

    public void doAnim() {
//        ValueAnimator xValue = ValueAnimator.ofInt(1, 20);
//        xValue.setDuration(3000L);
//        xValue.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
//            @Override
//            public void onAnimationUpdate(ValueAnimator animation) {
//                mRelationAssist.getRender().setVideoSampleAspectRatio(1, (int)animation.getAnimatedValue());
//
//            }
//        });
//        xValue.setInterpolator(new LinearInterpolator());
//        xValue.start();
        //普通模式播放这里会导致空指针
//        mRelationAssist.getRender().updateVideoWidthWithAnim(AppSpec.getInstance().width, 1620, AspectRatio.AspectRatio_FIT_PARENT);
    }

    public View getRenderView() {
        return mRelationAssist.getRender().getRenderView();
    }


}
