package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

/**
 * 优惠券数据模型
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class CouponInfo implements Serializable {
    private String couponId;
    private String couponName;
    private int couponType;
    private String batchId;
    private String subCouponTypeDesc;
    private String tag;
    private int tagType;
    private int couponMode;
    private int channelType;
    private String channelTypeName;
    private String amountDesc;
    private String discount;
    private String validateTime;
    private String needMoney;
    private int couponStatus;
    private String ruleDescSimple;
    private String ruleDescDetail;

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public int getCouponType() {
        return couponType;
    }

    public void setCouponType(int couponType) {
        this.couponType = couponType;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getSubCouponTypeDesc() {
        return subCouponTypeDesc;
    }

    public void setSubCouponTypeDesc(String subCouponTypeDesc) {
        this.subCouponTypeDesc = subCouponTypeDesc;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getTagType() {
        return tagType;
    }

    public void setTagType(int tagType) {
        this.tagType = tagType;
    }

    public int getCouponMode() {
        return couponMode;
    }

    public void setCouponMode(int couponMode) {
        this.couponMode = couponMode;
    }

    public int getChannelType() {
        return channelType;
    }

    public void setChannelType(int channelType) {
        this.channelType = channelType;
    }

    public String getChannelTypeName() {
        return channelTypeName;
    }

    public void setChannelTypeName(String channelTypeName) {
        this.channelTypeName = channelTypeName;
    }

    public String getAmountDesc() {
        return amountDesc;
    }

    public void setAmountDesc(String amountDesc) {
        this.amountDesc = amountDesc;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getValidateTime() {
        return validateTime;
    }

    public void setValidateTime(String validateTime) {
        this.validateTime = validateTime;
    }

    public String getNeedMoney() {
        return needMoney;
    }

    public void setNeedMoney(String needMoney) {
        this.needMoney = needMoney;
    }

    public int getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(int couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getRuleDescSimple() {
        return ruleDescSimple;
    }

    public void setRuleDescSimple(String ruleDescSimple) {
        this.ruleDescSimple = ruleDescSimple;
    }

    public String getRuleDescDetail() {
        return ruleDescDetail;
    }

    public void setRuleDescDetail(String ruleDescDetail) {
        this.ruleDescDetail = ruleDescDetail;
    }
}
