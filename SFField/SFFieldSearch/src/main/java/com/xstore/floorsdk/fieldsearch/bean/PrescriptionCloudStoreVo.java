package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 时效云卖场商品和标题信息
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class PrescriptionCloudStoreVo implements Serializable {
    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 商品集合
     */
    private List<SkuInfoBean> productCardVoList;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }
}
