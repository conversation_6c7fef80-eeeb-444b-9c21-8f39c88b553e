package com.xstore.floorsdk.fieldsearch.interfaces;

import android.widget.TextView;

import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.productcard.widget.SfCardPriceView;

/**
 * 搜索结果页容器接口
 *
 * <AUTHOR>
 * @date 2022/10/03
 */
public interface SearchResultContainerInterface {

    /**
     * 当前加载第几页，目前用于反馈按钮的显隐
     *
     * @param page
     */
    void onRequestPage(int page);

    /**
     * 是否展示回顶部按钮
     *
     * @param show
     */
    void showGotoTop(boolean show);

    /**
     * 侧边栏展开收起切换
     *
     * @param open
     */
    void onDrawerLayoutChange(boolean open);

    /**
     * 设置状态栏
     *
     * @param darkStatusBar
     */
    void setDarkStatusBar(boolean darkStatusBar);

    /**
     * 促销搜计算合计
     *
     * @param promotionId
     * @param promotionBean
     * @param tvTotalPrice
     * @param tvTips
     * @param isAddBuy
     */
    void promotionUpdateTotalPrice(String promotionId, ProductDetailBean.WareInfoBean.PromotionTypesBean promotionBean, SfCardPriceView tvTotalPrice, TextView tvTips, boolean isAddBuy);

    /**
     * @return 获取埋点接口
     */
    JDMaUtils.JdMaPageImp getJdMaPageImp();

    /**
     * 试金石埋点
     *
     * @return
     */
    String getSearchShowTypeBuriedExpLabel();

    /**
     * 试金石埋点
     *
     * @return
     */
    String getSearchUnStockBuriedExpLabel();

    /**
     * 主搜接口
     *
     * @return
     */
    String getMainSearchFunId();

    /**
     * 查询促销信息
     *
     * @return
     */
    String getPromotionDescFunId();

    /**
     * 查询优惠券信息
     *
     * @return
     */
    String getCouponInfoDescFunId();

    /**
     * 底部推荐
     *
     * @return
     */
    String getBottomWareInfoFunId();

    /**
     * 商品无货推荐
     *
     * @return
     */
    String getSkuRecommendInfogFunId();

    void openFeedback(boolean selectFirstOpenKeyboard);

    boolean isShowSearchDaPeiGou();

    /**
     * 半隐藏AI小七
     * @param hide true进入半隐藏态，false退出半隐藏态
     */
    void slideAi7HalfHide(boolean hide);

    void showRepurchase(ProductDetailBean.WareInfoBean.PromotionTypesBean bean);
}