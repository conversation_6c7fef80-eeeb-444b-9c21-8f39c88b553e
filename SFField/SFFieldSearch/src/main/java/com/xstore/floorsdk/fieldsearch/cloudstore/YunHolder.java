package com.xstore.floorsdk.fieldsearch.cloudstore;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.adapter.SearchProductAdapter;
import com.xstore.floorsdk.fieldsearch.bean.PrescriptionCloudStoreVo;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.floorsdk.fieldsearch.widget.RecycleSpacesItemDecoration;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 云卖场入口holder
 *
 * <AUTHOR>
 * @date 2022/10/03
 */
public class YunHolder extends RecyclerView.ViewHolder {

    RoundCornerImageView1 ivYunBg;
    ImageView ivYunTitle;
    TextView tvYunDesc;
    ImageView ivYunArrow;
    RecyclerView rvYunProductVertical;
    RecyclerView rvYunProductHorizontal;

    RecycleSpacesItemDecoration verticalItemDecoration;
    RecycleSpacesItemDecoration horizontalItemDecoration;
    YunProductHorizontalAdapter productHorizontalAdapter;

    public YunHolder(AppCompatActivity activity, @NonNull View itemView) {
        super(itemView);
        ivYunBg = itemView.findViewById(R.id.iv_yun_bg);
        ivYunTitle = itemView.findViewById(R.id.iv_yun_title);
        tvYunDesc = itemView.findViewById(R.id.tv_yun_desc);
        ivYunArrow = itemView.findViewById(R.id.iv_yun_arrow);
        rvYunProductVertical = itemView.findViewById(R.id.rv_yun_product_vertical);
        rvYunProductHorizontal = itemView.findViewById(R.id.rv_yun_product_horizontal);
        int corner = ScreenUtils.dip2px(activity, 12);
        ivYunBg.setRadius(corner, corner, corner, corner);
    }

    /**
     * 绑定数据
     *
     * @param activity
     * @param cloudStoreVo
     * @param onItemClickListener
     * @param searchResultReporter
     */
    public void bindData(AppCompatActivity activity, PrescriptionCloudStoreVo cloudStoreVo,
                         SearchProductAdapter.OnItemClickListener onItemClickListener, SearchResultReporter searchResultReporter) {
        if (cloudStoreVo == null) {
            return;
        }
        if (StringUtil.isNotEmpty(cloudStoreVo.getSubTitle())) {
            tvYunDesc.setText(cloudStoreVo.getSubTitle());
        }
        if (cloudStoreVo.getProductCardVoList().size() > 2) {
            ivYunArrow.setVisibility(View.VISIBLE);
        } else {
            ivYunArrow.setVisibility(View.GONE);
        }
        if (cloudStoreVo.getProductCardVoList().size() <= 2) {
            rvYunProductVertical.setVisibility(View.VISIBLE);
            rvYunProductHorizontal.setVisibility(View.GONE);
            rvYunProductVertical.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false));
            YunProductVerticalAdapter productVerticalAdapter = new YunProductVerticalAdapter(activity, searchResultReporter, cloudStoreVo.getProductCardVoList());
            rvYunProductVertical.setAdapter(productVerticalAdapter);
            if (verticalItemDecoration == null) {
                verticalItemDecoration = new RecycleSpacesItemDecoration(0, ScreenUtils.dip2px(activity, 5));
            }
            rvYunProductVertical.removeItemDecoration(verticalItemDecoration);
            rvYunProductVertical.addItemDecoration(verticalItemDecoration);
        } else {
            List<SkuInfoBean> productInfos = new ArrayList<>();
            productInfos.addAll(cloudStoreVo.getProductCardVoList().size() > 10 ? cloudStoreVo.getProductCardVoList().subList(0, 10) : cloudStoreVo.getProductCardVoList());
            if (productInfos.size() > 3) {
                SkuInfoBean productInfoBean = new SkuInfoBean();
                productInfoBean.setViewType(YunProductHorizontalAdapter.VIEW_TYPE_FOOTER);
                productInfos.add(productInfoBean);
            }
            rvYunProductVertical.setVisibility(View.GONE);
            rvYunProductHorizontal.setVisibility(View.VISIBLE);
            rvYunProductHorizontal.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false));
            if (productHorizontalAdapter == null) {
                productHorizontalAdapter = new YunProductHorizontalAdapter(activity, productInfos, onItemClickListener, searchResultReporter);
                rvYunProductHorizontal.setAdapter(productHorizontalAdapter);
            } else {
                productHorizontalAdapter.setProductInfos(productInfos);
            }
            if (horizontalItemDecoration == null) {
                horizontalItemDecoration = new RecycleSpacesItemDecoration(ScreenUtils.dip2px(activity, 5));
            }
            rvYunProductHorizontal.removeItemDecoration(horizontalItemDecoration);
            rvYunProductHorizontal.addItemDecoration(horizontalItemDecoration);
        }
    }
}
