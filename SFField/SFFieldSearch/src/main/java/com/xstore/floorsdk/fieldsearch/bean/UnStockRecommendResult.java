package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 无货推荐商品模型
 *
 * <AUTHOR>
 * @date 2022/10/02
 */
public class UnStockRecommendResult implements Serializable {

    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 相似推荐 页面  用于区分是否从首页拉取数据
     */
    private String recommendDataSource;
    /**
     * 推荐品列表
     */
    private List<SkuInfoBean> productCardVoList;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getRecommendDataSource() {
        return recommendDataSource;
    }

    public void setRecommendDataSource(String recommendDataSource) {
        this.recommendDataSource = recommendDataSource;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }
}
