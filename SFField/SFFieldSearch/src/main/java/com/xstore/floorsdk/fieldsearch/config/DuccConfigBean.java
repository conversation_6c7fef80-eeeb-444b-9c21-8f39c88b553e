package com.xstore.floorsdk.fieldsearch.config;

import java.io.Serializable;

/**
 * 大促活动配置信息
 *
 * <AUTHOR>
 * @date 2022/10/02
 */
public class DuccConfigBean implements Serializable {
    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * json字符串，使用需要转换成object
     */
    private String activitySearchConfig;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getActivitySearchConfig() {
        return activitySearchConfig;
    }

    public void setActivitySearchConfig(String activitySearchConfig) {
        this.activitySearchConfig = activitySearchConfig;
    }
}
