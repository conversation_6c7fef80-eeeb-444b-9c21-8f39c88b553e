package com.xstore.floorsdk.fieldsearch.adapter;

import android.app.Activity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sevenfresh.floor.modules.floor.recommend.maylike.MayLikeGoodListAdapter;

/**
 * 相似推荐卡片viewHolder
 *
 * <AUTHOR>
 * @date 2022/09/26
 */
public class SimilarRecommendCardViewHolder extends RecyclerView.ViewHolder {

    private Activity activity;
    private TextView tvTitle;
    private LinearLayout llAll;
    private RecyclerView recyclerView;

    public SimilarRecommendCardViewHolder(Activity activity, @NonNull View itemView) {
        super(itemView);
        tvTitle = itemView.findViewById(R.id.tv_title);
        llAll = itemView.findViewById(R.id.ll_all);
        recyclerView = itemView.findViewById(R.id.rv_recyclerview);

        recyclerView.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false));
        MayLikeGoodListAdapter recommendCardAdapter = new MayLikeGoodListAdapter(activity, null, null, null, false);
        recyclerView.setAdapter(recommendCardAdapter);
    }
}
