package com.xstore.floorsdk.fieldsearch.cloudstore;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.adapter.SearchProductAdapter;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.holder.ProductCardFixedHeightViewHolder;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.List;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 云卖场入口横向商品适配器
 *
 * <AUTHOR>
 * @date 2022/09/25
 */
public class YunProductHorizontalAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    public final static int VIEW_TYPE_PRODUCT = 0;
    public final static int VIEW_TYPE_FOOTER = 1;

    private AppCompatActivity activity;
    private List<SkuInfoBean> productInfos;
    private SearchProductAdapter.OnItemClickListener onItemClickListener;
    private SearchResultReporter searchResultReporter;

    public YunProductHorizontalAdapter(AppCompatActivity activity, List<SkuInfoBean> productInfos, SearchProductAdapter.OnItemClickListener onItemClickListener, SearchResultReporter searchResultReporter) {
        this.activity = activity;
        this.productInfos = productInfos;
        this.onItemClickListener = onItemClickListener;
        this.searchResultReporter = searchResultReporter;
    }

    public void setProductInfos(List<SkuInfoBean> productInfos) {
        this.productInfos = productInfos;
        notifyDataSetChanged();
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case VIEW_TYPE_PRODUCT:
                View productCardView = LayoutInflater.from(activity).inflate(R.layout.sf_card_product_card_fixed_item, parent, false);
                return new ProductCardFixedHeightViewHolder(productCardView);
            case VIEW_TYPE_FOOTER:
                View footerView = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_yun_look_more_item, parent, false);
                footerView.setOnClickListener(v -> {
                    if (onItemClickListener != null) {
                        onItemClickListener.onYunClick();
                    }
                });
                return new SearchProductAdapter.SimpleViewHolder(footerView);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int position) {
        SkuInfoBean productInfo = productInfos.get(position);
        if (productInfo == null) {
            return;
        }
        if (viewHolder instanceof ProductCardFixedHeightViewHolder) {
            ProductCardFixedHeightViewHolder cardViewHolder = (ProductCardFixedHeightViewHolder) viewHolder;
            cardViewHolder.showPriceUnit(false);
            cardViewHolder.setCardSize(activity, false, 100, 190);
            cardViewHolder.bindData(activity, productInfo, new ProductCardInterfaces() {
                @Override
                public int setCardAbilityType() {
                    return 0B1011;
                }

                @Override
                public void onCardClick(SkuInfoBean productInfoBean) {
                    if (NoDoubleClickUtils.isDoubleClick()) {
                        return;
                    }
                    if (productInfoBean != null) {
                        FloorJumpManager.getInstance().jumpProductDetail(activity, productInfoBean, true);
                    }
                    searchResultReporter.cloudSkuClick(productInfoBean, position);
                }

                @Override
                public void onAddCartClick(SkuInfoBean productInfoBean) {
                    searchResultReporter.cloudSkuAddCart(productInfoBean, position);
                }

                @Override
                public void bookNowClick(SkuInfoBean skuInfoVoBean) {
                    FloorJumpManager.getInstance().preSaleJustNow(activity, skuInfoVoBean);
                    searchResultReporter.bookNow(skuInfoVoBean, 1);
                }

                @Override
                public void findSimilarClick(SkuInfoBean skuInfoBean) {
                    if (skuInfoBean != null) {
                        FloorJumpManager.getInstance().jumpSimilarList(activity, skuInfoBean.getSkuId(), "2");
                        searchResultReporter.clickFindSimilar(skuInfoBean.getSkuId(), skuInfoBean.getSkuName(), 2);
                    }
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return productInfos == null ? 0 : productInfos.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (position < 0 || productInfos.size() <= position || productInfos.get(position) == null) {
            return -1;
        }
        return productInfos.get(position).getViewType();
    }
}
