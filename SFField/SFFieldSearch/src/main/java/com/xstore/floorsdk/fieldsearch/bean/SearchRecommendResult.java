package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索推荐数据模型
 *
 * <AUTHOR>
 * @date 2022/10/02
 */
public class SearchRecommendResult implements Serializable {
    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    private int page;
    private int nextPage;
    private List<SkuInfoBean> productCardVoList;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getNextPage() {
        return nextPage;
    }

    public void setNextPage(int nextPage) {
        this.nextPage = nextPage;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }
}
