package com.xstore.floorsdk.fieldsearch.ma;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;

/**
 * 搜索首页埋点
 */
public class SearchHomeMa extends BaseMaEntity {

    /**
     * 搜索热词列表
     */
    public String keyword_list;

    /**
     * id
     */
    public String skuId;

    /**
     * name
     */
    public String skuName;

    /**
     * 自上而下的顺序 从1开始
     */
    public Integer listPageIndex;

    /**
     * 热搜词，展示在搜索框内的词语，即别名
     */
    public String hotwords;


    /**
     * 用户点击的热搜词
     */
    public String keyword;

    /**
     * 实际搜索词
     */
    public String enkwd;

    /**
     * 暗纹词类型
     */
    public Integer type;

    /**
     * 联想词
     */
    public String associateWord;


    public interface Constants {
        /**
         * 搜索主页-热搜词曝光
         */
        String EXPOSE_HOT_SEARCH = "searchPage_hotSearch_expose";

        /**
         * 搜索主页-常购清单曝光
         */
        String EXPOSE_REGULAR_PURCHASE = "searchPage_regularPurchase_expose";

        /**
         * 搜索主页-热搜榜曝光
         */
        String EXPOSE_HOT_LIST = "searchPage_hotList_expose";

        /**
         * 搜索主页-搜索框-暗文词
         */
        String EXPOSE_SEARCH_BUTTON_HOT_WORD = "searchPage_searchButton_hotWordsExpose";

        /**
         * 搜索主页-热搜词点击
         */
        String CLICK_HOT_SEARCH_CLICK = "searchPage_hotSearch_click";

        /**
         * 搜索主页-常购清单-加车
         */
        String CLICK_SEARCH_REGULAR_PURCHASE_ADD_CART = "searchPage_regularPurchase_addCart";

        /**
         * 搜索主页-常购清单-点击进入商详
         */
        String CLICK_SEARCH_REGULAR_PURCHASE_CLICK_COMMODITY = "searchPage_regularPurchase_clickCommodity";

        /**
         * 搜索主页-常购清单-查看全部
         */
        String CLICK_SEARCH_REGULAR_PURCHASE_ALL = "searchPage_regularPurchase_all";

        /**
         * 搜索主页-热榜-点击
         */
        String CLICK_SEARCH_HOT_LIST_CLICK = "searchPage_hotList_click";

        /**
         * 搜索主页-搜索框-搜索（搜索按钮）
         */
        String CLICK_SEARCH_BAR_SEARCH_BUTTON = "searchPage_searchBar_searchButton";

        /**
         * 搜索主页-搜索框-点击（唤起键盘）
         */
        String CLICK_SEARCH_BAR = "searchPage_searchBar_click";

        /**
         * 搜索主页-搜索键盘-搜索按钮点击
         */
        String CLICK_KEY_BOARD_SEARCH_BTN = "searchPage_searchKeyBoard_searchButton";

        /**
         * 搜索主页-返回（页面返回按钮）
         */
        String CLICK_SEARCH_BACK = "searchPage_back";

        /**
         * 搜索主页-历史搜索-展开
         */
        String CLICK_SEARCH_HISTORY_UNFOLD = "searchPage_historySearch_unfold";

        /**
         * 搜索主页-历史搜索-收齐
         */
        String CLICK_SEARCH_HISTORY_FOLD = "searchPage_historySearch_fold";

        /**
         * 搜索主页-历史搜索-点击
         */
        String CLICK_HISTORY = "searchPage_historySearch_click";

        /**
         * 搜索主页-历史搜索-删除
         */
        String CLICK_HISTORY_DEL = "searchPage_historySearch_delete";

    }

    /**
     * 联想词
     */
    public interface AssociateWord {
        /**
         * 普通搜索曝光
         */
        String SEARCHLISTPAGE_SKULIST_ASSOCIATEWORD_EXPOSE = "searchListPage_skuList_associateWord_expose";
        /**
         * 凑单搜索曝光
         */
        String SEARCHLISTPAGE_NORMALPROMOTIONSKULIST_ASSOCIATEWORD_EXPOSE = "searchListPage_normalPromotionSkuList_associateWord_expose";

        /**
         * 优惠券凑单页曝光
         */
        String SEARCHLISTPAGE_PROMOTIONWITHCOUPONSKULIST_ASSOCIATEWORD_EXPOSE = "searchListPage_promotionWithCouponSkuList_associateWord_expose";

        /**
         * 加价购页面曝光
         */
        String SEARCHLISTPAGE_PROMOTIONWITHRISEPRICESKULIST_ASSOCIATEWORD_EXPOSE = "searchListPage_promotionWithRisePriceSkuList_associateWord_expose";

        /**
         * 领货码页面曝光
         */
        String SEARCHLISTPAGE_LINGHUOMASKULIST_ASSOCIATEWORD_EXPOSE = "searchListPage_linghuomaSkuList_associateWord_expose";

        /**
         * 普通搜索联想词点击
         */
        String SEARCHLISTPAGE_SKULIST_ASSOCIATEWORD_CLICK = "searchListPage_skuList_associateWord_click";

        /**
         * 搜索凑单联想词点击
         */
        String SEARCHLISTPAGE_NORMALPROMOTIONSKULIST_ASSOCIATEWORD_CLICK = "searchListPage_normalPromotionSkuList_associateWord_click";

        /**
         * 优惠券凑单页联想词点击
         */
        String SEARCHLISTPAGE_PROMOTIONWITHCOUPONSKULIST_ASSOCIATEWORD_CLICK = "searchListPage_promotionWithCouponSkuList_associateWord_click";

        /**
         * 加价购页面
         */
        String SEARCHLISTPAGE_PROMOTIONWITHRISEPRICESKULIST_ASSOCIATEWORD_CLICK = "searchListPage_promotionWithRisePriceSkuList_associateWord_click";

        /**
         * 领货码页面
         */
        String SEARCHLISTPAGE_LINGHUOMASKULIST_ASSOCIATEWORD_CLICK = "searchListPage_linghuomaSkuList_associateWord_click";

    }
}
