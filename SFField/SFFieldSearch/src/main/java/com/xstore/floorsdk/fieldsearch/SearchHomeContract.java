package com.xstore.floorsdk.fieldsearch;

import android.os.Bundle;

import com.xstore.floorsdk.fieldsearch.adapter.NewSearchAdapter;
import com.xstore.floorsdk.fieldsearch.bean.HotSearchWordBean;
import com.xstore.floorsdk.fieldsearch.bean.SearchHomeBean;
import com.xstore.floorsdk.fieldsearch.interfaces.SearchHomeContainerInterface;
import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.List;

/**
 * 搜索首页
 */
public interface SearchHomeContract {

    /**
     * 逻辑
     */
    interface Presenter {

        /**
         * 页面resume
         */
        void onResume();

        /**
         * 页面stop
         */
        void onStop();

        /**
         * 页面销毁
         */
        void onDestroy();

        /**
         * 初始化数据
         */
        void initData(Bundle bundle);

        int getFromType();

        /**
         * 用户点击返回
         */
        void clickSearchBarBack();

        /**
         * 点击搜索栏上的搜索按钮
         *
         * @param inputWord
         * @param hintWord
         */
        void clickSearchBarSearch(String inputWord, String hintWord);

        /**
         * @param keyword 联想词
         */
        void getAutoSpellList(String keyword);

        /**
         * 用户点击了键盘上的搜索按钮
         *
         * @param inputWord
         * @param hintWord
         */
        void clickEditorAction(String inputWord, String hintWord);

        /**
         * 点击热搜词
         *
         * @param hotSearchWordBean
         */
        void clickHotWord(HotSearchWordBean hotSearchWordBean, int position);

        /**
         * 用户点击联想词
         *
         * @param autoSpellWord 联想词
         * @param position      当前点击的位置
         */
        void clickAutoSpellWord(String keyword, String autoSpellWord, int position);

        void requestRecommend();
    }

    /**
     * 视图
     */
    interface View {

        /**
         * 设置自动拼写
         *
         * @param list
         * @param keyword
         */
        void setAutoSpellList(List<String> list, String keyword);

        /**
         * 展示搜索内容区域
         */
        void showSearchContent(SearchHomeBean searchHomeBean);

        void setKeyWord(String keyword, String hotWordDefault);
        void setKeyWord(String keyword, String hotWordDefault,String hotWordDefaultIcon);
        /**
         * @param searchHistoryAdapter 展示搜索历史
         */
        void showSearchHistory(NewSearchAdapter searchHistoryAdapter);

        /**
         * 清空搜索词
         */
        void clearSearchWord();

        /**
         * @param skuInfos 追加常购清单商品
         */
        void appendFrequentList(List<SkuInfoBean> skuInfos);

        /**
         * 获取搜索主页容器接口
         *
         * @return
         */
        SearchHomeContainerInterface getSearchHomeContainerInterface();
    }

}
