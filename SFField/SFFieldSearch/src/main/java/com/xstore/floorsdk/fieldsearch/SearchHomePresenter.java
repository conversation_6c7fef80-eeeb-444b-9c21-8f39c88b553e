package com.xstore.floorsdk.fieldsearch;

import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_HISTORY;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_HISTORY_DEL;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_HOT_SEARCH_CLICK;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_KEY_BOARD_SEARCH_BTN;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_BACK;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_BAR_SEARCH_BUTTON;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.EXPOSE_SEARCH_BUTTON_HOT_WORD;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.inputmethod.InputMethodManager;

import com.xstore.floorsdk.fieldsearch.bean.DiscoveryDataBean;
import com.xstore.floorsdk.fieldsearch.bean.DiscoveryResultData;
import com.xstore.floorsdk.fieldsearch.request.SearchResultNetwork;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.jd.framework.json.JDJSON;
import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sdk.floor.floorcore.FloorBaseNetwork;
import com.xstore.sdk.floor.floorcore.widget.DialogUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.floorsdk.fieldsearch.adapter.NewSearchAdapter;
import com.xstore.floorsdk.fieldsearch.bean.HotSearchWordBean;
import com.xstore.floorsdk.fieldsearch.bean.SearchAutoSpellListBean;
import com.xstore.floorsdk.fieldsearch.bean.SearchHomeBean;
import com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa;
import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索的业务逻辑处理
 */
public class SearchHomePresenter implements SearchHomeContract.Presenter {

    public Map<String, Map<String, Integer>> hotTopList = new HashMap<>();
    /**
     * 视图逻辑
     */
    private SearchHomeContract.View view = null;
    /**
     * 页面activity
     */
    private Activity activity = null;
    /**
     * 埋点
     */
    private JDMaUtils.JdMaPageImp jdMaPageImp = null;

    /**
     * 页面类型
     */
    private int fromType = SearchConstant.Value.FROM_TYPE_SEARCH;//默认值

    /**
     * 搜索历史
     */
    private NewSearchAdapter searchHistoryAdapter;
    /**
     * 是否从搜索结果页跳转过来的
     */
    private boolean fromSearchResult;

    /**
     * 去搜索了
     */
    private boolean hasGoSearch;
    /**
     * 当前展示哪个门店的数据 如果回来后不再同一个门店 需要刷新
     */
    private String storeId;
    /**
     * 当前页面是否是已经登录了，登录态发生变化需要刷新
     */
    private boolean isLogin;

    /**
     * 围栏id, 当围栏id需要发生变化的时候刷新
     */
    private String fenceId;

    public SearchHomePresenter(Activity activity, SearchHomeContract.View view, JDMaUtils.JdMaPageImp jdMaPageImp) {
        this.activity = activity;
        this.view = view;
        this.jdMaPageImp = jdMaPageImp;
    }


    @Override
    public void initData(Bundle bundle) {
        if (bundle != null) {
            fromType = bundle.getInt(SearchConstant.Key.FROM_TYPE, 0);
            fromSearchResult = bundle.getBoolean(SearchConstant.Key.FROM_SEARCH_RESULT, false);
            setEtSearch(bundle);
        }
        initListData();
    }

    @Override
    public void onResume() {
        if (fromType == SearchConstant.Value.FROM_TYPE_SEARCH) {
            if (searchHistoryAdapter != null) {
                searchHistoryAdapter.refreshData();
            }
        }
        if (hasGoSearch) {
            hasGoSearch = false;
            view.clearSearchWord();
        }

        // 只有是普通搜索 并且 是否切换了店铺或者重新登录时  重新请求
        if (fromType == SearchConstant.Value.FROM_TYPE_SEARCH &&
                (!TextUtils.equals(TenantIdUtils.getStoreId(), storeId) ||
                        (TextUtils.equals(TenantIdUtils.getStoreId(), storeId) && !TextUtils.equals(TenantIdUtils.getFenceId(), fenceId)) ||
                        isLogin != FloorInit.getFloorConfig().isLogin() ||
                        !TextUtils.equals(TenantIdUtils.getFenceId(), fenceId))) {
            isLogin = FloorInit.getFloorConfig().isLogin();
            storeId = TenantIdUtils.getStoreId();
            fenceId = TenantIdUtils.getFenceId();
            requestRelatedSearch();
        }
    }

    @Override
    public void onStop() {
    }

    @Override
    public void onDestroy() {

    }

    @Override
    public int getFromType() {
        return fromType;
    }

    @Override
    public void clickSearchBarBack() {
        JDMaUtils.save7FClick(CLICK_SEARCH_BACK, jdMaPageImp, null);
        activity.finish();
    }

    @Override
    public void clickSearchBarSearch(String inputWord, String hintWord) {
        closePan();
        String keyword = TextUtils.isEmpty(inputWord) ? hintWord : inputWord;
        if (TextUtils.isEmpty(keyword)) {
            return;
        }
        doSearch(inputWord, hintWord, TextUtils.isEmpty(inputWord) ? "3" : "5");
        SearchHomeMa ma = new SearchHomeMa();
        ma.enkwd = keyword;
        JDMaUtils.save7FClick(CLICK_SEARCH_BAR_SEARCH_BUTTON, jdMaPageImp, ma);
    }

    @Override
    public void getAutoSpellList(final String keyword) {
        if (view.getSearchHomeContainerInterface() == null) {
            return;
        }
        JDJSONObject varsExtra = new JDJSONObject();
        varsExtra.put("keyword", keyword);
        SearchResultNetwork.requestPost(activity, view.getSearchHomeContainerInterface().getAutoSpellFunId(), FreshHttpSetting.NO_EFFECT, varsExtra,
                new BaseFreshResultCallback<ResponseData<SearchAutoSpellListBean>, SearchAutoSpellListBean>() {
                    @Override
                    public SearchAutoSpellListBean onData(ResponseData<SearchAutoSpellListBean> data, FreshHttpSetting httpSetting) {
                        if (data != null) {
                            return data.getData();
                        }
                        return null;
                    }

                    @Override
                    public void onEnd(SearchAutoSpellListBean object, FreshHttpSetting httpSetting) {
                        if (object == null || object.getAutoSpells() == null || object.getAutoSpells().size() == 0) {
                            view.setAutoSpellList(null, keyword);
                            SgmBusinessErrorUtil.reportGetAutoSpellEmpty(keyword);
                            return;
                        }
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < object.getAutoSpells().size(); i++) {
                            sb.append(object.getAutoSpells().get(i))
                                    .append("#")
                                    .append(i + 1);
                            if (i < object.getAutoSpells().size() - 1) {
                                sb.append("+");
                            }
                        }
                        String clickId = null;
                        switch (fromType) {
                            case SearchConstant.Value.FROM_TYPE_COUPON:
                                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_PROMOTIONWITHCOUPONSKULIST_ASSOCIATEWORD_EXPOSE;
                                break;
                            case SearchConstant.Value.FROM_TYPE_PROMOTION:
                                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_NORMALPROMOTIONSKULIST_ASSOCIATEWORD_EXPOSE;
                                break;
                            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_PROMOTIONWITHRISEPRICESKULIST_ASSOCIATEWORD_EXPOSE;
                                break;
                            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_LINGHUOMASKULIST_ASSOCIATEWORD_EXPOSE;
                                break;
                            case SearchConstant.Value.FROM_TYPE_SEARCH:
                            default:
                                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_SKULIST_ASSOCIATEWORD_EXPOSE;
                                break;
                        }
                        SearchHomeMa ma = new SearchHomeMa();
                        ma.enkwd = keyword;

                        ma.associateWord = sb.toString();
                        JDMaUtils.save7FExposure(clickId, null, ma, null, jdMaPageImp);
                        view.setAutoSpellList(object.getAutoSpells(), keyword);

                    }

                    @Override
                    public void onError(FreshHttpException error) {
                        //联想失败 清空搜索词
                        view.setAutoSpellList(null, keyword);
                    }
                });

    }

    @Override
    public void clickEditorAction(String inputWord, String hintWord) {

        String keyword = TextUtils.isEmpty(inputWord) ? hintWord : inputWord;
        if (TextUtils.isEmpty(keyword)) {
            return;
        }
        doSearch(inputWord, hintWord, TextUtils.isEmpty(inputWord) ? "3" : "5");
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("enkwd", keyword);
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick(CLICK_KEY_BOARD_SEARCH_BTN, jdMaPageImp, maEntity);
    }

    @Override
    public void clickHotWord(HotSearchWordBean hotSearchWordBean, int position) {
        String keyword = hotSearchWordBean.getHotWord();
        boolean isUrl = !StringUtil.isNullByString(hotSearchWordBean.getUrl());
        if (!isUrl) {
            doSearch(keyword, true, "2");
        } else {
            FloorJumpManager.getInstance().startH5(activity, hotSearchWordBean.url, false);
        }

        SearchHomeMa ma = new SearchHomeMa();
        ma.keyword = hotSearchWordBean.showWord;
        ma.enkwd = hotSearchWordBean.hotWord;
        ma.listPageIndex = position + 1;
        JDMaUtils.save7FClick(CLICK_HOT_SEARCH_CLICK, jdMaPageImp, ma);
    }

    /**
     * 设置搜索框关键字
     *
     * @param bundle
     */
    private void setEtSearch(Bundle bundle) {
        String keyword = bundle.getString(SearchConstant.Key.SEARCH_KEYWORD);
        //默认搜索词
        String hotWordDefault = "";
        String hotWordDefaultIcon = "";
        if (fromType == SearchConstant.Value.FROM_TYPE_PROMOTION || fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE) {//促销活动搜索
            hotWordDefault = activity.getResources().getString(R.string.sf_field_search_pomotion_goods);
        } else if (fromType == SearchConstant.Value.FROM_TYPE_COUPON) {
            hotWordDefault = activity.getResources().getString(R.string.sf_field_search_coupon_goods);
        } else if (fromType == SearchConstant.Value.FROM_TYPE_PICKING_CODE) {
            hotWordDefault = activity.getResources().getString(R.string.sf_field_search_picking_code_goods);
        } else if (fromType == SearchConstant.Value.FROM_TYPE_SEARCH) {
            hotWordDefault = PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.HOT_WORDS_INFO + TenantIdUtils.getStoreId());
            hotWordDefaultIcon = PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.HOT_WORDS_INFO_ICON + TenantIdUtils.getStoreId());

            //曝光----只有主搜才有暗纹词 所以采用曝光
            SearchHomeMa ma = new SearchHomeMa();
            ma.hotwords = PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.HOT_WORDS_INFO + TenantIdUtils.getStoreId());
            //优先取暗纹词，如果暗纹词为空，则取搜索词
            ma.enkwd = !TextUtils.isEmpty(hotWordDefault) ? hotWordDefault : PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.CUR_SEARCH_WORD + TenantIdUtils.getStoreId());
            ma.type = PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.CUR_SEARCH_JUMP + TenantIdUtils.getStoreId()) == null ? 1 : 2;
            if (!TextUtils.isEmpty(ma.enkwd)) {
                JDMaUtils.save7FExposure(EXPOSE_SEARCH_BUTTON_HOT_WORD, null, ma, null, jdMaPageImp);
            }
        }
        view.setKeyWord(keyword, hotWordDefault,hotWordDefaultIcon);
    }


    private void doSearch(String keyword, String keywordClickFrom) {
        if (TextUtils.isEmpty(keyword)) {
            return;
        }
        doSearch(keyword, "", keywordClickFrom, false);
    }

    private void doSearch(String keyword, boolean isAction, String keywordClickFrom) {
        if (TextUtils.isEmpty(keyword)) {
            return;
        }
        doSearch(keyword, "", keywordClickFrom, isAction);
    }

    private void doSearch(String inputWord, String hintWord, String keywordClickFrom) {
        doSearch(inputWord, hintWord, keywordClickFrom, false);
    }

    private void doSearch(String inputWord, String hintWord, String keywordClickFrom, boolean isAction) {
        if (TextUtils.isEmpty(inputWord) && TextUtils.isEmpty(hintWord)) {
            return;
        }

        //优先级更高
        if (TextUtils.isEmpty(inputWord) && !TextUtils.isEmpty(hintWord) && hintWord.equals(PreferenceUtil.getString("HOTWORDSINFO" + TenantIdUtils.getStoreId(), null))) {
//            String url = HomeSearchFloor.getCurSearchJump();
//            if (!TextUtils.isEmpty(url)) {
//                WebRouterHelper.startWebActivityWithNewInstance(activity, url, "", 0, null);
//                return;
//            }
            hintWord = PreferenceUtil.getString(SearchHomeConstants.HomeSearchFloor.CUR_SEARCH_WORD + TenantIdUtils.getStoreId(), null);
        }

        String searchKeyword = TextUtils.isEmpty(inputWord) ? hintWord : inputWord;


        if (!TextUtils.isEmpty(searchKeyword) && fromType == SearchConstant.Value.FROM_TYPE_SEARCH) {
            SearchHistoryTable.saveSearchHistory(searchKeyword);
        }

        if (fromSearchResult) {
            Intent intent = new Intent();
            intent.putExtra(SearchConstant.Key.SEARCH_KEYWORD, TextUtils.isEmpty(inputWord) ? (fromType == SearchConstant.Value.FROM_TYPE_SEARCH ? hintWord : "") : inputWord);
            intent.putExtra(SearchConstant.Key.KEYWORD_CLICKFROM, keywordClickFrom);
            activity.setResult(Activity.RESULT_OK, intent);
            activity.finish();
        } else {
            Integer hotSort = getHotSort(searchKeyword);
            FloorJumpManager.getInstance().startProductList(activity, fromType, searchKeyword, keywordClickFrom, hotSort == null ? 0 : hotSort.intValue());
            hasGoSearch = true;
        }
    }

    /**
     * 获取热词排行
     *
     * @param searchKeyword
     * @return
     */
    private Integer getHotSort(String searchKeyword) {
        try {
            if (hotTopList.get(TenantIdUtils.getStoreId()) != null) {
                Integer index = hotTopList.get(TenantIdUtils.getStoreId()).get(searchKeyword);
                return index;
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        return null;
    }


    /**
     * 请求搜索首页展示的数据数据
     */
    private void requestRelatedSearch() {
        ArrayList<String> fieldNames = new ArrayList<>();
        fieldNames.add("searchNewHotWord");
        //如果已经登录了 就查询常购清单
        if (FloorInit.getFloorConfig().isLogin()) {
            fieldNames.add("queryFrequentPurchasePage");
        }
        fieldNames.add("rankBaseInfoList");
        int requestStep = 0;
        String sdkVersion = "1";
        FloorBaseNetwork.requestGql(activity, FreshHttpSetting.NO_EFFECT, null, 0,
                fieldNames, null, requestStep, sdkVersion, 9999,
                new BaseFreshResultCallback<ResponseData<SearchHomeBean>, SearchHomeBean>() {
                    @Override
                    public SearchHomeBean onData(ResponseData<SearchHomeBean> data, FreshHttpSetting httpSetting) {
                        if (data != null) {
                            return data.getData();
                        }
                        return null;
                    }

                    @Override
                    public void onEnd(SearchHomeBean object, FreshHttpSetting httpSetting) {
                        view.showSearchContent(object);
                    }

                    @Override
                    public void onError(FreshHttpException error) {
                        view.showSearchContent(null);
                    }
                });
    }


    @Override
    public void clickAutoSpellWord(String keyword, String autoSpellWord, int position) {

        String clickId = null;
        switch (fromType) {
            case SearchConstant.Value.FROM_TYPE_COUPON:
                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_PROMOTIONWITHCOUPONSKULIST_ASSOCIATEWORD_CLICK;
                break;
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_NORMALPROMOTIONSKULIST_ASSOCIATEWORD_CLICK;
                break;
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_PROMOTIONWITHRISEPRICESKULIST_ASSOCIATEWORD_CLICK;
                break;
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_LINGHUOMASKULIST_ASSOCIATEWORD_CLICK;
                break;
            case SearchConstant.Value.FROM_TYPE_SEARCH:
            default:
                clickId = SearchHomeMa.AssociateWord.SEARCHLISTPAGE_SKULIST_ASSOCIATEWORD_CLICK;
                break;
        }
        SearchHomeMa ma = new SearchHomeMa();
        ma.enkwd = keyword;
        ma.associateWord = "" + autoSpellWord + "#" + String.valueOf(position + 1);
        JDMaUtils.save7FClick(clickId, jdMaPageImp, ma);
        doSearch(autoSpellWord, "1");
    }


    private void initListData() {
        searchHistoryAdapter = new NewSearchAdapter(activity, jdMaPageImp, fromType == SearchConstant.Value.FROM_TYPE_SEARCH, true);
        searchHistoryAdapter.setListener(new NewSearchAdapter.onItemListener() {
            @Override
            public void onDelListener() {
                delHistory();
                JDMaUtils.save7FClick(CLICK_HISTORY_DEL, jdMaPageImp, null);
            }

            @Override
            public void onHotwordListener(int position, boolean isUrl, String keyword, String url) {
                //do nothing
            }

            @Override
            public void onHistoryListener(String keyword) {

                SearchHomeMa ma = new SearchHomeMa();
                ma.enkwd = keyword;
                JDMaUtils.save7FClick(CLICK_HISTORY, jdMaPageImp, ma);

                doSearch(keyword, "4");
            }
        });
        searchHistoryAdapter.setDiscoveryItemListener(new NewSearchAdapter.OnDiscoveryItemListener() {
            @Override
            public void onItemListener(DiscoveryDataBean discoveryDataBean) {
                if (discoveryDataBean == null) {
                    return;
                }
                doSearch(discoveryDataBean.getQuery(), "6");
            }
        });
        view.showSearchHistory(searchHistoryAdapter);
        SearchResultNetwork.requestDiscovery(activity,
                new BaseFreshResultCallback<ResponseData<DiscoveryResultData>, DiscoveryResultData>() {
                    @Override
                    public DiscoveryResultData onData(ResponseData<DiscoveryResultData> data, FreshHttpSetting httpSetting) {
                        if (data != null) {
                            return data.getData();
                        }
                        return null;
                    }

                    @Override
                    public void onEnd(DiscoveryResultData discoveryResultData, FreshHttpSetting httpSetting) {
                        if (discoveryResultData != null && searchHistoryAdapter != null) {
                            searchHistoryAdapter.setDiscoveryResultData(discoveryResultData);
                        }
                    }

                    @Override
                    public void onError(FreshHttpException error) {

                    }
                });
    }

    /**
     * 删除搜索历史弹窗
     */
    private void delHistory() {
        DialogUtils.showDialog(activity).setCancelable(false)
                .setStyle(R.style.sf_floor_core_alert)
                .setTitle(R.string.sf_field_search_delete_search_history_certain)
                .setPositiveButton(R.string.sf_field_search_ok, new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        clearHistory();
                        dialog.dismiss();
                    }
                }, activity.getResources().getColor(R.color.sf_theme_color_level_1))
                .setNegativeButton(R.string.sf_field_search_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .build().show();
    }


    /**
     * 清除搜索历史
     */
    private void clearHistory() {
        SearchHistoryTable.deleteAllHistory();
        if (searchHistoryAdapter != null) {
            searchHistoryAdapter.refreshData();
        }
    }

    @Override
    public void requestRecommend() {
        JDJSONObject varsExtra = new JDJSONObject();
        varsExtra.put("dynamicParam", "{\"queryIndexRecommend\":[{\"mid\":1,\"contentWithState\":1,\"materialDensity\":0}]}");
        List<String> fieldName = new ArrayList();
        fieldName.add("queryIndexRecommend");
        FloorBaseNetwork.requestGql(activity, 0, "queryIndexRecommend", 0, fieldName, varsExtra, 0, FloorBaseNetwork.INNER_SDK_VERSION, 9999, new BaseFreshResultCallback<String, String>() {
            public String onData(String data, FreshHttpSetting httpSetting) {
                return data;
            }

            public void onEnd(String response, FreshHttpSetting httpSetting) {
                JDJSONObject jdjsonObject = JDJSONObject.parseObject(response);
                if (jdjsonObject == null
                        || jdjsonObject.optJSONObject("data") == null
                        || jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend") == null
                        || !jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend").optBoolean("success")) {
                    return;
                }
                JDJSONArray skuInfos = jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend").optJSONArray("skuInfos");
                ArrayList<SkuInfoBean> list = new ArrayList<>();

                for (int i = 0; i < skuInfos.size(); i++) {
                    try {
                        JDJSONObject skuInfo = skuInfos.getJSONObject(i);
                        if (skuInfo == null) {
                            continue;
                        }

                        if (skuInfo.optInt("style") == 1) {
                            SkuInfoBean wareBean = JDJSON.parseObject(skuInfo.optString("recommendVo"), SkuInfoBean.class);
                            if (wareBean != null) {
                                list.add(wareBean);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        JdCrashReport.postCaughtException(e);
                    }
                }
                if (list.size() == 0) {
                    return;
                }

                view.appendFrequentList(list);
            }

            public void onError(FreshHttpException error) {
                JdCrashReport.postCaughtException(error);
            }
        });
    }

    public void closePan() {
        try {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(activity.getWindow().getDecorView().getWindowToken(), 0);
            }
        } catch (Exception var2) {
            var2.printStackTrace();
        }

    }

}
