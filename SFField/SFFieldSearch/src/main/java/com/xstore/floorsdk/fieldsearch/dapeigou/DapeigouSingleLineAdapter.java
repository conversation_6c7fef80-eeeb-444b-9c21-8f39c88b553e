package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.ArrayList;
import java.util.List;

/**
 * 瀑布流模式下,搭配购Item卡片中的商品适配器
 */
public class DapeigouSingleLineAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    /**
     * activity
     */
    private AppCompatActivity activity;
    /**
     * 布局加载器
     */
    private LayoutInflater inflater;
    /**
     * 商品数据列表
     */
    private List<SkuInfoBean> productList = new ArrayList<>();

    private ProductCardInterfaces productCardInterfaces;

    public void setProductCardInterfaces(ProductCardInterfaces productCardInterfaces) {
        this.productCardInterfaces = productCardInterfaces;
    }

    public DapeigouSingleLineAdapter(Activity activity, List<SkuInfoBean> skuInfoBeans) {
        this.activity = (AppCompatActivity) activity;
        this.inflater = LayoutInflater.from(activity);
        this.productList = skuInfoBeans;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View productView = inflater.inflate(R.layout.sf_field_search_product_card_dapeigou_singleline_item, parent, false);
        return new ProductCardDapeigouSingleLineHolder(productView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        try {
            SkuInfoBean skuInfoBean = productList.get(position);
            skuInfoBean.setPageIndex(position + 1 + "");
            if (holder instanceof ProductCardDapeigouSingleLineHolder) {
                ProductCardDapeigouSingleLineHolder dapeigouHolder = (ProductCardDapeigouSingleLineHolder) holder;
                dapeigouHolder.bindData(activity, skuInfoBean, productCardInterfaces);
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e, "dapeigouAdapter");
        }
    }

    @Override
    public int getItemCount() {
        return productList.size();
    }

    public SkuInfoBean getItem(int position) {
        if (productList == null) {
            return null;
        }
        if (position < 0 || productList.size() <= position) {
            return null;
        }
        return productList.get(position);
    }
}
