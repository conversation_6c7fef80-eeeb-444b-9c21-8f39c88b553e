package com.xstore.floorsdk.fieldsearch.widget.filter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.config.SearchActivityConfig;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 筛选条件均分样式的adapter
 *
 * <AUTHOR>
 * @date 2022/09/19
 */
public class SearchEqualFilterAdapter extends RecyclerView.Adapter<SearchEqualFilterAdapter.FilterHolder> {

    private Context context;
    private SearchResultDataManager searchResultDataManager;
    private OnItemClickListener onItemClickListener;
    /**
     * 是否只有排序筛选
     */
    private boolean onlySortFilter = false;
    private List<SearchFilterQuery> filterQueryList;
    private SearchActivityConfig activitySearchConfig;

    private int equalItemWith;
    /**
     * 是否显示大促活动标
     */
    boolean canShowActivity;

    public SearchEqualFilterAdapter(Context context, SearchResultDataManager searchResultDataManager, boolean canShowActivity) {
        this.context = context;
        this.searchResultDataManager = searchResultDataManager;
        this.canShowActivity = canShowActivity;
    }

    /**
     * 绑定数据
     *
     * @param filterQueryList
     * @param onlySortFilter
     */
    public void setFilterQueryList(List<SearchFilterQuery> filterQueryList, boolean onlySortFilter, int equalItemWith) {
        this.filterQueryList = filterQueryList;
        this.onlySortFilter = onlySortFilter;
        this.activitySearchConfig = searchResultDataManager.getActivitySearchConfig();
        this.equalItemWith = equalItemWith;
        notifyDataSetChanged();
    }


    @NonNull
    @Override
    public FilterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_filter_equal_item, null);
        return new FilterHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FilterHolder holder, int position) {
        if (position == 0 && canShowActivity) {
            holder.llFilterItem.setVisibility(View.GONE);
            holder.ivFilterAction.setVisibility(View.VISIBLE);
            setActivityFilter(holder.ivFilterAction, searchResultDataManager.isActionFilterSelected());
            holder.ivFilterAction.setOnClickListener(v -> {
                if (searchResultDataManager.isActionFilterSelected()) {
                    searchResultDataManager.clearActionFilter();
                    setActivityFilter(holder.ivFilterAction, false);
                    searchResultDataManager.searchResultReporter.clickActivity("2");
                } else {
                    searchResultDataManager.selectAction();
                    setActivityFilter(holder.ivFilterAction, true);
                    searchResultDataManager.searchResultReporter.clickActivity("1");
                }
            });
            return;
        } else {
            holder.llFilterItem.setVisibility(View.VISIBLE);
            holder.ivFilterAction.setVisibility(View.GONE);

            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) holder.llFilterItem.getLayoutParams();
            params.width = equalItemWith;
            holder.llFilterItem.setLayoutParams(params);
        }
        int realPosition = position;
        if (canShowActivity) {
            realPosition -= 1;
        }
        SearchFilterQuery filterQuery = filterQueryList.get(realPosition);
        if (filterQuery == null) {
            return;
        }
        holder.tvFilterItem.setText(filterQuery.getFilterLable());
        if (filterQuery.canExpand()) {
            holder.ivFilterExpand.setVisibility(View.VISIBLE);
        } else if (onlySortFilter && position == getItemCount() - 1) {
            holder.ivFilterExpand.setVisibility(View.VISIBLE);
        } else {
            holder.ivFilterExpand.setVisibility(View.GONE);
        }
        if (filterQuery.isSelected() || filterQuery.isExpanded()) {
            holder.tvFilterItem.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
            holder.tvFilterItem.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            if (onlySortFilter && position == getItemCount() - 1) {
                if (filterQuery.getFilterValues() != null && filterQuery.getFilterValues().size() == 2) {
                    if (filterQuery.getFilterValues().get(0).isSelected()) {
                        holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_price_asc_2);
                    } else {
                        holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_price_desc_2);
                    }
                } else {
                    holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_price_unselect_2);
                }
            } else {
                if (filterQuery.isExpanded()) {
                    holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_feature_selected_expanded);
                } else {
                    holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_feature_selected_unexpand);
                }
            }
        } else {
            holder.tvFilterItem.setTextColor(ContextCompat.getColor(context, R.color.sf_field_search_color_1a1a1a));
            holder.tvFilterItem.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            if (onlySortFilter && position == getItemCount() - 1) {
                holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_price_unselect_2);
            } else {
                holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_feature_unselect_unexpand);
            }
        }
        holder.itemView.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (filterQuery.canExpand()) {
                holder.tvFilterItem.setTextColor(ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
                holder.tvFilterItem.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                holder.ivFilterExpand.setImageResource(R.drawable.sf_field_search_filter_feature_selected_expanded);
                if (onItemClickListener != null) {
                    filterQuery.setExpanded(true);
                    onItemClickListener.showDropDown(filterQuery);
                }
            } else {
                if (filterQuery.isSortFilter()) {
                    if (position == getItemCount() - 1) {
                        if (filterQuery.getFilterValues().get(0).isSelected()) {
                            filterQuery.getFilterValues().get(0).setSelected(false);
                            filterQuery.getFilterValues().get(1).setSelected(true);
                            filterQuery.setSelected(true);
                            searchResultDataManager.updateSortFilter(filterQuery.getFilterValues().get(1).getFilterKey(), filterQuery.getFilterValues().get(1).getFilterValue());
                        } else {
                            if (!filterQuery.getFilterValues().get(1).isSelected()) {
                                clearSortFilterSelectStatus();
                            }
                            filterQuery.getFilterValues().get(0).setSelected(true);
                            filterQuery.getFilterValues().get(1).setSelected(false);
                            filterQuery.setSelected(true);
                            searchResultDataManager.updateSortFilter(filterQuery.getFilterValues().get(0).getFilterKey(), filterQuery.getFilterValues().get(0).getFilterValue());
                        }
                    } else if (!filterQuery.isSelected()) {
                        clearSortFilterSelectStatus();
                        filterQuery.setSelected(true);
                        if (StringUtil.isNullByString(filterQuery.getFilterKey())) { //选中综合
                            searchResultDataManager.clearSortFilter(true);
                        } else { //销量
                            searchResultDataManager.updateSortFilter(filterQuery.getFilterKey(), filterQuery.getFilterValue());
                        }
                    } else {
                        //点击已选排序，重新请求接口
                        clearSortFilterSelectStatus();
                        filterQuery.setSelected(true);
                        if (StringUtil.isNullByString(filterQuery.getFilterKey())) { //选中综合
                            searchResultDataManager.clearSortFilter(true);
                        } else { //销量
                            searchResultDataManager.updateSortFilter(filterQuery.getFilterKey(), filterQuery.getFilterValue());
                        }
                    }
                } else {
                    if (filterQuery.isSelected()) {
                        filterQuery.setSelected(false);
                        if (filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
                            for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                                query.setSelected(false);
                            }
                        }
                        searchResultDataManager.removeFeatureFilter(filterQuery.getFilterKey());
                    } else {
                        filterQuery.setSelected(true);
                        List<String> filterValues = new ArrayList<>();
                        if (filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
                            for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                                filterValues.add(query.getFilterValue());
                                query.setSelected(true);
                            }
                        } else {
                            filterValues.add(filterQuery.getFilterValue());
                        }
                        searchResultDataManager.addFeatureFilter(filterQuery.getFilterKey(), filterValues);
                    }
                }
            }
            searchResultDataManager.searchResultReporter.clickFilter(filterQuery.getFilterLable());
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(position);
            }
        });
    }

    /**
     * 排序是单选，因此点击时需要先清除其他item选中态
     */
    private void clearSortFilterSelectStatus() {
        for (SearchFilterQuery filterQuery : filterQueryList) {
            filterQuery.setSelected(false);
            if (filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
                for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                    query.setSelected(false);
                }
            }
        }
    }

    /**
     * 设置活动筛选按钮
     *
     * @param ivFilterAction
     * @param selected
     */
    private void setActivityFilter(ImageView ivFilterAction, boolean selected) {
        if (selected) {
            ImageloadUtils.loadImageWithDefalutScaleType(context, ivFilterAction, activitySearchConfig.getSearchBarSelectedImg(), 0, R.drawable.sf_field_search_filter_action_select);
        } else {
            ImageloadUtils.loadImageWithDefalutScaleType(context, ivFilterAction, activitySearchConfig.getSearchBarNoSelectedImg(), 0, R.drawable.sf_field_search_filter_action_unselect);
        }
    }

    @Override
    public int getItemCount() {
        int count = filterQueryList == null ? 0 : filterQueryList.size();
        if (canShowActivity) {
            count++;
        }
        return count;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public class FilterHolder extends RecyclerView.ViewHolder {

        ImageView ivFilterAction;
        LinearLayout llFilterItem;
        TextView tvFilterItem;
        ImageView ivFilterExpand;

        public FilterHolder(@NonNull View itemView) {
            super(itemView);
            ivFilterAction = itemView.findViewById(R.id.iv_filter_action);
            llFilterItem = itemView.findViewById(R.id.ll_filter_item);
            tvFilterItem = itemView.findViewById(R.id.tv_filter_item);
            ivFilterExpand = itemView.findViewById(R.id.iv_filter_expand);
        }
    }

    interface OnItemClickListener {

        void onItemClick(int position);

        void showDropDown(SearchFilterQuery searchFilterQuery);
    }
}
