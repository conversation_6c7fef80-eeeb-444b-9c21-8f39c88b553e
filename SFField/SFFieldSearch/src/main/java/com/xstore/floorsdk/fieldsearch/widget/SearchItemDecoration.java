package com.xstore.floorsdk.fieldsearch.widget;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.View;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

/**
 * 搜索item边框
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
public class SearchItemDecoration extends RecyclerView.ItemDecoration {

    private int recyclerviewItemDistance;
    private int recyclerviewMarginLR;

    public SearchItemDecoration(Context context) {
        this.recyclerviewItemDistance = DisplayUtils.dp2px(context, 9);
        this.recyclerviewMarginLR = DisplayUtils.dp2px(context, 14);
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) view.getLayoutParams();
        if (params.isFullSpan()) {
            outRect.right = 0;
            outRect.left = 0;
            outRect.top = 0;
            outRect.bottom = 0;
            return;
        }
        // 获取item在span中的下标
        int spanIndex = params.getSpanIndex();
        if (spanIndex % 2 == 0) {
            outRect.right = recyclerviewItemDistance / 2;
            //outRect.left = recyclerviewMarginLR;
            outRect.left = recyclerviewItemDistance / 2;
        } else {
            outRect.left = recyclerviewItemDistance / 2;
            //outRect.right = recyclerviewMarginLR;
            outRect.right = recyclerviewItemDistance / 2;
        }
        outRect.top = recyclerviewItemDistance;
    }
}
