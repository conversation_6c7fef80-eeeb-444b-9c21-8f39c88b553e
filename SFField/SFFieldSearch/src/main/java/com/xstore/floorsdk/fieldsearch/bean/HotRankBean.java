package com.xstore.floorsdk.fieldsearch.bean;

/**
 * 榜单列表数据
 */
public class HotRankBean {

    /**
     * 榜单名称
     */
    private String title;
    /**
     * 榜单id
     */
    private String rankingId;
    /**
     * 榜单详情跳转url
     */
    private String jumpUrl;
    /**
     * 排行榜图标
     */
    private String imageUrl;
    /**
     * 前n名榜单小火苗 ,n和小火苗ducc可配置
     */
    private String hotSellRankImage;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRankingId() {
        return rankingId;
    }

    public void setRankingId(String rankingId) {
        this.rankingId = rankingId;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getHotSellRankImage() {
        return hotSellRankImage;
    }

    public void setHotSellRankImage(String hotSellRankImage) {
        this.hotSellRankImage = hotSellRankImage;
    }
}