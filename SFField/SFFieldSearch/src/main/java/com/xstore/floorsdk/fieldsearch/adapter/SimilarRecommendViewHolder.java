package com.xstore.floorsdk.fieldsearch.adapter;

import android.app.Activity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.widget.HorizontalMoreRecyclerView;


/**
 * 相似推荐viewHolder
 *
 * <AUTHOR>
 * @date 2022/09/26
 */
public class SimilarRecommendViewHolder extends RecyclerView.ViewHolder {
    private Activity activity;

    private TextView tvTitle;
    private HorizontalMoreRecyclerView recyclerView;
    private LinearLayout llRightMore;

    public SimilarRecommendViewHolder(Activity activity, @NonNull View itemView) {
        super(itemView);
        this.activity = activity;

        tvTitle = itemView.findViewById(R.id.tv_title);
        recyclerView = itemView.findViewById(R.id.hmrv_recyclerview);
        llRightMore = itemView.findViewById(R.id.ll_bottom_right_more);

        recyclerView.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false));
        SimilarRecommendAdapter recommendAdapter = new SimilarRecommendAdapter(activity);
        recyclerView.setAdapter(recommendAdapter);
    }
}
