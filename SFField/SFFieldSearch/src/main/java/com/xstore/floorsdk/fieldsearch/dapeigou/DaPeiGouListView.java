package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * 搭配购View-一行一个-横滑列表式
 */
public class DaPeiGouListView extends LinearLayout {

    private final Activity mActivity;
    private View moreView;          // "更多"布局
    private View titleView;         // 标题区域
    private RecyclerView recyclerView; // 商品列表
    private DapeigouAdapter adapter;   // 适配器
    private SearchResultDataManager searchResultDataManager; // 数据管理器
    private SkuInfoBean currentWareInfo;     // 当前商品SKU
    private String currentWareInfoSkuId;     // 当前触发搭配购的商品SKUid
    private List<SkuInfoBean> skuList; // 商品数据列表
    private XiaoFeiDaPeiGouListener xiaoFeiDaPeiGouListener;

    public DaPeiGouListView(Activity activity) {
        super(activity);
        mActivity = activity;
        initView(activity);
    }

    private void initView(Context context) {
        setOrientation(VERTICAL);
        LayoutInflater.from(context).inflate(R.layout.sf_field_search_item_dapeigou, this, true);
        titleView = findViewById(R.id.more_layout_title);
        moreView = findViewById(R.id.more_layout);
        recyclerView = findViewById(R.id.dapeigou_recycler);
    }

    /**
     * 绑定数据
     *
     * @param skuList      商品列表数据
     * @param currentSkuId 当前商品SKU ID
     * @param dataManager  数据管理器
     */
    public void bindData(List<SkuInfoBean> skuList, SkuInfoBean targetWareInfo, SearchResultDataManager searchResultDataManager) {
        this.skuList = skuList;
        this.currentWareInfo = targetWareInfo;
        this.currentWareInfoSkuId = targetWareInfo == null ? "" : targetWareInfo.getSkuId();
        this.searchResultDataManager = searchResultDataManager;

        setupRecyclerView();
        setupClickListeners();
        moreView.setVisibility(skuList != null && skuList.size() > 4 ? View.VISIBLE : View.GONE);
    }

    // 设置RecyclerView
    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        recyclerView.setLayoutManager(layoutManager);

        int itemCount = skuList != null ? skuList.size() : 0;
        adapter = new DapeigouAdapter(mActivity, itemCount > 4 ? 82 : 84, 146, skuList);

        // 设置间距装饰
        int defaultSpace = ScreenUtils.dip2px(getContext(), 6);
        int edgeSpace = ScreenUtils.dip2px(getContext(), 8);
        HorizontalSpaceItemDecoration decoration = new HorizontalSpaceItemDecoration(defaultSpace, edgeSpace, edgeSpace);

        // 先移除已添加的 ItemDecoration 否则会由于复用导致添加多个decoration
        for (int i = 0; i < recyclerView.getItemDecorationCount(); i++) {
            RecyclerView.ItemDecoration tDecoration = recyclerView.getItemDecorationAt(i);
            recyclerView.removeItemDecoration(tDecoration);
        }
        recyclerView.addItemDecoration(decoration);
        // 需要先移除已添加的 ItemDecoration 否则会由于复用导致添加多个ScrollListener
        recyclerView.clearOnScrollListeners();
        // 设置适配器
        recyclerView.setAdapter(adapter);
        // 设置曝光监听
        setupExposureTracking();
    }

    private void setupClickListeners() {
        // 标题点击（查看更多）
        titleView.setOnClickListener(v -> {
            if (skuList == null || skuList.size() < 4) return;
            if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                searchResultDataManager.searchResultReporter.dapeigouProductClickMore(currentWareInfo);
            }
            new DapeigouDialog(getContext(), skuList, currentWareInfoSkuId, searchResultDataManager.searchResultReporter).show();
            if (xiaoFeiDaPeiGouListener != null) {
                xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
            }
        });

        // 设置商品卡片点击事件
        adapter.setProductCardInterfaces(new ProductCardInterfaces() {
            @Override
            public int setCardAbilityType() {
                if (FloorInit.getFloorConfig().getProductShowFindSimilar()) {
                    return 0B11011;
                } else {// 不展示找相似按钮、找相似蒙层
                    return 0B11;
                }
            }

            @Override
            public void onCardClick(SkuInfoBean skuInfo) {
                if (skuInfo == null || NoDoubleClickUtils.isDoubleClick()) return;
                FloorJumpManager.getInstance().jumpProductDetail(mActivity, skuInfo, true, 0);
                if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                    searchResultDataManager.searchResultReporter.dapeigouProductClick(0, skuInfo, currentWareInfoSkuId);
                }
                if (xiaoFeiDaPeiGouListener != null) {
                    xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
                }
            }

            @Override
            public void onAddCartClick(SkuInfoBean skuInfo) {
                if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                    searchResultDataManager.searchResultReporter.dapeigouProductAddCart(0, skuInfo, currentWareInfoSkuId);
                }
                if (xiaoFeiDaPeiGouListener != null) {
                    xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
                }
            }
        });
    }

    // 设置曝光跟踪
    private void setupExposureTracking() {
        DapeigouProductExposureHelper exposureHelper = new DapeigouProductExposureHelper(searchResultDataManager.searchResultReporter);

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                exposureHelper.exposureByHand(recyclerView, newState, currentWareInfoSkuId);
            }
        });

        // 初始曝光
        recyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                exposureHelper.exposureByHand(recyclerView, RecyclerView.SCROLL_STATE_IDLE, currentWareInfoSkuId);
            }
        }, 100);
    }

    public void setXiaoFeiDaPeiGouListener(XiaoFeiDaPeiGouListener xiaoFeiDaPeiGouListener) {
        this.xiaoFeiDaPeiGouListener = xiaoFeiDaPeiGouListener;
    }

    /**
     * 点击搭配购商品/加购搭配购商品/点击查看更多按钮都认为是消费搭配购的行为
     */
    public interface XiaoFeiDaPeiGouListener {
        void xiaoFeiDaPeiGou();
    }
}