package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索结果数据实体类
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class SearchResultResponse implements Serializable {
    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 搜索场景词对象
     */
    private SearchSceneBean sceneWordVo;
    /**
     * 自定义筛选条件
     */
    private CustomFilterVo customizeFilterCriteriaVo;
    /**
     * 综合、价格排序筛选
     */
    private List<SearchFilterQuery> secondFilterCriteria;
    /**
     * 外露的筛选横条上的属性筛选条件数组
     */
    private List<SearchFilterQuery> showSearchFilerQuery;
    /**
     * 搜索结果信息
     */
    private SearchResultInfo searchResultInfoVo;
    /**
     * 搜索推荐数据
     */
    private List<SkuInfoBean> bottomProductCardInfo;
    /**
     * 相关词穿插
     */
    private SearchRelateWord relatedWords;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public SearchSceneBean getSceneWordVo() {
        return sceneWordVo;
    }

    public void setSceneWordVo(SearchSceneBean sceneWordVo) {
        this.sceneWordVo = sceneWordVo;
    }

    public CustomFilterVo getCustomizeFilterCriteriaVo() {
        return customizeFilterCriteriaVo;
    }

    public void setCustomizeFilterCriteriaVo(CustomFilterVo customizeFilterCriteriaVo) {
        this.customizeFilterCriteriaVo = customizeFilterCriteriaVo;
    }

    public List<SearchFilterQuery> getSecondFilterCriteria() {
        return secondFilterCriteria;
    }

    public void setSecondFilterCriteria(List<SearchFilterQuery> secondFilterCriteria) {
        this.secondFilterCriteria = secondFilterCriteria;
    }

    public List<SearchFilterQuery> getShowSearchFilerQuery() {
        return showSearchFilerQuery;
    }

    public void setShowSearchFilerQuery(List<SearchFilterQuery> showSearchFilerQuery) {
        this.showSearchFilerQuery = showSearchFilerQuery;
    }

    public SearchResultInfo getSearchResultInfoVo() {
        return searchResultInfoVo;
    }

    public void setSearchResultInfoVo(SearchResultInfo searchResultInfoVo) {
        this.searchResultInfoVo = searchResultInfoVo;
    }

    public List<SkuInfoBean> getBottomProductCardInfo() {
        return bottomProductCardInfo;
    }

    public void setBottomProductCardInfo(List<SkuInfoBean> bottomProductCardInfo) {
        this.bottomProductCardInfo = bottomProductCardInfo;
    }

    public SearchRelateWord getRelatedWords() {
        return relatedWords;
    }

    public void setRelatedWords(SearchRelateWord relatedWords) {
        this.relatedWords = relatedWords;
    }
}
