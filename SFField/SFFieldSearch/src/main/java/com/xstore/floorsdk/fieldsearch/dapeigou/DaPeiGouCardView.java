package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.List;

/**
 * 搭配购View-瀑布流-卡片式
 */
public class DaPeiGouCardView extends LinearLayout {

    private View moreView;
    private View titleView;
    private RecyclerView dapeigouView;
    private Activity activity;
    private SearchResultDataManager searchResultDataManager;
    private int position;
    private SkuInfoBean wareInfo;
    private XiaoFeiDaPeiGouListener xiaoFeiDaPeiGouListener;

    public DaPeiGouCardView(Context context) {
        super(context);
        init(context);
    }

    public DaPeiGouCardView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public DaPeiGouCardView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setOrientation(VERTICAL);
        LayoutInflater.from(context).inflate(R.layout.sf_field_search_item_dapeigou_card, this, true);

        titleView = findViewById(R.id.more_layout_title);
        moreView = findViewById(R.id.more_layout);
        dapeigouView = findViewById(R.id.dapeigou_recycler);

        LinearLayoutManager layoutManager = new LinearLayoutManager(context);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        dapeigouView.setLayoutManager(layoutManager);
    }

    public void bindData(Activity activity, SkuInfoBean wareInfo, int position, SearchResultDataManager dataManager) {
        this.activity = activity;
        this.wareInfo = wareInfo;
        this.position = position;
        this.searchResultDataManager = dataManager;

        List<SkuInfoBean> skuInfoBeans = wareInfo.getDapeigouList();
        if (skuInfoBeans == null || skuInfoBeans.isEmpty()) {
            return;
        }

        // 设置"更多"点击事件
        titleView.setOnClickListener(v -> {
            if (skuInfoBeans.size() < 4) {
                return;
            }
            if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                searchResultDataManager.searchResultReporter.dapeigouProductClickMore(wareInfo);
            }
            new DapeigouDialog(activity, skuInfoBeans, wareInfo.getSkuId(), searchResultDataManager.searchResultReporter).show();
            if (xiaoFeiDaPeiGouListener != null) {
                xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
            }
        });

        // "更多"按钮显示
        moreView.setVisibility(4 <= skuInfoBeans.size() ? View.VISIBLE : View.GONE);

        // 设置RecyclerView适配器
        List<SkuInfoBean> showItems = skuInfoBeans.subList(0, Math.min(3, skuInfoBeans.size()));
        DapeigouSingleLineAdapter adapter = new DapeigouSingleLineAdapter(activity, showItems);
        adapter.setProductCardInterfaces(createCardInterfaces());
        dapeigouView.setAdapter(adapter);

        // 曝光埋点,固定3条直接报
        dapeigouView.postDelayed(new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < showItems.size(); i++) {
                    if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                        searchResultDataManager.searchResultReporter.dapeigouProductExposure(i, showItems.get(i), wareInfo.getSkuId());
                    }
                }
            }
        }, 100);
    }

    private ProductCardInterfaces createCardInterfaces() {
        return new ProductCardInterfaces() {

            @Override
            public int setCardAbilityType() {
                return 0B1011;
            }

            @Override
            public void onCardClick(SkuInfoBean skuInfoBean) {
                if (skuInfoBean == null || NoDoubleClickUtils.isDoubleClick()) {
                    return;
                }
                FloorJumpManager.getInstance().jumpProductDetail(activity, skuInfoBean, true, 0);
                if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                    searchResultDataManager.searchResultReporter.dapeigouProductClick(position, skuInfoBean, wareInfo.getSkuId());
                }
                if (xiaoFeiDaPeiGouListener != null) {
                    xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
                }
            }

            @Override
            public void onAddCartClick(SkuInfoBean skuInfoBean) {
                if (searchResultDataManager != null && searchResultDataManager.searchResultReporter != null) {
                    searchResultDataManager.searchResultReporter.dapeigouProductAddCart(position, skuInfoBean, wareInfo.getSkuId());
                }
                if (xiaoFeiDaPeiGouListener != null) {
                    xiaoFeiDaPeiGouListener.xiaoFeiDaPeiGou();
                }
            }
        };
    }

    public void setXiaoFeiDaPeiGouListener(XiaoFeiDaPeiGouListener xiaoFeiDaPeiGouListener) {
        this.xiaoFeiDaPeiGouListener = xiaoFeiDaPeiGouListener;
    }

    /**
     * 点击搭配购商品/加购搭配购商品/点击查看更多按钮都认为是消费搭配购的行为
     */
    public interface XiaoFeiDaPeiGouListener {
        void xiaoFeiDaPeiGou();
    }

}