package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.holder.ProductCardDapeigouHolder;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.ArrayList;
import java.util.List;

public class DapeigouAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    /**
     * activity
     */
    private AppCompatActivity activity;
    /**
     * 布局加载器
     */
    private LayoutInflater inflater;
    /**
     * 商品数据列表
     */
    private List<SkuInfoBean> productList = new ArrayList<>();

    private int cardWidth;
    private int cardHeight;

    private ProductCardInterfaces productCardInterfaces;

    public void setProductCardInterfaces(ProductCardInterfaces productCardInterfaces) {
        this.productCardInterfaces = productCardInterfaces;
    }

    public DapeigouAdapter(Activity activity, int cardWidth, int cardHeight, List<SkuInfoBean> skuInfoBeans) {
        this.activity = (AppCompatActivity) activity;
        this.inflater = LayoutInflater.from(activity);
        this.cardWidth = cardWidth;
        this.cardHeight = cardHeight;
        this.productList = skuInfoBeans;
    }

//    public void setData(List<SkuInfoBean> skuInfoBeans) {
//        if (productList != null) {
//            productList.clear();
//        } else {
//            productList = new ArrayList<>();
//        }
//        productList.addAll(skuInfoBeans);
//        try {
//            notifyDataSetChanged();
//        } catch (Exception e) {
//            JdCrashReport.postCaughtException(e,"dapeigouAdapter setData");
//        }
//    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View productView = inflater.inflate(R.layout.sf_card_product_card_dapeigou_item, parent, false);
        return new ProductCardDapeigouHolder(productView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        try {
            SkuInfoBean skuInfoBean = productList.get(position);
            skuInfoBean.setPageIndex(position + 1 + "");
            if (holder instanceof ProductCardDapeigouHolder) {
                ProductCardDapeigouHolder dapeigouHolder = (ProductCardDapeigouHolder) holder;
                dapeigouHolder.bindData(activity, skuInfoBean, productCardInterfaces);
                if (cardHeight != 0 && cardWidth != 0) {
                    dapeigouHolder.setCardSize(activity, cardWidth, cardHeight);
                }
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e,"dapeigouAdapter");
        }
    }

    @Override
    public int getItemCount() {
        return productList.size();
    }

    public SkuInfoBean getItem(int position) {
        if (productList == null) {
            return null;
        }
        if (position < 0 || productList.size() <= position) {
            return null;
        }
        return productList.get(position);
    }
}
