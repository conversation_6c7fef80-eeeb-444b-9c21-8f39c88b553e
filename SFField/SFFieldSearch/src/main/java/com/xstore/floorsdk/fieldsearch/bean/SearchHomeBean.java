package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sdk.floor.floorcore.bean.BaseData;

/**
 * 搜索主页数据
 */
public class SearchHomeBean extends BaseData {

    /**
     * 排行榜
     */
    private RankListResponseData rankBaseInfoList;

    /**
     * 常购清单
     */
    private SearchHomeFrequentPurchaseBean queryFrequentPurchasePage;

    /**
     * 搜索热词
     */
    private SearchHomeHotWordBean searchNewHotWord;

    public RankListResponseData getRankBaseInfoList() {
        return rankBaseInfoList;
    }

    public void setRankBaseInfoList(RankListResponseData rankBaseInfoList) {
        this.rankBaseInfoList = rankBaseInfoList;
    }

    public SearchHomeFrequentPurchaseBean getQueryFrequentPurchasePage() {
        return queryFrequentPurchasePage;
    }

    public void setQueryFrequentPurchasePage(SearchHomeFrequentPurchaseBean queryFrequentPurchasePage) {
        this.queryFrequentPurchasePage = queryFrequentPurchasePage;
    }

    public SearchHomeHotWordBean getSearchNewHotWord() {
        return searchNewHotWord;
    }

    public void setSearchNewHotWord(SearchHomeHotWordBean searchNewHotWord) {
        this.searchNewHotWord = searchNewHotWord;
    }
}
