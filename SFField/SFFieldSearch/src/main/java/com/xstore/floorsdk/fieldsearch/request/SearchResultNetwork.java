package com.xstore.floorsdk.fieldsearch.request;

import android.content.Context;

import com.jd.framework.json.JDJSONObject;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

/**
 * 搜索结果页数据请求
 *
 * <AUTHOR>
 * @date 2022/09/26
 */
public class SearchResultNetwork {

    /**
     * 搜索请求
     *
     * @param context
     * @param functionId
     * @param effect
     * @param varsExtra
     * @param callback
     */
    public static void requestPost(Context context, String functionId, int effect, JDJSONObject varsExtra, BaseFreshResultCallback callback) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setFunctionId(functionId);
        httpSetting.setEffect(effect);
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        if (varsExtra != null) {
            httpSetting.getJsonParams().putAll(varsExtra);
        }
        httpSetting.setResultCallback(callback);
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }

    public static void requestDiscovery(Context context,BaseFreshResultCallback callback) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setFunctionId("omnitech_search_searchDiscovery");
        httpSetting.setEffect(FreshHttpSetting.NO_EFFECT);
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setResultCallback(callback);
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }
}
