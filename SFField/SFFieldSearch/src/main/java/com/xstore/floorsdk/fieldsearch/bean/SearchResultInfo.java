package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索结果信息
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class SearchResultInfo implements Serializable {

    /**
     * 总数量
     */
    private int totalCount;
    /**
     * 当前页码
     */
    private int page;
    /**
     * 每页数据量
     */
    private int pageSize;
    /**
     * 总页数
     */
    private int totalPage;
    /**
     * 商品集合
     */
    private List<SkuInfoBean> productCardVoList;
    /**
     * 时效云卖场商品和标题信息
     */
    private PrescriptionCloudStoreVo prescriptionCloudStoreVo;
    /**
     * 时效云卖场顺序从0开始
     */
    private int prescriptionCloudStoreIndex;
    /**
     * 搜索条件数组
     */
    private List<SearchFilterQuery> filterQueries;
    /**
     * 关键词跳转url
     */
    private String jumpUrl;
    /**
     * 平铺类目列表
     */
    private List<SearchCategory> tileCategoryList;
    /**
     * 扩词搜索信息
     */
    private WordSearch wordSearchVo;
    /**
     * 搜索纠错
     */
    private ExpandQuery expandQueryVo;
    /**
     * 试试搜 关键字列表
     */
    private List<String> trySearchWords;
    /**
     * 价格区间范围列表
     */
    private List<SearchFilterQuery> priceRangeList;
    //query高相关1/2/3/4级类目(7fresh)
    private String hcCid1s;
    private String hcCid2s;
    private String hcCid3s;
    private String hcCid4s;
    /**
     * 排序策略AB(搜索算法标志 用于埋点上传)
     */
    private String mtest = "";
    /**
     * 券搜限制提示
     */
    private String couponLimitDesc;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public List<SkuInfoBean> getProductCardVoList() {
        return productCardVoList;
    }

    public void setProductCardVoList(List<SkuInfoBean> productCardVoList) {
        this.productCardVoList = productCardVoList;
    }

    public PrescriptionCloudStoreVo getPrescriptionCloudStoreVo() {
        return prescriptionCloudStoreVo;
    }

    public void setPrescriptionCloudStoreVo(PrescriptionCloudStoreVo prescriptionCloudStoreVo) {
        this.prescriptionCloudStoreVo = prescriptionCloudStoreVo;
    }

    public int getPrescriptionCloudStoreIndex() {
        return prescriptionCloudStoreIndex;
    }

    public void setPrescriptionCloudStoreIndex(int prescriptionCloudStoreIndex) {
        this.prescriptionCloudStoreIndex = prescriptionCloudStoreIndex;
    }

    public List<SearchFilterQuery> getFilterQueries() {
        return filterQueries;
    }

    public void setFilterQueries(List<SearchFilterQuery> filterQueries) {
        this.filterQueries = filterQueries;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public List<SearchCategory> getTileCategoryList() {
        return tileCategoryList;
    }

    public void setTileCategoryList(List<SearchCategory> tileCategoryList) {
        this.tileCategoryList = tileCategoryList;
    }

    public WordSearch getWordSearchVo() {
        return wordSearchVo;
    }

    public void setWordSearchVo(WordSearch wordSearchVo) {
        this.wordSearchVo = wordSearchVo;
    }

    public ExpandQuery getExpandQueryVo() {
        return expandQueryVo;
    }

    public void setExpandQueryVo(ExpandQuery expandQueryVo) {
        this.expandQueryVo = expandQueryVo;
    }

    public List<String> getTrySearchWords() {
        return trySearchWords;
    }

    public void setTrySearchWords(List<String> trySearchWords) {
        this.trySearchWords = trySearchWords;
    }

    public List<SearchFilterQuery> getPriceRangeList() {
        return priceRangeList;
    }

    public void setPriceRangeList(List<SearchFilterQuery> priceRangeList) {
        this.priceRangeList = priceRangeList;
    }

    public String getHcCid1s() {
        return hcCid1s;
    }

    public void setHcCid1s(String hcCid1s) {
        this.hcCid1s = hcCid1s;
    }

    public String getHcCid2s() {
        return hcCid2s;
    }

    public void setHcCid2s(String hcCid2s) {
        this.hcCid2s = hcCid2s;
    }

    public String getHcCid3s() {
        return hcCid3s;
    }

    public void setHcCid3s(String hcCid3s) {
        this.hcCid3s = hcCid3s;
    }

    public String getHcCid4s() {
        return hcCid4s;
    }

    public void setHcCid4s(String hcCid4s) {
        this.hcCid4s = hcCid4s;
    }

    public String getMtest() {
        return mtest;
    }

    public void setMtest(String mtest) {
        this.mtest = mtest;
    }

    public String getCouponLimitDesc() {
        return couponLimitDesc;
    }

    public void setCouponLimitDesc(String couponLimitDesc) {
        this.couponLimitDesc = couponLimitDesc;
    }
}
