package com.xstore.floorsdk.fieldsearch.cloudstore;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.holder.ProductListViewHolder;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 云卖场入口竖向商品适配器
 *
 * <AUTHOR>
 * @date 2022/09/25
 */
public class YunProductVerticalAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private AppCompatActivity activity;
    private SearchResultReporter searchResultReporter;
    private List<SkuInfoBean> productInfos;

    public YunProductVerticalAdapter(AppCompatActivity activity, SearchResultReporter searchResultReporter, List<SkuInfoBean> productInfos) {
        this.activity = activity;
        this.searchResultReporter = searchResultReporter;
        this.productInfos = productInfos;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View productListView = LayoutInflater.from(activity).inflate(R.layout.sf_card_product_list_item, parent, false);
        return new ProductListViewHolder(productListView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        SkuInfoBean productInfo = productInfos.get(position);
        if (productInfo == null) {
            return;
        }
        if (holder instanceof ProductListViewHolder) {
            ProductListViewHolder productListViewHolder = (ProductListViewHolder) holder;
            productListViewHolder.setCardHeightAndLeftMargin(135, 10, 14, 4, 105);
            productListViewHolder.bindData(activity, productInfo, new ProductCardInterfaces() {
                @Override
                public int setCardAbilityType() {
                    return 0B1011;
                }

                @Override
                public void onCardClick(SkuInfoBean skuInfoVoBean) {
                    if (NoDoubleClickUtils.isDoubleClick()) {
                        return;
                    }
                    if (skuInfoVoBean == null) {
                        return;
                    }
                    FloorJumpManager.getInstance().jumpProductDetail(activity, skuInfoVoBean, true);
                    searchResultReporter.cloudSkuClick(skuInfoVoBean, position);
                }

                @Override
                public void onAddCartClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean != null) {
                        searchResultReporter.cloudSkuAddCart(skuInfoVoBean, position);
                    }
                }

                @Override
                public void findSimilarClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean != null) {
                        FloorJumpManager.getInstance().jumpSimilarList(activity, skuInfoVoBean.getSkuId(), "2");
                        searchResultReporter.clickFindSimilar(skuInfoVoBean.getSkuId(), skuInfoVoBean.getSkuName(), 2);
                    }
                }

                @Override
                public void bookNowClick(SkuInfoBean skuInfoVoBean) {
                    FloorJumpManager.getInstance().preSaleJustNow(activity, skuInfoVoBean);
                    searchResultReporter.bookNow(skuInfoVoBean, 1);
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return productInfos == null ? 0 : productInfos.size();
    }
}
