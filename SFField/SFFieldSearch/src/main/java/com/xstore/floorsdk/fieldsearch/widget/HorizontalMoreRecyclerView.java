package com.xstore.floorsdk.fieldsearch.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.widget.BaseHeaderFooterRecyclerAdapter;


public class HorizontalMoreRecyclerView extends RecyclerView {

    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;

    private OnItemClickLitener mOnItemClickLitener;
    private OnScrollLitenerDistance mOnScrollLitenerDistance;
    private boolean mFirstLayout = true;
    protected int mItemWidth;
    private float edgeWidth = ScreenUtils.dip2px(getContext(),55);// view外部边距之和
    private boolean goRedirect = true;
    protected int footerWidth;
    float mLastX;
    float width;
    private static final float OFFSET_RADIO = 2.0f;
    private boolean checkForReset = false;
    private int mode = NONE;

    private OnScrollListener onScrollListener = new OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (checkForReset) {
                    checkForReset();
                }
            }
        }

    };

    public HorizontalMoreRecyclerView(Context context) {
        super(context);
        initView();
    }

    public HorizontalMoreRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    /**
     * 重新设置控件的外部边距之和
     * @param edgeWidth
     */
    public void resetEdgeWidth(float edgeWidth){
        this.edgeWidth = edgeWidth;
    }

    private void initView() {
        this.setOnScrollListener(onScrollListener);
        mItemWidth = ScreenUtils.dip2px(getContext(), 130);
        footerWidth = ScreenUtils.dip2px(getContext(), 67);
    }

    private int lastX, lastY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        int x = (int) ev.getX();
        int y = (int) ev.getY();
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                getParent().requestDisallowInterceptTouchEvent(false);
                break;
            case MotionEvent.ACTION_MOVE:
                int deltaX = x - lastX;
                int deltaY = y - lastY;
                if (Math.abs(deltaX) > Math.abs(deltaY) + 5) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                }
                break;
            default:
        }
        lastX = x;
        lastY = y;
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {

        if (!goRedirect) {
            try {
                return super.onTouchEvent(e);
            } catch (Throwable th) {
                return false;
            }
        }

        if (mLastX == -1) {
            mLastX = e.getRawX();
        }

        int canJump = 0;

        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mLastX = e.getRawX();
                mode = DRAG;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                mLastX = e.getRawX();
                mode = ZOOM;
                break;
            case MotionEvent.ACTION_MOVE:
                float deltaX = 0f;
                if (mode == DRAG) {
                    deltaX = e.getRawX() - mLastX;
                } else if (e.getPointerCount() == 2) {
                    float x1 = e.getX(0) - mLastX;
                    float x2 = e.getX(1) - mLastX;
                    deltaX = Math.min(x1, x2);
                }
                deltaX = e.getRawX() - mLastX;

                mLastX = e.getRawX();

                try {
                    if (null != this.getLayoutManager()) {
                        int lastCompletelyVisibleItemPosition = ((LinearLayoutManager) this.getLayoutManager()).findLastVisibleItemPosition();
                        if (lastCompletelyVisibleItemPosition == this.getAdapter().getItemCount() - 1) {
                            //当recycleview  小于一屏幕的时候
                            if(getRight()<(ScreenUtils.getScreenWidth(getContext())-edgeWidth)){
                                break;
                            }
                            float left = this.getLayoutManager().findViewByPosition(lastCompletelyVisibleItemPosition).getLeft();
                            //到达最底部，最后一个item为footerView
                            width += -deltaX / OFFSET_RADIO;
                            if (width > footerWidth) {
                                width = footerWidth;
                            }
                            if (mOnScrollLitenerDistance != null) {
                                mOnScrollLitenerDistance.onScrollX((int)(left - getRight()));
                            }
                            resetMoreView((int) width);
                        }
                    }
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
                break;
            case MotionEvent.ACTION_POINTER_UP:
            case MotionEvent.ACTION_UP:
                try {
                    if (null != this.getLayoutManager()) {
                        int lastCompletelyVisibleItemPosition2 = ((LinearLayoutManager) this.getLayoutManager()).findLastVisibleItemPosition();

                        if (lastCompletelyVisibleItemPosition2 == this.getAdapter().getItemCount() - 1) {
                            if (width >=120) {
                                canJump = 1;
                                jump();
                            }
                        }
                        resetMoreView(0);
                        if (mOnScrollLitenerDistance != null) {
                            mOnScrollLitenerDistance.onScrollXSmall();
                        }
                    }
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
                width = 1;
                mLastX = -1;
                break;
            case MotionEvent.ACTION_CANCEL:
                if (canJump == 1) {
                    jump();
                }
                resetMoreView(0);
            default:
                resetMoreView(0);
                mLastX = -1;
                width = 1;
                break;
        }
        try {
            return super.onTouchEvent(e);
        } catch (Throwable th) {
            return false;
        }
    }

    private void jump() {
        if (null != mOnItemClickLitener && mOnScrollLitenerDistance != null) {
            mOnItemClickLitener.onItemClick();
        }
    }


    private void resetMoreView(final int width) {
        post(new Runnable() {
            @Override
            public void run() {
                if (getAdapter() != null && (getAdapter() instanceof BaseHeaderFooterRecyclerAdapter) && ((BaseHeaderFooterRecyclerAdapter) getAdapter()).getFooterView() != null) {
                    View footView = ((BaseHeaderFooterRecyclerAdapter) getAdapter()).getFooterView();


                    TextView blank = (TextView) footView.findViewById(R.id.more_blank);
//                    TextView blankLeft = (TextView) footView.findViewById(R.id.more_blank_left);

                    if (blank != null && mOnScrollLitenerDistance != null) {
                        blank.setPadding(0, 0, width, 0);
                    }

                }
            }
        });
    }


    private void checkForReset() {
        // 获取第一个Item的left
        int left = getChildAt(0).getLeft();
        if (left == 0) {
            return;
        }

        if (Math.abs(left) > mItemWidth >> 1) {
            int position = ((LinearLayoutManager) this.getLayoutManager()).findLastVisibleItemPosition();
            if (position < this.getAdapter().getItemCount() - 1) {
                smoothScrollBy(mItemWidth - Math.abs(left), 0);
            }
        } else {
            smoothScrollBy(-Math.abs(left), 0);
        }

    }

    public void setGoRedirect(boolean goRedirect) {
        this.goRedirect = goRedirect;
    }

    @Override
    protected void onAttachedToWindow() {
        if (mFirstLayout) {
            super.onAttachedToWindow();
        }
        mFirstLayout = false;
    }

    public interface OnItemClickLitener {
        void onItemClick();
    }

    public interface OnScrollLitenerDistance {
        void onScrollX(int scrollX);

        void onScrollXSmall();
    }

    public void setmOnScrollLitenerDistance(OnScrollLitenerDistance mOnScrollLitenerDistance) {
        this.mOnScrollLitenerDistance = mOnScrollLitenerDistance;
    }

    public void setOnLastItemClickLitener(OnItemClickLitener mOnItemClickLitener) {
        this.mOnItemClickLitener = mOnItemClickLitener;
    }

    public void setCheckForReset(boolean checkForReset) {
        this.checkForReset = checkForReset;
    }
}