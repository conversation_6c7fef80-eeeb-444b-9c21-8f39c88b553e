package com.xstore.floorsdk.fieldsearch.ma;

import android.text.TextUtils;

import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.xstore.floorsdk.fieldsearch.SearchConstant;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.datareport.utils.JsonUtils;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;

import java.util.HashMap;

/**
 * 搜索结果埋点类
 *
 * <AUTHOR>
 * @date 2022/09/13
 */
public class SearchResultReporter {

    private SearchResultDataManager searchResultDataManager;
    private int fromType;
    private ProductDetailBean.WareInfoBean.PromotionTypesBean promotionTypesBean;
    private JDMaUtils.JdMaPageImp jdMaPageImp;

    public SearchResultReporter(SearchResultDataManager searchResultDataManager) {
        this.searchResultDataManager = searchResultDataManager;
    }

    public void initData(int fromType, ProductDetailBean.WareInfoBean.PromotionTypesBean promotionTypesBean) {
        this.fromType = fromType;
        this.promotionTypesBean = promotionTypesBean;
        this.jdMaPageImp = searchResultDataManager.getJdMaPageImp();
    }

    private void clearReporter() {
        fromType = 0;
        promotionTypesBean = null;
        jdMaPageImp = null;
    }

    private String getPromotionSubType() {
        if (fromType == SearchConstant.Value.FROM_TYPE_SEARCH) {
            return "0";
        } else if (fromType == SearchConstant.Value.FROM_TYPE_COUPON) {
            return "1";
        } else if (fromType == SearchConstant.Value.FROM_TYPE_PROMOTION) {
            return promotionTypesBean == null ? "" : promotionTypesBean.getPromotionSubType();
        }
        return "";
    }

    /**
     * 履约时效
     *
     * @param promiseType
     */
    public void clickDelivery(String promiseType) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("promiseType", promiseType);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_promiseTypeSelect", jdMaPageImp, baseMaEntity);
    }

    /**
     * 大促活动
     *
     * @param promotionButton
     */
    public void clickActivity(String promotionButton) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("promotionButton", promotionButton);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_promotionButton", jdMaPageImp, baseMaEntity);
    }

    /**
     * 筛选面板大促活动
     *
     * @param promotionButton
     */
    public void clickFragmentActivity(String promotionButton) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("promotionButton", promotionButton);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_screenPage_promotionButton", jdMaPageImp, baseMaEntity);
    }

    /**
     * 打开筛选面板
     */
    public void openFragmentFilter() {
        JDMaUtils.save7FClick("searchListPage_sortArea", jdMaPageImp, null);
    }

    /**
     * 点击外露筛选
     *
     * @param filterLable
     */
    public void clickFilter(String filterLable) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("sortArea", filterLable);
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_skuList_sortClick", jdMaPageImp, baseMaEntity);
    }

    /**
     * 点击外露筛选下拉框子条件
     *
     * @param filterLable
     * @param filterValue
     */
    public void clickFilterQuery(String filterLable, String filterValue) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("sortArea", filterLable);
        hashMap.put("sortValue", filterValue);
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_skuList_sortArea_selectSort", jdMaPageImp, baseMaEntity);
    }

    /**
     * 点击外露筛选下拉框确认按钮
     *
     * @param filterLable
     * @param filterValueArray
     */
    public void clickFilterQueryConfirm(String filterLable, JDJSONArray filterValueArray) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("sortArea", filterLable);
        hashMap.put("sortValue", filterValueArray.toString());
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_skuList_sortArea_sortConfirm", jdMaPageImp, baseMaEntity);
    }

    /**
     * 筛选侧面板筛选条件曝光
     *
     * @param filterLableArray
     * @param filterValueArray
     */
    public void exposureFragmentFilter(JDJSONArray filterLableArray, JDJSONArray filterValueArray) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("sortArea", filterLableArray.toString());
        jdjsonObject.put("sortValue", filterValueArray.toString());
        jdjsonObject.put("enkwd", searchResultDataManager.searchKeyword);
        JDMaUtils.save7FExposure("searchListPage_screenPage_sortExpose", null, null, jdjsonObject.toString(), jdMaPageImp);
    }
    /**
     * 筛选侧面板筛选条件点击埋点
     *
     * @param parentSearchFilterQuery
     * @param childSearchFilterQuery
     */
    public void exposureFragmentFilterClick(SearchFilterQuery parentSearchFilterQuery, SearchFilterQuery childSearchFilterQuery) {
        if (parentSearchFilterQuery == null || childSearchFilterQuery == null) {
            return;
        }

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("parentfilterLable", parentSearchFilterQuery.getFilterLable());
        hashMap.put("parentfilterKey", parentSearchFilterQuery.getFilterKey());
        hashMap.put("filterLable", childSearchFilterQuery.getFilterLable());
        hashMap.put("filterKey", childSearchFilterQuery.getFilterKey());
        hashMap.put("filterValue", childSearchFilterQuery.getFilterValue());
        hashMap.put("name", childSearchFilterQuery.getName());
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_skuList_sortArea_selectSort_drawer", jdMaPageImp, baseMaEntity);
    }

    /**
     * 点击筛选侧面板确认
     *
     * @param filterLableArray
     * @param filterValueArray
     */
    public void clickFragmentFilterConfirm(JDJSONArray filterLableArray, JDJSONArray filterValueArray) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("sortArea", filterLableArray.toString());
        hashMap.put("sortValue", filterValueArray.toString());
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_screenPage_sortConfirm", jdMaPageImp, baseMaEntity);
    }

    /**
     * 点击筛选侧面板筛选项展开收起
     *
     * @param filterLable
     * @param type        1 收起，2 展开
     */
    public void clickFragmentFilterExpand(String filterLable, String type) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("sortArea", filterLable);
        hashMap.put("type", type);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_screenPage_fold", jdMaPageImp, baseMaEntity);
    }

    /**
     * 场景词曝光
     */
    public void exposureScene() {
        JDJSONObject jsonParam = new JDJSONObject();
        jsonParam.put("hotSearch", searchResultDataManager.searchKeyword);
        jsonParam.put("rank", searchResultDataManager.rank + "");
        JDMaUtils.save7FExposure("searchListPage_hotSearchExpose", null, null, jsonParam.toString(), jdMaPageImp);
    }

    /**
     * 云卖场商品加车
     *
     * @param productInfoBean
     * @param position
     */
    public void cloudSkuAddCart(SkuInfoBean productInfoBean, int position) {
        JDJSONObject cloudSkuParamMap = searchResultDataManager.searchResultListExposureHelper.getCloudReportParam(productInfoBean, position);
        cloudSkuParamMap.put("clickType", "1");
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "1";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(cloudSkuParamMap.toJSONString()));
        JDMaUtils.save7FClick("searchListPage_skuList_jdBuy_addCart", "", productInfoBean.getSkuId(), null,
                jdMaPageImp, baseMaEntity);
    }

    /**
     * 云卖场商品点击进商详
     *
     * @param productInfoBean
     * @param position
     */
    public void cloudSkuClick(SkuInfoBean productInfoBean, int position) {
        JDJSONObject cloudSkuParamMap = searchResultDataManager.searchResultListExposureHelper.getCloudReportParam(productInfoBean, position);
        cloudSkuParamMap.put("clickType", "2");
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "2";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(cloudSkuParamMap.toJSONString()));
        JDMaUtils.save7FClick("searchListPage_skuList_jdBuy_clickCommodity", "", productInfoBean.getSkuId(), null,
                jdMaPageImp, baseMaEntity);
    }

    /**
     * 穿插词试试搜
     *
     * @param word
     */
    public void clickTrySearch(String word) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("relevantSearchWords", word);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_relevantSearchWords_clickSearchWords", jdMaPageImp, baseMaEntity);
    }

    /**
     * 类目平铺曝光
     *
     * @param categoryFlatKeyWord
     * @param position
     */
    public void exposureCategory(String categoryFlatKeyWord, int position) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("categoryFlatKeyWord", categoryFlatKeyWord);
        jdjsonObject.put("listPageIndex", (position + 1) + "");
        JDMaUtils.save7FExposure("searchListPage_categoryFlat_categoryExpose", hashMap, null, jdjsonObject.toString(), jdMaPageImp);
    }

    /**
     * 类目平铺点击
     *
     * @param categoryFlatKeyWord
     * @param position
     */
    public void clickCategory(String categoryFlatKeyWord, int position) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("categoryFlatKeyWord", categoryFlatKeyWord);
        hashMap.put("listPageIndex", (position + 1) + "");
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_categoryFlat_clickCategory", jdMaPageImp, baseMaEntity);
    }

    /**
     * 底部推荐商品加车
     *
     * @param productInfoBean
     * @param position
     */
    public void recommendSkuAddCart(SkuInfoBean productInfoBean, int position) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        if (!TextUtils.isEmpty(productInfoBean.getSkuId())) {
            jdjsonObject.put("skuId", productInfoBean.getSkuId());
        } else {
            jdjsonObject.put("skuId", "");
        }
        if (!TextUtils.isEmpty(productInfoBean.getSkuName())) {
            jdjsonObject.put("skuName", productInfoBean.getSkuName());
        } else {
            jdjsonObject.put("skuName", "");
        }
        if (productInfoBean.getSalePrice() != null && !TextUtils.isEmpty(productInfoBean.getSalePrice().getValue())) {
            jdjsonObject.put("price", productInfoBean.getSalePrice().getValue());
        } else {
            jdjsonObject.put("price", "");
        }
        jdjsonObject.put("listPageIndex", (position + 1) + "");
        jdjsonObject.put("skuStockStatus", productInfoBean.getStatus() + "");
        jdjsonObject.put("clickType", "1");
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "1";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(jdjsonObject.toJSONString()));
        JDMaUtils.save7FClick("searchListPage_recommend_addCart", "", productInfoBean.getSkuId(), null,
                jdMaPageImp, baseMaEntity);
    }

    /**
     * 底部推荐商品点击进商详
     *
     * @param productInfoBean
     * @param position
     */
    public void recommendSkuClick(SkuInfoBean productInfoBean, int position) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        if (!TextUtils.isEmpty(productInfoBean.getSkuId())) {
            jdjsonObject.put("skuId", productInfoBean.getSkuId());
        } else {
            jdjsonObject.put("skuId", "");
        }
        if (!TextUtils.isEmpty(productInfoBean.getSkuName())) {
            jdjsonObject.put("skuName", productInfoBean.getSkuName());
        } else {
            jdjsonObject.put("skuName", "");
        }
        if (productInfoBean.getSalePrice() != null && !TextUtils.isEmpty(productInfoBean.getSalePrice().getValue())) {
            jdjsonObject.put("price", productInfoBean.getSalePrice().getValue());
        } else {
            jdjsonObject.put("price", "");
        }
        jdjsonObject.put("listPageIndex", (position + 1) + "");
        jdjsonObject.put("skuStockStatus", productInfoBean.getStatus() + "");
        jdjsonObject.put("clickType", "2");
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "2";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(jdjsonObject.toJSONString()));
        JDMaUtils.save7FClick("searchListPage_recommend_clickCommodity", "", productInfoBean.getSkuId(), null,
                jdMaPageImp, baseMaEntity);
    }

    /**
     * 搜索无结果词上报
     */
    public void searchNoData() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_skuList_nonresult", searchResultDataManager.searchKeyword,
                "", null, jdMaPageImp, baseMaEntity);
    }

    /**
     * 无结果试试搜
     *
     * @param word
     */
    public void clickNoDataTrySearch(String word) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("keyword", word);
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_nonResult_clickKeyWord", jdMaPageImp, baseMaEntity);
    }

    /**
     * 无货相似推荐曝光
     *
     * @param lastSkuId
     * @param lastSkuName
     * @param mode
     */
    public void exposureUnStockSimilar(String lastSkuId, String lastSkuName, int mode) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("lastSkuId", lastSkuId);
        jdjsonObject.put("lastSkuName", lastSkuName);
        jdjsonObject.put("mode", mode);
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("mode", mode + "");
        hashMap.put("mtest", searchResultDataManager.mtest);
        JDMaUtils.save7FExposure("Searchlist_noStock_similarSku", hashMap, null, jdjsonObject.toString(), jdMaPageImp);
    }

    /**
     * 点击进入找相似页面
     *
     * @param lastSkuId
     * @param lastSkuName
     * @param mode
     */
    public void clickFindSimilar(String lastSkuId, String lastSkuName, int mode) {
        SearchResultMaEntity entity = new SearchResultMaEntity();
        entity.setPublicParam(new SearchResultMaEntity.Constants.SEARCHLISTPAGE_FINDSIMILAR_CLICKINTOLANDINGPAGE());
        entity.lastSkuId = lastSkuId;
        entity.lastSkuName = lastSkuName;
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("mode", mode);
        hashMap.put("mtest", searchResultDataManager.mtest);
        entity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick(SearchResultMaEntity.Constants.SEARCHLISTPAGE_FINDSIMILAR_CLICKINTOLANDINGPAGE.CLICKID, "", "", null, jdMaPageImp, entity);
    }

    /**
     * 找相似浮层关闭
     *
     * @param lastSkuId
     * @param lastSkuName
     * @param mode
     */
    public void clickFindSimilarClose(String lastSkuId, String lastSkuName, int mode) {
        SearchResultMaEntity entity = new SearchResultMaEntity();
        entity.setPublicParam(new SearchResultMaEntity.Constants.SEARCHLISTPAGE_FINDSIMILAR_CLOSE());
        entity.lastSkuId = lastSkuId;
        entity.lastSkuName = lastSkuName;
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("mode", mode);
        hashMap.put("mtest", searchResultDataManager.mtest);
        entity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick(SearchResultMaEntity.Constants.SEARCHLISTPAGE_FINDSIMILAR_CLOSE.CLICKID, "", "", null, jdMaPageImp, entity);
    }

    /**
     * 到货提醒
     *
     * @param skuId
     * @param skuName
     */
    public void remindMeStock(String skuId, String skuName) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("skuId", skuId);
        hashMap.put("skuName", skuName);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_recommend_remindMeStock", jdMaPageImp, baseMaEntity);
    }

    /**
     * 立即预定
     *
     * @param skuInfoVoBean 商品组模型
     * @param type          默认 0表示无 搜索列表 1 搜索卡片 2
     */
    public void bookNow(SkuInfoBean skuInfoVoBean, int type) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("skuId", skuInfoVoBean.getSkuId());
        hashMap.put("skuName", skuInfoVoBean.getSkuName());
        hashMap.put("saleStockStatus", skuInfoVoBean.getStatus());

        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "5";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(hashMap);
        String eventId = "searchListPage_skuList_bookNow_addCart";
        switch (fromType) {
            case SearchConstant.Value.FROM_TYPE_SEARCH:
                eventId = "searchListPage_skuList_bookNow_addCart";
                hashMap.put("mode", type);
                break;
            case SearchConstant.Value.FROM_TYPE_COUPON:
                eventId = "searchListPage_promotionWithCouponSkuList_bookNow_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
                eventId = "searchListPage_normalPromotionSkuList_bookNow_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                eventId = "searchListPage_promotionWithRisePriceSkuList_bookNow_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                eventId = "searchListPage_linghuomaSkuList_bookNow_addCart";
                break;
            default:
                break;
        }
        JDMaUtils.save7FClick(eventId, jdMaPageImp, baseMaEntity);
    }

    /**
     * 仍要搜索
     */
    public void clickStillSearch() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("enkwd", searchResultDataManager.searchKeyword);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        baseMaEntity.setMa7FextParam(hashMap);
        JDMaUtils.save7FClick("searchListPage_correctSearchWord_searchOldWord", jdMaPageImp, baseMaEntity);
    }

    /**
     * 纠错词曝光
     *
     * @param rightWord
     */
    public void exposureErrorWord(String rightWord) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("enkwd", searchResultDataManager.searchKeyword);
        jdjsonObject.put("keyword", rightWord);
        JDMaUtils.save7FExposure("searchListPage_correctSearchWord", null, null, jdjsonObject.toString(), jdMaPageImp);
    }

    /**
     * 搜索滑词曝光
     *
     * @param newWord
     */
    public void exposureExpandWord(String newWord) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("enkwd", searchResultDataManager.searchKeyword);
        jdjsonObject.put("keyword", newWord);
        JDMaUtils.save7FExposure("searchListPage_suggestedWord", null, null, jdjsonObject.toString(), jdMaPageImp);
    }

    /**
     * 价格带点击
     *
     * @param priceRange
     */
    public void clickPriceRange(String priceRange) {
        SearchResultMaEntity entity = new SearchResultMaEntity();
        if (fromType == SearchConstant.Value.FROM_TYPE_FREIGHT) {
            entity.tabName = priceRange;
            JDMaUtils.save7FClick(SearchResultMaConstants.SEARCHLISTPAGE_CHARGE_CLICKRANGE, jdMaPageImp, entity);
        } else {
            entity.promotionType = getPromotionSubType();
            entity.price = priceRange;
            JDMaUtils.save7FClick(SearchResultMaConstants.SEARCHLISTPAGE_PRICEBAND, jdMaPageImp, entity);
        }
    }

    /**
     * 去换购/重新换购点击
     *
     * @param buttonStatus
     */
    public void clickRepurchase(String buttonStatus) {
        SearchResultMaEntity entity = new SearchResultMaEntity();
        entity.setPublicParam(new SearchResultMaEntity.Constants.SEARCH_LIST_EXCHANGEBUTTON());
        entity.buttonStatus = buttonStatus;
        entity.promotionType = getPromotionSubType();
        JDMaUtils.save7FClick(SearchResultMaEntity.Constants.SEARCH_LIST_EXCHANGEBUTTON.CLICKID, jdMaPageImp, entity);
    }

    /**
     * 合计栏的购物车按钮点击
     */
    public void clickGotoCart() {
        JDMaUtils.save7FClick("searchListPage_normalPromotionSkuList_toCart", jdMaPageImp, null);
    }

    /**
     * 点击榜单
     *
     * @param productInfoBean
     * @param marketEntrance
     * @param fromType
     */
    public void clickRank(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance, int fromType) {
        if (productInfoBean == null || marketEntrance == null) {
            return;
        }
        RankMaEntity rankMaEntity = new RankMaEntity();
        rankMaEntity.skuId = productInfoBean.getSkuId();
        rankMaEntity.skuName = productInfoBean.getSkuName();
        rankMaEntity.rankName = marketEntrance.getText();
        rankMaEntity.rankSortId = String.valueOf(marketEntrance.getSubType());
        String eventId = RankMaEntity.SEARCHRESULTPAGE_RANKINGLISTENTRANCE;
        if (fromType == SearchConstant.Value.FROM_TYPE_COUPON) {
            eventId = RankMaEntity.SEARCHLISTPAGE_COUPON_RANKINGLIST;
        } else if (fromType == SearchConstant.Value.FROM_TYPE_PROMOTION || fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE) {
            eventId = RankMaEntity.SEARCHLISTPAGE_PROMO_RANKINGLIST;
        }
        JDMaUtils.save7FClick(eventId, "", productInfoBean.getSkuId(), null, jdMaPageImp, rankMaEntity);
    }

    /**
     * 榜单埋点曝光
     *
     * @param skuInfoVoBean
     * @param marketEntrance
     * @param fromType
     */
    public void exposureRank(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance, int fromType) {
        if (skuInfoVoBean == null || marketEntrance == null) {
            return;
        }
        RankMaEntity rankMaEntity = new RankMaEntity();
        rankMaEntity.skuId = skuInfoVoBean.getSkuId();
        rankMaEntity.skuName = skuInfoVoBean.getSkuName();
        rankMaEntity.rankName = marketEntrance.getText();
        rankMaEntity.rankSortId = String.valueOf(marketEntrance.getSubType());
        //如果是推荐 需要区分点位 和参数
        if (skuInfoVoBean.isRecommend()) {

            if(skuInfoVoBean.getSalePrice()!=null){
                rankMaEntity.salePriceType = skuInfoVoBean.getSalePrice().getType();
            }
            if(skuInfoVoBean.getComparePrice()!=null){
                rankMaEntity.comparePriceType = skuInfoVoBean.getComparePrice().getType();
            }
            if(skuInfoVoBean.getDiscountPrice()!=null){
                rankMaEntity.discountPriceType = skuInfoVoBean.getDiscountPrice().getType();
            }

            rankMaEntity.feedLocation = jdMaPageImp.getPageName(); //和其他地方不一样 使用pageName 上报
            JDMaUtils.save7FExposure(RankMaEntity.FEEDLIST_CARDENTRANCE_RANKING_EXPOSE, null, rankMaEntity, null, jdMaPageImp);
        } else {
            JDMaUtils.save7FExposure(RankMaEntity.SEARCHLIST_SKULIST_RANGKINGEXPOSE, null, rankMaEntity, null, jdMaPageImp);
        }
    }

    /**
     * 搜素结果页-商品卡片-百科点击
     *
     * @param productInfoBean
     * @param marketEntrance
     */
    public void clickJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null && marketEntrance != null) {
            HashMap<String, Object> hashMap = new HashMap<>();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            hashMap.put("skuId", skuId);
            hashMap.put("skuName", skuName);
            hashMap.put("status", productInfoBean.getStatus());
            hashMap.put("keyword", marketEntrance.getText());
            hashMap.put("hotSearch", searchResultDataManager.searchKeyword);
            hashMap.put("rank", searchResultDataManager.rank + "");

            BaseMaEntity baseMaEntity = new BaseMaEntity();
            baseMaEntity.setMa7FextParam(hashMap);
            JDMaUtils.save7FClick("searchListPage_skuList_clickHealthChannel", jdMaPageImp, baseMaEntity);
        }
    }

    /**
     * 搜索结果页-商品卡片-百科曝光
     *
     * @param productInfoBean
     * @param marketEntrance
     */
    public void showJk(SkuInfoBean productInfoBean, SkuMarketEntrance marketEntrance) {
        if (productInfoBean != null && marketEntrance != null) {
            JDJSONObject jdjsonObject = new JDJSONObject();
            String skuId = productInfoBean.getSkuId();
            String skuName = productInfoBean.getSkuName();
            jdjsonObject.put("skuId", skuId);
            jdjsonObject.put("skuName", skuName);
            jdjsonObject.put("status", productInfoBean.getStatus());
            jdjsonObject.put("keyword", marketEntrance.getText());
            jdjsonObject.put("hotSearch", searchResultDataManager.searchKeyword);
            jdjsonObject.put("rank", searchResultDataManager.rank + "");

            JDMaUtils.save7FExposure("searchListPage_skuList_HealthChannelExpose", null, null, jdjsonObject.toString(), jdMaPageImp);
        }
    }

    /**
     * 搜索结果页-反馈入口文案-去反馈文案曝光
     *
     * @param searchKeyword
     */
    public void showFeedBack(String searchKeyword) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("searchkey", searchKeyword);
        JDMaUtils.save7FExposure("searchListPage_nonResult_SearchFeedbackExpose", null,null,jdjsonObject.toString(),jdMaPageImp);
    }

    /**
     * 搜索结果页-反馈入口文案-去反馈按钮点击
     *
     * @param searchKeyword
     */
    public void clickFeedBack(String searchKeyword) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("searchkey", searchKeyword);
        JDMaUtils.save7FExposure("searchListPage_nonResult_SearchFeedbackClick", null,null,jdjsonObject.toString(),jdMaPageImp);
    }

    /**
     * 搜索结果页-相关性提示文案-文案曝光
     *
     * @param searchKeyword
     */
    public void showRelated(String searchKeyword) {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("searchkey", searchKeyword);
        JDMaUtils.save7FExposure("searchListPage_DisrelatedExpose", null,null,jdjsonObject.toString(),jdMaPageImp);
    }

    public void dapeigouProductClickMore(SkuInfoBean productInfoBean) {
        if (productInfoBean == null) {
            return;
        }
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("coreskuId", productInfoBean.getSkuId());
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick("SearchListPage_MatchProduct_MoreClick", jdMaPageImp, maEntity);
    }

    public void dapeigouProductClick(int position, SkuInfoBean productInfoBean, String coreSkuId) {
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("Index", position);//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", productInfoBean.getStockStatus());//商品库存状态
        map.put("skuStatus", productInfoBean.getSkuStatus());//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", productInfoBean.getLogicInfo().isClearanceFlag());//是否是出清商品
        }
        map.put("sitetype", "0");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick("SearchListPage_MatchProduct_SkuClick", jdMaPageImp, maEntity);
    }

    public void dapeigouProductAddCart(int position, SkuInfoBean productInfoBean, String coreSkuId) {
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("Index", position);//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", productInfoBean.getStockStatus());//商品库存状态
        map.put("skuStatus", productInfoBean.getSkuStatus());//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", productInfoBean.getLogicInfo().isClearanceFlag());//是否是出清商品
        }
        map.put("sitetype", "0");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick("SearchListPage_MatchProduct_Addcart", jdMaPageImp, maEntity);
    }

    public void dapeigouProductExposure(int index, SkuInfoBean productInfoBean, String coreSkuId) {
        HashMap<String, String> map = new HashMap<>();
        map.put("Index", index + "");//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", String.valueOf(productInfoBean.getStockStatus()));//商品库存状态
        map.put("skuStatus", String.valueOf(productInfoBean.getSkuStatus()));//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", String.valueOf(productInfoBean.getLogicInfo().isClearanceFlag()));//是否是出清商品
        }
        map.put("sitetype", "0");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        JDMaUtils.save7FExposure("SearchListPage_MatchProduct_SkuExpose", map, null, null, jdMaPageImp);
    }

    public void dapeigouDialogProductClick(SkuInfoBean productInfoBean, String coreSkuId) {
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("Index", productInfoBean.getPageIndex());//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", productInfoBean.getStockStatus());//商品库存状态
        map.put("skuStatus", productInfoBean.getSkuStatus());//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", productInfoBean.getLogicInfo().isClearanceFlag());//是否是出清商品
        }
        map.put("sitetype", "1");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick("SearchListPage_MatchProduct_SkuClick", jdMaPageImp, maEntity);
    }

    public void dapeigouDialogProductAddCart(SkuInfoBean productInfoBean, String coreSkuId) {
        BaseMaEntity maEntity = new BaseMaEntity();
        HashMap<String, Object> map = new HashMap<>();
        map.put("Index", productInfoBean.getPageIndex());//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", productInfoBean.getStockStatus());//商品库存状态
        map.put("skuStatus", productInfoBean.getSkuStatus());//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", productInfoBean.getLogicInfo().isClearanceFlag());//是否是出清商品
        }
        map.put("sitetype", "1");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        maEntity.setMa7FextParam(map);
        JDMaUtils.save7FClick("SearchListPage_MatchProduct_Addcart", jdMaPageImp, maEntity);
    }

    public void dapeigouDialogProductExposure(int index, SkuInfoBean productInfoBean, String coreSkuId) {
        HashMap<String, String> map = new HashMap<>();
        map.put("Index", index + "");//商品在搭配购列表的曝光是第x个，从1开始
        map.put("skuId", productInfoBean.getSkuId());//商品ID
        map.put("coreskuId", coreSkuId);//来自哪个商品的搭配购
        map.put("skuName", productInfoBean.getSkuName());//商品名称
        map.put("skuPointStatus", productInfoBean.getPointStatus());//商品状态
        map.put("skuStockStatus", String.valueOf(productInfoBean.getStockStatus()));//商品库存状态
        map.put("skuStatus", String.valueOf(productInfoBean.getSkuStatus()));//商品上下架状态
        if (productInfoBean.getLogicInfo() != null) {
            map.put("skuType", String.valueOf(productInfoBean.getLogicInfo().isClearanceFlag()));//是否是出清商品
        }
        map.put("sitetype", "1");//搭配购商品位置，是在搜索列表还是在搭配购弹窗里，sitetype ：0 搜索，1 弹窗
        JDMaUtils.save7FExposure("SearchListPage_MatchProduct_SkuExpose", map, null, null, jdMaPageImp);
    }

}
