package com.xstore.floorsdk.fieldsearch.widget;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.Selection;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.service.sfuikit.imagespan.VerticalCenterSpan;

/**
 * 搜索控件
 */
public class SearchView extends RelativeLayout {
    private Context mContext;

    private View mRootView;

    private ImageView mBack;

    private TextView tvCancal;

    private ImageView imageButtonDelete;

    private AutoCompleteTextView etSearch;

    private String inputWord;

    private String hintWord;

    /**
     * lastInputWord 最近一次输入的搜索词 解决重复回调的问题
     */
    private String lastInputWord;

    private SearchActionListener listener;
    /**
     * IMM
     */
    private InputMethodManager inputMethodManager;

    public SearchView(Context context) {
        super(context);
        init(context);
        initListener();
    }

    public SearchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
        initListener();
    }

    public SearchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
        initListener();

    }

    private void init(Context context) {
        mContext = context;

        mRootView = LayoutInflater.from(context).inflate(R.layout.sf_field_search_default_title, this, true);

        mBack = mRootView.findViewById(R.id.ib_title_model_back);
        tvCancal = mRootView.findViewById(R.id.tv_my_order_pop_search_cancal);
        imageButtonDelete = mRootView.findViewById(R.id.iv_my_order_delete);
        etSearch = mRootView.findViewById(R.id.et_pop_search);
        String bizName = !StringUtil.isNullByString(FloorInit.getFloorConfig().getBizName())?FloorInit.getFloorConfig().getBizName():"";
        etSearch.setHint(getResources().getString(R.string.sf_field_search_edittext_hint, bizName));
    }

    private void initListener() {
        mBack.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onBack();
                }
            }
        });

        tvCancal.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                //关闭软键盘
                closePan();
                //有键入的话返回键入词，否则返回暗纹词
                if (listener != null) {
                    listener.onButton(etSearch.getText().toString().trim(), TextUtils.isEmpty(hintWord) ? "" : hintWord);
                }

            }
        });

        imageButtonDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                etSearch.setText("");
            }
        });

        etSearch.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (listener != null && event != null && event.getAction() == MotionEvent.ACTION_DOWN && !isSoftShowing()) {
                    listener.touchAndShowKeyboard();
                }
                return false;
            }
        });

        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s)) {
                    imageButtonDelete.setVisibility(View.GONE);
                } else {
                    imageButtonDelete.setVisibility(View.VISIBLE);
                }
                if (s != null) {
                    if (StringUtil.safeEquals(lastInputWord, s.toString())) {
                        return;
                    }
                    lastInputWord = s.toString();

                    if (listener != null) {
                        listener.onEdit(lastInputWord);
                    }
                }
            }
        });

        etSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                boolean onAction = (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE
                        || (event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode() && KeyEvent.ACTION_DOWN == event.getAction()));
                if (onAction) {
                    //发送请求
                    closePan();
                    //有键入的话返回键入词，否则返回暗纹词
                    if (listener != null) {
                        listener.onEditorAction(etSearch.getText().toString().trim(), TextUtils.isEmpty(hintWord) ? "" : hintWord);
                    }
                    return true;
                }
                return false;
            }
        });
    }

    private void closePan() {
        try {
            InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(((Activity) mContext).getWindow().getDecorView().getWindowToken(), 0);
            }
        } catch (Exception var2) {
            var2.printStackTrace();
        }

    }

    private boolean isSoftShowing() {
        if (mContext instanceof Activity) {
            //获取当屏幕内容的高度
            int screenHeight = ((Activity) mContext).getWindow().getDecorView().getHeight();
            //获取View可见区域的bottom
            Rect rect = new Rect();
            //DecorView即为activity的顶级view
            ((Activity) mContext).getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
            //考虑到虚拟导航栏的情况(虚拟导航栏情况下：screenHeight = rect.bottom + 虚拟导航栏高度)
            //选取screenHeight*2/3进行判断
            return screenHeight * 2 / 3 > rect.bottom;
        } else {
            return true;
        }
    }

    public void setKeyWord(String inputWord, String hintWord) {
        this.inputWord = inputWord;
        this.hintWord = hintWord;

        if (!TextUtils.isEmpty(inputWord)) {
            etSearch.setText(inputWord);
            Editable etext = etSearch.getText();
            Selection.setSelection(etext, etext.length());
        }

        if (!TextUtils.isEmpty(hintWord)) {
            etSearch.setHint(hintWord);
        }

    }
    public void setKeyWord(String inputWord, String hintWord,String hintWordIcon){
        this.inputWord = inputWord;
        this.hintWord = hintWord;

        if (!TextUtils.isEmpty(inputWord)) {
            etSearch.setText(inputWord);
            Editable etext = etSearch.getText();
            Selection.setSelection(etext, etext.length());
        }
        if (!TextUtils.isEmpty(hintWord)) {
            //进行图文混排
            final  String TEXT_SPACE = "#icon";

            String iconUrl = hintWordIcon;
            //图片大小
            final int size = ScreenUtils.dip2px(mContext,20);
//            有icon图没有#icon，有#icon没有icon图时都不展示icon图，并且舍去#icon的展示
//            文案里有多个#icon时，第一个展示icon图，其余的舍去不展示
            //不含icon占位词，直接展示
            if(!hintWord.contains(TEXT_SPACE)){
                etSearch.setHint(hintWord);
            } else{
                int start = hintWord.indexOf(TEXT_SPACE);
                //icon的url为空，去除所有占位词，展示
                //先展示文字，再加载展示icon
                // 多个占位词，只保留第一个
                String newHintStr = hintWord.replaceAll(TEXT_SPACE,"");
                etSearch.setHint(newHintStr);
                if(!TextUtils.isEmpty(iconUrl) && start != -1) {
                    String beforeStr = newHintStr.substring(0,start)+" "+newHintStr.substring(start);
                    SpannableStringBuilder ssb = new SpannableStringBuilder(beforeStr);

                    ImageloadUtils.loadImage(getContext(), iconUrl, new ImageloadUtils.LoadListener(){

                        @Override
                        public void onSuccess(Bitmap bitmap) {
                            post(new Runnable() {
                                @Override
                                public void run() {
                                    Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, size, size, true);
                                    Drawable drawable = new BitmapDrawable(getResources(),scaledBitmap);
                                    int width = drawable.getIntrinsicWidth();
                                    int height = drawable.getIntrinsicHeight();
                                    drawable.setBounds(0, 0, Math.max(width, 0), Math.max(height, 0));
                                    ssb.setSpan(new VerticalCenterSpan(drawable,ScreenUtils.dip2px(mContext,2)),start,start+1, 0);
                                    etSearch.setHint(ssb);
                                }
                            });
                        }
                        @Override
                        public void onFailed() {

                        }
                    });
                }
            }
        }


    }


    /**
     * @return 返回当前的输入框
     */
    public AutoCompleteTextView getEtSearch() {
        return etSearch;
    }

    public void setListener(SearchActionListener listener) {
        this.listener = listener;
    }

    /**
     * 弹出键盘
     */
    public void showKeyboard() {
        etSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                etSearch.setFocusable(true);
                etSearch.setFocusableInTouchMode(true);
                etSearch.requestFocus();
                if (inputMethodManager == null) {
                    inputMethodManager = (InputMethodManager) etSearch.getContext().getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                }
                if (inputMethodManager != null) {
                    inputMethodManager.showSoftInput(etSearch, 0);
                }
            }
        }, 200);
    }

    public interface SearchActionListener {
        void onBack();


        void onEdit(String keyword);

        /**
         * 搜索词回调
         *
         * @param inputWord
         * @param hintWord
         */
        void onButton(String inputWord, String hintWord);

        /**
         * 搜索词回调
         *
         * @param inputWord
         * @param hintWord  暗纹词
         */
        void onEditorAction(String inputWord, String hintWord);

        /**
         * 触摸输入框并且弹出键盘
         */
        void touchAndShowKeyboard();
    }
}
