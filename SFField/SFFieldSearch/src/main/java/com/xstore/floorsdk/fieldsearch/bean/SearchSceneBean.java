package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索结果场景词数据实体类
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class SearchSceneBean implements Serializable {

    /**
     * 场景词
     */
    private String sceneWord;
    /**
     * 标题
     */
    private String title;
    /**
     * 排行图
     */
    private String rankImg;
    /**
     * 场景词描述
     */
    private String sceneWordDesc;
    /**
     * 更多榜单链接(目前未使用)
     */
    private String linkUrl;
    /**
     * 背景图
     */
    private String bgImg;
    /**
     * 配置简介图对象数组
     */
    private List<SceneImage> sceneWordImageVos;

    public String getSceneWord() {
        return sceneWord;
    }

    public void setSceneWord(String sceneWord) {
        this.sceneWord = sceneWord;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRankImg() {
        return rankImg;
    }

    public void setRankImg(String rankImg) {
        this.rankImg = rankImg;
    }

    public String getSceneWordDesc() {
        return sceneWordDesc;
    }

    public void setSceneWordDesc(String sceneWordDesc) {
        this.sceneWordDesc = sceneWordDesc;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public String getBgImg() {
        return bgImg;
    }

    public void setBgImg(String bgImg) {
        this.bgImg = bgImg;
    }

    public List<SceneImage> getSceneWordImageVos() {
        return sceneWordImageVos;
    }

    public void setSceneWordImageVos(List<SceneImage> sceneWordImageVos) {
        this.sceneWordImageVos = sceneWordImageVos;
    }

    public class SceneImage implements Serializable {
        /**
         * 配置简介图
         */
        private String image;
        /**
         * 跳转链接
         */
        private String link;
        /**
         * 跳转链接类型-先支持 M页面
         */
        private int linkType;

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public int getLinkType() {
            return linkType;
        }

        public void setLinkType(int linkType) {
            this.linkType = linkType;
        }
    }
}
