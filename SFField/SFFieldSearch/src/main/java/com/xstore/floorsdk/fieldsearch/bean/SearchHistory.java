package com.xstore.floorsdk.fieldsearch.bean;

import java.util.Date;

public class SearchHistory {

    private int id;

    private String word;

    private Date searchDate;
    /**
     * 历史类型：商品；店铺
     * Keyword TYPE_PRODUCT TYPE_SHOP
     */
    private int type;

    public SearchHistory() {
    }

    public SearchHistory(String word) {
        this.word = word;
        searchDate = new Date();
    }

    public SearchHistory(int id, String word, long time) {
        update(id, word, time, Keyword.TYPE_PRODUCT);
    }

    public SearchHistory(int id, String word, long time, int type) {
        update(id, word, time, type);
    }

    public void update(int id, String word, long time, int type) {
        this.word = word;
        this.id = id;
        this.type = type;
        try {
            this.searchDate = new Date(time);
        } catch (Exception e) {
        }
    }

    /**
     * 当前是不是店铺关键字
     *
     * @return
     */
    public boolean isShop() {
        return (type - Keyword.TYPE_SHOP) == 0;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getWord() {
        return word;
    }

    public String getShowWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public Date getSearchDate() {
        return searchDate;
    }

    public void setSearchDate(Date searchDate) {
        this.searchDate = searchDate;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "SearchHistory [id=" + id + ", word=" + word + ", searchDate=" + searchDate + ", type=" + type + "]";
    }

}
