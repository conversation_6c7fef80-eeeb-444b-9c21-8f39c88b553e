package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

/**
 * 扩词搜索信息
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
public class WordSearch implements Serializable {
    /**
     * 是否扩词
     */
    private boolean hasWordSearch;
    /**
     * 原词
     */
    private String oldWord;
    /**
     * 新词
     */
    private String newWord;

    public boolean isHasWordSearch() {
        return hasWordSearch;
    }

    public void setHasWordSearch(boolean hasWordSearch) {
        this.hasWordSearch = hasWordSearch;
    }

    public String getOldWord() {
        return oldWord;
    }

    public void setOldWord(String oldWord) {
        this.oldWord = oldWord;
    }

    public String getNewWord() {
        return newWord;
    }

    public void setNewWord(String newWord) {
        this.newWord = newWord;
    }
}
