package com.xstore.floorsdk.fieldsearch.bean;

public class Keyword {
    public static final int TYPE_PRODUCT = 0;
    public static final int TYPE_SHOP = 1;
    public static final int TYPE_ADDRESS = 2;
    public static final int TYPE_INVOICE_TITLE = 3;
    public static final int TYPE_ORDER = 4;

    public static final int TIP = 0;
    public static final int TIP_SHOP = 1;

    private String name;
    private Integer count;
    private int type;// 历史类型：商品0；店铺1

    public Keyword() {

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCount() {
        if (count == null) {
            return 0;
        }
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    /**
     * 当前是不是店铺关键字
     *
     * @return
     */
    public boolean isShop() {
        return (type - TYPE_SHOP) == 0;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
