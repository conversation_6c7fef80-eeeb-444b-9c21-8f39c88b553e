package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 试试搜适配器
 *
 * <AUTHOR>
 * @date 2022/09/25
 */
public class TrySearchAdapter extends RecyclerView.Adapter<TrySearchAdapter.TrySearchItemHolder> {

    private Context context;
    private OnItemClickListener onItemClickListener;
    private List<String> data;

    public TrySearchAdapter(Context context, List<String> data) {
        this.context = context;
        this.data = data;
    }

    @NonNull
    @Override
    public TrySearchItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_item_try_search_item, parent, false);
        return new TrySearchItemHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TrySearchItemHolder holder, int position) {
        holder.tvWord.setText(data.get(position));
        holder.itemView.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    class TrySearchItemHolder extends RecyclerView.ViewHolder {

        TextView tvWord;

        public TrySearchItemHolder(@NonNull View itemView) {
            super(itemView);
            tvWord = itemView.findViewById(R.id.tv_word);
        }
    }

    public interface OnItemClickListener {

        void onItemClick(int position);
    }
}
