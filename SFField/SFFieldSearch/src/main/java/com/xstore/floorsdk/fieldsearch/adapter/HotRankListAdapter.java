package com.xstore.floorsdk.fieldsearch.adapter;


import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sdk.floor.floorcore.adapter.BaseQuickAdapter;
import com.xstore.sdk.floor.floorcore.adapter.BaseViewHolder;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.floorsdk.fieldsearch.bean.HotRankBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class HotRankListAdapter extends BaseQuickAdapter<HotRankBean, BaseViewHolder> {


    public HotRankListAdapter(@Nullable List<HotRankBean> data) {
        super(R.layout.sf_field_search_item_hot_rank_list, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, HotRankBean item) {

        helper.setText(R.id.tv_rank_name, item.getTitle());
        ImageView ivRank = helper.getView(R.id.iv_rank_pic);
        if (!StringUtil.isNullByString(item.getImageUrl())) {
            ivRank.setVisibility(View.VISIBLE);
            ImageloadUtils.loadImage(mContext, ivRank, item.getImageUrl());
        } else {
            ivRank.setVisibility(View.GONE);
        }

        TextView tvRank = helper.getView(R.id.tv_rank);
        tvRank.setText(String.valueOf(helper.getAdapterPosition()));
        tvRank.setBackground(mContext.getResources().getDrawable(getDrawable(helper.getAdapterPosition())));
        ImageView ivTopIcon = helper.getView(R.id.iv_rank_top_icon);
        if (!StringUtil.isNullByString(item.getHotSellRankImage())) {
            ivTopIcon.setVisibility(View.VISIBLE);
            ImageloadUtils.loadImage(mContext, ivTopIcon, item.getHotSellRankImage());
        } else {
            ivTopIcon.setVisibility(View.GONE);
        }
    }

    private int getDrawable(int pos) {
        if (pos == 1) {
            return R.drawable.sf_field_search_rank_first_bg;
        }

        if (pos == 2) {
            return R.drawable.sf_field_search_rank_second_bg;
        }

        if (pos == 3) {
            return R.drawable.sf_field_search_rank_third_bg;
        }
        return R.drawable.sf_field_search_rank_other_gray_bg;
    }
}
