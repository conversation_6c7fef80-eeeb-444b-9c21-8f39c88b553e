package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 展示少量内容且不滑动的RecyclerView
 */
public class SearchMaxHeightRecyclerView extends RecyclerView {

    public SearchMaxHeightRecyclerView(Context context) {
        super(context);
    }

    public SearchMaxHeightRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SearchMaxHeightRecyclerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST));
    }
}
