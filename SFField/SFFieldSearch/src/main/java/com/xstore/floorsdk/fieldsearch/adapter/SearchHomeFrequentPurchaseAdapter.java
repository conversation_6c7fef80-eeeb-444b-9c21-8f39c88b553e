package com.xstore.floorsdk.fieldsearch.adapter;

import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_REGULAR_PURCHASE_ADD_CART;

import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.bean.SearchCardClickEvent;
import com.xstore.sdk.floor.floorcore.adapter.BaseQuickAdapter;
import com.xstore.sdk.floor.floorcore.adapter.BaseViewHolder;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.cart.interfaces.AddCartMaListener;
import com.xstore.sevenfresh.cart.widget.AddCartView;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;
import com.xstore.sevenfresh.productcard.utils.PriceUtilV3;
import com.xstore.sevenfresh.productcard.widget.ProductTagViewV3;

import java.util.List;

public class SearchHomeFrequentPurchaseAdapter extends BaseQuickAdapter<SkuInfoVoBean, BaseViewHolder> {

    private AppCompatActivity activity;
    /**
     * 埋点
     */
    private JDMaUtils.JdMaPageImp jdMaPageImp = null;

    public SearchHomeFrequentPurchaseAdapter(AppCompatActivity activity, @Nullable List<SkuInfoVoBean> data, JDMaUtils.JdMaPageImp jdMaPageImp) {
        super(R.layout.sf_field_search_item_frequent_purchase_good, data);
        this.activity = activity;
        this.jdMaPageImp = jdMaPageImp;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, final SkuInfoVoBean skuInfoVoBean) {
        if (skuInfoVoBean == null || skuInfoVoBean.getSkuBaseInfoRes() == null) {
            return;
        }

        TextView tvGoodsUnit = helper.getView(R.id.tv_goods_unit);
        TextView tvGoodsPrice = helper.getView(R.id.tv_goods_price);
        TextView tvPurchaseTime = helper.getView(R.id.tv_purchase_time);
        TextView tvGoodName = helper.getView(R.id.tv_goods_name);
        final ImageView ivGoodImage = helper.getView(R.id.tv_goods_image);
        ProductTagViewV3 tagView = helper.getView(R.id.product_tag);
        AddCartView addCartView = helper.getView(R.id.acv_addcart);

        // 商品图片
        ImageloadUtils.loadImageWithOutScale(mContext, ivGoodImage, skuInfoVoBean.getSkuBaseInfoRes().getImageUrl());
        // 设置价格
        PriceUtilV3.setPrice(tvGoodsPrice, skuInfoVoBean.getSkuPriceInfoRes() == null ? null : skuInfoVoBean.getSkuPriceInfoRes().getJdPrice(), true, false);
        // 设置单位
        if (!StringUtil.isNullByString(skuInfoVoBean.getSkuBaseInfoRes().getSkuBaseExtInfoRes().getBuyUnitDesc())) {
            tvGoodsUnit.setText(skuInfoVoBean.getSkuBaseInfoRes().getSkuBaseExtInfoRes().getBuyUnitDesc());
        } else {
            tvGoodsUnit.setText("");
        }
        // 购买次数
        if (skuInfoVoBean.getSkuBuyNum() != null && skuInfoVoBean.getSkuBuyNum() > 0) {
            tvPurchaseTime.setTextColor(mContext.getResources().getColor(R.color.sf_theme_color_level_1));
            tvPurchaseTime.setText(mContext.getString(R.string.sf_field_search_frequent_purchase_sku_buy_times, skuInfoVoBean.getSkuBuyNum()));
        } else {
            tvPurchaseTime.setTextColor(Color.parseColor("#95969F"));
            tvPurchaseTime.setText("为您推荐");
        }

        // 打标
//        tagView.initCoupon();
//        tagView.initAction();
        tagView.showCover(false, skuInfoVoBean);
        // 加车按钮状态
        addCartView.setEnabled(skuInfoVoBean.isAddCart());

        //商品名
        //改为采用富文本实现
        SpannableStringBuilder ssb = null;
        if (!StringUtil.isNullByString(skuInfoVoBean.getSkuBaseInfoRes().getSkuShortName())) {
            ssb = new SpannableStringBuilder(skuInfoVoBean.getSkuBaseInfoRes().getSkuShortName());
        } else if (!StringUtil.isNullByString(skuInfoVoBean.getSkuBaseInfoRes().getSkuName())) {
            ssb = new SpannableStringBuilder(skuInfoVoBean.getSkuBaseInfoRes().getSkuName());
        } else {
            ssb = new SpannableStringBuilder();
        }

//        //预定标
//        if (item.isPreSale() && item.getStatus() == ProductDetailBean.STATUS_STOCKOUT) {
//            Drawable preSaleDrawable = mContext.getResources().getDrawable(R.drawable.sf_theme_image_pre_sale);
//            if (preSaleDrawable != null) {
//                preSaleDrawable.setBounds(0, 0, (int) (preSaleDrawable.getMinimumWidth() * 1.0f / preSaleDrawable.getMinimumHeight() * PRE_TAG_HEIGHT), (int) PRE_TAG_HEIGHT);
//                ssb.insert(0, "\u0020\u0020");
//                ssb.setSpan(new RelativeSizeSpan(0.5f), 1, 2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//                ssb.setSpan(new RecommendStyleHelper.CenterImageSpan(preSaleDrawable), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//            }
//
//        } else if (item.isPeriod()) {
//            Drawable periodDrawable = mContext.getResources().getDrawable(R.drawable.ic_home_recommend_tab_period);
//            if (periodDrawable != null) {
//                periodDrawable.setBounds(0, 0, (int) (periodDrawable.getMinimumWidth() * 1.0f / periodDrawable.getMinimumHeight() * PRE_TAG_HEIGHT), (int) PRE_TAG_HEIGHT);
//                ssb.insert(0, "\u0020\u0020");
//                ssb.setSpan(new RelativeSizeSpan(0.5f), 1, 2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//                ssb.setSpan(new RecommendStyleHelper.CenterImageSpan(periodDrawable), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//            }
//        }
        tvGoodName.setText(ssb);

        // 加车事件
        addCartView.bindWareInfo(activity, skuInfoVoBean, TenantIdUtils.getStoreId(), ivGoodImage);
        addCartView.setAddCartMaListener(new AddCartMaListener() {
            @Override
            public void onAddCartMa(ProductDetailBean.WareInfoBean wareInfo) {
                // 加车事件埋点
                SearchCardClickEvent searchCardClickEvent = new SearchCardClickEvent(wareInfo.getSkuId(), wareInfo.getSkuName(), null);
                BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
                baseMaPublicParam.CLICKTYPE = "1";
                searchCardClickEvent.setPublicParam(baseMaPublicParam);
                JDMaUtils.save7FClick(CLICK_SEARCH_REGULAR_PURCHASE_ADD_CART, jdMaPageImp, searchCardClickEvent);
            }
        });
    }
}
