package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 穿插搜索词
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
public class SearchRelateWord implements Serializable {

    private String title;
    private int showNum;
    private List<String> words;

    /* 试试搜->添加icon新增字段 */
    private List<SearchRelateWordRecQuery> recQueryList;


    public List<SearchRelateWordRecQuery> getRecQueryList() {
        return recQueryList;
    }

    public void setRecQueryList(List<SearchRelateWordRecQuery> recQueryList) {
        this.recQueryList = recQueryList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getShowNum() {
        return showNum;
    }

    public void setShowNum(int showNum) {
        this.showNum = showNum;
    }

    public List<String> getWords() {
        return words;
    }

    public void setWords(List<String> words) {
        this.words = words;
    }
}
