package com.xstore.floorsdk.fieldsearch.cloudstore;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 卡片样式云卖场入口商品适配器
 *
 * <AUTHOR>
 * @date 2022/09/25
 */
public class YunCardMultProductAdapter extends RecyclerView.Adapter<YunCardMultProductAdapter.ProductHolder> {

    private Activity activity;
    private SearchResultReporter searchResultReporter;
    private List<SkuInfoBean> productInfos;
    private int corner;

    public YunCardMultProductAdapter(Activity activity, SearchResultReporter searchResultReporter, List<SkuInfoBean> productInfos) {
        this.activity = activity;
        this.searchResultReporter = searchResultReporter;
        this.productInfos = productInfos;
        this.corner = ScreenUtils.dip2px(activity, 6);
    }

    @NonNull
    @Override
    public ProductHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_item_yun_card_item, parent, false);
        return new ProductHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductHolder holder, int position) {
        SkuInfoBean productInfo = productInfos.get(position);
        if (productInfo == null) {
            return;
        }
        ImageloadUtils.loadImage(activity, holder.ivYunMultProductImg, productInfo.getSkuImageInfo().getMainUrl());
        //打各种标
//        holder.productTagView.initCold(false);
//        holder.productTagView.initSeven();
//        holder.productTagView.initCoupon();
//        holder.productTagView.initAction();
//        holder.productTagView.showCover(false, productInfo);

        holder.itemView.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            FloorJumpManager.getInstance().jumpProductDetail(activity, productInfo, true);
            searchResultReporter.cloudSkuClick(productInfo, position);
        });
    }

    @Override
    public int getItemCount() {
        return productInfos == null ? 0 : productInfos.size();
    }

    class ProductHolder extends RecyclerView.ViewHolder {

        RoundCornerImageView1 ivYunMultProductImg;
//        ProductTagViewV2 productTagView;

        public ProductHolder(@NonNull View itemView) {
            super(itemView);
            ivYunMultProductImg = itemView.findViewById(R.id.iv_yun_mult_product_img);
            ivYunMultProductImg.setRadius(corner, corner, corner, corner);
//            productTagView = itemView.findViewById(R.id.product_tag);
        }
    }
}
