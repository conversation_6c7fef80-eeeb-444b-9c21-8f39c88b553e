package com.xstore.floorsdk.fieldsearch;

import android.app.Activity;
import android.os.Bundle;

import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.xstore.floorsdk.fieldsearch.bean.SearchCouponResult;
import com.xstore.floorsdk.fieldsearch.bean.SearchPromotionResult;
import com.xstore.floorsdk.fieldsearch.bean.SearchRecommendResult;
import com.xstore.floorsdk.fieldsearch.bean.SearchResultInfo;
import com.xstore.floorsdk.fieldsearch.bean.SearchResultResponse;
import com.xstore.floorsdk.fieldsearch.bean.UnStockRecommendResult;
import com.xstore.floorsdk.fieldsearch.config.DuccConfigManager;
import com.xstore.floorsdk.fieldsearch.config.SearchActivityConfig;
import com.xstore.floorsdk.fieldsearch.interfaces.SearchResultContainerInterface;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultListExposureHelper;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.floorsdk.fieldsearch.request.SearchResultCallback;
import com.xstore.floorsdk.fieldsearch.request.SearchResultNetwork;
import com.xstore.sdk.floor.floorcore.FloorActionConstants;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.utils.Utils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * 搜索结果页数据处理
 *
 * <AUTHOR>
 * @date 2022/09/26
 */
public class SearchResultDataManager {

    /**
     * 日志tag
     */
    public static final String TAG = "SearchResultDataManager";
    private Activity activity;

    /**
     * 来源 1 主搜 2 券搜 3 促销搜 4 加价购 5 领货码 6 运费凑单 7 直播
     */
    public int fromType = 0;

    //搜索关键字
    public String searchKeyword = "";
    public String keywordClickFrom;

    //分页相关条件
    private int totalPage = 0;
    private int totalCount = 0;
    //初次关键字搜索总数，即没有其他筛选条件时搜索总数
    private int totalCountWithoutFilter = 0;
    public int pageSize = 10;
    private int currPage = 1;
    private int recommendTotalPage = 0;
    private int recommendTotalCount = 0;
    public int recommendPageSize = 10;
    private int recommendCurrPage = 1;

    //场景词榜单排行数(排名从1开始)
    public int rank;

    //券、促销相关条件
    private String couponId;
    private String batchId;
    private String batchKey;
    private String promotionId;

    //领货码相关条件
    private String pickUpCodeBrandId;

    //类目相关条件
    public String tileCategoryId;
    public String tileCategoryName;
    private String type = "";

    //是否仍然搜索(纠错词用)
    private boolean stillSearch = false;

    //筛选相关，包括履约时效、活动、排序、价格区间、属性等筛选
    //履约时效筛选
    private JDJSONObject deliveryFilter;
    //活动筛选
    public JDJSONObject activityFilter;
    //排序筛选
    public JDJSONObject sortFilter;
    //价格区间筛选
    public JDJSONObject priceRangeFilter;
    //加价购筛选
    public JDJSONObject addBuyFilter;
    /**
     * 属性筛选条件
     */
    public Map<String, List<String>> selectedFilterMap = new HashMap<>();
    /**
     * 已筛选的最小价格
     */
    public String filterMinPrice;
    /**
     * 已筛选的最大价格
     */
    public String filterMaxPrice;

    //搜索来源
    private int source = SearchEntrance.DEFAULT_SEARCH.ordinal();
    /**
     * 当次搜索的随机序号,每次重新搜索都会变化
     */
    public String pvId;
    public String logId;
    public String mtest;

    /**
     * 搜索数据回调
     */
    private SearchResultCallback searchResultCallback;
    /**
     * 搜索结果页容器接口
     */
    public SearchResultContainerInterface searchResultContainerInterface;
    public SearchResultListExposureHelper searchResultListExposureHelper;
    public SearchResultReporter searchResultReporter;
    /**
     * 缓存搜索结果
     */
    public SearchResultInfo searchResultInfo;
    /**
     * 缓存搜索商品Id列表，用于查询推荐时去重
     */
    private List<String> skuList = new ArrayList<>();
    /**
     * 无货推荐Map
     */
    private Map<String, List<SkuInfoBean>> unStockRecomMap = new HashMap<>();
    /**
     * 需要请求无货相似推荐的数量(统计数量一次刷新列表，防止频繁刷新造成滑动卡顿)
     */
    private int needRequestUnStockCount;
    /**
     * 已完成请求无货相似推荐的数量(值与needRequestUnStockCount相等时一次刷新列表，防止频繁刷新造成滑动卡顿)
     */
    private int requestedUnStockCount;

    /**
     * 是否出现次相关
     * 1是；0否
     */
    private int isAppearTimesRelated = 0;

    enum SearchEntrance {
        /**
         * 0
         */
        DEFAULT_SEARCH,
        /**
         * 1
         */
        MAIN_SEARCH,
        /**
         * 2
         */
        PROMOTION_SEARCH,
        /**
         * 3
         */
        COUPON_SEARCH,
        /**
         * 4
         */
        PICKINGCODE_SEARCH,
        /**
         * 5
         */
        CLUB_SKU_LIST,
        /**
         * 6
         */
        FREIGHT_SEARCH;
    }

    public SearchResultDataManager() {
    }

    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    public void setSearchResultListExposureHelper(SearchResultListExposureHelper searchResultListExposureHelper) {
        this.searchResultListExposureHelper = searchResultListExposureHelper;
    }

    public void setSearchResultReporter(SearchResultReporter searchResultReporter) {
        this.searchResultReporter = searchResultReporter;
    }

    /**
     * 清除缓存数据
     */
    public void clearCachedData() {
        activity = null;
        fromType = 0;
        searchKeyword = "";
        totalPage = 0;
        totalCount = 0;
        totalCountWithoutFilter = 0;
        pageSize = 10;
        currPage = 1;
        recommendTotalPage = 0;
        recommendTotalCount = 0;
        recommendPageSize = 10;
        recommendCurrPage = 1;
        rank = 0;
        couponId = "";
        batchId = "";
        batchKey = "";
        promotionId = "";
        pickUpCodeBrandId = "";
        tileCategoryId = "";
        tileCategoryName = "";
        type = "";
        stillSearch = false;
        deliveryFilter = null;
        activityFilter = null;
        sortFilter = null;
        priceRangeFilter = null;
        addBuyFilter = null;
        selectedFilterMap.clear();
        filterMinPrice = "";
        filterMaxPrice = "";
        source = SearchEntrance.DEFAULT_SEARCH.ordinal();
        searchResultCallback = null;
        searchResultContainerInterface = null;
        searchResultInfo = null;
        unStockRecomMap.clear();
    }

    /**
     * 二次搜索，需要把之前的筛选条件清除
     *
     * @param searchKeyword
     */
    public void secondSearch(String searchKeyword) {
        this.searchKeyword = searchKeyword;
        totalPage = 0;
        totalCount = 0;
        totalCountWithoutFilter = 0;
        pageSize = 10;
        currPage = 1;
        recommendTotalPage = 0;
        recommendTotalCount = 0;
        recommendPageSize = 10;
        recommendCurrPage = 1;
        rank = 0;
        tileCategoryId = "";
        tileCategoryName = "";
        type = "";
        stillSearch = false;
        deliveryFilter = null;
        activityFilter = null;
        sortFilter = null;
        priceRangeFilter = null;
        addBuyFilter = null;
        selectedFilterMap.clear();
        filterMinPrice = "";
        filterMaxPrice = "";
        if (searchResultCallback != null) {
            searchResultCallback.secondSearchToClearCache(searchKeyword);
        }
        //二次搜索也可以直达活动页
        refreshData(fromType == SearchConstant.Value.FROM_TYPE_SEARCH);
    }

    /**
     * 仍然搜索
     */
    public void stillSearch() {
        currPage = 1;
        requestData(activity, true, false, true);
    }

    /**
     * 更新履约时效筛选条件
     *
     * @param filterKey
     * @param filterValue
     */
    public void updateDeliveryFilter(String filterKey, String filterValue) {
        if (StringUtil.isNullByString(filterKey) || StringUtil.isNullByString(filterValue)) {
            return;
        }
        if (deliveryFilter == null) {
            deliveryFilter = new JDJSONObject();
        } else {
            deliveryFilter.clear();
        }
        deliveryFilter.put("filterKey", filterKey);
        deliveryFilter.put("filterValue", filterValue);
        refreshData();
    }

    /**
     * 清空履约时效筛选条件
     *
     * @param needRefresh
     */
    public void clearDeliveryFilter(boolean needRefresh) {
        if (deliveryFilter != null) {
            deliveryFilter.clear();
        }
        if (needRefresh) {
            refreshData();
        }
    }

    /**
     * 更新类目筛选条件
     *
     * @param tileCategoryId
     * @param tileCategoryName
     * @param type
     */
    public void updateCategoryFilter(String tileCategoryId, String tileCategoryName, String type) {
        this.tileCategoryId = tileCategoryId;
        this.tileCategoryName = tileCategoryName;
        this.type = type;
        refreshData();
    }

    /**
     * 清空类目筛选条件
     */
    public void clearCategoryFilter() {
        this.tileCategoryId = "";
        this.tileCategoryName = "";
        this.type = "";
    }

    /**
     * 是否可显示大促活动筛选
     *
     * @return
     */
    public boolean canShowActivity() {
        if (fromType == SearchConstant.Value.FROM_TYPE_SEARCH && totalCountWithoutFilter > 0 && getActivitySearchConfig() != null) {
            return getActivitySearchConfig().showActivityImg();
        }
        return false;
    }

    /**
     * 获取搜索ducc配置
     *
     * @return
     */
    public SearchActivityConfig getActivitySearchConfig() {
        return DuccConfigManager.getInstance().getActivitySearchConfig();
    }

    /**
     * 是否选中了活动筛选
     *
     * @return
     */
    public boolean isActionFilterSelected() {
        return activityFilter != null && !activityFilter.isEmpty();
    }

    /**
     * 选中活动筛选
     */
    public void selectAction() {
        if (activityFilter == null) {
            activityFilter = new JDJSONObject();
        }
        activityFilter.clear();
        activityFilter.put("filterKey", "activitySelect");
        activityFilter.put("filterValue", getActivitySearchConfig().getActivitySelectValue());
        refreshData();
    }

    /**
     * 清楚活动筛选信息
     *
     * @return
     */
    public void clearActionFilter() {
        if (activityFilter != null && !activityFilter.isEmpty()) {
            activityFilter.clear();
            refreshData();
        }
    }

    /**
     * 更新排序筛选条件
     *
     * @param filterKey
     * @param filterValue
     */
    public void updateSortFilter(String filterKey, String filterValue) {
        if (StringUtil.isNullByString(filterKey) || StringUtil.isNullByString(filterValue)) {
            return;
        }
        if (sortFilter == null) {
            sortFilter = new JDJSONObject();
        } else {
            sortFilter.clear();
        }
        sortFilter.put("filterKey", filterKey);
        sortFilter.put("filterValue", filterValue);
        refreshData();
    }

    /**
     * 清空排序筛选
     *
     * @param needRefresh
     */
    public void clearSortFilter(boolean needRefresh) {
        if (sortFilter != null) {
            sortFilter.clear();
        }
        if (needRefresh) {
            refreshData();
        }
    }

    /**
     * 增加属性筛选条件
     *
     * @param filterKey
     * @param filterValues
     */
    public void addFeatureFilter(String filterKey, List<String> filterValues) {
        if (StringUtil.isNullByString(filterKey) || filterValues == null || filterValues.isEmpty()) {
            return;
        }
        selectedFilterMap.put(filterKey, filterValues);
        refreshData();
    }

    /**
     * 移除属性筛选条件
     *
     * @param filterKey
     */
    public void removeFeatureFilter(String filterKey) {
        if (StringUtil.isNullByString(filterKey)) {
            return;
        }
        if (selectedFilterMap.containsKey(filterKey)) {
            selectedFilterMap.remove(filterKey);
            refreshData();
        }
    }

    /**
     * 增加属性筛选条件子项
     *
     * @param filterKey
     * @param filterValue
     */
    public void addFeatureFilterQuery(String filterKey, String filterValue) {
        if (StringUtil.isNullByString(filterKey) || StringUtil.isNullByString(filterValue)) {
            return;
        }
        if (selectedFilterMap.containsKey(filterKey) && selectedFilterMap.get(filterKey) != null) {
            selectedFilterMap.get(filterKey).add(filterValue);
        } else {
            List<String> querys = new ArrayList<>();
            querys.add(filterValue);
            selectedFilterMap.put(filterKey, querys);
        }
        refreshData();
    }

    /**
     * 移除属性筛选条件子项
     *
     * @param filterKey
     * @param filterValue
     */
    public void removeFeatureFilterQuery(String filterKey, String filterValue) {
        if (StringUtil.isNullByString(filterKey) || StringUtil.isNullByString(filterValue)) {
            return;
        }
        if (selectedFilterMap.containsKey(filterKey) && selectedFilterMap.get(filterKey) != null
                && selectedFilterMap.get(filterKey).contains(filterValue)) {
            selectedFilterMap.get(filterKey).remove(filterValue);
            if (selectedFilterMap.get(filterKey).isEmpty()) {
                selectedFilterMap.remove(filterKey);
            }
            refreshData();
        }
    }

    /**
     * 获取某个filterKey下筛选条件数量
     *
     * @param filterKey
     * @return
     */
    public int getFeatureFilterQueryCount(String filterKey) {
        if (StringUtil.isNullByString(filterKey)) {
            return 0;
        }
        if (selectedFilterMap.containsKey(filterKey) && selectedFilterMap.get(filterKey) != null) {
            return selectedFilterMap.get(filterKey).size();
        }
        return 0;
    }

    /**
     * 更新价格区间筛选条件
     *
     * @param filterKey
     * @param minPrice
     * @param maxPrice
     * @param needRefresh
     */
    public void updatePriceRangeFilter(String filterKey, String minPrice, String maxPrice, boolean needRefresh) {
        this.filterMinPrice = minPrice;
        this.filterMaxPrice = maxPrice;
        if (priceRangeFilter == null) {
            priceRangeFilter = new JDJSONObject();
        } else {
            priceRangeFilter.clear();
        }
        if (!StringUtil.isNullByString(minPrice) || !StringUtil.isNullByString(maxPrice)) {
            if (StringUtil.isNullByString(minPrice)) {
                minPrice = "0";
            }
            if (StringUtil.isNullByString(maxPrice)) {
                maxPrice = String.valueOf(Integer.MAX_VALUE);
            }
            String filterValue = "";
            if (Utils.compare(maxPrice, minPrice) >= 0) {
                filterValue = minPrice + "," + maxPrice;
            } else {
                filterValue = maxPrice + "," + minPrice;
                this.filterMinPrice = maxPrice;
                this.filterMaxPrice = minPrice;
            }
            priceRangeFilter.put("filterKey", filterKey);
            priceRangeFilter.put("filterValue", filterValue);
        }
        if (needRefresh) {
            refreshData();
        }
    }

    public int getIsAppearTimesRelated() {
        return isAppearTimesRelated;
    }

    public void setIsAppearTimesRelated(int isAppearTimesRelated) {
        this.isAppearTimesRelated = isAppearTimesRelated;
    }

    /**
     * 清空价格区间筛选条件
     */
    public void clearPriceRangeFilter() {
        this.filterMinPrice = "";
        this.filterMaxPrice = "";
        if (priceRangeFilter != null) {
            priceRangeFilter.clear();
        }
    }

    /**
     * 筛选面板重置
     */
    public void resetFeatureFilter() {
        boolean needRequest = false;
        if (activityFilter != null && !activityFilter.isEmpty()) {
            needRequest = true;
            activityFilter.clear();
        }
        if (!StringUtil.isNullByString(filterMinPrice) || !StringUtil.isNullByString(filterMaxPrice)) {
            needRequest = true;
            clearPriceRangeFilter();
        }
        if (selectedFilterMap != null && !selectedFilterMap.isEmpty()) {
            needRequest = true;
            selectedFilterMap.clear();
        }
        if (needRequest) {
            refreshData();
        }
    }

    /**
     * 更新加价购筛选
     *
     * @param filterKey
     * @param filterValue
     */
    public void updateAddBuyFilter(String filterKey, String filterValue) {
        if (StringUtil.isNullByString(filterKey)) {
            return;
        }
        if (addBuyFilter == null) {
            addBuyFilter = new JDJSONObject();
        } else {
            addBuyFilter.clear();
        }
        if (StringUtil.isNullByString(filterValue)) {
            //选择了全部
            refreshData();
            return;
        }
        addBuyFilter.put("filterKey", filterKey);
        addBuyFilter.put("filterValue", filterValue);
        refreshData();
    }

    /**
     * 清空加价购筛选条件
     */
    public void clearAddBuyFilter() {
        if (addBuyFilter != null) {
            addBuyFilter.clear();
        }
    }

    /**
     * 是否有筛选条件
     *
     * @return
     */
    public boolean hasFilter() {
        if (!StringUtil.isNullByString(tileCategoryId)) {
            return true;
        }
        if (deliveryFilter != null && !deliveryFilter.isEmpty()) {
            return true;
        }
        if (sortFilter != null && !sortFilter.isEmpty()) {
            return true;
        }
        if (activityFilter != null && !activityFilter.isEmpty()) {
            return true;
        }
        if (priceRangeFilter != null && !priceRangeFilter.isEmpty()) {
            return true;
        }
        if (addBuyFilter != null && !addBuyFilter.isEmpty()) {
            return true;
        }
        if (selectedFilterMap != null && !selectedFilterMap.isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * 获取筛选项拼接字符串
     *
     * @param selectedQuery
     * @return
     */
    private String getFeatureFilterValue(List<String> selectedQuery) {
        if (selectedQuery == null || selectedQuery.isEmpty()) {
            return "";
        }
        StringBuilder querySb = new StringBuilder();
        for (int i = 0; i < selectedQuery.size(); i++) {
            querySb.append(selectedQuery.get(i));
            if (i != selectedQuery.size() - 1) {
                querySb.append(",");
            }
        }
        return querySb.toString();
    }

    /**
     * 获取请求入参
     *
     * @param isRefresh
     * @param stillSearch
     * @return
     */
    private JDJSONObject getRequestParam(boolean isRefresh, boolean stillSearch) {
        // 每次重新搜索的时候重置
        if (currPage == 1) {
            isAppearTimesRelated = 0;
        }

        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("keyword", searchKeyword);
        jdjsonObject.put("page", currPage);
        jdjsonObject.put("pageSize", pageSize);
        if (rank > 0) {
            jdjsonObject.put("rank", rank);
        }
        if (StringUtil.isNotEmpty(batchId)) {
            jdjsonObject.put("batchId", batchId);
        }
        if (!StringUtil.isNullByString(batchKey)) {
            jdjsonObject.put("batchKey", batchKey);
        }
        if (StringUtil.isNotEmpty(promotionId)) {
            jdjsonObject.put("promotionId", promotionId);
        }
        if (StringUtil.isNotEmpty(pickUpCodeBrandId)) {
            jdjsonObject.put("pickUpCodeBrandId", pickUpCodeBrandId);
        }
        jdjsonObject.put("needPriceRange", (isRefresh && (fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE || isFreightSearch())));
        jdjsonObject.put("source", source);
        jdjsonObject.put("pvid", pvId);
        jdjsonObject.put("logid", logId);

        jdjsonObject.put("stillSearch", stillSearch);

        jdjsonObject.put("isAppearTimesRelated", isAppearTimesRelated);

        if (!StringUtil.isNullByString(tileCategoryId)) {
            jdjsonObject.put("tileCategoryId", tileCategoryId);
        }
        if (!StringUtil.isNullByString(tileCategoryName)) {
            jdjsonObject.put("tileCategoryName", tileCategoryName);
        }
        if (!StringUtil.isNullByString(type)) {
            jdjsonObject.put("type", type);
        }

        JDJSONArray jdjsonArray = new JDJSONArray();
        if (deliveryFilter != null && !deliveryFilter.isEmpty()) {
            jdjsonArray.add(deliveryFilter);
        }
        if (sortFilter != null && !sortFilter.isEmpty()) {
            jdjsonArray.add(sortFilter);
        }
        if (activityFilter != null && !activityFilter.isEmpty()) {
            jdjsonArray.add(activityFilter);
        }
        if (priceRangeFilter != null && !priceRangeFilter.isEmpty()) {
            jdjsonArray.add(priceRangeFilter);
        }
        if (addBuyFilter != null && !addBuyFilter.isEmpty()) {
            jdjsonArray.add(addBuyFilter);
        }
        if (selectedFilterMap != null && !selectedFilterMap.isEmpty()) {
            Set<String> filterKeySet = selectedFilterMap.keySet();
            if (filterKeySet != null && !filterKeySet.isEmpty()) {
                for (String filterKey : filterKeySet) {
                    List<String> filterValueList = selectedFilterMap.get(filterKey);
                    if (filterValueList != null && !filterValueList.isEmpty()) {
                        JDJSONObject featureFilter = new JDJSONObject();
                        featureFilter.put("filterKey", filterKey);
                        featureFilter.put("filterValue", getFeatureFilterValue(filterValueList));
                        jdjsonArray.add(featureFilter);
                    }
                }
            }
        }
        if (jdjsonArray != null && !jdjsonArray.isEmpty()) {
            jdjsonObject.put("query", jdjsonArray.toString());
        }
        return jdjsonObject;
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        currPage = 1;
        requestData(activity, true, false, false);
    }

    /**
     * 刷新数据
     */
    public void refreshData(boolean needJumpUrl) {
        currPage = 1;
        requestData(activity, true, needJumpUrl, false);
    }

    /**
     * 请求数据
     *
     * @param activity
     * @param isRefresh
     * @param needJumpUrl
     * @param stillSearch
     */
    private void requestData(Activity activity, boolean isRefresh, boolean needJumpUrl, boolean stillSearch) {
        if (searchResultContainerInterface == null) {
            return;
        }
        searchResultContainerInterface.onRequestPage(currPage);
        if (isRefresh) {
            skuList.clear();
        }
        generateNewSearchPvId();
        String storeId = TenantIdUtils.getStoreId();
        JDJSONObject varsExtra = getRequestParam(isRefresh, stillSearch);
        SearchResultNetwork.requestPost(activity, searchResultContainerInterface.getMainSearchFunId(), isRefresh ? FreshHttpSetting.DEFAULT_EFFECT : FreshHttpSetting.NO_EFFECT, varsExtra, new BaseFreshResultCallback<String, ResponseData<SearchResultResponse>>() {
            @Override
            public ResponseData<SearchResultResponse> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<SearchResultResponse> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<SearchResultResponse>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<SearchResultResponse> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    handlerRequestFail(isRefresh);
                    SgmBusinessErrorUtil.reportMainSearchEmpty(varsExtra.toJSONString());
                    if (varsExtra.containsKey("tileCategoryId") && !varsExtra.containsKey("query")) {
                        SgmBusinessErrorUtil.reportCategorySearchEmpty(varsExtra.toJSONString(),
                                varsExtra.getString("tileCategoryId"), varsExtra.getString("tileCategoryName"),searchKeyword);
                    }
                    return;
                }
                SearchResultResponse searchResultResponse = responseData.getData();
                searchResultInfo = searchResultResponse.getSearchResultInfoVo();
                if (searchResultInfo == null || searchResultInfo.getProductCardVoList() == null || searchResultInfo.getProductCardVoList().isEmpty()) {
                    SgmBusinessErrorUtil.reportMainSearchEmpty(varsExtra.toJSONString());
                    if (varsExtra.containsKey("tileCategoryId") && !varsExtra.containsKey("query")) {
                        SgmBusinessErrorUtil.reportCategorySearchEmpty(varsExtra.toJSONString(),
                                varsExtra.getString("tileCategoryId"), varsExtra.getString("tileCategoryName"),searchKeyword);
                    }
                }
                if (searchResultInfo != null) {
                    if (needJumpUrl && !StringUtil.isNullByString(searchResultInfo.getJumpUrl())) {
                        Bundle bundle = new Bundle();
                        bundle.putString(FloorJumpManager.TO_URL, searchResultInfo.getJumpUrl());
                        bundle.putInt(FloorJumpManager.URL_TYPE, FloorActionConstants.URL_TYPE_M);
                        bundle.putBoolean(FloorJumpManager.NEED_LOGIN, false);
                        FloorInit.getFloorConfig().startPage(activity, bundle);
                        activity.finish();
                        return;
                    }
                    totalCount = searchResultInfo.getTotalCount();
                    if (totalCountWithoutFilter == 0 && totalCount > 0) {
                        totalCountWithoutFilter = totalCount;
                    }
                    totalPage = searchResultInfo.getTotalPage();
                    currPage = searchResultInfo.getPage();

                    mtest = searchResultInfo.getMtest();

                    if (searchResultInfo.getProductCardVoList() != null && !searchResultInfo.getProductCardVoList().isEmpty()) {
                        //移除空对象数据，兼容脏数据情况
                        searchResultInfo.getProductCardVoList().removeAll(Collections.singleton(null));
                        for (SkuInfoBean productInfoBean : searchResultInfo.getProductCardVoList()) {
                            if (productInfoBean != null && !StringUtil.isNullByString(productInfoBean.getSkuId())) {
                                productInfoBean.setLogId(logId);
                                skuList.add(productInfoBean.getSkuId());
                            }
                        }
                    }

                    handlerUnstockRecommend(activity, searchResultInfo.getProductCardVoList());
                }
                if (!hasNextPage() && (searchResultResponse.getBottomProductCardInfo() == null || searchResultResponse.getBottomProductCardInfo().isEmpty())) {
                    SgmBusinessErrorUtil.reportMainSearchBottomWareInfoEmpty(varsExtra.toJSONString());
                }
                if (fromType == SearchConstant.Value.FROM_TYPE_INCREASE_PRICE) {
                    //加价购要求不展示属性筛选条件，但网关无法区分普通促销和加价购，端上单独处理
                    searchResultResponse.setShowSearchFilerQuery(null);
                    if (searchResultInfo != null) {
                        searchResultInfo.setFilterQueries(null);
                    }
                }
                if (searchResultCallback != null) {
                    searchResultCallback.setSearchResult(searchResultResponse, isRefresh, source);
                }
            }

            @Override
            public void onError(FreshHttpException error) {
                handlerRequestFail(isRefresh);
            }
        });
    }

    /**
     * 无货推荐处理
     *
     * @param activity
     * @param productInfoBeans
     */
    private void handlerUnstockRecommend(Activity activity, List<SkuInfoBean> productInfoBeans) {
        needRequestUnStockCount = 0;
        requestedUnStockCount = 0;
        if (productInfoBeans != null && !productInfoBeans.isEmpty()) {
            //主搜商品无货推荐逻辑
            for (SkuInfoBean productInfo : productInfoBeans) {
                if (productInfo == null) {
                    continue;
                }
                if ((productInfo.getStockStatus() == SkuEnumInterface.StockStatus.NO_STOCK) && !isPreSaleing(productInfo)) {
                    String skuId = productInfo.getSkuId();
                    if (unStockRecomMap.containsKey(skuId)) {
                        productInfo.setSimilarList(unStockRecomMap.get(skuId));
                    } else {
                        needRequestUnStockCount++;
                        queryUnStockRecommend(activity, productInfo);
                    }
                }
            }
        }
    }

    /**
     * 是否预售中状态
     *
     * @param wareInfo
     * @return
     */
    private boolean isPreSaleing(SkuInfoBean wareInfo) {
        return wareInfo != null && wareInfo.getLogicInfo() != null && wareInfo.getLogicInfo().getPreSaleInfo() != null
                && wareInfo.getLogicInfo().getPreSaleInfo().isPreSale() && wareInfo.getLogicInfo().getPreSaleInfo().getStatus() == 2;
    }

    /**
     * 请求失败处理
     *
     * @param isRefresh
     */
    private void handlerRequestFail(boolean isRefresh) {
        if (searchResultCallback != null) {
            if (isRefresh) {
                searchResultCallback.showNoData();
            } else {
                searchResultCallback.finishLoadMore();
            }
        }
        if (!isRefresh) {
            currPage--;
        }
    }

    /**
     * 搜索加载更多
     *
     * @param activity
     */
    public void loadMore(Activity activity) {
        if (!hasNextPage()) {
            return;
        }
        currPage++;
        requestData(activity, false, false, false);
    }

    /**
     * 加载更多底部推荐
     *
     * @param activity
     */
    public void loadMoreRecommend(Activity activity) {
        if (searchResultContainerInterface == null) {
            return;
        }
        recommendCurrPage++;
        JDJSONObject varsExtra = getRecommendRequestParam();
        SearchResultNetwork.requestPost(activity, searchResultContainerInterface.getBottomWareInfoFunId(), FreshHttpSetting.NO_EFFECT, varsExtra, new BaseFreshResultCallback<String, ResponseData<SearchRecommendResult>>() {
            @Override
            public ResponseData<SearchRecommendResult> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<SearchRecommendResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<SearchRecommendResult>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<SearchRecommendResult> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    recommendCurrPage--;
                    if (searchResultCallback != null) {
                        searchResultCallback.finishLoadMore();
                    }
                    SgmBusinessErrorUtil.reportBottomWareInfoEmpty(varsExtra.toJSONString());
                    return;
                }
                if (responseData.getData().getProductCardVoList() == null || responseData.getData().getProductCardVoList().isEmpty()) {
                    SgmBusinessErrorUtil.reportBottomWareInfoEmpty(varsExtra.toJSONString());
                }
                if (searchResultCallback != null) {
                    searchResultCallback.setRecommendResult(responseData.getData());
                }
            }

            @Override
            public void onError(FreshHttpException error) {
                recommendCurrPage--;
                if (searchResultCallback != null) {
                    searchResultCallback.finishLoadMore();
                }
            }
        });
    }

    /**
     * 获取请求推荐的入参
     *
     * @return
     */
    private JDJSONObject getRecommendRequestParam() {
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("page", recommendCurrPage);
        jdjsonObject.put("pageSize", recommendPageSize);
        jdjsonObject.put("source", 1);
        if (searchResultInfo != null) {
            jdjsonObject.put("hcCid1s", searchResultInfo.getHcCid1s());
            jdjsonObject.put("hcCid2s", searchResultInfo.getHcCid2s());
            jdjsonObject.put("hcCid3s", searchResultInfo.getHcCid3s());
            jdjsonObject.put("hcCid4s", searchResultInfo.getHcCid4s());
        }
        if (skuList != null && !skuList.isEmpty()) {
            JDJSONArray jdjsonArray = new JDJSONArray();
            for (String skuId : skuList) {
                jdjsonArray.add(skuId);
            }
            if (!jdjsonArray.isEmpty()) {
                jdjsonObject.put("skuList", jdjsonArray);
            }
        }
        return jdjsonObject;
    }

    /**
     * 查询促销描述
     *
     * @param activity
     */
    public void queryPromotionDesc(Activity activity) {
        if (searchResultContainerInterface == null) {
            return;
        }
        if (StringUtil.isNullByString(promotionId)) {
            return;
        }
        long promotionIdL = 0L;
        try {
            promotionIdL = Long.parseLong(promotionId);
        } catch (Exception e) {
            promotionIdL = 0L;
        }

        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("promotionId", promotionIdL);
        SearchResultNetwork.requestPost(activity, searchResultContainerInterface.getPromotionDescFunId(), FreshHttpSetting.DEFAULT_EFFECT, jdjsonObject, new BaseFreshResultCallback<String, ResponseData<SearchPromotionResult>>() {
            @Override
            public ResponseData<SearchPromotionResult> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<SearchPromotionResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<SearchPromotionResult>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<SearchPromotionResult> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    SgmBusinessErrorUtil.reportGetPromotionDescEmpty(promotionId);
                    return;
                }
                if (searchResultCallback != null) {
                    searchResultCallback.setPromotionResult(responseData.getData().getPromotion());
                }
            }

            @Override
            public void onError(FreshHttpException error) {
            }
        });
    }

    /**
     * 查询优惠券详情
     *
     * @param activity
     */
    public void queryCouponDetail(Activity activity) {
        if (searchResultContainerInterface == null) {
            return;
        }
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("couponId", couponId);
        SearchResultNetwork.requestPost(activity, searchResultContainerInterface.getCouponInfoDescFunId(), FreshHttpSetting.DEFAULT_EFFECT, jdjsonObject, new BaseFreshResultCallback<String, ResponseData<SearchCouponResult>>() {
            @Override
            public ResponseData<SearchCouponResult> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<SearchCouponResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<SearchCouponResult>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<SearchCouponResult> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    return;
                }
                if (searchResultCallback != null) {
                    searchResultCallback.setCouponInfoResult(responseData.getData().getSearchCouponInfoVo());
                }
            }

            @Override
            public void onError(FreshHttpException error) {
            }
        });
    }

    /**
     * 查询无货推荐
     *
     * @param activity
     * @param productInfoBean
     */
    public void queryUnStockRecommend(Activity activity, SkuInfoBean productInfoBean) {
        if (searchResultContainerInterface == null) {
            return;
        }
        if (productInfoBean == null || StringUtil.isNullByString(productInfoBean.getSkuId())) {
            return;
        }
        JDJSONArray jdjsonArray = new JDJSONArray();
        jdjsonArray.add(productInfoBean.getSkuId());
        JDJSONObject jdjsonObject = new JDJSONObject();
        jdjsonObject.put("skuIdList", jdjsonArray);
        jdjsonObject.put("source", 7);
        jdjsonObject.put("page", 1);
        jdjsonObject.put("pageSize", 3);
        SearchResultNetwork.requestPost(activity, searchResultContainerInterface.getSkuRecommendInfogFunId(), FreshHttpSetting.DEFAULT_EFFECT, jdjsonObject, new BaseFreshResultCallback<String, ResponseData<UnStockRecommendResult>>() {
            @Override
            public ResponseData<UnStockRecommendResult> onData(String data, FreshHttpSetting httpSetting) {
                ResponseData<UnStockRecommendResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<UnStockRecommendResult>>() {
                }.getType());
                return responseData;
            }

            @Override
            public void onEnd(ResponseData<UnStockRecommendResult> responseData, FreshHttpSetting httpSetting) {
                if (responseData == null || responseData.getData() == null || !responseData.getData().isSuccess()) {
                    handleRefreshUnStockRecommend();
                    SgmBusinessErrorUtil.reportWareRecommendEmpty(productInfoBean.getSkuId());
                    return;
                }
                List<SkuInfoBean> skuRecommendProducts = responseData.getData().getProductCardVoList();
                if (skuRecommendProducts != null && !skuRecommendProducts.isEmpty()) {
                    unStockRecomMap.put(productInfoBean.getSkuId(), skuRecommendProducts);
                    productInfoBean.setSimilarList(skuRecommendProducts);
                    handleRefreshUnStockRecommend();
                } else {
                    handleRefreshUnStockRecommend();
                    SgmBusinessErrorUtil.reportWareRecommendEmpty(productInfoBean.getSkuId());
                }
            }

            @Override
            public void onError(FreshHttpException error) {
                handleRefreshUnStockRecommend();
            }
        });
    }

    /**
     * 无货推荐请求完成，校验是否执行刷新列表
     */
    private void handleRefreshUnStockRecommend() {
        requestedUnStockCount++;
        if (requestedUnStockCount >= needRequestUnStockCount && searchResultCallback != null) {
            searchResultCallback.setUnStockRecommend();
        }
    }

    /**
     * 是否可打开筛选面板
     *
     * @return
     */
    public boolean canOpenFragmentFilter() {
        if (totalCount == 0 && !hasSelectedFilter()) {
            return false;
        }
        return true;
    }

    /**
     * 是否有选中的筛选条件，包括大促活动、价格区间、属性筛选
     *
     * @return
     */
    public boolean hasSelectedFilter() {
        if ((activityFilter == null || activityFilter.isEmpty())
                && (priceRangeFilter == null || priceRangeFilter.isEmpty())
                && (selectedFilterMap == null || selectedFilterMap.isEmpty())) {
            return false;
        }
        return true;
    }

    /**
     * 是否还有下一页
     *
     * @return
     */
    public boolean hasNextPage() {
        return currPage < totalPage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    /**
     * 是否运费凑单
     *
     * @return
     */
    public boolean isFreightSearch() {
        return fromType == SearchConstant.Value.FROM_TYPE_FREIGHT;
    }

    /**
     * 透传参数集合
     *
     * @param bundle
     */
    public void setExtraBundle(Bundle bundle) {
        fromType = bundle.getInt(SearchConstant.Key.FROM_TYPE);
        searchKeyword = bundle.getString(SearchConstant.Key.SEARCH_KEYWORD, "");
        keywordClickFrom = bundle.getString(SearchConstant.Key.KEYWORD_CLICKFROM);
        batchId = bundle.getString(SearchConstant.Key.BATCH_ID, "");
        batchKey = bundle.getString(SearchConstant.Key.BATCH_KEY, "");
        couponId = bundle.getString(SearchConstant.Key.COUPON_ID);
        promotionId = bundle.getString(SearchConstant.Key.PROMOTION_ID, "");
        pickUpCodeBrandId = bundle.getString(SearchConstant.Key.PICKING_CODE_BRANDID);
        rank = bundle.getInt(SearchConstant.Key.RANK_SORT_INDEX);

        switch (fromType) {
            case SearchConstant.Value.FROM_TYPE_SEARCH:
                source = SearchEntrance.MAIN_SEARCH.ordinal();
                break;
            case SearchConstant.Value.FROM_TYPE_COUPON:
                source = SearchEntrance.COUPON_SEARCH.ordinal();
                break;
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                source = SearchEntrance.PROMOTION_SEARCH.ordinal();
                break;
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                source = SearchEntrance.PICKINGCODE_SEARCH.ordinal();
                break;
            case SearchConstant.Value.FROM_TYPE_FREIGHT:
                source = SearchEntrance.FREIGHT_SEARCH.ordinal();
                break;
            default:
                break;
        }
    }

    public void setSearchResultCallback(SearchResultCallback searchResultCallback) {
        this.searchResultCallback = searchResultCallback;
    }

    public void setSearchResultContainerInterface(SearchResultContainerInterface searchResultContainerInterface) {
        this.searchResultContainerInterface = searchResultContainerInterface;
    }

    /**
     * @return 获取埋点接口
     */
    public JDMaUtils.JdMaPageImp getJdMaPageImp() {
        if (searchResultContainerInterface != null) {
            return searchResultContainerInterface.getJdMaPageImp();
        }
        return null;
    }

    /**
     * 新的搜索 要生成一个新的pvId
     * 新的搜索都是从第一页开始的
     */
    private void generateNewSearchPvId() {
        if (currPage == 1) {
            pvId = getRandomString(32);
            logId = pvId;
        } else {
            //当前页码是其他页 需要生成新的logId
            logId = getRandomString(32);
        }
    }

    /**
     * 获取一个随机值
     *
     * @param length 随机字符串的长度
     * @return 返回一个随机值
     */
    private String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(36);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }
}
