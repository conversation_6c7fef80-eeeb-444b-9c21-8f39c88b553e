package com.xstore.floorsdk.fieldsearch.adapter;

import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_HISTORY_FOLD;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_HISTORY_UNFOLD;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchHistoryTable;
import com.xstore.floorsdk.fieldsearch.bean.DiscoveryDataBean;
import com.xstore.floorsdk.fieldsearch.bean.DiscoveryResultData;
import com.xstore.floorsdk.fieldsearch.bean.SearchHistory;
import com.xstore.floorsdk.fieldsearch.ma.DiscoveryMaBean;
import com.xstore.floorsdk.fieldsearch.widget.FlowLayout;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.image.manager.ImageLoaderUtil;
import com.xstore.sevenfresh.tks.baseinfo.BaseInfoProxyUtil;
import com.xstore.sevenfresh.datareport.JDMaUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/08/05
 */
public class NewSearchAdapter extends RecyclerView.Adapter {
    private final boolean searchHome;
    /**
     * baseActivity
     */
    private Activity mBaseActivity;
    /**
     * 埋点
     */
    private JDMaUtils.JdMaPageImp jdMaPageImp = null;
    /**
     * 历史数据
     */
    private List<SearchHistory> mHistoryWords = new ArrayList<>();

    /**
     * 历史搜索
     */
    public final static int TYPE_HISTORY = 1;
    /**
     * 热门搜索
     */
    public final static int TYPE_HOT = 2;

    /**
     * 搜索发现
     */
    public final static int TYPE_DISCOVERY = 3;
    /**
     * 历史搜索-删除
     */
    public final static int TYPE_HISTORY_DEL = 100;
    /**
     * 历史搜索-展开
     */
    public final static int TYPE_HISTORY_OPEN = 101;

    private onItemListener listener;

    private OnDiscoveryItemListener discoveryItemListener;

    private boolean showHistory;
    /**
     * 监听
     */
    private int mMaxLine = 2;

    private DiscoveryResultData discoveryResultData;

    private List<DiscoveryDataBean> discoveryDataBeans;


    public void setDiscoveryResultData(DiscoveryResultData discoveryResultData) {
        this.discoveryResultData = discoveryResultData;
        if (discoveryResultData != null) {
            this.discoveryDataBeans = discoveryResultData.getRecQueryList();
            notifyDataSetChanged();
        }
    }

    public NewSearchAdapter(Activity baseActivity, JDMaUtils.JdMaPageImp jdMaPageImp, boolean showHistory, boolean searchHome) {
        this.mBaseActivity = baseActivity;
        this.jdMaPageImp = jdMaPageImp;
        this.mMaxLine = 2;
        this.showHistory = showHistory;
        if (showHistory) {
            mHistoryWords = SearchHistoryTable.getAllSearchHistory();
        } else {
            mHistoryWords.clear();
        }
        this.searchHome = searchHome;
    }

    public void setListener(onItemListener listener) {
        this.listener = listener;
    }

    public void setDiscoveryItemListener(OnDiscoveryItemListener discoveryItemListener) {
        this.discoveryItemListener = discoveryItemListener;
    }

    public interface onItemListener {
        void onDelListener();

        void onHotwordListener(int position, boolean isUrl, String keyword, String url);

        void onHistoryListener(String keyword);
    }

    public interface OnDiscoveryItemListener {

        void onItemListener(DiscoveryDataBean discoveryDataBean);
    }

    public void refreshData() {
        if (showHistory) {
            mHistoryWords = SearchHistoryTable.getAllSearchHistory();
        } else {
            mHistoryWords.clear();
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

        if (viewType == TYPE_HISTORY) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.sf_field_search_item_new_history, parent, false);
            return new HistoryHolder(itemView);
        } else if (viewType == TYPE_DISCOVERY) {
            View discoveryView = LayoutInflater.from(mBaseActivity).inflate(R.layout.sf_field_search_item_discovery, parent, false);
            return new DiscoveryViewHolder(discoveryView);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof HistoryHolder) {
            ((HistoryHolder) holder).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mMaxLine == 2) {
                        mMaxLine = 4;
                        JDMaUtils.save7FClick(CLICK_SEARCH_HISTORY_UNFOLD, jdMaPageImp, null);
                    } else {
                        mMaxLine = 2;
                        JDMaUtils.save7FClick(CLICK_SEARCH_HISTORY_FOLD, jdMaPageImp, null);
                    }
                    notifyDataSetChanged();
                }
            });

            ((HistoryHolder) holder).setOnItemListener(listener);
            ((HistoryHolder) holder).fillData(mBaseActivity, mHistoryWords, false, mMaxLine);
        } else if (holder instanceof DiscoveryViewHolder) {
            ((DiscoveryViewHolder) holder).setData(mBaseActivity, discoveryItemListener);
        }
    }

    @Override
    public int getItemCount() {
        int count = 0;
        if (mHistoryWords != null && mHistoryWords.size() > 0) {
            count += 1;
        }
        if (isShowDiscovery()) {
            count += 1;
        }
        return count;
    }

    private boolean isShowDiscovery() {
        return discoveryResultData != null && discoveryResultData.getRecQueryList() != null && discoveryResultData.getRecQueryList().size() > 3;
    }

    @Override
    public int getItemViewType(int position) {
        int viewType = 0;
        if (mHistoryWords != null && mHistoryWords.size() > 0) {
            if (position == 0) {
                viewType = TYPE_HISTORY;
            } else if (position == 1 && isShowDiscovery()) {
                viewType = TYPE_DISCOVERY;
            } else {
                viewType = TYPE_HOT;
            }
        } else {
            if (position == 0 && isShowDiscovery()) {
                viewType = TYPE_DISCOVERY;
            } else {
                viewType = TYPE_HOT;
            }
        }
        return viewType;
    }

    public static class HistoryHolder extends RecyclerView.ViewHolder {

        /**
         * 监听
         */
        private View.OnClickListener mClickListener;
        private onItemListener listener;
        /**
         * 历史标题布局
         */
        private RelativeLayout mHistoryTittleLayout;
        /**
         * 历史标题
         */
        private TextView mHistoryTittle;
        /**
         * 历史删除按钮
         */
        private ImageView mHistoryDel;

        /**
         * 历史tags布局
         */
        private RelativeLayout mHistoryTagsLayout;

        /**
         * 历史tags
         */
        private LinearLayout mTagsLayout;

        /**
         * 分割线
         */
        private View mHistoryHotLine;
        /**
         * 热门标题布局
         */
        private LinearLayout mHotTittleLayout;

        /**
         * 热门标题
         */
        private TextView mHotTittleTv;

        /**
         * 遍历结束
         */
        private boolean isEnd = false;


        /**
         * @param itemView
         */
        public HistoryHolder(View itemView) {
            super(itemView);

            mHistoryTittleLayout = (RelativeLayout) itemView.findViewById(R.id.history_layout);
            mHistoryTittle = (TextView) itemView.findViewById(R.id.tv_search_history);
            mHistoryDel = (ImageView) itemView.findViewById(R.id.delete_history);
            mHistoryTagsLayout = (RelativeLayout) itemView.findViewById(R.id.tagflow_layout);
            mTagsLayout = (LinearLayout) itemView.findViewById(R.id.history_wordsgrid);


            mHistoryHotLine = (View) itemView.findViewById(R.id.history_hot_line);
            mHotTittleLayout = (LinearLayout) itemView.findViewById(R.id.ll_hot_words);
            mHotTittleTv = (TextView) itemView.findViewById(R.id.tv_hot_words);
        }

        /**
         * @param l
         */
        public void setOnClickListener(View.OnClickListener l) {
            mClickListener = l;
        }

        public void setOnItemListener(onItemListener listener) {
            this.listener = listener;
        }

        /**
         * @param mContext
         */
        public void fillData(Activity mContext, List<SearchHistory> mHistoryWords, boolean isShowHot, int mMaxLine) {
            if (mHistoryWords != null && mHistoryWords.size() > 0) {
                isEnd = false;

                mHistoryTittleLayout.setVisibility(View.VISIBLE);
                mHistoryDel.setVisibility(View.VISIBLE);
                mHistoryDel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) {
                            listener.onDelListener();
                        }
                    }
                });
                mHistoryDel.setTag(TYPE_HISTORY_DEL);
                mHistoryTagsLayout.setVisibility(View.VISIBLE);
                mTagsLayout.setVisibility(View.VISIBLE);

                if (isShowHot) {
                    mHistoryHotLine.setVisibility(View.VISIBLE);
                    mHotTittleLayout.setVisibility(View.VISIBLE);
                } else {
                    mHistoryHotLine.setVisibility(View.GONE);
                    mHotTittleLayout.setVisibility(View.GONE);
                }
            } else {
                mHistoryTittleLayout.setVisibility(View.GONE);
                mHistoryTagsLayout.setVisibility(View.GONE);
                mTagsLayout.setVisibility(View.GONE);
                mHistoryHotLine.setVisibility(View.GONE);
                mHistoryDel.setVisibility(View.GONE);
                if (isShowHot) {
                    mHotTittleLayout.setVisibility(View.VISIBLE);
                } else {
                    mHotTittleLayout.setVisibility(View.GONE);
                }
            }


            int lineWidth = 0;
            int lineHeight = 0;
            int lines = 0;

            // 获取屏幕宽和高
            int mScreenWidths = BaseInfoProxyUtil.getScreenWidth();
            mTagsLayout.removeAllViewsInLayout();

            //展开按钮
            LinearLayout.LayoutParams layoutParams1 = new LinearLayout.LayoutParams(ScreenUtils.dip2px(mContext, 28), ScreenUtils.dip2px(mContext, 28));
            layoutParams1.setMargins(0, 0, ScreenUtils.dip2px(mContext, 8), ScreenUtils.dip2px(mContext, 8));

            //每行布局
            LinearLayout linearLayout = new LinearLayout(mContext.getApplicationContext());
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            linearLayout.setLayoutParams(params);
            linearLayout.setOrientation(LinearLayout.HORIZONTAL);

            //tv属性
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, ScreenUtils.dip2px(mContext, 28));
            layoutParams.setMargins(0, 0, ScreenUtils.dip2px(mContext, 8), ScreenUtils.dip2px(mContext, 8));


            ImageView imageView = new ImageView(mContext);
            imageView.setScaleType(ImageView.ScaleType.CENTER);
            imageView.setImageResource(R.drawable.sf_field_search_history_combined_shape);
            imageView.setBackgroundResource(R.drawable.sf_field_search_corner_6_white_bg);
            imageView.setLayoutParams(layoutParams1);
            imageView.setPadding(ScreenUtils.dip2px(mContext, 7), ScreenUtils.dip2px(mContext, 7), ScreenUtils.dip2px(mContext, 7), ScreenUtils.dip2px(mContext, 7));
            imageView.setOnClickListener(mClickListener);
            imageView.setTag(TYPE_HISTORY_OPEN);
            for (int i = 0; i < mHistoryWords.size(); i++) {
                if (isEnd) {
                    continue;
                }
                final SearchHistory searchHistory = mHistoryWords.get(i);
                TextView textView = new TextView(mContext);
                textView.setIncludeFontPadding(false);
                textView.setBackgroundResource(R.drawable.sf_field_search_corner_6_white_bg);
                textView.setText(mHistoryWords.get(i).getWord());
                textView.setTextColor(ContextCompat.getColor(mContext, R.color.sf_field_search_color_1a1a1a));
                textView.setMaxLines(1);
                textView.setEllipsize(TextUtils.TruncateAt.END);
                textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
                textView.setGravity(Gravity.CENTER);
                textView.setLayoutParams(layoutParams);
                textView.setPadding(ScreenUtils.dip2px(mContext, 10), ScreenUtils.dip2px(mContext, 5), ScreenUtils.dip2px(mContext, 10), ScreenUtils.dip2px(mContext, 5));
                int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                textView.measure(w, h);
                int height = textView.getMeasuredHeight();
                int width = textView.getMeasuredWidth();

                if (lines < 1) {
                    if (width + lineWidth > mScreenWidths - 2 * ScreenUtils.dip2px(mContext, 20)) {
                        if (i == 0) {
                            linearLayout.addView(textView);
                            lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = Math.max(lineHeight, height);
                        } else {
                            mTagsLayout.addView(linearLayout);

                            linearLayout = new LinearLayout(mContext.getApplicationContext());
                            params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                            linearLayout.setLayoutParams(params);
                            linearLayout.setOrientation(LinearLayout.HORIZONTAL);

                            if (i < mHistoryWords.size() - 1 && mMaxLine == 2) {
                                textView.setMaxWidth(mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28));
                            }
                            linearLayout.addView(textView);

                            lines++;
                            lineWidth = width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = height;
                        }
                    } else {
                        linearLayout.addView(textView);
                        lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                        lineHeight = Math.max(lineHeight, height);
                    }
                    if (i == mHistoryWords.size() - 1) {
                        mTagsLayout.addView(linearLayout);
                    }
                } else if ((lines == 1 && mMaxLine == 2)) {
                    if (width + lineWidth > mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28)) {
                        isEnd = true;
                        linearLayout.addView(imageView);
                        mTagsLayout.addView(linearLayout);

                    } else {
                        linearLayout.addView(textView);
                        lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                        lineHeight = Math.max(lineHeight, height);

                        if (i == mHistoryWords.size() - 1) {
                            mTagsLayout.addView(linearLayout);
                        }
                    }
                } else if (lines == 1 && mMaxLine == 4) {
                    if (i == mHistoryWords.size() - 1) {
                        if (width + lineWidth > mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28)) {

                            mTagsLayout.addView(linearLayout);
                            linearLayout = new LinearLayout(mContext.getApplicationContext());
                            params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                            linearLayout.setLayoutParams(params);
                            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
                            if (i == mHistoryWords.size() - 1) {
                                textView.setMaxWidth(mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28));
                            }
                            linearLayout.addView(textView);

                            lines++;
                            lineWidth = width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = height;

                        } else {
                            linearLayout.addView(textView);
                            lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = Math.max(lineHeight, height);
                        }
                        if (i == mHistoryWords.size() - 1) {
                            linearLayout.addView(imageView);
                            mTagsLayout.addView(linearLayout);
                        }
                    } else {
                        if (width + lineWidth > mScreenWidths - 2 * ScreenUtils.dip2px(mContext, 20)) {

                            mTagsLayout.addView(linearLayout);
                            linearLayout = new LinearLayout(mContext.getApplicationContext());
                            params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                            linearLayout.setLayoutParams(params);
                            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
                            linearLayout.addView(textView);

                            lines++;
                            lineWidth = width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = height;

                        } else {
                            linearLayout.addView(textView);
                            lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = Math.max(lineHeight, height);
                        }
                    }
                } else if (lines == 2 && mMaxLine == 4) {
                    if (i == mHistoryWords.size() - 1) {
                        if (width + lineWidth > mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28)) {

                            mTagsLayout.addView(linearLayout);
                            linearLayout = new LinearLayout(mContext.getApplicationContext());
                            params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                            linearLayout.setLayoutParams(params);
                            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
                            if (i == mHistoryWords.size() - 1) {
                                textView.setMaxWidth(mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28));
                            }
                            linearLayout.addView(textView);

                            lines++;
                            lineWidth = width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = height;

                        } else {
                            linearLayout.addView(textView);
                            lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = Math.max(lineHeight, height);
                        }
                        if (i == mHistoryWords.size() - 1) {
                            linearLayout.addView(imageView);
                            mTagsLayout.addView(linearLayout);
                        }
                    } else {
                        if (width + lineWidth > mScreenWidths - 2 * ScreenUtils.dip2px(mContext, 20)) {

                            mTagsLayout.addView(linearLayout);
                            linearLayout = new LinearLayout(mContext.getApplicationContext());
                            params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                            linearLayout.setLayoutParams(params);
                            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
                            textView.setMaxWidth(mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28));
                            linearLayout.addView(textView);

                            lines++;
                            lineWidth = width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = height;

                        } else {
                            linearLayout.addView(textView);
                            lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                            lineHeight = Math.max(lineHeight, height);
                        }
                    }


                } else if (lines == 3 && mMaxLine == 4) {
                    if (width + lineWidth > mScreenWidths - 3 * ScreenUtils.dip2px(mContext, 20) - ScreenUtils.dip2px(mContext, 28)) {
                        isEnd = true;
                        linearLayout.addView(imageView);
                        mTagsLayout.addView(linearLayout);

                    } else {
                        linearLayout.addView(textView);
                        lineWidth += width + ScreenUtils.dip2px(mContext, 8);
                        lineHeight = Math.max(lineHeight, height);

                        if (i == mHistoryWords.size() - 1) {
                            linearLayout.addView(imageView);
                            mTagsLayout.addView(linearLayout);
                        }
                    }
                }


                textView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        if (listener != null) {
                            listener.onHistoryListener(searchHistory.getWord());
                        }

                    }
                });

            }
            if (mMaxLine == 2) {
                imageView.setRotation(0);
            } else {
                imageView.setRotation(180);
            }

        }
    }

    public class DiscoveryViewHolder extends RecyclerView.ViewHolder {

        public FlowLayout flowLayout;

        public DiscoveryViewHolder(View itemView) {
            super(itemView);
            flowLayout = itemView.findViewById(R.id.flow_discovery_layout);
        }

        public void setData(Context context, OnDiscoveryItemListener discoveryItemListener) {
            flowLayout.removeAllViews();
            flowLayout.setAdapter(new BaseAdapter() {
                @Override
                public int getCount() {
                    return discoveryDataBeans == null ? 0 : discoveryDataBeans.size();
                }

                @Override
                public DiscoveryDataBean getItem(int position) {
                    if (discoveryDataBeans != null && discoveryDataBeans.size() > position) {
                        return discoveryDataBeans.get(position);
                    }
                    return null;
                }

                @Override
                public long getItemId(int position) {
                    return position;
                }

                @Override
                public View getView(int position, View convertView, ViewGroup parent) {
                    DiscoveryDataBean dataBean = getItem(position);
                    if (dataBean == null || TextUtils.isEmpty(dataBean.getQuery())) {
                        return null;
                    }
                    DiscoveryMaBean ma = new DiscoveryMaBean();
                    ma.abTestResult = discoveryResultData.getAbTestResult();
                    if (discoveryResultData.getAbTestResult() != null) {
                        ma.touchstone_expids = discoveryResultData.getAbTestResult().buriedExpLabel;
                    }
                    ma.query = dataBean.getQuery();
                    ma.baseExt = dataBean.getBaseExt();
                    ma.source = dataBean.getSource();
                    if (context instanceof JDMaUtils.JdMaPageImp) {
                        JDMaUtils.save7FExposure("searchPage_discover_expose", null, ma, null, (JDMaUtils.JdMaPageImp) context);
                    }
                    int imgCorner = ScreenUtils.dip2px(context, 4);
                    View itemView = LayoutInflater.from(context).inflate(R.layout.sf_field_search_discovery_item, parent, false);
                    TextView textView = itemView.findViewById(R.id.tv_discovery);
                    RoundCornerImageView1 imageView = itemView.findViewById(R.id.iv_discovery);
                    imageView.setRadius(imgCorner, imgCorner, imgCorner, imgCorner);
                    textView.setText(dataBean.getQuery());
                    if (!StringUtil.isNullByString(dataBean.getImgUrl())) {
                        imageView.setVisibility(View.VISIBLE);
                        ImageloadUtils.loadImage(context, imageView, dataBean.getImgUrl(), R.drawable.sf_field_search_corner_4_f6f6f6_bg, R.drawable.sf_field_search_corner_4_f6f6f6_bg);
                    } else {
                        imageView.setVisibility(View.GONE);
                    }
                    itemView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (context instanceof JDMaUtils.JdMaPageImp) {
                                JDMaUtils.save7FClick("searchPage_discover_click", (JDMaUtils.JdMaPageImp) context, ma);
                            }
                            if (discoveryItemListener != null) {
                                discoveryItemListener.onItemListener(dataBean);
                            }
                        }
                    });
                    return itemView;
                }
            });
        }
    }
}
