package com.xstore.floorsdk.fieldsearch;

import static com.xstore.floorsdk.fieldsearch.SearchConstant.Value.FROM_TYPE_PROMOTION;
import static com.xstore.floorsdk.fieldsearch.SearchConstant.Value.FROM_TYPE_SEARCH;

import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.interfaces.FloorConfig;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

/**
 * 搜索领域业务异常上报工具类
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
public class SgmBusinessErrorUtil {

    private static void reportSearchHomeError(String errorCode, String ext1) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9529;
            errorLog.errorCode = errorCode;
            errorLog.ext1 = ext1;
            errorLog.location = "搜索页";
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportSearchNewHotWordEmpty() {
//        StringBuilder sb = new StringBuilder("搜索热词为空，tenantId = ");
//        sb.append(TenantIdUtils.getTenantId());
//        sb.append(", storeId = ");
//        sb.append(TenantIdUtils.getStoreId());
//        reportSearchHomeError("搜索主页_搜索热词为空", sb.toString());
    }

    public static void reportFrequentPurchaseEmpty() {
        StringBuilder sb = new StringBuilder("常购清单为空，tenantId = ");
        sb.append(TenantIdUtils.getTenantId());
        sb.append(", storeId = ");
        sb.append(TenantIdUtils.getStoreId());
        SgmBusinessErrorUtil.reportSearchHomeError("搜索主页_常购清单为空", sb.toString());
    }

    public static void reportRankBaseInfoListEmpty() {
        StringBuilder sb = new StringBuilder("热销榜单为空，tenantId = ");
        sb.append(TenantIdUtils.getTenantId());
        sb.append(", storeId = ");
        sb.append(TenantIdUtils.getStoreId());
        SgmBusinessErrorUtil.reportSearchHomeError("搜索主页_热销榜单为空", sb.toString());
    }

    public static void reportGetAutoSpellEmpty(String keyword) {
//        SgmBusinessErrorUtil.reportSearchHomeError("搜索主页_联想词为空", "联想词为空，keyword = " + keyword);
    }

    private static void reportSearchResultError(String errorCode, String ext1) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9529;
            errorLog.errorCode = errorCode;
            errorLog.ext1 = ext1;
            errorLog.location = "搜索结果页";
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 搜索业务告警
     * @param errorCode
     * @param ext1
     */
    private static void reportNewSearchResultError(String errorCode, String ext1) {
        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9550;
            errorLog.errorCode = errorCode;
            errorLog.ext1 = ext1;
            errorLog.location = "搜索页";
            errorLog.errorMsg ="搜索业务数据告警";
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reportMainSearchEmpty(String param) {
        reportSearchResultError("搜索结果_未搜索到结果", "搜索结果为空，param = " + param);
    }

    public static void reportCategorySearchEmpty(String param, String cateId, String cateName, String keyword) {
        String storeName = "";
        if (AddressStoreHelper.getAddressStoreBean() != null) {
            storeName = AddressStoreHelper.getAddressStoreBean().getStoreName();
        }
        String storeId = TenantIdUtils.getStoreId();
        reportNewSearchResultError("平铺类目搜索结果为空" + "_搜索关键词:" + keyword + "_门店名称:" + storeName + "_门店ID:" + storeId + "_类目名称:" + cateName + "_类目ID:" + cateId, "搜索结果为空，param = " + param);
    }

    public static void reportSceneWordImageEmpty(String keyword) {
        reportSearchResultError("搜索结果_有场景词_图片为空" + "_场景词:" + keyword, "搜索结果返回了场景词，但图片为空，keyword = " + keyword);
    }

    public static void reportFirstFilterCriteriaEmpty(String keyword, int source) {
        //促销搜3 本身没有筛选
        if (source != FROM_TYPE_SEARCH) {
            return;
        }
        //云超也不支持
        if (FloorInit.getFloorConfig() != null && FloorInit.getFloorConfig().isCloudMarket()) {
            return;
        }

//        reportSearchResultError("搜索结果_一级筛选项（时效筛选）为空或小于2个", "搜索结果返回的一级筛选项（时效筛选）为空或小于2个，keyword = " + keyword);
    }

    public static void reportSecondFilterCriteriaEmpty(String keyword) {
        reportSearchResultError("搜索结果_二级筛选项（排序筛选）为空" + "_场景词:" + keyword + "_门店Id:" + TenantIdUtils.getStoreId(), "搜索结果返回的二级筛选项（排序筛选）为空，keyword = " + keyword);
    }

    public static void reportMainSearchBottomWareInfoEmpty(String param) {
//        reportSearchResultError("搜索结果_第一页推荐数据为空"+ "_门店Id:"+TenantIdUtils.getStoreId(), "搜索结果返回的第一页推荐数据为空，param = " + param);
    }

    public static void reportBottomWareInfoEmpty(String param) {
        reportSearchResultError("搜索结果_底部推荐数据为空" + "_门店Id:" + TenantIdUtils.getStoreId(), "底部推荐数据为空，param = " + param);
    }

    public static void reportGetPromotionDescEmpty(String promotionId) {
        reportSearchResultError("搜索结果_促销信息为空" + "_门店Id:" + TenantIdUtils.getStoreId(), "促销信息为空，promotionId = " + promotionId);
    }

    public static void reportWareRecommendEmpty(String skuId) {
        reportSearchResultError("搜索结果_无货推荐数据为空" + "_门店Id:" + TenantIdUtils.getStoreId(), "无货推荐数据为空，skuId = " + skuId);
    }
}
