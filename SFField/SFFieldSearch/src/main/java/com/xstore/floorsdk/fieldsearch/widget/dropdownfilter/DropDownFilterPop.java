package com.xstore.floorsdk.fieldsearch.widget.dropdownfilter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.TranslateAnimation;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.jd.framework.json.JDJSONArray;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 下拉展开筛选面板
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
public class DropDownFilterPop extends PopupWindow {

    private Context context;
    private View vMask;
    private LinearLayout llDropDownContainer;
    private RecyclerView rvFilter;
    private LinearLayout llAction;
    private TextView tvReset;
    private TextView tvConfirm;
    private DropDownFilterAdapter filterAdapter;
    private OnActionClickListener onActionClickListener;
    private SearchResultDataManager searchResultDataManager;
    private SearchFilterQuery filterQuery;

    public DropDownFilterPop(Context context) {
        super(context);
        this.context = context;
        View contentView = LayoutInflater.from(context).inflate(R.layout.sf_field_search_drop_down_filter, null);
        setContentView(contentView);
        initView(contentView);
        initListener();

        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        this.setFocusable(true);
        this.setClippingEnabled(false);
        this.setOutsideTouchable(true);
    }

    private void initView(View view) {
        vMask = view.findViewById(R.id.v_mask);
        vMask.startAnimation(AnimationUtils.loadAnimation(context, R.anim.sf_field_search_pop_win_alpha_in));
        llDropDownContainer = view.findViewById(R.id.ll_drop_down_container);
        rvFilter = view.findViewById(R.id.rv_drop_down_filter);
        llAction = view.findViewById(R.id.ll_drop_down_action);
        tvReset = view.findViewById(R.id.tv_reset);
        tvConfirm = view.findViewById(R.id.tv_confirm);

        TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_SELF, -1, Animation.RELATIVE_TO_SELF, 0);
        animation.setDuration(200);
        llDropDownContainer.startAnimation(animation);
    }

    private void initListener() {
        vMask.setOnClickListener(v -> dismiss());
        tvReset.setOnClickListener(v -> {
            if (filterQuery != null && searchResultDataManager != null) {
                searchResultDataManager.removeFeatureFilter(filterQuery.getFilterKey());
            }
            if (onActionClickListener != null) {
                onActionClickListener.onReset();
            }
        });
        tvConfirm.setOnClickListener(v -> {
            if (onActionClickListener != null) {
                onActionClickListener.onConfirm();
            }
            dismiss();
            searchResultDataManager.searchResultReporter.clickFilterQueryConfirm(filterQuery.getFilterLable(), getSelectQuery());
        });
    }

    /**
     * 获取选中的筛选子选项（埋点用）
     *
     * @return
     */
    private JDJSONArray getSelectQuery() {
        JDJSONArray jdjsonArray = new JDJSONArray();
        if (filterQuery != null && filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
            for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                if (query != null && query.isSelected()) {
                    jdjsonArray.add(filterQuery.getFilterLable() + "#" + query.getName());
                }
            }
        }
        return jdjsonArray;
    }

    /**
     * 绑定数据
     *
     * @param searchResultDataManager
     * @param filterQuery
     */
    public void setFilterQuery(SearchResultDataManager searchResultDataManager, SearchFilterQuery filterQuery) {
        this.searchResultDataManager = searchResultDataManager;
        this.filterQuery = filterQuery;
        if (filterAdapter == null) {
            filterAdapter = new DropDownFilterAdapter(context, searchResultDataManager);
            filterAdapter.setOnItemClickListener(new DropDownFilterAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    SearchFilterQuery parentFilterQuery = filterAdapter.getFilterQuery();
                    SearchFilterQuery childFilterQuery = filterAdapter.getItem(position);

                    searchResultDataManager.searchResultReporter.exposureFragmentFilterClick(parentFilterQuery, childFilterQuery);
                }

                @Override
                public void dismissPop() {
                    dismiss();
                }
            });
            rvFilter.setAdapter(filterAdapter);
        }
        setRecyclerViewHeight();
        if (filterQuery == null) {
            return;
        }
        if (filterQuery.isSortFilter()) {
            rvFilter.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
            llAction.setVisibility(View.GONE);
        } else {
            rvFilter.setLayoutManager(new GridLayoutManager(context, 2));
            llAction.setVisibility(View.VISIBLE);
        }
        filterAdapter.setFilterQuery(filterQuery);
    }

    /**
     * 设置总数量
     *
     * @param totalCount
     */
    public void setTotalCount(int totalCount) {
        if (tvConfirm != null) {
            tvConfirm.setText(context.getString(R.string.sf_field_search_confirm_with_count, totalCount));
        }
    }

    public void notifyDataSetChanged() {
        if (filterAdapter != null) {
            filterAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 更新弹窗高度
     */
    private void setRecyclerViewHeight() {
        ViewGroup.LayoutParams lp = rvFilter.getLayoutParams();
        if (filterQuery != null && filterQuery.getFilterValues() != null && filterQuery.getFilterValues().size() > 8) {
            lp.height = ScreenUtils.dip2px(context, 180);
        } else {
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        rvFilter.setLayoutParams(lp);
    }

    @Override
    public void showAsDropDown(View anchor) {
        try {
//            super.showAsDropDown(anchor);
            if (Build.VERSION.SDK_INT < 24) {
                super.showAsDropDown(anchor);
            } else {
                int[] location = new int[2];
                anchor.getLocationInWindow(location);
                super.showAtLocation(anchor, Gravity.NO_GRAVITY, location[0], location[1] + anchor.getHeight());
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public void dismiss() {
        TranslateAnimation animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_SELF, 1, Animation.RELATIVE_TO_SELF, 0);
        animation.setDuration(300);
        llDropDownContainer.startAnimation(animation);
        vMask.startAnimation(AnimationUtils.loadAnimation(context, R.anim.sf_field_search_pop_win_alpha_out));
        super.dismiss();
    }

    public void setOnActionClickListener(OnActionClickListener onActionClickListener) {
        this.onActionClickListener = onActionClickListener;
    }

    interface OnActionClickListener {

        void onConfirm();

        void onReset();

        void onFilter();
    }
}
