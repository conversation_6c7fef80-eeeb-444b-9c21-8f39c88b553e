package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

public class DiscoveryDataBean implements Serializable {

    /**
     * query词
     */
    private String query;
    /**
     * 来源
     */
    private String source;
    /**
     * 扩展字段
     */
    private String baseExt;
    /**
     * 图标链接
     */
    private String imgUrl;

    public void setBaseExt(String baseExt) {
        this.baseExt = baseExt;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getQuery() {
        return query;
    }

    public String getSource() {
        return source;
    }

    public String getBaseExt() {
        return baseExt;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
}
