package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

/**
 * 搜索优惠券详情数据模型
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class SearchCouponResult implements Serializable {
    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 优惠券信息
     */
    private CouponInfo searchCouponInfoVo;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public CouponInfo getSearchCouponInfoVo() {
        return searchCouponInfoVo;
    }

    public void setSearchCouponInfoVo(CouponInfo searchCouponInfoVo) {
        this.searchCouponInfoVo = searchCouponInfoVo;
    }
}
