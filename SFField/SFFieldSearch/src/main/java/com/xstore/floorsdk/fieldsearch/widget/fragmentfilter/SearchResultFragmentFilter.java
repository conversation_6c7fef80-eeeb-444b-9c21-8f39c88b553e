package com.xstore.floorsdk.fieldsearch.widget.fragmentfilter;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.gyf.barlibrary.ImmersionBar;
import com.jd.framework.json.JDJSONArray;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchConstant;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;

import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 搜索结果页筛选面板
 *
 * <AUTHOR>
 * @date 2022/09/24
 */
public class SearchResultFragmentFilter extends Fragment implements View.OnClickListener {

    /**
     * 根view
     */
    private View rootView;
    private RecyclerView rvFragmentFilter;
    private TextView tvReset;
    private TextView tvConfirm;
    private SearchFragmentFilterAdapter filterAdapter;
    private SearchFragmentFilterCallback searchFragmentFilterCallback;
    private SearchResultDataManager searchResultDataManager;
    private List<SearchFilterQuery> filterQueryList;
    /**
     * 搜索来源
     */
    private int fromType;

    public SearchResultFragmentFilter() {
    }

    public SearchResultFragmentFilter(SearchResultDataManager searchResultDataManager) {
        this.searchResultDataManager = searchResultDataManager;
    }

    public static SearchResultFragmentFilter getInstance(SearchResultDataManager searchResultDataManager, int fromType) {
        SearchResultFragmentFilter searchResultFragmentFilter = new SearchResultFragmentFilter(searchResultDataManager);
        Bundle bundle = new Bundle();
        bundle.putInt(SearchConstant.Key.FROM_TYPE, fromType);
        searchResultFragmentFilter.setArguments(bundle);
        return searchResultFragmentFilter;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.sf_field_search_fragment_filter, container, false);
        rootView.setPadding(0, ImmersionBar.getStatusBarHeight(getActivity()), 0, 0);
        initData();
        initView();
        return rootView;
    }

    private void initData() {
        if (getArguments() != null) {
            fromType = getArguments().getInt(SearchConstant.Key.FROM_TYPE);
        }
    }

    private void initView() {
        rvFragmentFilter = rootView.findViewById(R.id.rv_fragment_filter);
        tvReset = rootView.findViewById(R.id.tv_reset);
        tvConfirm = rootView.findViewById(R.id.tv_confirm);

        tvReset.setOnClickListener(this);
        tvConfirm.setOnClickListener(this);

        rvFragmentFilter.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
    }

    /**
     * 设置筛选面板数据
     *
     * @param filterQueryList
     * @param totalCount
     */
    public void setFragmentFilterQuery(List<SearchFilterQuery> filterQueryList, int totalCount) {
        this.filterQueryList = filterQueryList;
        syncFeatureFilterSelectStatus();
        if (filterAdapter == null) {
            filterAdapter = new SearchFragmentFilterAdapter(getContext(), searchResultDataManager, rvFragmentFilter, filterQueryList);
            rvFragmentFilter.setAdapter(filterAdapter);
        } else {
            filterAdapter.setFilterQueryList(filterQueryList);
        }
        if (tvConfirm != null) {
            tvConfirm.setText(getString(R.string.sf_field_search_confirm_with_count, totalCount));
        }
    }

    /**
     * 同步属性筛选数据选中态
     */
    private void syncFeatureFilterSelectStatus() {
        if (filterQueryList == null || filterQueryList.isEmpty()) {
            return;
        }
        Map<String, List<String>> selectedFilterMap = searchResultDataManager.selectedFilterMap;
        for (SearchFilterQuery featureFilterQuery : filterQueryList) {
            if (featureFilterQuery == null) {
                continue;
            }
            if (selectedFilterMap.containsKey(featureFilterQuery.getFilterKey())) {
                List<String> selectedQuery = selectedFilterMap.get(featureFilterQuery.getFilterKey());
                if (selectedQuery != null && !selectedQuery.isEmpty()) {
                    boolean hasChildSelected = false;
                    List<SearchFilterQuery> featureQuerys = featureFilterQuery.getFilterValues();
                    if (featureQuerys != null && !featureQuerys.isEmpty()) {
                        for (SearchFilterQuery query : featureQuerys) {
                            if (query == null) {
                                continue;
                            }
                            if (StringUtil.isNotEmpty(query.getFilterValue()) && selectedQuery.contains(query.getFilterValue())) {
                                query.setSelected(true);
                                hasChildSelected = true;
                            } else {
                                query.setSelected(false);
                            }
                        }
                        if (hasChildSelected) {
                            featureFilterQuery.setSelected(true);
                        } else if (StringUtil.isNotEmpty(featureFilterQuery.getFilterValue()) && selectedQuery.contains(featureFilterQuery.getFilterValue())) {
                            featureFilterQuery.setSelected(true);
                        } else {
                            featureFilterQuery.setSelected(false);
                        }
                    } else {
                        if (StringUtil.isNotEmpty(featureFilterQuery.getFilterValue()) && selectedQuery.contains(featureFilterQuery.getFilterValue())) {
                            featureFilterQuery.setSelected(true);
                        } else {
                            featureFilterQuery.setSelected(false);
                        }
                    }
                } else {
                    featureFilterQuery.setSelected(false);
                    selectedFilterMap.remove(featureFilterQuery.getFilterKey());
                }
            } else {
                featureFilterQuery.setSelected(false);
                if (featureFilterQuery.getFilterValues() != null && !featureFilterQuery.getFilterValues().isEmpty()) {
                    for (SearchFilterQuery query : featureFilterQuery.getFilterValues()) {
                        if (query != null) {
                            query.setSelected(false);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取筛选面板属性筛选条件，埋点用
     *
     * @param sortArea
     * @param sortValue
     */
    public void getFragmentFeatureFilter(JDJSONArray sortArea, JDJSONArray sortValue) {
        if (sortArea == null || sortValue == null) {
            return;
        }
        if (filterQueryList != null && !filterQueryList.isEmpty()) {
            for (SearchFilterQuery filterQuery : filterQueryList) {
                if (filterQuery == null) {
                    continue;
                }
                sortArea.add(filterQuery.getFilterLable());
                if (filterQuery.isSelected()) {
                    if (filterQuery.getFilterValues() != null && !filterQuery.getFilterValues().isEmpty()) {
                        for (SearchFilterQuery query : filterQuery.getFilterValues()) {
                            if (query != null && query.isSelected()) {
                                sortValue.add(filterQuery.getFilterLable() + "#" + query.getName());
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (NoDoubleClickUtils.isDoubleClick()) {
            return;
        }
        int id = v.getId();
        if (id == R.id.tv_reset) {
            searchResultDataManager.resetFeatureFilter();
        } else if (id == R.id.tv_confirm) {
            if (filterAdapter != null) {
                filterAdapter.updatePriceRangeFilter(true);
            }
            JDJSONArray sortArea = new JDJSONArray();
            JDJSONArray sortValue = new JDJSONArray();
            getFragmentFeatureFilter(sortArea, sortValue);
            searchResultDataManager.searchResultReporter.clickFragmentFilterConfirm(sortArea, sortValue);
            if (searchFragmentFilterCallback != null) {
                searchFragmentFilterCallback.onClose();
            }
        }
    }

    public void setSearchFragmentFilterCallback(SearchFragmentFilterCallback searchFragmentFilterCallback) {
        this.searchFragmentFilterCallback = searchFragmentFilterCallback;
    }

    public interface SearchFragmentFilterCallback {
        void onClose();
    }
}
