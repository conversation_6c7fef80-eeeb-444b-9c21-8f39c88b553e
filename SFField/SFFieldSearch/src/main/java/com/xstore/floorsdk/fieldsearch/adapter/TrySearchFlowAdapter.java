package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.bean.SearchRelateWordRecQuery;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sevenfresh.image.ImageloadUtils;

import java.util.List;

/**
 * 试试搜 item flow模式 dapter
 */
public class TrySearchFlowAdapter extends BaseAdapter {
    private Context mContext;
    private List<SearchRelateWordRecQuery> data;
    private OnItemClickListener onItemClickListener;

    public TrySearchFlowAdapter(Context mContext, List<SearchRelateWordRecQuery> data, OnItemClickListener onItemClickListener) {
        this.mContext = mContext;
        this.data = data;
        this.onItemClickListener = onItemClickListener;
    }

    @Override
    public int getCount() {
        return data == null ? 0 : data.size();
    }

    @Override
    public SearchRelateWordRecQuery getItem(int position) {
        if (data != null && data.size() > position) {
            return data.get(position);
        }
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        SearchRelateWordRecQuery item = getItem(position);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.sf_field_search_item_try_search_item_v2,null);
        TextView tv = (TextView) view.findViewById(R.id.tv_word);
        ImageView iv = (ImageView) view.findViewById(R.id.iv_word);

        if (item == null || TextUtils.isEmpty(item.getQuery())) {
            tv.setText("");
            iv.setVisibility(View.GONE);
            return view;
        }

        boolean hasImage = !TextUtils.isEmpty(item.getImgUrl());
        if(hasImage){
            iv.setVisibility(View.VISIBLE);
            ImageloadUtils.loadRoundCornerImage(mContext,  item.getImgUrl(),iv,4,R.drawable.sf_field_search_corner_4_f6f6f6_bg);
        }else {
            iv.setVisibility(View.GONE);
        }
        tv.setText(item.getQuery());

        tv.setPadding(ScreenUtils.dip2px(mContext,hasImage ?4:8), 0, ScreenUtils.dip2px(mContext, 8), 0);


            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(onItemClickListener!=null) {
                        onItemClickListener.onItemClick(position);
                    }
                }
            });
        return view;
    }

    public OnItemClickListener getOnItemClickListener() {
        return onItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }
}
