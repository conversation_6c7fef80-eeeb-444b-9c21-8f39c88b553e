package com.xstore.floorsdk.fieldsearch.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sdk.floor.floorcore.utils.PriceUtls;
import com.xstore.sevenfresh.cart.interfaces.AddCartMaListener;
import com.xstore.sevenfresh.cart.widget.AddCartView;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.modules.productdetail.bean.ProductDetailBean;

import java.util.List;

/**
 * 相似推荐适配器
 *
 * <AUTHOR>
 * @date 2022/09/26
 */
public class SimilarRecommendAdapter extends RecyclerView.Adapter<SimilarRecommendAdapter.RecommendViewHolder> {

    private Activity activity;
    private OnItemClickListener onItemClickListener;
    /**
     * 数据源
     */
    private List<ProductDetailBean.WareInfoBean> wareInfos;
    /**
     * 加车动效目标view
     */
    private View endView;

    public SimilarRecommendAdapter(Activity activity) {
        this.activity = activity;
    }

    @NonNull
    @Override
    public RecommendViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_similar_recommend_item, parent, false);
        return new RecommendViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecommendViewHolder holder, int position) {
        ProductDetailBean.WareInfoBean wareInfo = wareInfos.get(position);
        if (wareInfo == null) {
            return;
        }
        ImageloadUtils.loadCustomRoundCornerImage(activity, wareInfo.getImgUrl(),
                holder.ivGoodsIcon, 5, 5, 5, 5);
        holder.tvGoodsName.setText(wareInfo.getSkuName());
        PriceUtls.setPrice(holder.tvPrice, wareInfo.getJdPrice());
        holder.tvPrice.setIncludeFontPadding(false);
        if (!TextUtils.isEmpty(wareInfo.getJdPrice())) {
            holder.tvUnit.setText(wareInfo.getBuyUnit());
        } else {
            holder.tvUnit.setText("");
        }
//        holder.addCartView.bindWareInfo(activity, wareInfo, holder.ivGoodsIcon, endView);
        holder.addCartView.setAddCartMaListener(new AddCartMaListener() {
            @Override
            public void onAddCartMa(ProductDetailBean.WareInfoBean wareInfo) {
                if (onItemClickListener != null) {
                    onItemClickListener.onAddCart(position, wareInfo);
                }
            }
        });
    }

    public void setWareInfos(List<ProductDetailBean.WareInfoBean> wareInfos) {
        this.wareInfos = wareInfos;
        notifyDataSetChanged();
    }

    public void setEndView(View endView) {
        this.endView = endView;
    }

    @Override
    public int getItemCount() {
        return wareInfos == null ? 0 : wareInfos.size();
    }

    class RecommendViewHolder extends RecyclerView.ViewHolder {
        /**
         * 商品图片
         */
        ImageView ivGoodsIcon;
        /**
         * 商品名称
         */
        TextView tvGoodsName;
        /**
         * 商品价格
         */
        TextView tvPrice;
        /**
         * 商品单位
         */
        TextView tvUnit;
        /**
         * 购物车按钮容器
         */
        AddCartView addCartView;

        public RecommendViewHolder(@NonNull View itemView) {
            super(itemView);
            ivGoodsIcon = itemView.findViewById(R.id.iv_goods_icon);
            tvGoodsName = itemView.findViewById(R.id.tv_goods_name);
            tvPrice = itemView.findViewById(R.id.tv_price);
            tvUnit = itemView.findViewById(R.id.tv_unit);
            addCartView = itemView.findViewById(R.id.acv_addcart);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    /**
     * 点击事件接口
     */
    public interface OnItemClickListener {
        /**
         * 点击事件
         */
        void onItemClick(String skuId, int postion, String imageUrl);

        void onAddCart(int position, ProductDetailBean.WareInfoBean wareInfo);
    }
}
