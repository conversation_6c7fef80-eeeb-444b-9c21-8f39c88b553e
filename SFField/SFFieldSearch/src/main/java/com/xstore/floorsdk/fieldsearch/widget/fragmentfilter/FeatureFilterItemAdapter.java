package com.xstore.floorsdk.fieldsearch.widget.fragmentfilter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.sdk.floor.floorcore.FloorInit;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 搜索筛选面板属性筛选适配器
 *
 * <AUTHOR>
 * @date 2022/09/24
 */
public class FeatureFilterItemAdapter extends RecyclerView.Adapter<FeatureFilterItemAdapter.FeatureItemHolder> {

    private Context context;
    private SearchFragmentFilterAdapter filterAdapter;
    private OnItemClickListener onItemClickListener;
    private SearchResultDataManager searchResultDataManager;
    private SearchFilterQuery filterQuery;
    private List<SearchFilterQuery> childFilterQueryList;
    private boolean expanded = false;

    public SearchFilterQuery getFilterQuery() {
        return filterQuery;
    }

    public FeatureFilterItemAdapter(Context context, SearchFragmentFilterAdapter filterAdapter,
                                    SearchResultDataManager searchResultDataManager,
                                    SearchFilterQuery filterQuery, boolean expanded) {
        this.context = context;
        this.filterAdapter = filterAdapter;
        this.searchResultDataManager = searchResultDataManager;
        this.expanded = expanded;
        this.filterQuery = filterQuery;
        if (filterQuery != null) {
            childFilterQueryList = filterQuery.getFilterValues();
        } else {
            childFilterQueryList = null;
        }
    }

    public SearchFilterQuery getItem(int position) {
        if (childFilterQueryList == null || position >= childFilterQueryList.size() || position < 0) {
            return null;
        }
        return childFilterQueryList.get(position);
    }

    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
        notifyDataSetChanged();
    }
    @NonNull
    @Override
    public FeatureItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.sf_field_search_fragment_filter_feature_item, parent, false);
        return new FeatureItemHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FeatureItemHolder holder, int position) {
        SearchFilterQuery query = childFilterQueryList.get(position);
        if (query == null) {
            return;
        }
        holder.tvFilter.setText(query.getName());
        holder.tvFilter.setSelected(query.isSelected());
        holder.tvFilter.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (query.isSelected()) {
                query.setSelected(false);
                clearParentWhenNoChildSelect();
                updatePriceRangeFilter();
                searchResultDataManager.removeFeatureFilterQuery(filterQuery.getFilterKey(), query.getFilterValue());
            } else {
                if (searchResultDataManager.getFeatureFilterQueryCount(filterQuery.getFilterKey()) >= 5) {
                    FloorInit.getFloorConfig().showToast(context.getString(R.string.sf_field_search_max_select_5));
                    return;
                }
                query.setSelected(true);
                filterQuery.setSelected(true);
                updatePriceRangeFilter();
                searchResultDataManager.addFeatureFilterQuery(filterQuery.getFilterKey(), query.getFilterValue());
            }
            if (onItemClickListener != null) {
                onItemClickListener.clickItem(position);
            }
        });
    }

    /**
     * 点击抽屉里筛选条件时要校验价格是否变动
     */
    private void updatePriceRangeFilter() {
        if (filterAdapter != null) {
            filterAdapter.updatePriceRangeFilter(false);
        }
    }

    /**
     * 当所有子选项都未选中时，清除父选项选中态
     */
    private void clearParentWhenNoChildSelect() {
        boolean hasChildSelected = false;
        for (SearchFilterQuery query : childFilterQueryList) {
            if (query.isSelected()) {
                hasChildSelected = true;
            }
        }
        if (!hasChildSelected) {
            filterQuery.setSelected(false);
        }
    }

    @Override
    public int getItemCount() {
        if (childFilterQueryList == null || childFilterQueryList.size() == 0) {
            return 0;
        }
        if (!expanded && childFilterQueryList.size() > 9) {
            return 9;
        }
        return childFilterQueryList.size();
    }

    class FeatureItemHolder extends RecyclerView.ViewHolder {

        TextView tvFilter;

        public FeatureItemHolder(@NonNull View itemView) {
            super(itemView);
            tvFilter = itemView.findViewById(R.id.tv_filter_feature_item);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    interface OnItemClickListener {

        void clickItem(int position);
    }
}
