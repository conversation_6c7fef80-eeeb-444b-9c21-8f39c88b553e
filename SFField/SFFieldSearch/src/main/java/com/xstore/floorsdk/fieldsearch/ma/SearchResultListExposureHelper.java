package com.xstore.floorsdk.fieldsearch.ma;

import android.text.TextUtils;

import com.jd.framework.json.JDJSON;
import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldsearch.SearchConstant;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.SearchResultFragment;
import com.xstore.floorsdk.fieldsearch.adapter.SearchProductAdapter;
import com.xstore.floorsdk.fieldsearch.bean.SearchRelateWordRecQuery;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.datareport.utils.JsonUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuSellPoint;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuTag;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

/**
 * 搜索列表曝光埋点帮助类
 *
 * <AUTHOR>
 * @date 2022/10/11
 */
public class SearchResultListExposureHelper {

    private SearchResultDataManager searchResultDataManager;
    private Map<String, Boolean> exposuredSkuMap;
    private Map<String, Boolean> exposuredUnStockSimilarSkuMap;
    private Map<String, Boolean> exposureItemMap;
    private SearchResultFragment searchResultFragment;
    private SearchProductAdapter searchProductAdapter;
    private JDMaUtils.JdMaPageImp jdMaPageImp;

    public SearchResultListExposureHelper(SearchResultDataManager searchResultDataManager) {
        this.searchResultDataManager = searchResultDataManager;
    }

    public void initData(SearchResultFragment searchResultFragment) {
        clearExposure();
        this.searchResultFragment = searchResultFragment;
        this.jdMaPageImp = searchResultDataManager.getJdMaPageImp();
    }

    private void clearExposure() {
        if (exposuredSkuMap != null) {
            exposuredSkuMap.clear();
        }
        if (exposuredUnStockSimilarSkuMap != null) {
            exposuredUnStockSimilarSkuMap.clear();
        }
        if (exposureItemMap != null) {
            exposureItemMap.clear();
        }
        searchResultFragment = null;
        searchProductAdapter = null;
        jdMaPageImp = null;
    }

    /**
     * 曝光埋点
     *
     * @param recyclerView
     */
    public void exposure(RecyclerView recyclerView) {
        if (recyclerView == null) {
            return;
        }
        searchProductAdapter = (SearchProductAdapter) recyclerView.getAdapter();
        if (exposuredSkuMap == null) {
            exposuredSkuMap = new HashMap();
        }
        if (exposuredUnStockSimilarSkuMap == null) {
            exposuredUnStockSimilarSkuMap = new HashMap<>();
        }
        if (exposureItemMap == null) {
            exposureItemMap = new HashMap();
        }

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                exposureByHand(recyclerView);
            }
        });
    }

    public void exposureByHand(RecyclerView recyclerView) {

        if (recyclerView == null || searchProductAdapter == null) {
            return;
        }
        if (exposuredSkuMap == null || exposureItemMap == null) {
            return;
        }
        StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) recyclerView.getLayoutManager();
        int[] firstArray = layoutManager.findFirstCompletelyVisibleItemPositions(null);
        int[] lastArray = layoutManager.findLastCompletelyVisibleItemPositions(null);
        int first = firstArray[0] > firstArray[1] ? firstArray[1] : firstArray[0];
        int last = lastArray[0] > lastArray[1] ? lastArray[0] : lastArray[1];

        for (int i = first; i <= last; i++) {
            SkuInfoBean productInfoBean = searchProductAdapter.getItem(i);
            if (productInfoBean == null) {
                continue;
            }
            if (productInfoBean.getViewType() == SearchProductAdapter.VIEW_TYPE_TRY_SEARCH) {
                List<SearchRelateWordRecQuery> relatedWords = searchProductAdapter.getRelatedWordRecQueryList();
                if (relatedWords == null || relatedWords.isEmpty()) {
                    continue;
                }
                String relateWordHash = String.valueOf(relatedWords.hashCode());
                if (exposureItemMap.containsKey(relateWordHash) && exposureItemMap.get(relateWordHash)) {
                    continue;
                }
                JDJSONObject jdjsonObject = new JDJSONObject();
                JDJSONArray jdjsonArray = new JDJSONArray();
                for (SearchRelateWordRecQuery recQuery : relatedWords) {
                    if(recQuery!=null && !TextUtils.isEmpty(recQuery.getQuery())){
                        jdjsonArray.add(recQuery.getQuery());
                    }
                }
                jdjsonObject.put("relevantSearchWords", jdjsonArray.toString());
                jdjsonObject.put("enkwd", searchResultDataManager.searchKeyword);
                if (searchResultDataManager.searchResultContainerInterface != null && searchResultDataManager.searchResultContainerInterface.getSearchShowTypeBuriedExpLabel() != null) {
                    try {
                        jdjsonObject.put("touchstone_expids", searchResultDataManager.searchResultContainerInterface.getSearchShowTypeBuriedExpLabel());
                    } catch (Exception e) {
                        JdCrashReport.postCaughtException(e);
                    }
                }
                HashMap<String, String> map = new HashMap<>();
                map.put("mode", searchProductAdapter.isShowCard() ? "1" : "2");
                JDMaUtils.save7FExposure("searchListPage_relevantSearchWordsFloor", map, null, jdjsonObject.toString(), jdMaPageImp);
                exposureItemMap.put(relateWordHash, true);
            } else if (productInfoBean.getViewType() == SearchProductAdapter.VIEW_TYPE_NODATA_TRY_SEARCH) {
                List<String> trySearchWords = searchProductAdapter.getTrySearchWords();
                if (trySearchWords == null || trySearchWords.isEmpty()) {
                    continue;
                }
                String trySearchWordHash = String.valueOf(trySearchWords.hashCode());
                if (exposureItemMap.containsKey(trySearchWordHash) && exposureItemMap.get(trySearchWordHash)) {
                    continue;
                }
                JDJSONObject jdjsonObject = new JDJSONObject();
                JDJSONArray jdjsonArray = new JDJSONArray();
                for (String relatedWord : trySearchWords) {
                    jdjsonArray.add(relatedWord);
                }
                jdjsonObject.put("keyword", jdjsonArray.toString());
                jdjsonObject.put("enkwd", searchResultDataManager.searchKeyword);
                HashMap<String, String> map = new HashMap<>();
                map.put("enkwd", searchResultDataManager.searchKeyword);
                JDMaUtils.save7FExposure("searchListPage_nonResult_keyWordExpose", map, null, jdjsonObject.toString(), jdMaPageImp);
                exposureItemMap.put(trySearchWordHash, true);
            } else if (productInfoBean.getViewType() == SearchProductAdapter.VIEW_TYPE_YUN) {
                List<SkuInfoBean> cloudSkus = searchProductAdapter.getCloudStoreSkus();
                if (cloudSkus == null || cloudSkus.isEmpty()) {
                    continue;
                }
                String cloudHash = String.valueOf(cloudSkus.hashCode());
                if (exposureItemMap.containsKey(cloudHash) && exposureItemMap.get(cloudHash)) {
                    continue;
                }
                JDJSONObject jsonParamMap = getCloudReportParam(null, i);
                HashMap<String, String> map = new HashMap<>();
                map.put("mode", searchProductAdapter.isShowCard() ? "1" : "2");
                JDMaUtils.save7FExposure("searchListPage_skuList_jdBuyModuleExpose", map, null, jsonParamMap.toString(), jdMaPageImp);
                exposureItemMap.put(cloudHash, true);

                //云卖场商品曝光
                for (int j = 0; j < cloudSkus.size(); j++) {
                    SkuInfoBean cloudSku = cloudSkus.get(j);
                    JDJSONObject cloudSkuParamMap = getCloudReportParam(cloudSku, j);
                    HashMap<String, String> cloudSkuMap = new HashMap<>();
                    cloudSkuMap.put("mode", searchProductAdapter.isShowCard() ? "1" : "2");
                    JDMaUtils.save7FExposure("searchListPage_skuList_jdBuyCommodityExpose", cloudSkuMap, null, cloudSkuParamMap.toString(), jdMaPageImp);
                }
            } else if (productInfoBean.getViewType() == SearchProductAdapter.VIEW_TYPE_PRODUCT_LIST) {
                if (StringUtil.isNullByString(productInfoBean.getSkuId())) {
                    continue;
                }
                if (exposuredSkuMap.containsKey(productInfoBean.getSkuId()) && exposuredSkuMap.get(productInfoBean.getSkuId())) {
                    exposureUnStockSimilar(productInfoBean);
                    continue;
                }
                String pointName = "searchListPage";
                switch (searchResultDataManager.fromType) {
                    case SearchConstant.Value.FROM_TYPE_SEARCH:
                        pointName = "searchListPage_skuList";
                        break;
                    case SearchConstant.Value.FROM_TYPE_COUPON:
                        pointName = "searchListPage_promotionWithCouponSkuList";
                        break;
                    case SearchConstant.Value.FROM_TYPE_PROMOTION:
                        pointName = "searchListPage_normalPromotionSkuList";
                        break;
                    case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                        pointName = "searchListPage_promotionWithRisePriceSkuList";
                        break;
                    case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                        pointName = "searchListPage_linghuomaSkuList";
                        break;
                    case SearchConstant.Value.FROM_TYPE_FREIGHT:
                        pointName = "searchListPage_skuList";
                        break;
                    default:
                        break;
                }
                HashMap<String, String> paramMap = new HashMap<>();
                if (!StringUtil.isNullByString(searchResultDataManager.mtest)) {
                    paramMap.put("mtest", searchResultDataManager.mtest);
                } else {
                    paramMap.put("mtest", "");
                }
                String searchKeyword = searchResultDataManager.searchKeyword;
                if (productInfoBean.isRecommend()) {
                    pointName = "searchListPage_recommend";
                    JDJSONObject elaObject = new JDJSONObject();
                    elaObject.put("enkwd", searchKeyword);
                    paramMap.put("ela", elaObject.toString());
                } else {
                    String ela = "关键词:" + searchKeyword + "-sku:" + productInfoBean.getSkuId() + "-state:" + productInfoBean.getStatus() + "-index:" + i + "-enkwd:" + searchKeyword;
                    paramMap.put("ela", ela);
                }
                if (productInfoBean.getCartInfo() != null && productInfoBean.getCartInfo().getType() == SkuEnumInterface.CartBtnType.PRE_SALE) {
                    paramMap.put("isPreSale", "1");
                } else {
                    paramMap.put("isPreSale", "0");
                }
                if (productInfoBean.getLogicInfo() != null && productInfoBean.getLogicInfo().isClearanceFlag()) {
                    paramMap.put("skuType", "1");
                } else {
                    paramMap.put("skuType", "0");
                }
                if(productInfoBean.getLogicInfo() != null && productInfoBean.getLogicInfo().getBoolTagMap() != null && productInfoBean.getLogicInfo().getBoolTagMap().isSecKill()){
                    paramMap.put("secKill", "1");
                }else{
                    paramMap.put("secKill", "0");
                }
                paramMap.put("listPageNum", (i / searchResultDataManager.pageSize + 1) + "");
                paramMap.put("listPageIndex", (i + 1) + "");
                paramMap.put("price", getSkuPrice(productInfoBean));
                paramMap.put("price_500g", productInfoBean.getMaFieldPrice500g());
                paramMap.put("broker_info", productInfoBean.getBrokerInfo());
                paramMap.put("clk", productInfoBean.getClsTag());
                paramMap.put("sellPointList", getSellPointString(productInfoBean, searchProductAdapter.isShowCard() ? 2 : 3));
                paramMap.put("tagList", getTagListString(productInfoBean, searchProductAdapter.isShowCard() ? 2 : 3));
                JDJSONObject jsonParamMap = getReportParam(pointName, productInfoBean, i);
                JDMaUtils.save7FExposure(pointName, paramMap, null, jsonParamMap.toString(), jdMaPageImp);
                exposuredSkuMap.put(productInfoBean.getSkuId(), true);

                exposureUnStockSimilar(productInfoBean);
            }
        }
    }

    /**
     * 无货相似商品曝光
     *
     * @param productInfoBean
     */
    private void exposureUnStockSimilar(SkuInfoBean productInfoBean) {
        if (!productInfoBean.isRecommend() && productInfoBean.getStockStatus() == SkuEnumInterface.StockStatus.NO_STOCK && productInfoBean.getSimilarList() != null) {
            if (exposuredUnStockSimilarSkuMap.containsKey(productInfoBean.getSkuId()) && exposuredUnStockSimilarSkuMap.get(productInfoBean.getSkuId())) {
                return;
            }
            searchResultDataManager.searchResultReporter.exposureUnStockSimilar(productInfoBean.getSkuId(), productInfoBean.getSkuName(), searchProductAdapter.isShowCard() ? 1 : 2);
            exposuredUnStockSimilarSkuMap.put(productInfoBean.getSkuId(), true);
        }
    }

    /**
     * 点击埋点
     *
     * @param productInfoBean
     * @param position
     */
    public void clickReport(SkuInfoBean productInfoBean, int position) {
        if (productInfoBean == null || StringUtil.isNullByString(productInfoBean.getSkuId())) {
            return;
        }
        String pointName = "searchListPage";
        switch (searchResultDataManager.fromType) {
            case SearchConstant.Value.FROM_TYPE_SEARCH:
                pointName = "searchListPage_skuList_clickCommodity";
                break;
            case SearchConstant.Value.FROM_TYPE_COUPON:
                pointName = "searchListPage_promotionWithCouponSkuList_clickCommodity";
                break;
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
                pointName = "searchListPage_normalPromotionSkuList_clickCommodity";
                break;
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                pointName = "searchListPage_promotionWithRisePriceSkuList_clickCommodity";
                break;
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                pointName = "searchListPage_linghuomaSkuList_clickCommodity";
                break;
            case SearchConstant.Value.FROM_TYPE_FREIGHT:
                pointName = "searchListPage_skuList_clickCommodity";
                break;
            default:
                break;
        }
        HashMap<String, String> paramMap = new HashMap<>();
        if (!StringUtil.isNullByString(searchResultDataManager.mtest)) {
            paramMap.put("mtest", searchResultDataManager.mtest);
        } else {
            paramMap.put("mtest", "");
        }
        JDJSONObject jsonParamMap = getReportParam(pointName, productInfoBean, position);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "2";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(jsonParamMap.toJSONString()));
        JDMaUtils.save7FClick(pointName, "", productInfoBean.getSkuId(), paramMap, jdMaPageImp, baseMaEntity);
    }

    /**
     * 加车埋点
     *
     * @param productInfoBean
     * @param position
     */
    public void addCartReport(SkuInfoBean productInfoBean, int position) {
        if (productInfoBean == null || StringUtil.isNullByString(productInfoBean.getSkuId())) {
            return;
        }
        String pointName = "searchListPage";
        switch (searchResultDataManager.fromType) {
            case SearchConstant.Value.FROM_TYPE_SEARCH:
                pointName = "searchListPage_skuList_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_COUPON:
                pointName = "searchListPage_promotionWithCouponSkuList_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_PROMOTION:
                pointName = "searchListPage_normalPromotionSkuList_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_INCREASE_PRICE:
                pointName = "searchListPage_promotionWithRisePriceSkuList_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_PICKING_CODE:
                pointName = "searchListPage_linghuomaSkuList_addCart";
                break;
            case SearchConstant.Value.FROM_TYPE_FREIGHT:
                pointName = "searchListPage_skuList_addCart";
                break;
            default:
                break;
        }
        HashMap<String, String> paramMap = new HashMap<>();
        if (!StringUtil.isNullByString(searchResultDataManager.mtest)) {
            paramMap.put("mtest", searchResultDataManager.mtest);
        } else {
            paramMap.put("mtest", "");
        }
        JDJSONObject jsonParamMap = getReportParam(pointName, productInfoBean, position);
        BaseMaEntity baseMaEntity = new BaseMaEntity();
        BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
        baseMaPublicParam.CLICKTYPE = "1";
        baseMaEntity.setPublicParam(baseMaPublicParam);
        baseMaEntity.setMa7FextParam(JsonUtils.fromJsonToMap(jsonParamMap.toJSONString()));
        JDMaUtils.save7FClick(pointName, "", productInfoBean.getSkuId(), paramMap, jdMaPageImp, baseMaEntity);
    }

    /**
     * 获取埋点参数
     *
     * @param productInfoBean
     * @param position
     * @return
     */
    private JDJSONObject getReportParam(String pointName, SkuInfoBean productInfoBean, int position) {
        JDJSONObject jsonParamMap = new JDJSONObject();
        jsonParamMap.put("pvid", searchResultDataManager.pvId);
        jsonParamMap.put("logid", productInfoBean.getLogId());
        jsonParamMap.put("enkwd", searchResultDataManager.searchKeyword);
        if (productInfoBean.getLogicInfo() != null && productInfoBean.getLogicInfo().isClearanceFlag()) {
            jsonParamMap.put("skuType", "1");
        } else {
            jsonParamMap.put("skuType", "0");
        }
        if(productInfoBean.getLogicInfo() != null && productInfoBean.getLogicInfo().getBoolTagMap() != null && productInfoBean.getLogicInfo().getBoolTagMap().isSecKill()){
            jsonParamMap.put("secKill", "1");
        }else{
            jsonParamMap.put("secKill", "0");
        }
        if (searchResultDataManager.searchResultInfo != null) {
            if (!TextUtils.isEmpty(searchResultDataManager.searchResultInfo.getHcCid1s())) {
                jsonParamMap.put("hc_7f_cid1", searchResultDataManager.searchResultInfo.getHcCid1s());
            } else {
                jsonParamMap.put("hc_7f_cid1", "");
            }
            if (!TextUtils.isEmpty(searchResultDataManager.searchResultInfo.getHcCid2s())) {
                jsonParamMap.put("hc_7f_cid2", searchResultDataManager.searchResultInfo.getHcCid2s());
            } else {
                jsonParamMap.put("hc_7f_cid2", "");
            }
            if (!TextUtils.isEmpty(searchResultDataManager.searchResultInfo.getHcCid3s())) {
                jsonParamMap.put("hc_7f_cid3", searchResultDataManager.searchResultInfo.getHcCid3s());
            } else {
                jsonParamMap.put("hc_7f_cid3", "");
            }
            if (!TextUtils.isEmpty(searchResultDataManager.searchResultInfo.getHcCid4s())) {
                jsonParamMap.put("hc_7f_cid4", searchResultDataManager.searchResultInfo.getHcCid4s());
            } else {
                jsonParamMap.put("hc_7f_cid4", "");
            }
        }
        int page = position / searchResultDataManager.pageSize + 1;
        jsonParamMap.put("listPageNum", page + "");
        if (pointName.equals("searchListPage_normalPromotionSkuList")
                || pointName.equals("searchListPage_promotionWithCouponSkuList")
                || pointName.equals("searchListPage_promotionWithRisePriceSkuList")
                || pointName.equals("searchListPage_normalPromotionSkuList_addCart")
                || pointName.equals("searchListPage_promotionWithRisePriceSkuList_addCart")
                || pointName.equals("searchListPage_promotionWithCouponSkuList_addCart")
                || pointName.equals("searchListPage_normalPromotionSkuList_clickCommodity")
                || pointName.equals("searchListPage_promotionWithRisePriceSkuList_clickCommodity")
                || pointName.equals("searchListPage_promotionWithCouponSkuList_clickCommodity")
        ) {
            jsonParamMap.put("listPageIndex", position + "");
        } else {
            jsonParamMap.put("listPageIndex", (position + 1) + "");
        }
        if (!TextUtils.isEmpty(searchResultDataManager.keywordClickFrom)) {
            jsonParamMap.put("event_sf", searchResultDataManager.keywordClickFrom);
        } else {
            jsonParamMap.put("event_sf", "");
        }
//        if (productInfoBean.getTags() != null && productInfoBean.getTags().size() > 0) {
//            for (TagInfo tag : productInfoBean.getTags()) {
//                //代表天天低价
//                if (tag.getTag() == 1) {
//                    jsonParamMap.put("edlp", 1);
//                }
//            }
//        } else {
//            jsonParamMap.put("edlp", 0);
//        }
        if (!TextUtils.isEmpty(productInfoBean.getSkuId())) {
            jsonParamMap.put("skuId", productInfoBean.getSkuId());
        } else {
            jsonParamMap.put("skuId", "");
        }
        if (!TextUtils.isEmpty(productInfoBean.getSkuName())) {
            jsonParamMap.put("skuName", productInfoBean.getSkuName());
        } else {
            jsonParamMap.put("skuName", "");
        }
        if (productInfoBean.getSalePrice() != null && !TextUtils.isEmpty(productInfoBean.getSalePrice().getValue())) {
            jsonParamMap.put("price", productInfoBean.getSalePrice().getValue());
        } else {
            jsonParamMap.put("price", "");
        }
        jsonParamMap.put("price_500g", productInfoBean.getMaFieldPrice500g());
        jsonParamMap.put("broker_info", productInfoBean.getBrokerInfo());
        jsonParamMap.put("clk", productInfoBean.getClsTag());
        jsonParamMap.put("sellPointList", getSellPointString(productInfoBean, searchProductAdapter.isShowCard() ? 2 : 3));
        jsonParamMap.put("tagList", getTagListString(productInfoBean, searchProductAdapter.isShowCard() ? 2 : 3));
        if (productInfoBean.getLogicInfo() != null) {
            jsonParamMap.put("ifTakeaway", productInfoBean.getLogicInfo().getIsTakeaway());
            jsonParamMap.put("productCardType", productInfoBean.getLogicInfo().getProductCardType());
        }


        if(productInfoBean.getSalePrice()!=null){
            jsonParamMap.put("salePriceType" , productInfoBean.getSalePrice().getType());
        }
        if(productInfoBean.getComparePrice()!=null){
            jsonParamMap.put("comparePriceType",productInfoBean.getComparePrice().getType());
        }
        if(productInfoBean.getDiscountPrice()!=null){
            jsonParamMap.put("discountPriceType",productInfoBean.getDiscountPrice().getType());
        }


        if (searchProductAdapter != null) {
            jsonParamMap.put("mode", searchProductAdapter.isShowCard() ? "1" : "2");
        }
        if (searchResultFragment != null) {
            int sortType = searchResultFragment.getSortType();
            if (sortType > 0) {
                jsonParamMap.put("sort_type", sortType + "");
            } else {
                jsonParamMap.put("sort_type", "");
            }
            String deliveryType = searchResultFragment.getDeliveryType();
            if (!TextUtils.isEmpty(deliveryType)) {
                jsonParamMap.put("promiseType", deliveryType);
            } else {
                jsonParamMap.put("promiseType", "");
            }
            JDJSONArray sortArea = new JDJSONArray();
            JDJSONArray sortValue = new JDJSONArray();
            searchResultFragment.getFeatureFilter(sortArea, sortValue);
            if (!sortArea.isEmpty()) {
                jsonParamMap.put("sortArea", sortArea.toString());
            } else {
                jsonParamMap.put("sortArea", "");
            }
            if (!sortValue.isEmpty()) {
                jsonParamMap.put("sortValue", sortValue.toString());
            } else {
                jsonParamMap.put("sortValue", "");
            }
            if (searchResultFragment.showScene()) {
                jsonParamMap.put("hotSearch", searchResultDataManager.searchKeyword);
            } else {
                jsonParamMap.put("hotSearch", "");
            }
        }
        if (searchResultDataManager.hasFilter()) {
            jsonParamMap.put("is_active_filt", "1");
        } else {
            jsonParamMap.put("is_active_filt", "0");
        }
        if (!TextUtils.isEmpty(searchResultDataManager.tileCategoryName)) {
            jsonParamMap.put("categoryFlatKeyWord", searchResultDataManager.tileCategoryName);
        } else {
            jsonParamMap.put("categoryFlatKeyWord", "");
        }
        if (searchResultDataManager.canShowActivity()) {
            if (searchResultDataManager.activityFilter != null && !searchResultDataManager.activityFilter.isEmpty()) {
                jsonParamMap.put("promotionButton", "1");
            } else {
                jsonParamMap.put("promotionButton", "2");
            }
        } else {
            jsonParamMap.put("promotionButton", "0");
        }
        if (searchResultDataManager.searchResultContainerInterface != null && searchResultDataManager.searchResultContainerInterface.getSearchShowTypeBuriedExpLabel() != null) {
            try {
                jsonParamMap.put("touchstone_expids", searchResultDataManager.searchResultContainerInterface.getSearchShowTypeBuriedExpLabel());
            } catch (Exception e) {
                JdCrashReport.postCaughtException(e);
            }
        }
        if (!productInfoBean.isRecommend() && searchResultDataManager.getTotalCount() > 0) {
            jsonParamMap.put("resultCount", searchResultDataManager.getTotalCount() + "");
        } else {
            jsonParamMap.put("resultCount", "");
        }

        jsonParamMap.put("skuPointStatus", productInfoBean.getPointStatus());
        jsonParamMap.put("skuStockStatus", productInfoBean.getStockStatus() + "");
        jsonParamMap.put("skuStatus", productInfoBean.getSkuStatus() + "");
        jsonParamMap.put("realtime_feature",productInfoBean.getRealtime_feature());

        return jsonParamMap;
    }

    /**
     * 获取云卖场埋点参数
     *
     * @param productInfoBean
     * @param position
     * @return
     */
    public JDJSONObject getCloudReportParam(SkuInfoBean productInfoBean, int position) {
        JDJSONObject jsonParamMap = new JDJSONObject();
        jsonParamMap.put("enkwd", searchResultDataManager.searchKeyword);
        jsonParamMap.put("listPageIndex", (position + 1) + "");
        if (searchProductAdapter != null) {
            jsonParamMap.put("mode", searchProductAdapter.isShowCard() ? "1" : "2");
        }
        if (searchResultFragment != null) {
            int sortType = searchResultFragment.getSortType();
            if (sortType > 0) {
                jsonParamMap.put("sort_type", sortType + "");
            } else {
                jsonParamMap.put("sort_type", "");
            }
        }
        if (!TextUtils.isEmpty(searchResultDataManager.tileCategoryName)) {
            jsonParamMap.put("categoryFlatKeyWord", searchResultDataManager.tileCategoryName);
        } else {
            jsonParamMap.put("categoryFlatKeyWord", "");
        }
        if (searchResultDataManager.canShowActivity()) {
            if (searchResultDataManager.activityFilter != null && !searchResultDataManager.activityFilter.isEmpty()) {
                jsonParamMap.put("promotionButton", "1");
            } else {
                jsonParamMap.put("promotionButton", "2");
            }
        } else {
            jsonParamMap.put("promotionButton", "0");
        }
        if (productInfoBean != null) {
            if (!TextUtils.isEmpty(productInfoBean.getSkuId())) {
                jsonParamMap.put("skuId", productInfoBean.getSkuId());
            } else {
                jsonParamMap.put("skuId", "");
            }
            if (!TextUtils.isEmpty(productInfoBean.getSkuName())) {
                jsonParamMap.put("skuName", productInfoBean.getSkuName());
            } else {
                jsonParamMap.put("skuName", "");
            }
            if (productInfoBean.getSalePrice() != null && !TextUtils.isEmpty(productInfoBean.getSalePrice().getValue())) {
                jsonParamMap.put("price", productInfoBean.getSalePrice().getValue());
            } else {
                jsonParamMap.put("price", "");
            }
            jsonParamMap.put("skuStockStatus", productInfoBean.getStatus() + "");
        }
        return jsonParamMap;
    }

    /**
     * 获取卖点行数据字符串（埋点用）
     *
     * @param skuInfo 商品信息
     * @param num     正数，要前几个
     * @return 埋点字符串
     */
    public static String getSellPointString(SkuInfoBean skuInfo, int num) {
        if (skuInfo == null || skuInfo.getSellPointList() == null || skuInfo.getSellPointList().isEmpty()) {
            return null;
        }

        if (num < 0) {
            num = 2;
        }

        List<SkuSellPoint> sellPointList = skuInfo.getSellPointList();

        ArrayList<SkuSellPoint> subList;
        if (sellPointList.size() > num) {
            subList = new ArrayList<>(sellPointList.subList(0, num));
        } else {
            subList = new ArrayList<>(sellPointList);
        }

        return JDJSON.toJSONString(subList);
    }

    /**
     * 获取促销行数据字符串（埋点用）
     *
     * @param skuInfo 商品信息
     * @param maxNum  正数，要前几个
     * @return 埋点字符串
     */
    public static String getTagListString(SkuInfoBean skuInfo, int maxNum) {
        if (skuInfo == null || skuInfo.getTagList() == null || skuInfo.getTagList().isEmpty()) {
            return null;
        }

        if (maxNum < 0) {
            maxNum = 2;
        }

        List<SkuTag> sellPointList = skuInfo.getTagList();

        ArrayList<SkuTag> subList;
        if (sellPointList.size() > maxNum) {
            subList = new ArrayList<>(sellPointList.subList(0, maxNum));
        } else {
            subList = new ArrayList<>(sellPointList);
        }

        return JDJSON.toJSONString(subList);
    }

    /**
     * 从SkuInfoBean读取价格
     *
     * @param skuInfo
     * @return
     */
    public static String getSkuPrice(SkuInfoBean skuInfo) {
        String price = "";
        if (skuInfo != null && skuInfo.getSalePrice() != null && !TextUtils.isEmpty(skuInfo.getSalePrice().getPriceStr())) {
            price = skuInfo.getSalePrice().getValue();
        }
        return price;
    }
}
