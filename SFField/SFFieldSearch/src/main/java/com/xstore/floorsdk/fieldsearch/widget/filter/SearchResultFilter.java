package com.xstore.floorsdk.fieldsearch.widget.filter;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.SearchFilterQuery;
import com.xstore.floorsdk.fieldsearch.widget.dropdownfilter.DropDownFilterPop;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.CenterLayoutManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 搜索结果页筛选条
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
public class SearchResultFilter extends LinearLayout {

    private static final float SPACE_66 = 66f / 375;
    private static final float SPACE_86 = 86f / 375;

    /**
     * 筛选按钮未选中
     */
    public final static int FILTER_STYLE_UNSELECT = 0;
    /**
     * 筛选按钮已选中
     */
    public final static int FILTER_STYLE_SELECTED = 1;
    /**
     * 筛选按钮置灰
     */
    public final static int FILTER_STYLE_UNENABLE = -1;

    private View rootView;
    private RecyclerView rvSearchFilter;
    private LinearLayout llFilter;
    private TextView tvFilter;
    private ImageView ivFilter;
    private View vFilterShadow;

    private SearchFilterAdapter searchFilterAdapter;
    private SearchEqualFilterAdapter searchEqualFilterAdapter;
    private FilterActionListener filterActionListener;
    private DropDownFilterPop dropDownFilterPop;
    /**
     * 外露的筛选横条上的属性筛选条件数组
     */
    private List<SearchFilterQuery> featureFilterList;
    private int totalCount;
    /**
     * 筛选条件是否均分展示
     */
    private boolean showEqualFilter = false;

    private TextView tvEqualFilter;

    public SearchResultFilter(Context context) {
        super(context);
        initView(context);
    }

    public SearchResultFilter(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public SearchResultFilter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化view
     *
     * @param context
     */
    private void initView(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.sf_field_search_widget_filter, this, true);
        rvSearchFilter = findViewById(R.id.rv_search_filter);
        llFilter = findViewById(R.id.ll_filter);
        tvFilter = findViewById(R.id.tv_filter);
        ivFilter = findViewById(R.id.iv_filter);
        vFilterShadow = findViewById(R.id.v_filter_shadow);
        tvEqualFilter = findViewById(R.id.tv_equal_filter);

        llFilter.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (filterActionListener != null) {
                filterActionListener.onFilterListener();
            }
        });

        tvEqualFilter.setOnClickListener(v -> {
            if (NoDoubleClickUtils.isDoubleClick()) {
                return;
            }
            if (filterActionListener != null) {
                filterActionListener.onFilterListener();
            }
        });

        CenterLayoutManager centerLayoutManager = new CenterLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
        rvSearchFilter.setLayoutManager(centerLayoutManager);
        setFilterStyle(FILTER_STYLE_UNENABLE);
    }

    /**
     * 设置筛选栏中筛选按钮状态
     *
     * @param state 0:未选择，1：已选择，-1：不可选
     */
    public void setFilterStyle(int state) {
        if (showEqualFilter) {
            // 平分样式的筛选
            if (state == FILTER_STYLE_UNSELECT) {
                tvEqualFilter.setTextColor(getResources().getColor(R.color.sf_field_search_color_1a1a1a));
                tvEqualFilter.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                tvEqualFilter.setEnabled(true);
            } else if (state == FILTER_STYLE_SELECTED) {
                tvEqualFilter.setTextColor(getResources().getColor(R.color.sf_theme_color_level_1));
                tvEqualFilter.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                tvEqualFilter.setEnabled(true);
            } else {
                tvEqualFilter.setTextColor(getResources().getColor(R.color.sf_field_search_color_cccccc));
                tvEqualFilter.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                tvEqualFilter.setEnabled(false);
            }
        } else {
            if (state == FILTER_STYLE_UNSELECT) {
                tvFilter.setTextColor(getResources().getColor(R.color.sf_field_search_color_313131));
                ivFilter.setImageResource(R.drawable.sf_field_search_filter_icon_unselect);
                ivFilter.setAlpha(1f);
                tvFilter.setEnabled(true);
                llFilter.setEnabled(true);
            } else if (state == FILTER_STYLE_SELECTED) {
                tvFilter.setTextColor(getResources().getColor(R.color.sf_theme_color_level_1));
                ivFilter.setImageResource(R.drawable.sf_theme_image_search_filter);
                ivFilter.setAlpha(1f);
                tvFilter.setEnabled(true);
                llFilter.setEnabled(true);
            } else {
                tvFilter.setTextColor(getResources().getColor(R.color.sf_field_search_color_cccccc));
                ivFilter.setImageResource(R.drawable.sf_field_search_filter_icon_unselect);
                ivFilter.setAlpha(0.4f);
                tvFilter.setEnabled(false);
                llFilter.setEnabled(false);
            }
        }
    }

    /**
     * 设置总数量
     *
     * @param totalCount
     */
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
        if (dropDownFilterPop != null) {
            dropDownFilterPop.setTotalCount(totalCount);
        }
    }

    /**
     * 设置筛选条数据
     *
     * @param searchResultDataManager
     * @param sortFilterList
     * @param featureFilterList
     */
    public void setFilterData(SearchResultDataManager searchResultDataManager, List<SearchFilterQuery> sortFilterList, List<SearchFilterQuery> featureFilterList) {
        if (sortFilterList == null || sortFilterList.isEmpty() || sortFilterList.size() != 4) {
            return;
        }

        int screenWidth = ScreenUtils.getScreenWidth(getContext());

        sortFilterList.get(0).setSelected(true);
        this.featureFilterList = featureFilterList;

        if (featureFilterList == null || featureFilterList.isEmpty()) {
            // 只有排序筛选的时候，平分样式
            showEqualFilter = true;
            tvEqualFilter.setVisibility(VISIBLE);
            llFilter.setVisibility(GONE);
            vFilterShadow.setVisibility(GONE);


            for (SearchFilterQuery filterQuery : sortFilterList) {
                filterQuery.setType(SearchFilterQuery.TYPE_FLAG);
                filterQuery.setSortFilter(true);
            }
            List<SearchFilterQuery> newSortFilterList = new ArrayList<>();
            newSortFilterList.add(sortFilterList.get(0));
            newSortFilterList.add(sortFilterList.get(1));
            SearchFilterQuery priceFilter = new SearchFilterQuery();
            priceFilter.setFilterLable(sortFilterList.get(2).getFilterLable());
            priceFilter.setFilterKey(sortFilterList.get(2).getFilterKey());
            priceFilter.setSortFilter(true);
            priceFilter.setType(SearchFilterQuery.TYPE_FLAG);
            if (sortFilterList.get(2).isSelected() || sortFilterList.get(3).isSelected()) {
                priceFilter.setSelected(true);
            }
            List<SearchFilterQuery> priceQuerys = new ArrayList<>();
            priceQuerys.add(sortFilterList.get(2));
            priceQuerys.add(sortFilterList.get(3));
            priceFilter.setFilterValues(priceQuerys);
            newSortFilterList.add(priceFilter);

            // 是否展示活动
            boolean hasActivity = searchResultDataManager.canShowActivity();

            if (searchEqualFilterAdapter == null) {
                searchEqualFilterAdapter = new SearchEqualFilterAdapter(getContext(), searchResultDataManager, hasActivity);
                rvSearchFilter.setAdapter(searchEqualFilterAdapter);
            }

            int size = newSortFilterList == null ? 0 : newSortFilterList.size();
            int equalItemWith = 0;
            if (size > 0) {
                if (hasActivity) {
                    // (屏宽-左间距16-活动标签宽96)/(排序筛选项长度+固定筛选)
                    equalItemWith = (screenWidth - ScreenUtils.dip2px(getContext(), (16 + 96))) / (size + 1);
                } else {
                    // (屏宽-左右间距16*2)/(排序筛选项长度+固定筛选)
                    equalItemWith = (screenWidth)/ (size + 1);
                }
            } else {
                if (hasActivity) {
                    equalItemWith = (int) (screenWidth * SPACE_66);
                } else {
                    equalItemWith = (int) (screenWidth * SPACE_86);
                }
            }

            //有活动筛选时，rvSearchFilter的右边距16，否则0
            int marginLeft = hasActivity ? ScreenUtils.dip2px(getContext(), 16) : 0;
            RelativeLayout.LayoutParams rvSearchFilterLayoutParams  = (RelativeLayout.LayoutParams) rvSearchFilter.getLayoutParams();
            rvSearchFilterLayoutParams.setMargins(marginLeft, 0, 0, 0);
            rvSearchFilter.setLayoutParams(rvSearchFilterLayoutParams);
            //tvEqualFilter的宽度要设置下
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) tvEqualFilter.getLayoutParams();
            layoutParams.width = equalItemWith;
            tvEqualFilter.setLayoutParams(layoutParams);

            searchEqualFilterAdapter.setFilterQueryList(newSortFilterList, true, equalItemWith);

            searchEqualFilterAdapter.setOnItemClickListener(new SearchEqualFilterAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    if (rvSearchFilter != null) {
//                        rvSearchFilter.smoothScrollToPosition(position);
                        rvSearchFilter.scrollToPosition(position);
                    }
                }

                @Override
                public void showDropDown(SearchFilterQuery searchFilterQuery) {
                    if (dropDownFilterPop != null && dropDownFilterPop.isShowing()) {
                        dropDownFilterPop.dismiss();
                        return;
                    }
                    if (dropDownFilterPop == null) {
                        dropDownFilterPop = new DropDownFilterPop(getContext());
                        dropDownFilterPop.setTotalCount(totalCount);
                    }
                    dropDownFilterPop.setFilterQuery(searchResultDataManager, searchFilterQuery);
                    dropDownFilterPop.setOnDismissListener(() -> {
                        searchFilterQuery.setExpanded(false);
                        if (searchEqualFilterAdapter != null) {
                            searchEqualFilterAdapter.notifyDataSetChanged();
                        }
                    });
                    dropDownFilterPop.showAsDropDown(rootView);
                }
            });
        } else {
            // 有其他筛选项的时候，不均分样式
            showEqualFilter = false;
            tvEqualFilter.setVisibility(GONE);
            llFilter.setVisibility(VISIBLE);
            vFilterShadow.setVisibility(VISIBLE);

            SearchFilterQuery zongheFilterQuery = new SearchFilterQuery();
            zongheFilterQuery.setFilterLable(sortFilterList.get(0).getName());
            zongheFilterQuery.setFilterValues(sortFilterList);
            zongheFilterQuery.setSortFilter(true);
            zongheFilterQuery.setType(SearchFilterQuery.TYPE_ENUM);
            zongheFilterQuery.setSelected(true);
            featureFilterList.add(0, zongheFilterQuery);

            if (searchFilterAdapter == null) {
                searchFilterAdapter = new SearchFilterAdapter(getContext(), searchResultDataManager);
                rvSearchFilter.setAdapter(searchFilterAdapter);
            }

            searchFilterAdapter.setFilterQueryList(featureFilterList, false);

            searchFilterAdapter.setOnItemClickListener(new SearchFilterAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    if (rvSearchFilter != null) {
                        rvSearchFilter.smoothScrollToPosition(position);
                    }
                }

                @Override
                public void showDropDown(SearchFilterQuery searchFilterQuery) {
                    if (dropDownFilterPop != null && dropDownFilterPop.isShowing()) {
                        dropDownFilterPop.dismiss();
                        return;
                    }
                    if (dropDownFilterPop == null) {
                        dropDownFilterPop = new DropDownFilterPop(getContext());
                        dropDownFilterPop.setTotalCount(totalCount);
                    }
                    dropDownFilterPop.setFilterQuery(searchResultDataManager, searchFilterQuery);
                    dropDownFilterPop.setOnDismissListener(() -> {
                        searchFilterQuery.setExpanded(false);
                        if (searchFilterAdapter != null) {
                            searchFilterAdapter.notifyDataSetChanged();
                        }
                    });
                    dropDownFilterPop.showAsDropDown(rootView);
                }
            });
        }

    }

    /**
     * 同步属性筛选数据选中态
     *
     * @param selectedFilterMap
     */
    public void syncFeatureFilterSelectStatus(Map<String, List<String>> selectedFilterMap) {
        if (featureFilterList == null || featureFilterList.isEmpty()) {
            //属性筛选数据为空，说明只有排序筛选，因此不需要同步选中态，只更新UI显示就可
            if (searchFilterAdapter != null) {
                searchFilterAdapter.notifyDataSetChanged();
            }
            if (searchEqualFilterAdapter != null) {
                searchEqualFilterAdapter.notifyDataSetChanged();
            }
            return;
        }
        for (SearchFilterQuery featureFilterQuery : featureFilterList) {
            if (featureFilterQuery == null || featureFilterQuery.isSortFilter()) {
                continue;
            }
            if (selectedFilterMap.containsKey(featureFilterQuery.getFilterKey())) {
                List<String> selectedQuery = selectedFilterMap.get(featureFilterQuery.getFilterKey());
                if (selectedQuery != null && !selectedQuery.isEmpty()) {
                    boolean hasChildSelected = false;
                    List<SearchFilterQuery> featureQuerys = featureFilterQuery.getFilterValues();
                    if (featureQuerys != null && !featureQuerys.isEmpty()) {
                        for (SearchFilterQuery query : featureQuerys) {
                            if (query == null) {
                                continue;
                            }
                            if (StringUtil.isNotEmpty(query.getFilterValue()) && selectedQuery.contains(query.getFilterValue())) {
                                query.setSelected(true);
                                hasChildSelected = true;
                            } else {
                                query.setSelected(false);
                            }
                        }
                        if (hasChildSelected) {
                            featureFilterQuery.setSelected(true);
                        } else if (StringUtil.isNotEmpty(featureFilterQuery.getFilterValue()) && selectedQuery.contains(featureFilterQuery.getFilterValue())) {
                            featureFilterQuery.setSelected(true);
                        } else {
                            featureFilterQuery.setSelected(false);
                        }
                    } else {
                        if (StringUtil.isNotEmpty(featureFilterQuery.getFilterValue()) && selectedQuery.contains(featureFilterQuery.getFilterValue())) {
                            featureFilterQuery.setSelected(true);
                        } else {
                            featureFilterQuery.setSelected(false);
                        }
                    }
                } else {
                    featureFilterQuery.setSelected(false);
                    selectedFilterMap.remove(featureFilterQuery.getFilterKey());
                }
            } else {
                featureFilterQuery.setSelected(false);
                if (featureFilterQuery.getFilterValues() != null && !featureFilterQuery.getFilterValues().isEmpty()) {
                    for (SearchFilterQuery query : featureFilterQuery.getFilterValues()) {
                        if (query != null) {
                            query.setSelected(false);
                        }
                    }
                }
            }
        }
        if (searchFilterAdapter != null) {
            searchFilterAdapter.notifyDataSetChanged();
        }
        if (searchEqualFilterAdapter != null) {
            searchEqualFilterAdapter.notifyDataSetChanged();
        }
        if (dropDownFilterPop != null && dropDownFilterPop.isShowing()) {
            dropDownFilterPop.notifyDataSetChanged();
        }
    }

    public void scrollToPosition(int position) {
        if (rvSearchFilter != null) {
            rvSearchFilter.scrollToPosition(position);
        }
    }

    public void setFilterActionListener(FilterActionListener actionListener) {
        this.filterActionListener = actionListener;
    }

    public interface FilterActionListener {
        /**
         * 筛选
         */
        void onFilterListener();
    }
}
