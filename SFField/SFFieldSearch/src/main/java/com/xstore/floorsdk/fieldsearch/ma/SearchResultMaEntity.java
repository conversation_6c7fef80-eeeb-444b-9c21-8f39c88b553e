package com.xstore.floorsdk.fieldsearch.ma;

import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;

public class SearchResultMaEntity extends BaseMaEntity {

    public String promotionType;
    public String price;
    public String sortAreaName;
    public String buttonStatus;
    public String skuId;
    public String skuName;
    public String lastSkuId;
    public String lastSkuName;
    public Integer isShow;
    public String touchstone_expids;
    public String enkwd;
    public String categoryFlatKeyWord;
    public int listPageIndex;
    public String tabName;

    public static class Constants {

        public static class SEARCH_LIST_EXCHANGEBUTTON extends BaseMaPublicParam {
            public final static String CLICKID = SearchResultMaConstants.SEARCHLISTPAGE_EXCHANGEBUTTON;

            public SEARCH_LIST_EXCHANGEBUTTON() {
            }
        }

        public static class SEARCHLISTPAGE_FINDSIMILAR_CLOSE extends BaseMaPublicParam {
            public final static String CLICKID = SearchResultMaConstants.SEARCHLISTPAGE_FINDSIMILAR_CLOSE;

            public SEARCHLISTPAGE_FINDSIMILAR_CLOSE() {
            }
        }

        public static class SEARCHLISTPAGE_FINDSIMILAR_CLICKINTOLANDINGPAGE extends BaseMaPublicParam {
            public final static String CLICKID = SearchResultMaConstants.SEARCHLISTPAGE_FINDSIMILAR_CLICKINTOLANDINGPAGE;

            public SEARCHLISTPAGE_FINDSIMILAR_CLICKINTOLANDINGPAGE() {
                CLICKTYPE = "3";
            }
        }
    }
}
