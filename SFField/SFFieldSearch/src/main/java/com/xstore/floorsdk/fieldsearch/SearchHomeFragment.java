package com.xstore.floorsdk.fieldsearch;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.floorsdk.fieldsearch.adapter.AutoSpellAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.HotRankListAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.HotWordSearchAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.NewSearchAdapter;
import com.xstore.floorsdk.fieldsearch.adapter.SearchHomeFrequentPurchaseAdapter;
import com.xstore.floorsdk.fieldsearch.bean.HotRankBean;
import com.xstore.floorsdk.fieldsearch.bean.HotSearchWordBean;
import com.xstore.floorsdk.fieldsearch.bean.RankListResponseData;
import com.xstore.floorsdk.fieldsearch.bean.SearchCardClickEvent;
import com.xstore.floorsdk.fieldsearch.bean.SearchHomeBean;
import com.xstore.floorsdk.fieldsearch.bean.SearchHomeFrequentPurchaseBean;
import com.xstore.floorsdk.fieldsearch.bean.SearchHomeHotWordBean;
import com.xstore.floorsdk.fieldsearch.interfaces.SearchHomeContainerInterface;
import com.xstore.floorsdk.fieldsearch.ma.RankMaEntity;
import com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa;
import com.xstore.floorsdk.fieldsearch.widget.SearchView;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.adapter.BaseQuickAdapter;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaPublicParam;
import com.xstore.sevenfresh.modules.newsku.bean.BooleanMapBean;
import com.xstore.sevenfresh.modules.newsku.bean.ProductExInfoBean;
import com.xstore.sevenfresh.modules.newsku.bean.SkuBaseExtInfoResBean;
import com.xstore.sevenfresh.modules.newsku.bean.SkuBaseInfoResBean;
import com.xstore.sevenfresh.modules.newsku.bean.SkuInfoVoBean;
import com.xstore.sevenfresh.modules.newsku.bean.SkuPriceInfoResBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_BAR;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_HOT_LIST_CLICK;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_REGULAR_PURCHASE_ALL;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.CLICK_SEARCH_REGULAR_PURCHASE_CLICK_COMMODITY;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.EXPOSE_HOT_LIST;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.EXPOSE_HOT_SEARCH;
import static com.xstore.floorsdk.fieldsearch.ma.SearchHomeMa.Constants.EXPOSE_REGULAR_PURCHASE;

/**
 * 搜索主页
 *
 * <AUTHOR>
 * @date 2023/05/16
 */
public class SearchHomeFragment extends Fragment implements SearchHomeContract.View {

    private AppCompatActivity activity;

    private View rootView;
    /**
     * 搜索框视图
     */
    private SearchView searchView;
    /**
     * 搜索list
     */
    private RecyclerView searchHistoryList;
    /**
     * 底部卡片容器
     */
    private LinearLayout llSearchPageContainer;
    /**
     * llRelateKey 联想词容器
     */
    private LinearLayout llRelateKey;
    /**
     * 联想 ListView
     */
    private ListView lvRelateKey;

    /**
     * 逻辑
     */
    private SearchHomePresenter presenter;
    /**
     * 搜索主页容器接口
     */
    private SearchHomeContainerInterface searchHomeContainerInterface;

    // header背景
    private int[] headerBg = {R.drawable.sf_field_search_hot_search_card_bg, R.drawable.sf_theme_image_search_frequent_purchase, R.drawable.sf_field_search_rank_card_bg};
    // header title
    private String[] headerTitle = {"xx热搜", "常购清单", "热销排行榜"};
    // header title color
    private String[] headerTitleColor = {"#F37C00", "#0A665E", "#FF3837"};
    /**
     * 榜单查看全部url
     */
    private String viewAllHotRankUrl;
    private AutoSpellAdapter autoSpellAdapter;
    private SearchHomeFrequentPurchaseAdapter frequentPurchaseAdapter;
    /**
     * 埋点
     */
    private JDMaUtils.JdMaPageImp jdMaPageImp = null;

    public SearchHomeFragment() {
    }

    /**
     * 防止getActivity()空指针
     */
    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        activity = (AppCompatActivity) context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.sf_field_search_home_fragment, container, false);
        activity = (AppCompatActivity) getActivity();
        jdMaPageImp = (JDMaUtils.JdMaPageImp) getActivity();
        headerTitle[0] = FloorInit.getFloorConfig().getBizName() + "热搜";
        presenter = new SearchHomePresenter(getActivity(), this, jdMaPageImp);
        initView();
        initListener();
        presenter.initData(getArguments());
        return rootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        presenter.onResume();
    }

    @Override
    public void onStop() {
        super.onStop();
        presenter.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        presenter.onDestroy();
    }

    public void setSearchHomeContainerInterface(SearchHomeContainerInterface searchHomeContainerInterface) {
        this.searchHomeContainerInterface = searchHomeContainerInterface;
    }

    /**
     * 初始化视图
     */
    private void initView() {
        searchView = rootView.findViewById(R.id.view_search);
        searchHistoryList = rootView.findViewById(R.id.hot_wordsgrid);
        llSearchPageContainer = rootView.findViewById(R.id.search_page_container);
        llRelateKey = rootView.findViewById(R.id.ll_relate_key);
        lvRelateKey = rootView.findViewById(R.id.lv_relate_key);
        lvRelateKey.setFooterDividersEnabled(true);

        final GridLayoutManager gridLayoutManager = new GridLayoutManager(activity, 2);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                int itemViewType = searchHistoryList.getAdapter().getItemViewType(position);
                if (itemViewType == NewSearchAdapter.TYPE_HISTORY || itemViewType == NewSearchAdapter.TYPE_DISCOVERY ) {
                    return 2;
                }
                return 1;
            }
        });

        searchHistoryList.setLayoutManager(gridLayoutManager);
        searchView.showKeyboard();


    }

    /**
     * 设置监听回调
     */
    private void initListener() {
        searchView.setListener(new SearchView.SearchActionListener() {
            @Override
            public void onBack() {
                presenter.clickSearchBarBack();
            }

            @Override
            public void onButton(String inputWord, String hintWord) {
                presenter.clickSearchBarSearch(inputWord, hintWord);
            }

            @Override
            public void onEdit(String keyword) {
                if (keyword != null && keyword.trim().length() > 0) {
                    setHotWordsVisiable(true);
                    presenter.getAutoSpellList(keyword);
                } else {
                    setHotWordsVisiable(false);
                }
            }

            @Override
            public void touchAndShowKeyboard() {
                JDMaUtils.save7FClick(CLICK_SEARCH_BAR, jdMaPageImp, null);
            }

            @Override
            public void onEditorAction(String inputWord, String hintWord) {
                presenter.clickEditorAction(inputWord, hintWord);
            }
        });

        lvRelateKey.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (autoSpellAdapter == null) {
                    return;
                }
                //点击联想词
                String temp = autoSpellAdapter.getItem(position);
                presenter.clickAutoSpellWord(autoSpellAdapter.getKeyword(), temp, position);
            }
        });

    }


    /**
     * 绑定联想词适配器
     *
     * @param list
     * @param keyword
     */
    @Override
    public void setAutoSpellList(List<String> list, String keyword) {
        if (StringUtil.isNullByString(searchView.getEtSearch().getText().toString())) {
            llRelateKey.setVisibility(View.GONE);
            searchHistoryList.setVisibility(View.VISIBLE);
        } else {
            llRelateKey.setVisibility(View.VISIBLE);
            searchHistoryList.setVisibility(View.GONE);
            if (autoSpellAdapter == null) {
                autoSpellAdapter = new AutoSpellAdapter(activity, list, keyword);
                lvRelateKey.setAdapter(autoSpellAdapter);
            } else {
                autoSpellAdapter.updateData(list, keyword);
            }
        }
    }

    /**
     * 设置热词显示与否
     *
     * @param isHasInputWord
     */
    public void setHotWordsVisiable(boolean isHasInputWord) {
        if (isHasInputWord) {
            searchHistoryList.setVisibility(View.GONE);
        } else {
            llRelateKey.setVisibility(View.GONE);
            searchHistoryList.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showSearchHistory(NewSearchAdapter searchHistoryAdapter) {
        searchHistoryList.setAdapter(searchHistoryAdapter);
    }

    @Override
    public void clearSearchWord() {
        AutoCompleteTextView et = searchView.getEtSearch();
        if (et == null) {
            return;
        }
        et.setText("");
    }

    @Override
    public void showSearchContent(SearchHomeBean searchHomeBean) {
        //设置底部需要展示的ui

        if (searchHomeBean == null) {
            //保持之前的ui
            llSearchPageContainer.removeAllViews();
            viewAllHotRankUrl = null;
            return;
        }

        boolean hasHotWord = searchHomeBean.getSearchNewHotWord() != null && searchHomeBean.getSearchNewHotWord().getHotWordList() != null && searchHomeBean.getSearchNewHotWord().getHotWordList().size() > 0;
        boolean hasFrequentPurchaseList = searchHomeBean.getQueryFrequentPurchasePage() != null && searchHomeBean.getQueryFrequentPurchasePage().getSkuInfos() != null && searchHomeBean.getQueryFrequentPurchasePage().getSkuInfos().size() > 0;
        boolean hasRank = searchHomeBean.getRankBaseInfoList() != null && searchHomeBean.getRankBaseInfoList().getDataInfoList() != null && searchHomeBean.getRankBaseInfoList().getDataInfoList().size() > 0;

        int cardCount = 0;
        if (hasHotWord) {
            cardCount++;
        } else {
            SgmBusinessErrorUtil.reportSearchNewHotWordEmpty();
        }
        if (hasFrequentPurchaseList) {
            cardCount++;
        } else {
            SgmBusinessErrorUtil.reportFrequentPurchaseEmpty();
        }
        if (hasRank) {
            cardCount++;
        } else {
            SgmBusinessErrorUtil.reportRankBaseInfoListEmpty();
        }

        // 添加热搜词card
        if (hasHotWord) {
            Map<String, Integer> hotTop = new HashMap<>();
            //如果热词就是空 也要进行覆盖替换 剔除老的数据
            for (int i = 0; i < searchHomeBean.getSearchNewHotWord().getHotWordList().size(); i++) {
                hotTop.put(searchHomeBean.getSearchNewHotWord().getHotWordList().get(i).hotWord, i + 1);
            }
            //存储一份数据 后边埋点使用
            presenter.hotTopList.put(TenantIdUtils.getStoreId(), hotTop);
            setHotWordCardData(addSearchCard(0, cardCount), searchHomeBean.getSearchNewHotWord());
        }

        // 添加常购清单card
        if (hasFrequentPurchaseList) {
            setFrequentPurchaseCardData(addSearchCard(1, cardCount), searchHomeBean.getQueryFrequentPurchasePage());
        }

        // 添加榜单card
        if (hasRank) {
            viewAllHotRankUrl = searchHomeBean.getRankBaseInfoList().getViewAllUrl();
            setHotRankListCardData(addSearchCard(2, cardCount), searchHomeBean.getRankBaseInfoList());
            JDMaUtils.save7FExposure(EXPOSE_HOT_LIST, null, null, "", jdMaPageImp);
        }

    }

    /**
     * 设置热搜词数据De
     *
     * @param hotWordSearchCard
     * @param searchNewHotWord
     */
    private void setHotWordCardData(RecyclerView hotWordSearchCard, SearchHomeHotWordBean searchNewHotWord) {
        if (searchNewHotWord.getHotWordList().size() > 10) {
            searchNewHotWord.setHotWordList(searchNewHotWord.getHotWordList().subList(0, 10));
        }
        final HotWordSearchAdapter hotWordSearchAdapter = new HotWordSearchAdapter(searchNewHotWord.getHotWordList());
        hotWordSearchCard.setAdapter(hotWordSearchAdapter);

        hotWordSearchAdapter.addHeaderView(getHeaderView(0));
        hotWordSearchAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                HotSearchWordBean hotSearchWordBean = hotWordSearchAdapter.getItem(position);
                if (hotSearchWordBean == null) {
                    return;
                }
                presenter.clickHotWord(hotSearchWordBean, position);
            }
        });

        SearchHomeMa ma = new SearchHomeMa();
        StringBuffer sb = new StringBuffer();
        for (HotSearchWordBean word : searchNewHotWord.getHotWordList()) {
            sb.append(word.hotWord).append("|");
        }
        ma.keyword_list = sb.toString();
        JDMaUtils.save7FExposure(EXPOSE_HOT_SEARCH, null, ma, "", jdMaPageImp);
    }


    /**
     * 设置常购清单数据
     *
     * @param frequentPurchaseCard
     * @param queryFrequentPurchasePage
     */
    private void setFrequentPurchaseCardData(RecyclerView frequentPurchaseCard, final SearchHomeFrequentPurchaseBean queryFrequentPurchasePage) {
        try {
            if (queryFrequentPurchasePage.getSkuInfos().size() > 7) {
                queryFrequentPurchasePage.setSkuInfos(queryFrequentPurchasePage.getSkuInfos().subList(0, 7));
            }
            List<SkuInfoVoBean> tmp = new ArrayList<SkuInfoVoBean>(queryFrequentPurchasePage.getSkuInfos());

            frequentPurchaseAdapter = new SearchHomeFrequentPurchaseAdapter(activity, tmp, jdMaPageImp);
            frequentPurchaseCard.setAdapter(frequentPurchaseAdapter);

            frequentPurchaseAdapter.addHeaderView(getHeaderView(1));
            frequentPurchaseAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    SkuInfoVoBean sku = frequentPurchaseAdapter.getItem(position);
                    if (sku == null || sku.getSkuBaseInfoRes() == null) {
                        return;
                    }
                    FloorJumpManager.getInstance().jumpProductDetail(activity, sku, true);
                    // 埋点
                    reportClickEvent(CLICK_SEARCH_REGULAR_PURCHASE_CLICK_COMMODITY, "", sku.getSkuBaseInfoRes().getSkuId(), sku.getSkuBaseInfoRes().getSkuName(), "2");
                }
            });

            // 如果常购清单小于7条数据，请求推荐数据补充
            if (tmp.size() < 7) {
                presenter.requestRecommend();
            }
            for (int i = 0; i < tmp.size(); i++) {
                SkuInfoVoBean sku = tmp.get(i);
                SearchHomeMa ma = new SearchHomeMa();
                if (sku.getSkuBaseInfoRes() != null) {
                    ma.skuId = sku.getSkuBaseInfoRes().getSkuId();
                    ma.skuName = sku.getSkuBaseInfoRes().getSkuName();
                    ma.listPageIndex = 1 + i;
                }
                JDMaUtils.save7FExposure(EXPOSE_REGULAR_PURCHASE, null, ma, "", jdMaPageImp);
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
            e.printStackTrace();
        }
    }

    @Override
    public void appendFrequentList(List<SkuInfoBean> skuInfos) {
        if (frequentPurchaseAdapter == null) {
            return;
        }
        try {
            int needAppendCount = 7 - frequentPurchaseAdapter.getData().size();
            if (skuInfos.size() < needAppendCount) {
                needAppendCount = skuInfos.size();
            }
            List<SkuInfoBean> tempList = skuInfos.subList(0, needAppendCount);
            List<SkuInfoVoBean> frequentPurchaseSkus = new ArrayList<>();
            for (SkuInfoBean skuInfoBean: tempList) {
                if (skuInfoBean == null) {
                    continue;
                }
                frequentPurchaseSkus.add(wareInfoConvert(skuInfoBean));
            }
            int count = frequentPurchaseAdapter.getData().size();
            frequentPurchaseAdapter.addData(frequentPurchaseSkus);
            for (int i = 0; i < frequentPurchaseSkus.size(); i++) {
                SkuInfoVoBean sku = frequentPurchaseSkus.get(i);
                SearchHomeMa ma = new SearchHomeMa();
                if (sku.getSkuBaseInfoRes() != null) {
                    ma.skuId = sku.getSkuBaseInfoRes().getSkuId();
                    ma.skuName = sku.getSkuBaseInfoRes().getSkuName();
                    ma.listPageIndex = count + 1 + i;
                }
                JDMaUtils.save7FExposure(EXPOSE_REGULAR_PURCHASE, null, ma, "", jdMaPageImp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    private SkuInfoVoBean wareInfoConvert(SkuInfoBean skuInfoBean) {
        if (skuInfoBean == null) {
            return null;
        }
        SkuInfoVoBean skuInfoVoBean = new SkuInfoVoBean();
        SkuBaseInfoResBean skuBaseInfoResBean = new SkuBaseInfoResBean();
        SkuPriceInfoResBean skuPriceInfoResBean = new SkuPriceInfoResBean();
        SkuBaseExtInfoResBean skuBaseExtInfoResBean = new SkuBaseExtInfoResBean();
        BooleanMapBean booleanMapBean = new BooleanMapBean();
        ProductExInfoBean productExInfoBean = new ProductExInfoBean();
        skuBaseInfoResBean.setSkuId(skuInfoBean.getSkuId());
        skuBaseInfoResBean.setSkuName(skuInfoBean.getSkuName());
        if (skuInfoBean.getSkuImageInfo() != null) {
            skuBaseInfoResBean.setImageUrl(skuInfoBean.getSkuImageInfo().getMainUrl());
        }
        if (skuInfoBean.getSalePrice() != null) {
            skuPriceInfoResBean.setJdPrice(skuInfoBean.getSalePrice().getValue());
            skuBaseExtInfoResBean.setBuyUnitDesc(skuInfoBean.getSalePrice().getBuyUnitDesc());
        }
        if (skuInfoBean.getLogicInfo() != null) {
            skuInfoVoBean.setServiceTags(skuInfoBean.getLogicInfo().getServiceTags());
            skuInfoVoBean.setTasteInfoList(skuInfoBean.getLogicInfo().getTasteInfoList());
            skuInfoVoBean.setPreSaleInfo(skuInfoBean.getLogicInfo().getPreSaleInfo());
            skuInfoVoBean.setRestrictRule(skuInfoBean.getLogicInfo().getRestrictRule());
            if (!StringUtil.isNullByString(skuInfoBean.getLogicInfo().getStartBuyUnitNum())) {
                skuBaseInfoResBean.setStartBuyUnitNum(Double.parseDouble(skuInfoBean.getLogicInfo().getStartBuyUnitNum()));
            }
            if (!StringUtil.isNullByString(skuInfoBean.getLogicInfo().getStepBuyUnitNum())) {
                skuBaseInfoResBean.setStepBuyUnitNum(Double.parseDouble(skuInfoBean.getLogicInfo().getStepBuyUnitNum()));
            }
            if (!StringUtil.isNullByString(skuInfoBean.getLogicInfo().getMaxBuyUnitNum())) {
                skuBaseInfoResBean.setMaxBuyUnitNum(Double.parseDouble(skuInfoBean.getLogicInfo().getMaxBuyUnitNum()));
            }
            skuBaseInfoResBean.setSaleSpecDesc(skuInfoBean.getLogicInfo().getSaleSpecDesc());
            skuBaseInfoResBean.setSaleAttrInfos(skuInfoBean.getLogicInfo().getSaleAttrInfos());
            skuBaseExtInfoResBean.setBuyUnitInCart(skuInfoBean.getLogicInfo().getBuyUnitInCart());
            skuBaseExtInfoResBean.setIsPop(skuInfoBean.getLogicInfo().isPop());
            booleanMapBean.setPop(skuInfoBean.getLogicInfo().isPop());
            booleanMapBean.setPeriod(skuInfoBean.getLogicInfo().isPeriod());
            booleanMapBean.setBuyButtonType(skuInfoBean.getLogicInfo().getBuyButtonType());
            productExInfoBean.setIsTakeaway(skuInfoBean.getLogicInfo().getIsTakeaway());
            productExInfoBean.setIsRealName(skuInfoBean.getLogicInfo().getIsRealName());

        }
        if (skuInfoBean.getCartInfo() != null) {
            booleanMapBean.setAddCart(!skuInfoBean.getCartInfo().isDisableAddCart());
        }
        skuBaseExtInfoResBean.setBooleanMap(booleanMapBean);
        skuBaseInfoResBean.setSkuBaseExtInfoRes(skuBaseExtInfoResBean);
        skuBaseInfoResBean.setProductExInfo(productExInfoBean);
        skuInfoVoBean.setSkuBaseInfoRes(skuBaseInfoResBean);
        skuInfoVoBean.setSkuPriceInfoRes(skuPriceInfoResBean);
        skuInfoVoBean.setBrokerInfo(skuInfoBean.getBrokerInfo());
        return skuInfoVoBean;
    }

    /**
     * 设置热榜榜单数据
     *
     * @param hotRankCard
     * @param rankBaseInfoList
     */
    private void setHotRankListCardData(RecyclerView hotRankCard, RankListResponseData rankBaseInfoList) {
        if (rankBaseInfoList.getDataInfoList().size() > 10) {
            rankBaseInfoList.setDataInfoList(rankBaseInfoList.getDataInfoList().subList(0, 10));
        }
        final HotRankListAdapter hotRankListAdapter = new HotRankListAdapter(rankBaseInfoList.getDataInfoList());
        hotRankCard.setAdapter(hotRankListAdapter);
        hotRankListAdapter.addHeaderView(getHeaderView(2));

        hotRankListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                HotRankBean sku = hotRankListAdapter.getItem(position);
                if (sku == null) {
                    return;
                }
                if (!StringUtil.isEmpty(sku.getJumpUrl())) {
                    FloorJumpManager.getInstance().startH5(activity, sku.getJumpUrl(), false);
                }

                reportClickEvent(CLICK_SEARCH_HOT_LIST_CLICK, sku.getJumpUrl(), "", "", null);
            }
        });
    }


    @Override
    public void setKeyWord(String keyword, String hotWordDefault) {
        //设置输入框内的默认搜索词
        searchView.setKeyWord(keyword, hotWordDefault);
    }
    @Override
    public void setKeyWord(String keyword, String hotWordDefault,String hotWordDefaultIcon) {
        //设置输入框内的默认搜索词
        searchView.setKeyWord(keyword, hotWordDefault,hotWordDefaultIcon);
    }


    /**
     * 添加搜索卡片
     */
    private RecyclerView addSearchCard(int type, int cardCount) {
        // 子view数量
        int childCount = llSearchPageContainer.getChildCount();
        View cardView = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_card_layout, null);

        RecyclerView recyclerView = cardView.findViewById(R.id.recyclerView);
        recyclerView.setFocusable(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(activity));

        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(calculateCardWidth(type, cardCount), DisplayUtils.dp2px(activity, 555));
        if (childCount == 0) {
            layoutParams.leftMargin = ScreenUtils.dip2px(activity, 15);
        }
        // 如果是最后一个card需要设置右边距
        if (childCount == cardCount - 1) {
            layoutParams.rightMargin = ScreenUtils.dip2px(activity, 12);
        } else {
            layoutParams.rightMargin = ScreenUtils.dip2px(activity, 8);
        }
        llSearchPageContainer.addView(cardView, layoutParams);

        return recyclerView;
    }

    /**
     * 计算卡片的宽度
     *
     * @return
     */
    private int calculateCardWidth(int type, int cardCount) {
        if (cardCount > 1) {
            return type == 1 ? ScreenUtils.dip2px(activity, 262) : (int) ((ScreenUtils.getScreenWidth(activity) - ScreenUtils.dip2px(activity, 30)) / 1.5f);
        } else {
            return ScreenUtils.getScreenWidth(activity) - ScreenUtils.dip2px(activity, 30);
        }
    }


    /**
     * headerView
     *
     * @param type 0热搜词 1常购清单 2热销榜单
     * @return
     */
    private View getHeaderView(final int type) {
        View headerView = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_header_hot_word, null);
        TextView title = headerView.findViewById(R.id.tv_title);
        ImageView ivHeaderBg = headerView.findViewById(R.id.iv_header_bg);
        title.setText(headerTitle[type]);
        title.setTextColor(Color.parseColor(headerTitleColor[type]));
        ivHeaderBg.setImageResource(headerBg[type]);

        ImageView arrow = headerView.findViewById(R.id.iv_arrow);
        TextView more = headerView.findViewById(R.id.tv_more);

        if (type == 0) {
            more.setText("");
            arrow.setVisibility(View.INVISIBLE);
        } else {
            more.setText("查看全部");
            arrow.setVisibility(View.VISIBLE);
        }
        headerView.findViewById(R.id.tv_more).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (type == 0) {

                } else if (type == 1) {
                    // 常购清单
                    FloorJumpManager.getInstance().jumpToFrequentPurchase(activity);
                    JDMaUtils.save7FClick(CLICK_SEARCH_REGULAR_PURCHASE_ALL, jdMaPageImp, null);
                } else if (type == 2) {
                    if (!StringUtil.isEmpty(viewAllHotRankUrl)) {
                        FloorJumpManager.getInstance().startH5(activity, viewAllHotRankUrl, false);
                    }
                    JDMaUtils.save7FClick(RankMaEntity.SEARCHPAGE_RANKINGLISTCARD_LOOKALL, jdMaPageImp, null);
                }
            }
        });
        return headerView;
    }

    /**
     * 点击埋点
     *
     * @param eId
     * @param url
     * @param skuId
     * @param skuName
     */
    private void reportClickEvent(String eId, String url, String skuId, String skuName, String clickType) {
        SearchCardClickEvent searchCardClickEvent = new SearchCardClickEvent(skuId, skuName, url);
        if (clickType != null) {
            BaseMaPublicParam baseMaPublicParam = new BaseMaPublicParam();
            baseMaPublicParam.CLICKTYPE = clickType;
            searchCardClickEvent.setPublicParam(baseMaPublicParam);
        }
        JDMaUtils.save7FClick(eId, jdMaPageImp, searchCardClickEvent);
    }

    @Override
    public SearchHomeContainerInterface getSearchHomeContainerInterface() {
        return searchHomeContainerInterface;
    }
}
