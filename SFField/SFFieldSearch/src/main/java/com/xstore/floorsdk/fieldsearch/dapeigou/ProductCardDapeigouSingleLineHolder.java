package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.R;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;

public class ProductCardDapeigouSingleLineHolder extends RecyclerView.ViewHolder {

    private ProductCardDapeigouSingleLineView productCardDapeigouSingleLineView;

    public ProductCardDapeigouSingleLineHolder(@NonNull View itemView) {
        super(itemView);
        productCardDapeigouSingleLineView = itemView.findViewById(R.id.product_card_dapeigou_view);
    }

    /**
     * 设置商品卡片宽度
     *
     * @param activity
     * @param cardWidth
     * @param cardHeight
     */
    public void setCardSize(AppCompatActivity activity, int cardWidth, int cardHeight) {
        if (productCardDapeigouSingleLineView != null) {
            int designWidth = ScreenUtils.dip2px(activity, cardWidth);
            int designHeight = ScreenUtils.dip2px(activity, cardHeight);
            ViewGroup.LayoutParams lp = productCardDapeigouSingleLineView.getLayoutParams();
            if (lp != null) {
                lp.width = designWidth;
                productCardDapeigouSingleLineView.setLayoutParams(lp);
            }
            productCardDapeigouSingleLineView.setCardSize(designWidth, designHeight, ScreenUtils.dip2px(activity, 2));
        }
    }


    /**
     * 绑定数据
     *
     * @param activity
     * @param skuInfoBean
     * @param productCardInterface
     */
    public void bindData(AppCompatActivity activity, SkuInfoBean skuInfoBean, ProductCardInterfaces productCardInterface) {
        if (productCardDapeigouSingleLineView != null) {
            productCardDapeigouSingleLineView.bindData(activity, skuInfoBean, productCardInterface);
        }
    }
}
