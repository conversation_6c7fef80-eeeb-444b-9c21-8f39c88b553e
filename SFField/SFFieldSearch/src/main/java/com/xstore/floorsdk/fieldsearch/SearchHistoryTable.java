package com.xstore.floorsdk.fieldsearch;

import android.text.TextUtils;

import com.jd.framework.json.JDJSON;
import com.xstore.floorsdk.fieldsearch.bean.SearchHistory;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索数据存储
 */
public class SearchHistoryTable {

    public final static int MAX_SEARCH_HISTORY_NUM = 30; // 最大历史记录数量
    private static final String SEARCH_HISTORY_LIST = "search_history_list_bean";


    /**
     * 插入一条搜索历史： 1. 如果记录中存在同样的关键词，则调整关键词位置 2. 如果记录总数大于最大值，则删除最后一条
     */
    public static void saveSearchHistory(String keyword) {
        if (keyword == null) {
            return;
        }
        List<SearchHistory> list = getAllSearchHistory();
        if (list == null) {
            list = new ArrayList<>();
            list.add(new SearchHistory(keyword));
        } else {
            //先判断下之前是否存在这个关键词 则给他移除，然后重新插入 这样他就是在最前面了
            for (int i = list.size() - 1; i >= 0; i--) {
                if (keyword.equals(list.get(i).getWord())) {
                    list.remove(i);
                }
            }
            list.add(0, new SearchHistory(keyword));
        }
        try {
            if (list.size() > MAX_SEARCH_HISTORY_NUM) {
                list = list.subList(0, MAX_SEARCH_HISTORY_NUM);
            }
            String searchHistoryListJson = JDJSON.toJSONString(list);
            PreferenceUtil.saveString(SEARCH_HISTORY_LIST, searchHistoryListJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 清空全部的搜索记录
     */
    public static void deleteAllHistory() {
        PreferenceUtil.saveString(SEARCH_HISTORY_LIST, null);
    }

    /**
     * @return 获取全部的搜索记录
     */
    public static List<SearchHistory> getAllSearchHistory() {
        try {
            String searchHistoryListJson = PreferenceUtil.getString(SEARCH_HISTORY_LIST, null);
            if (!TextUtils.isEmpty(searchHistoryListJson)) {
                List<SearchHistory> list = JDJSON.parseArray(searchHistoryListJson, SearchHistory.class);
                return list;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
