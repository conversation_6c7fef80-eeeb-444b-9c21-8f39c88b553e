package com.xstore.floorsdk.fieldsearch.cloudstore;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.bean.PrescriptionCloudStoreVo;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.widget.RoundCornerImageView1;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 卡片样式云卖场入口holder
 *
 * <AUTHOR>
 * @date 2022/10/03
 */
public class YunCardHolder extends RecyclerView.ViewHolder {

    RoundCornerImageView1 ivYunBg;
    ImageView ivYunTitle;
    TextView tvYunDesc;
    ImageView ivYunArrow;
    RelativeLayout rlYunSingleProduct;
    RoundCornerImageView1 ivYunSingleProductImg;
//    ProductTagViewV2 productTagView;
    RecyclerView rvYunMultProduct;

    public YunCardHolder(@NonNull View itemView) {
        super(itemView);
        ivYunBg = itemView.findViewById(R.id.iv_yun_bg);
        ivYunTitle = itemView.findViewById(R.id.iv_yun_title);
        tvYunDesc = itemView.findViewById(R.id.tv_yun_desc);
        ivYunArrow = itemView.findViewById(R.id.iv_yun_arrow);
        rlYunSingleProduct = itemView.findViewById(R.id.rl_yun_single_product);
        ivYunSingleProductImg = itemView.findViewById(R.id.iv_yun_single_product_img);
//        productTagView = itemView.findViewById(R.id.product_tag);
        rvYunMultProduct = itemView.findViewById(R.id.rv_yun_mult_product);
    }

    /**
     * 绑定数据
     *
     * @param activity
     * @param cloudStoreVo
     * @param searchResultReporter
     */
    public void bindData(AppCompatActivity activity, PrescriptionCloudStoreVo cloudStoreVo, SearchResultReporter searchResultReporter) {
        if (cloudStoreVo == null) {
            return;
        }
        int corner = ScreenUtils.dip2px(activity, 8);
        ivYunBg.setRadius(corner, corner, corner, corner);
        ivYunSingleProductImg.setRadius(corner, corner, corner, corner);
        if (StringUtil.isNotEmpty(cloudStoreVo.getSubTitle())) {
            tvYunDesc.setText(cloudStoreVo.getSubTitle());
        }
        if (cloudStoreVo.getProductCardVoList().size() > 2) {
            ivYunArrow.setVisibility(View.VISIBLE);
        } else {
            ivYunArrow.setVisibility(View.GONE);
        }
        if (cloudStoreVo.getProductCardVoList().size() == 1) {
            SkuInfoBean productInfo = cloudStoreVo.getProductCardVoList().get(0);
            rlYunSingleProduct.setVisibility(View.VISIBLE);
            rvYunMultProduct.setVisibility(View.GONE);
            if (productInfo != null) {
                ImageloadUtils.loadImage(activity, ivYunSingleProductImg, productInfo.getSkuImageInfo().getMainUrl());
                //打各种标
//                productTagView.initCold(false);
//                productTagView.initSeven();
//                productTagView.initCoupon();
//                productTagView.initAction();
//                productTagView.showCover(false, productInfo);
                rlYunSingleProduct.setOnClickListener(v -> {
                    FloorJumpManager.getInstance().jumpProductDetail(activity, productInfo, true);
                    searchResultReporter.cloudSkuClick(productInfo, 0);
                });
            }
        } else {
            List<SkuInfoBean> productInfos;
            if (cloudStoreVo.getProductCardVoList().size() < 4) {
                productInfos = cloudStoreVo.getProductCardVoList().subList(0, 2);
            } else {
                productInfos = cloudStoreVo.getProductCardVoList().subList(0, 4);
            }
            rlYunSingleProduct.setVisibility(View.GONE);
            rvYunMultProduct.setVisibility(View.VISIBLE);
            rvYunMultProduct.setLayoutManager(new GridLayoutManager(activity, 2));
            YunCardMultProductAdapter productAdapter = new YunCardMultProductAdapter(activity, searchResultReporter, productInfos);
            rvYunMultProduct.setAdapter(productAdapter);
        }
    }
}
