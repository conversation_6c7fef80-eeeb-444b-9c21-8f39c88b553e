package com.xstore.floorsdk.fieldsearch.dapeigou;

import static com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting.NO_EFFECT;

import android.content.Context;

import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;

/**
 * 搜索页搭配购Manager
 */
public class SearchDapeigouManager {

    private static SearchDapeigouManager searchDapeigouManager;

    private SearchDapeigouManager() {
    }

    public static SearchDapeigouManager getInstance() {
        if (searchDapeigouManager == null) {
            searchDapeigouManager = new SearchDapeigouManager();
        }
        return searchDapeigouManager;
    }

    public void postDapeigouData(Context context, String skuId, BaseFreshResultCallback callback) {
        FreshHttpSetting httpSetting = new FreshHttpSetting();
        httpSetting.setEffect(NO_EFFECT);
        httpSetting.setFunctionId("omnitech_category_getRecommendSkuList");
        httpSetting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        httpSetting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        httpSetting.setResultCallback(callback);
        httpSetting.putJsonParam("skuId", skuId);
        httpSetting.putJsonParam("sceneType", 1); //场景类型 1-搜索搭配购
        httpSetting.putJsonParam("pageId", "0005");
        FreshHttpGroupUtils.getHttpGroup().add(context, httpSetting);
    }

}
