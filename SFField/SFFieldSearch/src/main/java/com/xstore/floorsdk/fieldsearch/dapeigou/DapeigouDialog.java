package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.ma.SearchResultReporter;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class DapeigouDialog extends Dialog {
    private Activity context;
    private RecyclerView recyclerView;
    private List<SkuInfoBean> skuInfoBeanList;

    private String coreSkuId;

    private SearchResultReporter categoryReporterInterface;
    private DapeigouDialogProductExposureHelper productExposureHelper;

    public DapeigouDialog(@NonNull Context context, List<SkuInfoBean> skuInfoBeanList, String skuId, SearchResultReporter searchResultReporter) {
        super(context, R.style.sf_field_search_DapeigouDialogStyle);
        if (!(context instanceof Activity)) {
            return;
        }
        this.context = (Activity) context;
        this.skuInfoBeanList = skuInfoBeanList;
        this.coreSkuId = skuId;
        this.categoryReporterInterface = searchResultReporter;
        setContentView(R.layout.sf_field_search_dapeigou_dialog_layout);
        productExposureHelper = new DapeigouDialogProductExposureHelper(categoryReporterInterface, coreSkuId);
        initView();
    }

    /**
     * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
     */
    private int px2dip(float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    @Override
    public void show() {
        super.show();
        if (recyclerView != null) {
            recyclerView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (isShowing() && recyclerView != null && productExposureHelper != null) {
                        productExposureHelper.exposureByHand(recyclerView, RecyclerView.SCROLL_STATE_IDLE);
                    }
                }
            }, 200);
        }
    }

    private void initView() {
        recyclerView = findViewById(R.id.product_recycler_view);
        findViewById(R.id.mask_view).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });
        findViewById(R.id.close_image).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });
        int screenWidth = ScreenUtils.getScreenWidth(context);
        int itemWidth = (screenWidth - ScreenUtils.dip2px(context, 18) - ScreenUtils.dip2px(context, 12)) / 3;
        int itemHeight = (itemWidth * 177) / 115;
        DapeigouAdapter dapeigouAdapter = new DapeigouAdapter(context, px2dip(itemWidth), px2dip(itemHeight),skuInfoBeanList);
        int defaultItemWidthSpace = ScreenUtils.dip2px(context, 6);
//        int startEndItemWidth = ScreenUtils.dip2px(context, 9);
        GridLayoutManager layoutManager = new GridLayoutManager(context, 3);
        recyclerView.setLayoutManager(layoutManager);
        GrideSpaceItemDecoration itemDecoration = new GrideSpaceItemDecoration(defaultItemWidthSpace, defaultItemWidthSpace, ScreenUtils.dip2px(context, 20), 3);
        recyclerView.addItemDecoration(itemDecoration);
        recyclerView.setAdapter(dapeigouAdapter);
        if (skuInfoBeanList == null) {
            skuInfoBeanList = new ArrayList<>();
        }
//        dapeigouAdapter.setData(skuInfoBeanList);
        dapeigouAdapter.setProductCardInterfaces(new ProductCardInterfaces() {
            @Override
            public int setCardAbilityType() {
                return 0;
            }

            @Override
            public void onCardClick(SkuInfoBean skuInfoBean) {
                super.onCardClick(skuInfoBean);
                if (skuInfoBean == null) {
                    return;
                }
                if (NoDoubleClickUtils.isDoubleClick()) {
                    return;
                }
                FloorJumpManager.getInstance().jumpProductDetail(context, skuInfoBean, true, 0);
                if (categoryReporterInterface != null) {
                    categoryReporterInterface.dapeigouDialogProductClick(skuInfoBean, coreSkuId);
                }
            }

            @Override
            public Dialog getAddCartViewDialog() {
                return DapeigouDialog.this;
            }

            @Override
            public void onAddCartClick(SkuInfoBean skuInfoBean) {
                super.onAddCartClick(skuInfoBean);
                if (categoryReporterInterface != null) {
                    categoryReporterInterface.dapeigouDialogProductAddCart(skuInfoBean, coreSkuId);
                }
            }
        });

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (productExposureHelper != null) {
                    productExposureHelper.exposureByHand(recyclerView, newState);
                }
            }
        });

        int maxHeight = ScreenUtils.getScreenHeight(context) * 4 / 5 - ScreenUtils.dip2px(context, 48);
        int recyclerViewHeight = 0;
        int lines = 1;
        if(skuInfoBeanList.size() % 3 == 0 ){
            lines = skuInfoBeanList.size() / 3 ;
        }else{
            lines = skuInfoBeanList.size() / 3 + 1;
        }
        recyclerViewHeight = itemHeight * lines + ScreenUtils.dip2px(context, 20) + (ScreenUtils.dip2px(context, 6) * (lines - 1));
        if (recyclerViewHeight > maxHeight) {
            recyclerViewHeight = maxHeight;
        }
        ViewGroup.LayoutParams params = recyclerView.getLayoutParams();
        params.height = recyclerViewHeight;
        recyclerView.setLayoutParams(params);
        Window window = this.getWindow();
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
//        lp.height = recyclerViewHeight + ScreenUtils.dip2px(context, 48);
        lp.height = ViewGroup.LayoutParams.MATCH_PARENT;
//         设置显示位置
        onWindowAttributesChanged(lp);
        window.setGravity(Gravity.BOTTOM);
    }
}
