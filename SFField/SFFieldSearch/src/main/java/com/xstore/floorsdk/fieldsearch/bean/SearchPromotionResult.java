package com.xstore.floorsdk.fieldsearch.bean;

import com.xstore.sevenfresh.modules.productdetail.bean.PromotionTypeInfo;

import java.io.Serializable;

/**
 * 搜索促销数据模型
 *
 * <AUTHOR>
 * @date 2022/10/02
 */
public class SearchPromotionResult implements Serializable {
    /**
     * 请求返回code码
     */
    private int code;
    /**
     * msg
     */
    private String message;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 促销信息
     */
    private PromotionTypeInfo promotion;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public PromotionTypeInfo getPromotion() {
        return promotion;
    }

    public void setPromotion(PromotionTypeInfo promotion) {
        this.promotion = promotion;
    }
}
