package com.xstore.floorsdk.fieldsearch.dapeigou;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.sevenfresh.cart.SFActionObserverHelper;
import com.xstore.sevenfresh.cart.interfaces.AddCartMaListenerV3;
import com.xstore.sevenfresh.cart.widget.AddCartViewV3;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuCartInfo;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.interfaces.SkuEnumInterface;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;
import com.xstore.sevenfresh.productcard.utils.ProductPriceUtil;
import com.xstore.sevenfresh.productcard.utils.ScreenUtils;
import com.xstore.sevenfresh.productcard.utils.StringUtil;
import com.xstore.sevenfresh.productcard.widget.ProductImageTagView;
import com.xstore.sevenfresh.productcard.widget.RoundCornerImageViewV3;
import com.xstore.sevenfresh.productcard.widget.SfCardPriceView;


/**
 * @author: tianzhenyuan
 * @date: 2025/1/17
 * @description: 商品卡片：一行多个 分类页搭配购 高度固定 大于等于3个
 * <p>
 * 商品名：最多展示两行，标题前面不展示标
 * 卖点行：温层｜生产日期>新品>鲜系列｜属性词>广告语（优先展示第4层促销，没有第4层展示第3层卖点行） 不展示
 * 促销行：履约标(外卖>产地直发>极速达>次日达>京尊达)｜促销标(最多两个)｜跟风(回购>好评)，（最多展示2个）
 * 价格行（上）：灰字价(划线价/非划线价)，不展示
 * 价格行（下）：到手价>秒杀价>新人价>Plus/铂金/黄金/普通， 不展示
 * 价格单位：不展示
 * 榜单行：不展示
 * 百科入口：不展示
 * <p>
 * 卡片默认高度210：不展示灰字价时最小高度为190，展示灰字价时最小高度为210
 */
public class ProductCardDapeigouSingleLineView extends RelativeLayout {

    private RelativeLayout rlProductCardFixedContainer;
    private RoundCornerImageViewV3 ivProductImg;
    private ProductImageTagView productImgTagView;
    private TextView tvProductName;
    private SfCardPriceView sfCardPriceView;
    private AddCartViewV3 addCartView;
    private TextView imageTagText;
    /**
     * 找相似
     */
    private TextView tvFindSimilar;
    /**
     * corner 圆角
     */
    private static int imageCorner;
    /**
     * 图片宽度
     */
    private int imageWidth;

    public ProductCardDapeigouSingleLineView(Context context) {
        super(context);
        initView();
    }

    public ProductCardDapeigouSingleLineView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public ProductCardDapeigouSingleLineView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    public ProductCardDapeigouSingleLineView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    /**
     * 初始化
     */
    private void initView() {
        View itemView = LayoutInflater.from(getContext()).inflate(R.layout.sf_field_search_product_card_dapeigou_singleline_view, this, true);
        rlProductCardFixedContainer = itemView.findViewById(R.id.rl_product_card_fixed_container);
        ivProductImg = itemView.findViewById(R.id.iv_product_img);
        productImgTagView = itemView.findViewById(R.id.product_img_tag);
        imageCorner = ScreenUtils.dip2px(getContext(), 5);
        ivProductImg.setRadius(imageCorner, imageCorner, imageCorner, imageCorner);
        tvProductName = itemView.findViewById(R.id.tv_product_name);
        sfCardPriceView = itemView.findViewById(R.id.tv_price_view);
        addCartView = itemView.findViewById(R.id.acv_addcart);
        addCartView.setAddCartViewV3Type(AddCartViewV3.AddCartViewV3Type.TYPE_DAIPEIGOU);
        tvFindSimilar = itemView.findViewById(R.id.tv_find_similar);
        imageTagText = itemView.findViewById(R.id.image_tag_txt);
    }

    public ProductCardDapeigouSingleLineView showMarketPrice(boolean show) {
        return this;
    }

    public ProductCardDapeigouSingleLineView showPriceUnit(boolean show) {
        return this;
    }

    /**
     * 设置商品卡片大小
     *
     * @param cardWidth
     * @param cardHeight     cardHeight、最小高度, 取比较大的那个
     * @param imageMarginLTR 图片左、上、右的margin
     */
    public ProductCardDapeigouSingleLineView setCardSize(int cardWidth, int cardHeight, int imageMarginLTR) {
        if (ivProductImg == null) {
            return this;
        }
        //设置商品卡片宽、高
        ViewGroup.LayoutParams lpCard = rlProductCardFixedContainer.getLayoutParams();
        if (lpCard != null) {
            lpCard.width = cardWidth;
            lpCard.height = cardHeight;
            rlProductCardFixedContainer.setLayoutParams(lpCard);
        }
        //设置图片大小
        imageWidth = cardWidth - imageMarginLTR * 2;
        LayoutParams lpImage = (LayoutParams) ivProductImg.getLayoutParams();
        if (lpImage != null) {
            lpImage.width = imageWidth;
            lpImage.height = imageWidth;
            lpImage.setMargins(imageMarginLTR, imageMarginLTR, imageMarginLTR, 0);
            ivProductImg.setLayoutParams(lpImage);
        }
        return this;
    }

    public ProductCardDapeigouSingleLineView setTitleSingleLine(boolean singleLine) {
        if (tvProductName != null) {
            tvProductName.setSingleLine(singleLine);
        }
        return this;
    }

    public void bindData(AppCompatActivity activity, SkuInfoBean skuInfo, ProductCardInterfaces productCardInterface) {
        if (skuInfo == null) {
            return;
        }
        //商品图片
        ImageloadUtils.loadImage(activity, ivProductImg, skuInfo.getSkuImageInfo() == null ? "" : skuInfo.getSkuImageInfo().getMainUrl(), true);
        //商品图片上的文字

        String imageTxt = "";
        if (skuInfo.getTagList() != null && skuInfo.getTagList().size() > 0) {
            imageTxt = skuInfo.getTagList().get(0).getText();
        }
        if (TextUtils.isEmpty(imageTxt) && skuInfo.getSellPointList() != null && skuInfo.getSellPointList().size() > 0) {
            imageTxt = skuInfo.getSellPointList().get(0).getText();
        }
        if (TextUtils.isEmpty(imageTxt)) {
            imageTagText.setVisibility(View.GONE);
        } else {
            imageTagText.setText(imageTxt);
            imageTagText.setVisibility(View.VISIBLE);
        }
        //商品状态/图片标
        productImgTagView.setProductStatus(skuInfo.getSkuMaskInfo(), false);
        //商品名称
        if (!StringUtil.isNullByString(skuInfo.getSkuName())) {
            tvProductName.setText(skuInfo.getSkuName());
//            ProductTitleTagUtil.addTagBeforeNameWithMultiLine(activity, tvProductName, skuInfo, 14);
        } else {
            tvProductName.setText("");
        }
        sfCardPriceView.setStyle(SfCardPriceView.PRICE_STYLE_SMALL);
        ProductPriceUtil.setSalePrice(activity, sfCardPriceView, null, null, skuInfo.getSalePrice(), true);
//        if (!showPriceUnit) {
//            tvPriceSaleUnit.setVisibility(GONE);
//        }

        //展示底部按钮
        showBottomButtonStatus(activity, skuInfo, productCardInterface);
        //点击事件
        setClickListener(skuInfo, productCardInterface);
    }

    /**
     * 直播卡片使用
     *
     * @param cardWidth
     * @param cardHeight
     */
    public void reSetCartSize(int cardWidth, int cardHeight) {
        if (rlProductCardFixedContainer != null) {
            ViewGroup.LayoutParams lpCard = rlProductCardFixedContainer.getLayoutParams();
            if (lpCard != null) {
                lpCard.width = cardWidth;
                lpCard.height = cardHeight;
                rlProductCardFixedContainer.setLayoutParams(lpCard);
            }
        }
    }

    /**
     * 展示底部加车按钮、去预定、点外卖、找相似按钮
     *
     * @param activity
     * @param skuInfo
     * @param productCardInterface
     */
    private void showBottomButtonStatus(AppCompatActivity activity, SkuInfoBean skuInfo, ProductCardInterfaces productCardInterface) {
        if (skuInfo == null) {
            return;
        }
        SkuCartInfo skuCartInfo = skuInfo.getCartInfo();
        if (skuCartInfo == null || skuCartInfo.isHiddenBtn()) {
            addCartView.setVisibility(GONE);
            tvFindSimilar.setVisibility(GONE);
        } else if (skuCartInfo.getType() == SkuEnumInterface.CartBtnType.PRE_SALE || skuCartInfo.getType() == SkuEnumInterface.CartBtnType.FIND_SIMILAR) {
            addCartView.setVisibility(GONE);
            tvFindSimilar.setVisibility(VISIBLE);
            if (skuCartInfo.getType() == SkuEnumInterface.CartBtnType.PRE_SALE) {
                tvFindSimilar.setText(R.string.sf_card_pre_sale_now);
                tvFindSimilar.setTextColor(ContextCompat.getColor(activity, R.color.sf_card_white));
                tvFindSimilar.setBackgroundResource(R.drawable.sf_card_corner_20_fab608_bg);
                tvFindSimilar.setOnClickListener(v -> {
                    if (productCardInterface != null) {
                        productCardInterface.bookNowClick(skuInfo);
                    }
                });
                if (productCardInterface != null) {
                    productCardInterface.bookNowExposure(skuInfo);
                }
            } else {
                tvFindSimilar.setText(R.string.sf_card_find_similar);
                tvFindSimilar.setTextColor(ContextCompat.getColor(activity, R.color.sf_card_color_0A665E));
                tvFindSimilar.setBackgroundResource(R.drawable.sf_card_corner_20_stroke_0a665e_bg);
                tvFindSimilar.setOnClickListener(v -> {
                    if (productCardInterface != null) {
                        productCardInterface.findSimilarClick(skuInfo);
                    }
                });
            }
        } else {
            tvFindSimilar.setVisibility(GONE);
            addCartView.setVisibility(VISIBLE);
            //加车
            if (productCardInterface != null) {
                addCartView.bindWareInfo(activity, skuInfo, ivProductImg, productCardInterface.getAddCartViewEndView(), productCardInterface.getAddCartViewSourceFrom(), productCardInterface.getAddCartViewDialog());
            } else {
                addCartView.bindWareInfo(activity, skuInfo, ivProductImg);
            }
        }
    }

    /**
     * 设置点击事件
     */
    private void setClickListener(SkuInfoBean wareBean, ProductCardInterfaces productCardInterface) {
        this.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (productCardInterface != null) {
                    productCardInterface.onCardClick(wareBean);
                }
            }
        });

        addCartView.setAddCartMaListener(new AddCartMaListenerV3() {

            @Override
            public void onAddCartMa(SkuInfoBean skuInfoBean) {
                SFActionObserverHelper.getInstance().notifyAction(SFActionObserverHelper.ADDCART_ACTION);
                if (productCardInterface != null) {
                    productCardInterface.onAddCartClick(wareBean);
                }
            }
        });
    }

}
