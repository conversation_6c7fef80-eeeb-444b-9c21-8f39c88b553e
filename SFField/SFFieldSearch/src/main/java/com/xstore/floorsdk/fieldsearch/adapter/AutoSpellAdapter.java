package com.xstore.floorsdk.fieldsearch.adapter;

import android.app.Activity;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.xstore.floorsdk.fieldsearch.R;

import java.util.List;

/**
 * 自动匹配适配器
 *
 * <AUTHOR>
 * @date 2019/07/05
 */
public class AutoSpellAdapter extends BaseAdapter {
    /**
     * 数据列表
     */
    private List<String> entities;
    /**
     * 布局加载器
     */
    private LayoutInflater inflater;
    /**
     * context
     */
    private Activity context;
    /**
     * 关键字
     */
    private String keyword;

    /**
     * 构造适配器
     *
     * @param context
     * @param entities
     * @param keyword
     */
    public AutoSpellAdapter(Activity context, List<String> entities, String keyword) {
        this.entities = entities;
        this.context = context;
        this.keyword = keyword;
        inflater = LayoutInflater.from(context);
    }

    /**
     * 更新数据
     *
     * @param entities
     * @param keyword
     */
    public void updateData(List<String> entities, String keyword) {
        this.entities = entities;
        this.keyword = keyword;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return entities == null ? 0 : entities.size();
    }

    @Override
    public String getItem(int position) {
        if (entities != null && entities.size() > position) {
            return entities.get(position);
        } else {
            return null;
        }
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        AreaHolder holder = null;
        final String elements = entities.get(position);
        if (convertView == null) {
            holder = new AreaHolder();
            convertView = inflater.inflate(R.layout.sf_field_search_autospell_item, null);
            holder.active1 = convertView.findViewById(R.id.active1);
            convertView.setTag(holder);
        } else {
            holder = (AreaHolder) convertView.getTag();
        }
        SpannableStringBuilder builder = new SpannableStringBuilder(elements);
        int start = elements.indexOf(keyword);
        while (start > -1) {
            int end = start + keyword.length();
            if (end > elements.length()) {
                end = elements.length();
            }
            ForegroundColorSpan blueSpan = new ForegroundColorSpan(ContextCompat.getColor(context, R.color.sf_field_search_color_00a292));
            builder.setSpan(blueSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            start = elements.indexOf(keyword, end);
        }
        holder.active1.setText(builder);
        return convertView;
    }

    public String getKeyword() {
        return keyword;
    }

    private class AreaHolder {
        /**
         * 匹配的搜索词
         */
        private TextView active1;
    }
}
