package com.xstore.floorsdk.fieldsearch.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/02/16
 */
public class HotSearchWordBean implements Serializable {

    /**
     * 热词
     */
    public String hotWord;
    /**
     * 跳转url
     */
    public String url;
    /**
     * 后台配置的 icon
     */
    public String icon;

    public String picurl;
    /**
     * 跳转 icon
     */
    @Deprecated
    public String jumpIcon;

    public String showWord;
    /**
     * 是否 红色展示 热词
     */
    public boolean hotShow;

    public String getHotWord() {
        return hotWord;
    }

    public void setHotWord(String hotWord) {
        this.hotWord = hotWord;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getPicurl() {
        return picurl;
    }

    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    @Deprecated
    public String getJumpIcon() {
        return jumpIcon;
    }

    public void setJumpIcon(String jumpIcon) {
        this.jumpIcon = jumpIcon;
    }

    public boolean isHotShow() {
        return hotShow;
    }

    public void setHotShow(boolean hotShow) {
        this.hotShow = hotShow;
    }

    public String getShowWord() {
        return showWord;
    }

    public void setShowWord(String showWord) {
        this.showWord = showWord;
    }
}
