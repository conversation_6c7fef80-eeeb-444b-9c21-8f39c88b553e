package com.xstore.floorsdk.fieldsearch.adapter;

import android.content.Context;
import android.graphics.Outline;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.xstore.floorsdk.fieldsearch.R;
import com.xstore.floorsdk.fieldsearch.SearchResultDataManager;
import com.xstore.floorsdk.fieldsearch.bean.CouponInfo;
import com.xstore.floorsdk.fieldsearch.bean.ExpandQuery;
import com.xstore.floorsdk.fieldsearch.bean.PrescriptionCloudStoreVo;
import com.xstore.floorsdk.fieldsearch.bean.SearchRelateWordRecQuery;
import com.xstore.floorsdk.fieldsearch.bean.WordSearch;
import com.xstore.floorsdk.fieldsearch.cloudstore.YunCardHolder;
import com.xstore.floorsdk.fieldsearch.cloudstore.YunHolder;
import com.xstore.floorsdk.fieldsearch.dapeigou.DaPeiGouCardView;
import com.xstore.floorsdk.fieldsearch.dapeigou.DaPeiGouListView;
import com.xstore.floorsdk.fieldsearch.dapeigou.SearchDapeigouManager;
import com.xstore.floorsdk.fieldsearch.dapeigou.DapeigouResult;
import com.xstore.floorsdk.fieldsearch.widget.FlowLayout;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.modules.productdetail.bean.PromotionTypeInfo;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuInfoBean;
import com.xstore.sevenfresh.modules.skuV3.bean.SkuMarketEntrance;
import com.xstore.sevenfresh.productcard.holder.ProductCardViewHolder;
import com.xstore.sevenfresh.productcard.holder.ProductListViewHolder;
import com.xstore.sevenfresh.productcard.interfaces.ProductCardInterfaces;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

/**
 * 搜索结果页商品列表适配器
 *
 * <AUTHOR>
 * @date 2022/09/25
 */
public class SearchProductAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final float SPACE_80 = 80f / 375;

    /**
     * 优惠券提示 单行
     */
    public final static int VIEW_TYPE_COUPON_TIP = 1;
    /**
     * 促销提示 单行
     */
    public final static int VIEW_TYPE_PROMOTION_TIP = 2;
    /**
     * 优惠券预告 单行
     */
    public final static int VIEW_TYPE_COUPON_PREPARE = 3;
    /**
     * footer 单行
     */
    public final static int VIEW_TYPE_FOOTER = 4;
    /**
     * 商品类型  单行
     */
    public final static int VIEW_TYPE_PRODUCT_LIST = 5;
    /**
     * 商品类型  卡片
     */
    public final static int VIEW_TYPE_PRODUCT_CARD = 6;
    /**
     * 无数据类型  单行
     */
    public final static int VIEW_TYPE_NODATA = 7;
    /**
     * 无数据类型  单行
     */
    public final static int VIEW_TYPE_NODATA_TRY_SEARCH = 8;
    /**
     * 标题类型  单行
     */
    public final static int VIEW_TYPE_RECOMMEND_TITLE = 9;
    /**
     * 智能推荐 单行
     */
    public final static int VIEW_TYPE_RECOMMEND_PRODUCT = 10;
    /**
     * 智能推荐 卡片
     */
    public final static int VIEW_TYPE_RECOMMEND_PRODUCT_CARD = 11;
    /**
     * 搜索无结果滑词扩招回(当前搜索没有找到 然后联想搜索) 单行
     */
    public final static int VIEW_TYPE_WORD_SEARCH = 12;
    /**
     * 试试搜(搜索关键词推荐) 单行
     */
    public static final int VIEW_TYPE_TRY_SEARCH = 13;
    /**
     * 试试搜(搜索关键词推荐) 卡片
     */
    public static final int VIEW_TYPE_TRY_SEARCH_CARD = 14;
    /**
     * 纠错词
     */
    public final static int VIEW_TYPE_ERROR_WORD = 15;
    /**
     * 云卖场入口 单行
     */
    public static final int VIEW_TYPE_YUN = 16;
    /**
     * 云卖场入口 卡片
     */
    public static final int VIEW_TYPE_YUN_CARD = 17;
    /**
     * 搜索相关性提示
     */
    public static final int VIEW_TYPE_OTHER_RELATED_RESULT_TITLE = 18;

    /**
     * activity
     */
    private AppCompatActivity activity;
    private SearchResultDataManager searchResultDataManager;
    /**
     * 布局加载器
     */
    private LayoutInflater inflater;
    /**
     * item点击回调
     */
    private OnItemClickListener onItemClickListener;
    /**
     * 来源 1 主搜 2 券搜 3 促销搜 4 加价购 5 领货码 6 搜索列表 7 直播
     */
    private int fromType = 0;
    /**
     * 是否是卡片模式
     */
    private boolean showCard;
    /**
     * 商品列表数据
     */
    private List<SkuInfoBean> productInfoList = new ArrayList<>();
    /**
     * 扩词搜索
     */
    private WordSearch wordSearch;
    /**
     * 纠错词
     */
    private ExpandQuery expandQuery;
    /**
     * 试试搜
     */
    private List<String> trySearchWords;
    /**
     * 相关搜索词穿插
     */
    private List<String> relatedWords;
    /**
     * 相关搜索词穿插 带icon的数据
     * zuoruibao
     */
    private List<SearchRelateWordRecQuery> relatedWordRecQueryList;
    /**
     * 券搜提示语
     */
    private String tipsContent;
    /**
     * 促销信息(促销搜索用)
     */
    private PromotionTypeInfo promotionInfo;
    /**
     * 优惠券信息(券搜用)
     */
    private CouponInfo couponInfo;
    /**
     * 时效云卖场商品和标题信息
     */
    private PrescriptionCloudStoreVo cloudStoreVo;
    /**
     * 商品起始位置
     */
    public int skuStartPosition = -1;
    /**
     * 推荐商品位置
     */
    public int recommendSkuPosition = -1;

    private int screenWidth;

    // 搭配购展示频率用到的参数
    private boolean isMPaasShowDaPeiGou;//移动配置总开关
    private ConcurrentHashMap<String, Boolean> daPeiGouDisplayedSkuMaps = new ConcurrentHashMap<String, Boolean>();// 当前搜索适配器已经展示过搭配购的Sku
    private final AtomicInteger dapeigouIgnoreCount = new AtomicInteger(0);// 会话状态记录-当前会话中用户忽略搭配购的次数(出现一次搭配购就+1,点击搭配购的 更多 or 加车 or 进商详,重置为0, 大于阈值后,进入1小时的推荐冷却期)
    private static long mLastSessionLockTime = 0;//静态字段,APP全局的,重启APP就会重置

    public SearchProductAdapter(AppCompatActivity activity, SearchResultDataManager searchResultDataManager, int fromType, boolean showCard) {
        this.activity = activity;
        this.searchResultDataManager = searchResultDataManager;
        this.fromType = fromType;
        this.showCard = showCard;
        this.inflater = LayoutInflater.from(activity);
    }

    /**
     * 绑定数据，此方法只刷新操作用
     *
     * @param productInfos
     * @param wordSearch
     * @param expandQuery
     * @param trySearchWords
     * @param relatedWordRecQueryList
     * @param cloudStoreVo
     * @param bottomRecommendWares
     */
    public void setData(List<SkuInfoBean> productInfos, WordSearch wordSearch, ExpandQuery expandQuery,
                        List<String> trySearchWords,  List<SearchRelateWordRecQuery> relatedWordRecQueryList,
                        PrescriptionCloudStoreVo cloudStoreVo,
                        List<SkuInfoBean> bottomRecommendWares) {
        //删除搜索无结果场景下的试试搜功能(即不展示)
        if(trySearchWords!=null){
            trySearchWords.clear();
        }
        this.wordSearch = wordSearch;
        this.expandQuery = expandQuery;
        this.trySearchWords = trySearchWords;
//        this.relatedWords = relatedWords;
        this.relatedWordRecQueryList = relatedWordRecQueryList;
        this.cloudStoreVo = cloudStoreVo;
        this.productInfoList.clear();
        skuStartPosition = -1;
        recommendSkuPosition = -1;
        //券提示
        if (this.couponInfo != null) {
            SkuInfoBean wareInfoCouponInfo = new SkuInfoBean();
            wareInfoCouponInfo.setViewType(VIEW_TYPE_COUPON_PREPARE);
            productInfoList.add(wareInfoCouponInfo);
        } else if (StringUtil.isNotEmpty(tipsContent)) {
            SkuInfoBean wareInfoCouponTip = new SkuInfoBean();
            wareInfoCouponTip.setCustomString(tipsContent);
            wareInfoCouponTip.setViewType(VIEW_TYPE_COUPON_TIP);
            productInfoList.add(wareInfoCouponTip);
        }
        //促销提示
        if (this.promotionInfo != null) {
            SkuInfoBean wareInfoPromoTip = new SkuInfoBean();
            wareInfoPromoTip.setViewType(VIEW_TYPE_PROMOTION_TIP);
            productInfoList.add(wareInfoPromoTip);
        }
        // 优先处理纠错词
        if (expandQuery != null && expandQuery.isExpandQueryStatus() && StringUtil.isNotEmpty(expandQuery.getErrorWord())
                && StringUtil.isNotEmpty(expandQuery.getRightWord())) {
            SkuInfoBean wareInfoExpandQuery = new SkuInfoBean();
            wareInfoExpandQuery.setViewType(VIEW_TYPE_ERROR_WORD);
            productInfoList.add(wareInfoExpandQuery);
            searchResultDataManager.searchResultReporter.exposureErrorWord(expandQuery.getRightWord());
        } else if (wordSearch != null && wordSearch.isHasWordSearch() && StringUtil.isNotEmpty(wordSearch.getOldWord())
                && StringUtil.isNotEmpty(wordSearch.getNewWord())) {
            SkuInfoBean wareInfoWordSearch = new SkuInfoBean();
            wareInfoWordSearch.setViewType(VIEW_TYPE_WORD_SEARCH);
            productInfoList.add(wareInfoWordSearch);
            searchResultDataManager.searchResultReporter.exposureExpandWord(wordSearch.getNewWord());
        }
        if (productInfos != null && productInfos.size() > 0) {
            for (SkuInfoBean bean : productInfos) {
                setProductViewType(bean);
            }
            if (cloudStoreVo != null && cloudStoreVo.getProductCardVoList() != null && !cloudStoreVo.getProductCardVoList().isEmpty()) {
                //云卖场入口
                SkuInfoBean wareInfoYun = new SkuInfoBean();
                wareInfoYun.setViewType(VIEW_TYPE_YUN);
                if (productInfos.size() > 5) {
                    productInfos.add(5, wareInfoYun);
                } else {
                    productInfos.add(wareInfoYun);
                }
            }
            skuStartPosition = productInfoList.size();
            productInfoList.addAll(productInfos);
            // 试试搜 改用带icon的数据
//            if (relatedWords != null && !relatedWords.isEmpty()) {
            if (relatedWordRecQueryList != null && !relatedWordRecQueryList.isEmpty()) {
                SkuInfoBean wareInfoRelatedWord = new SkuInfoBean();
                wareInfoRelatedWord.setViewType(VIEW_TYPE_TRY_SEARCH);
                productInfoList.add(wareInfoRelatedWord);
            }
        } else {
            //空数据
            SkuInfoBean wareInfoNodata = new SkuInfoBean();
            wareInfoNodata.setViewType(VIEW_TYPE_NODATA);
            productInfoList.add(wareInfoNodata);
            searchResultDataManager.searchResultReporter.searchNoData();
            //试试搜
            if (trySearchWords != null && !trySearchWords.isEmpty()) {
                SkuInfoBean wareInfoTrySearch = new SkuInfoBean();
                wareInfoTrySearch.setViewType(VIEW_TYPE_NODATA_TRY_SEARCH);
                productInfoList.add(wareInfoTrySearch);
            }
        }
        if (!searchResultDataManager.hasNextPage()) {
            //底部推荐
            if (bottomRecommendWares != null && !bottomRecommendWares.isEmpty()) {
                SkuInfoBean wareInfoRecomTitle = new SkuInfoBean();
                wareInfoRecomTitle.setViewType(VIEW_TYPE_RECOMMEND_TITLE);
                productInfoList.add(wareInfoRecomTitle);
                for (SkuInfoBean bean : bottomRecommendWares) {
                    if (bean != null) {
                        bean.setViewType(VIEW_TYPE_PRODUCT_LIST);
                        bean.setRecommend(true);
                    }
                }
                recommendSkuPosition = productInfoList.size();
                productInfoList.addAll(bottomRecommendWares);
                if (bottomRecommendWares.size() < searchResultDataManager.recommendPageSize) {
                    addFooter();
                }
            } else if (productInfos != null && productInfos.size() > 0) {
                addFooter();
            }
        }
        notifyDataSetChanged();
    }

    private void addFooter() {
        //数据加载完footer
        SkuInfoBean wareInfoFooter = new SkuInfoBean();
        wareInfoFooter.setViewType(VIEW_TYPE_FOOTER);
        productInfoList.add(wareInfoFooter);
    }

    public void setTipsContent(String tipsContent) {
        this.tipsContent = tipsContent;
    }

    public void setPromotionInfo(PromotionTypeInfo promotionInfo) {
        this.promotionInfo = promotionInfo;
        if (promotionInfo == null) {
            return;
        }
        SkuInfoBean productInfo = new SkuInfoBean();
        productInfo.setViewType(VIEW_TYPE_PROMOTION_TIP);
        if (productInfoList.size() > 0 && productInfoList.get(0).getViewType() == VIEW_TYPE_PROMOTION_TIP) {
            productInfoList.remove(0);
        }
        productInfoList.add(0, productInfo);
        notifyDataSetChanged();
    }

    public void setCouponInfo(CouponInfo couponInfo) {
        this.couponInfo = couponInfo;
        if (couponInfo == null) {
            return;
        }
        SkuInfoBean productInfo = new SkuInfoBean();
        productInfo.setViewType(VIEW_TYPE_COUPON_PREPARE);
        if (productInfoList.size() > 0 && (productInfoList.get(0).getViewType() == VIEW_TYPE_COUPON_PREPARE
                || productInfoList.get(0).getViewType() == VIEW_TYPE_COUPON_TIP)) {
            productInfoList.remove(0);
        }
        productInfoList.add(0, productInfo);
        notifyDataSetChanged();
    }

    /**
     * 增加搜索主数据
     *
     * @param productInfos
     * @param bottomRecommendWares
     */
    public void addData(List<SkuInfoBean> productInfos, List<SkuInfoBean> bottomRecommendWares) {
        int startPosition = productInfoList.size();
        if (productInfos != null && productInfos.size() > 0) {
            for (SkuInfoBean bean : productInfos) {
                setProductViewType(bean);
            }
            productInfoList.addAll(productInfos);
        }
        if (!searchResultDataManager.hasNextPage()) {
            //底部推荐
            if (bottomRecommendWares != null && !bottomRecommendWares.isEmpty()) {
                SkuInfoBean wareInfoRecomTitle = new SkuInfoBean();
                wareInfoRecomTitle.setViewType(VIEW_TYPE_RECOMMEND_TITLE);
                productInfoList.add(wareInfoRecomTitle);
                for (SkuInfoBean bean : bottomRecommendWares) {
                    if (bean != null) {
                        bean.setViewType(VIEW_TYPE_PRODUCT_LIST);
                    }
                }
                recommendSkuPosition = productInfoList.size();
                productInfoList.addAll(bottomRecommendWares);
            }
            if (bottomRecommendWares == null || bottomRecommendWares.size() < searchResultDataManager.recommendPageSize) {
                addFooter();
            }
        }
        int insertCount = productInfoList.size() - startPosition;
        if (insertCount > 0) {
            notifyItemRangeInserted(startPosition, insertCount);
        }
    }

    private void setProductViewType(SkuInfoBean bean) {
        if (bean != null) {
            try {
                Map<String, String> extMap = bean.getExtMap();
                if (extMap != null && "1".equals(extMap.get("searchRelatedType"))) {
                    searchResultDataManager.setIsAppearTimesRelated(1);
                    bean.setViewType(VIEW_TYPE_OTHER_RELATED_RESULT_TITLE);
                } else {
                    bean.setViewType(VIEW_TYPE_PRODUCT_LIST);
                }

            } catch (Exception e) {
                bean.setViewType(VIEW_TYPE_PRODUCT_LIST);
            }
        }
    }

    /**
     * 增加推荐数据
     *
     * @param recommendProducts
     */
    public void addRecommendData(List<SkuInfoBean> recommendProducts) {
        int startPosition = productInfoList.size();
        if (recommendProducts != null && !recommendProducts.isEmpty()) {
            for (SkuInfoBean bean : recommendProducts) {
                if (bean != null) {
                    bean.setViewType(VIEW_TYPE_PRODUCT_LIST);
                    bean.setRecommend(true);
                }
            }
            productInfoList.addAll(recommendProducts);
        }
        if (recommendProducts == null || recommendProducts.size() < searchResultDataManager.recommendPageSize) {
            addFooter();
        }
        int insertCount = productInfoList.size() - startPosition;
        if (insertCount > 0) {
            notifyItemRangeInserted(startPosition, insertCount);
        }
    }

    /**
     * 切换展示样式
     *
     * @param showCard
     */
    public void changeShowMode(boolean showCard) {
        this.showCard = showCard;
        notifyDataSetChanged();
    }

    public boolean isShowCard() {
        return showCard;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case VIEW_TYPE_COUPON_TIP:
                View couponTipView = inflater.inflate(R.layout.sf_field_search_item_coupon_tip, parent, false);
                return new CouponTipHolder(couponTipView);
            case VIEW_TYPE_PROMOTION_TIP:
                View promotionTipView = inflater.inflate(R.layout.sf_field_search_item_promotion_tip, parent, false);
                return new PromotionTipHolder(promotionTipView);
            case VIEW_TYPE_COUPON_PREPARE:
                View couponPrepareView = inflater.inflate(R.layout.sf_field_search_item_coupon_prepare, parent, false);
                return new CouponPrepareHolder(couponPrepareView);
            case VIEW_TYPE_FOOTER:
                View footerView = inflater.inflate(R.layout.sf_field_search_item_footer, parent, false);
                return new FooterHolder(footerView);
            case VIEW_TYPE_PRODUCT_LIST:
                View productListView = inflater.inflate(R.layout.sf_card_product_list_item, parent, false);
                DaPeiGouListView daPeiGouListView = new DaPeiGouListView(activity);
                daPeiGouListView.setXiaoFeiDaPeiGouListener(new DaPeiGouListView.XiaoFeiDaPeiGouListener() {
                    @Override
                    public void xiaoFeiDaPeiGou() {
                        dapeigouIgnoreCount.set(0);
                        mLastSessionLockTime = 0;
                    }
                });
                ProductListViewHolder productListViewHolder = new ProductListViewHolder(productListView);
                productListViewHolder.setBottomExtraView(daPeiGouListView);
                return productListViewHolder;
            case VIEW_TYPE_PRODUCT_CARD:
                View productCardView = inflater.inflate(R.layout.sf_card_product_card_item, parent, false);
                DaPeiGouCardView daPeiGouCardView = new DaPeiGouCardView(activity);
                daPeiGouCardView.setXiaoFeiDaPeiGouListener(new DaPeiGouCardView.XiaoFeiDaPeiGouListener() {
                    @Override
                    public void xiaoFeiDaPeiGou() {
                        dapeigouIgnoreCount.set(0);
                        mLastSessionLockTime = 0;
                    }
                });
                ProductCardViewHolder productCardViewHolder = new ProductCardViewHolder(productCardView);
                productCardViewHolder.setBottomExtraView(daPeiGouCardView);
                return productCardViewHolder;
            case VIEW_TYPE_NODATA:
                View nodataView = inflater.inflate(R.layout.sf_field_search_item_nodata, parent, false);
                return new NoDataTipsHolder(nodataView);
            case VIEW_TYPE_NODATA_TRY_SEARCH:
                View noDataTrySearchView = inflater.inflate(R.layout.sf_field_search_item_try_search, parent, false);
                return new NoDataTrySearchHolder(noDataTrySearchView);
            case VIEW_TYPE_RECOMMEND_TITLE:
                View recommendTitleView = inflater.inflate(R.layout.sf_field_search_item_recommend_title, parent, false);
                return new RecommendTitleHolder(recommendTitleView);
            case VIEW_TYPE_RECOMMEND_PRODUCT:
                View recommendView = inflater.inflate(R.layout.sf_field_search_similar_recommend, parent, false);
                return new SimilarRecommendViewHolder(activity, recommendView);
            case VIEW_TYPE_RECOMMEND_PRODUCT_CARD:
                View recommendCardView = inflater.inflate(R.layout.sf_field_search_similar_recommend_card, parent, false);
                return new SimilarRecommendCardViewHolder(activity, recommendCardView);
            case VIEW_TYPE_WORD_SEARCH:
            case VIEW_TYPE_ERROR_WORD:
                View wordSearchView = inflater.inflate(R.layout.sf_field_search_item_word_search, parent, false);
                return new WordSearchHolder(wordSearchView);
            case VIEW_TYPE_TRY_SEARCH:
                //试试搜，使用带icon的Holder
                View trySearchView = inflater.inflate(R.layout.sf_field_search_item_try_search_v2, parent, false);
                return new TrySearchFlowHolder(trySearchView,false);
//                return new TrySearchHolder(trySearchView);
            case VIEW_TYPE_TRY_SEARCH_CARD:
                //试试搜，使用带icon的Holder
                View trySearchCardView = inflater.inflate(R.layout.sf_field_search_item_try_search_card_v2, parent, false);
                return new TrySearchFlowHolder(trySearchCardView,true);
            case VIEW_TYPE_YUN:
                View yunView = inflater.inflate(R.layout.sf_field_search_item_yun, parent, false);
                return new YunHolder(activity, yunView);
            case VIEW_TYPE_YUN_CARD:
                View yunCardView = inflater.inflate(R.layout.sf_field_search_item_yun_card, parent, false);
                return new YunCardHolder(yunCardView);
            case VIEW_TYPE_OTHER_RELATED_RESULT_TITLE:
                View relatedView = inflater.inflate(R.layout.sf_field_search_item_related_card, parent, false);
                return new RelatedHolder(relatedView);
            default:
                break;
        }
        return null;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        SkuInfoBean wareInfo = productInfoList.get(position);
        if (wareInfo == null) {
            return;
        }
        screenWidth = ScreenUtils.getScreenWidth(activity);
        if (holder instanceof CouponTipHolder) {
            CouponTipHolder couponTipHolder = (CouponTipHolder) holder;
            if (!StringUtil.isNullByString(wareInfo.getCustomString())) {
                couponTipHolder.tvCouponTip.setText(wareInfo.getCustomString());
            }
        } else if (holder instanceof PromotionTipHolder) {
            bindPromotionTipData((PromotionTipHolder) holder, this.promotionInfo);
        } else if (holder instanceof CouponPrepareHolder) {
            CouponPrepareHolder couponPrepareHolder = (CouponPrepareHolder) holder;
            if (this.couponInfo != null) {
                couponPrepareHolder.tvCouponPrepare.setText(this.couponInfo.getRuleDescSimple());
                couponPrepareHolder.tvCouponBeginTime.setText(activity.getString(R.string.sf_field_search_coupon_valid_time, this.couponInfo.getValidateTime()));
            }
        } else if (holder instanceof FooterHolder) {
            ((FooterHolder) holder).bindData(isShowCard());
        } else if (holder instanceof NoDataTrySearchHolder) {
            if (trySearchWords != null && !trySearchWords.isEmpty()) {
                ((NoDataTrySearchHolder) holder).bindData(trySearchWords);
            }
        } else if (holder instanceof ProductListViewHolder) {
            ProductListViewHolder productListViewHolder = (ProductListViewHolder) holder;
            productListViewHolder.setCardHeightAndLeftMargin(135, 10, 14, 4, 105);
            productListViewHolder.bindData(activity, wareInfo, new ProductCardInterfaces() {

                @Override
                public void bindDataBottomExtraView(View bottomExtraView, View bottomExtraContainer) {
                    if (bottomExtraView == null) {
                        return;
                    }
                    List<SkuInfoBean> skuInfoBeans = wareInfo.getDapeigouList();
                    if (skuInfoBeans == null || skuInfoBeans.isEmpty()) {
                        bottomExtraView.setVisibility(View.GONE);
                        bottomExtraContainer.setVisibility(View.GONE);
                        return;
                    }
                    if (bottomExtraView != null && bottomExtraView instanceof DaPeiGouListView) {
                        ((DaPeiGouListView) bottomExtraView).bindData(skuInfoBeans, wareInfo, searchResultDataManager);
                        bottomExtraView.setVisibility(View.VISIBLE);
                        bottomExtraContainer.setVisibility(View.VISIBLE);
                        productListViewHolder.setDividerVisibility(View.GONE);//是否隐藏分割线
                    }
                }

                @Override
                public int setCardAbilityType() {
                    if (FloorInit.getFloorConfig().getProductShowFindSimilar()) {
                        return 0B11011;
                    } else {
                        // 不展示找相似按钮、找相似蒙层
                        return 0B11;
                    }
                }

                @Override
                public void onCardClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean == null) {
                        return;
                    }

                    if (NoDoubleClickUtils.isDoubleClick()) {
                        return;
                    }
                    FloorJumpManager.getInstance().jumpProductDetail(activity, skuInfoVoBean, true, 0);
                    if (skuInfoVoBean.isRecommend()) {
                        searchResultDataManager.searchResultReporter.recommendSkuClick(skuInfoVoBean, position);
                    } else {
                        searchResultDataManager.searchResultListExposureHelper.clickReport(skuInfoVoBean, position);
                    }
                }

                @Override
                public void onAddCartClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean == null) {
                        return;
                    }
                    if (skuInfoVoBean.isRecommend()) {
                        searchResultDataManager.searchResultReporter.recommendSkuAddCart(skuInfoVoBean, position);
                    } else {
                        searchResultDataManager.searchResultListExposureHelper.addCartReport(skuInfoVoBean, position);
                    }
                    PreferenceUtil.saveStringMax100("carSkus", skuInfoVoBean.getSkuId());
                }

                @Override
                public void onAddCartFinalClick(SkuInfoBean skuInfoBean) {
                    super.onAddCartFinalClick(skuInfoBean);
                    // 根据开关和标识,发起搭配购请求
                    if (!shouldShowDaPeiGou(skuInfoBean.getSkuId())) {
                        return;
                    }
                    SearchDapeigouManager.getInstance().postDapeigouData(activity, wareInfo.getSkuId(), new BaseFreshResultCallback<String, ResponseData<DapeigouResult>>() {
                        @Override
                        public ResponseData<DapeigouResult> onData(String data, FreshHttpSetting httpSetting) {
                            ResponseData<DapeigouResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<DapeigouResult>>() {
                            }.getType());
                            return responseData;
                        }

                        @Override
                        public void onEnd(ResponseData<DapeigouResult> object, FreshHttpSetting httpSetting) {
                            SkuInfoBean item = getItem(position);
                            if (item == null || wareInfo == null || !TextUtils.equals(wareInfo.getSkuId(), item.getSkuId())) {
                                // 说明数据内容发生了变化,直接丢弃
                                return;
                            }
                            // 搭配购商品大于等于4个才展示
                            if (object != null && object.getData() != null && object.getData().getProductCardVoList() != null && 4 <= object.getData().getProductCardVoList().size()) {
                                if (productInfoList == null) {
                                    return;
                                }
                                if (position < 0 || productInfoList.size() <= position) {
                                    return;
                                }
                                item.setDapeigouList(object.getData().getProductCardVoList());
                                int itemCount = productInfoList.size() - position;
                                notifyItemRangeChanged(position, itemCount);
                                dapeigouIgnoreCount.incrementAndGet();
                            }
                        }

                        @Override
                        public void onError(FreshHttpException error) {

                        }
                    });
                }

                @Override
                public void onRankClick(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (marketEntrance != null) {
                        FloorJumpManager.getInstance().startH5(activity, marketEntrance.getToUrl(), false);
                    }

                    if (skuInfoVoBean != null && marketEntrance != null) {
                        searchResultDataManager.searchResultReporter.clickRank(skuInfoVoBean, marketEntrance, fromType);
                    }
                }

                @Override
                public void onRankExposure(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    super.onRankExposure(skuInfoVoBean, marketEntrance);
                    searchResultDataManager.searchResultReporter.exposureRank(skuInfoVoBean, marketEntrance, fromType);
                }

                @Override
                public void findSimilarClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean != null) {
                        FloorJumpManager.getInstance().jumpSimilarList(activity, skuInfoVoBean.getSkuId(), "2");

                        searchResultDataManager.searchResultReporter.clickFindSimilar(skuInfoVoBean.getSkuId(), skuInfoVoBean.getSkuName(), showCard ? 1 : 2);
                    }
                }

                @Override
                public void findSimilarCloseClick(SkuInfoBean skuInfoVoBean) {
                    if (skuInfoVoBean != null) {
                        searchResultDataManager.searchResultReporter.clickFindSimilarClose(skuInfoVoBean.getSkuId(), skuInfoVoBean.getSkuName(), showCard ? 1 : 2);
                    }
                }

                @Override
                public void bookNowClick(SkuInfoBean skuInfoVoBean) {
                    FloorJumpManager.getInstance().preSaleJustNow(activity, skuInfoVoBean);

                    searchResultDataManager.searchResultReporter.bookNow(skuInfoVoBean, 1);
                }

                @Override
                public void JKClick(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (skuInfoVoBean != null && marketEntrance != null) {
                        Bundle bundle = new Bundle();
                        bundle.putInt(FloorJumpManager.URL_TYPE, marketEntrance.getUrlType());
                        bundle.putString(FloorJumpManager.TO_URL, marketEntrance.getToUrl());
                        FloorJumpManager.getInstance().jumpAction(activity, bundle);
                        searchResultDataManager.searchResultReporter.clickJk(skuInfoVoBean, marketEntrance);
                    }

                }

                @Override
                public void JKExpose(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (skuInfoVoBean != null) {
                        searchResultDataManager.searchResultReporter.showJk(skuInfoVoBean, marketEntrance);
                    }
                }
            });
            //如果下一个是试试搜 单行，隐藏divider
            if(position<getItemCount()-1){
                SkuInfoBean wareInfoNext = productInfoList.get(position+1);
                if(wareInfoNext.getViewType()==VIEW_TYPE_TRY_SEARCH){
                    productListViewHolder.setDividerVisibility(View.GONE);//是否隐藏分割线

                }
            }
        } else if (holder instanceof ProductCardViewHolder) {
            ((ProductCardViewHolder) holder).bindData(activity, wareInfo, new ProductCardInterfaces() {

                @Override
                public void bindDataBottomExtraView(View bottomExtraView, View bottomExtraContainer) {
                    if (bottomExtraView == null) {
                        return;
                    }
                    List<SkuInfoBean> skuInfoBeans = wareInfo.getDapeigouList();
                    if (skuInfoBeans == null || skuInfoBeans.isEmpty()) {
                        bottomExtraView.setVisibility(View.GONE);
                        bottomExtraContainer.setVisibility(View.GONE);
                        return;
                    }
                    if (bottomExtraView != null && bottomExtraView instanceof DaPeiGouCardView) {
                        ((DaPeiGouCardView) bottomExtraView).bindData(activity, wareInfo, position, searchResultDataManager);
                        bottomExtraView.setVisibility(View.VISIBLE);
                        bottomExtraContainer.setVisibility(View.VISIBLE);
                    }
                }

                @Override
                public int setCardAbilityType() {
                    return 0B1011;
                }

                @Override
                public void onCardClick(SkuInfoBean skuInfoVoBean) {
                    if (NoDoubleClickUtils.isDoubleClick()) {
                        return;
                    }
                    FloorJumpManager.getInstance().jumpProductDetail(activity, wareInfo, true, 0);

                    searchResultDataManager.searchResultListExposureHelper.clickReport(wareInfo, position);
                }

                @Override
                public void onAddCartClick(SkuInfoBean skuInfoVoBean) {
                    searchResultDataManager.searchResultListExposureHelper.addCartReport(skuInfoVoBean, position);
                    PreferenceUtil.saveStringMax100("carSkus", skuInfoVoBean.getSkuId());
                }

                @Override
                public void onAddCartFinalClick(SkuInfoBean skuInfoBean) {
                    super.onAddCartFinalClick(skuInfoBean);
                    // 根据开关和标识,发起搭配购请求
                    if (!shouldShowDaPeiGou(skuInfoBean.getSkuId())) {
                        return;
                    }
                    SearchDapeigouManager.getInstance().postDapeigouData(activity, wareInfo.getSkuId(), new BaseFreshResultCallback<String, ResponseData<DapeigouResult>>() {
                        @Override
                        public ResponseData<DapeigouResult> onData(String data, FreshHttpSetting httpSetting) {
                            ResponseData<DapeigouResult> responseData = JDJSON.parseObject(data, new TypeReference<ResponseData<DapeigouResult>>() {
                            }.getType());
                            return responseData;
                        }

                        @Override
                        public void onEnd(ResponseData<DapeigouResult> object, FreshHttpSetting httpSetting) {
                            SkuInfoBean item = getItem(position);
                            if (item == null || wareInfo == null || !TextUtils.equals(wareInfo.getSkuId(), item.getSkuId())) {
                                // 说明数据内容发生了变化,直接丢弃
                                return;
                            }
                            // 搭配购商品大于等于4个才展示
                            if (object != null && object.getData() != null && object.getData().getProductCardVoList() != null && 4 <= object.getData().getProductCardVoList().size()) {
                                if (productInfoList == null) {
                                    return;
                                }
                                if (position < 0 || productInfoList.size() <= position) {
                                    return;
                                }
                                item.setDapeigouList(object.getData().getProductCardVoList());
                                int itemCount = productInfoList.size() - position;
                                notifyItemRangeChanged(position, itemCount);
                                dapeigouIgnoreCount.incrementAndGet();
                            }
                        }

                        @Override
                        public void onError(FreshHttpException error) {

                        }
                    });
                }

                @Override
                public void onRankClick(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (marketEntrance != null) {
                        FloorJumpManager.getInstance().startH5(activity, marketEntrance.getToUrl(), false);
                        searchResultDataManager.searchResultReporter.clickRank(skuInfoVoBean, marketEntrance, fromType);
                    }
                }

                @Override
                public void bookNowClick(SkuInfoBean skuInfoVoBean) {
                    FloorJumpManager.getInstance().preSaleJustNow(activity, skuInfoVoBean);

                    searchResultDataManager.searchResultReporter.bookNow(skuInfoVoBean, 2);
                }

                @Override
                public void findSimilarClick(SkuInfoBean skuInfoBean) {
                    if (skuInfoBean != null) {
                        FloorJumpManager.getInstance().jumpSimilarList(activity, skuInfoBean.getSkuId(), "2");

                        searchResultDataManager.searchResultReporter.clickFindSimilar(skuInfoBean.getSkuId(), skuInfoBean.getSkuName(), 1);
                    }
                }

                @Override
                public void JKClick(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (skuInfoVoBean != null && marketEntrance != null) {
                        Bundle bundle = new Bundle();
                        bundle.putInt(FloorJumpManager.URL_TYPE, marketEntrance.getUrlType());
                        bundle.putString(FloorJumpManager.TO_URL, marketEntrance.getToUrl());
                        FloorJumpManager.getInstance().jumpAction(activity, bundle);
                        searchResultDataManager.searchResultReporter.clickJk(skuInfoVoBean, marketEntrance);
                    }
                }

                @Override
                public void JKExpose(SkuInfoBean skuInfoVoBean, SkuMarketEntrance marketEntrance) {
                    if (skuInfoVoBean != null) {
                        searchResultDataManager.searchResultReporter.showJk(skuInfoVoBean, marketEntrance);
                    }
                }
            });
        } else if (holder instanceof SimilarRecommendViewHolder) {
        } else if (holder instanceof SimilarRecommendCardViewHolder) {
        } else if (holder instanceof WordSearchHolder) {
            bindWordSearchData((WordSearchHolder) holder);
        } else if (holder instanceof TrySearchHolder) {
            if (relatedWords != null && !relatedWords.isEmpty()) {
                ((TrySearchHolder) holder).bindData(relatedWords);
            }
        } else if (holder instanceof TrySearchCardHolder) {
            if (relatedWords != null && !relatedWords.isEmpty()) {
                ((TrySearchCardHolder) holder).bindData(relatedWords);
            }
        }  else if (holder instanceof TrySearchFlowHolder) {
            //试试搜flow holder
            if (relatedWordRecQueryList != null && !relatedWordRecQueryList.isEmpty()) {
                ((TrySearchFlowHolder) holder).bindData(relatedWordRecQueryList,activity);
            }
        }

        else if (holder instanceof YunHolder) {
            if (cloudStoreVo != null && cloudStoreVo.getProductCardVoList() != null && !cloudStoreVo.getProductCardVoList().isEmpty()) {
                ((YunHolder) holder).bindData(activity, cloudStoreVo, onItemClickListener, searchResultDataManager.searchResultReporter);
                ((YunHolder) holder).itemView.setOnClickListener(v -> {
                    if (cloudStoreVo.getProductCardVoList().size() <= 2) {
                        return;
                    }
                    if (onItemClickListener != null) {
                        onItemClickListener.onYunClick();
                    }
                });
            }
        } else if (holder instanceof YunCardHolder) {
            if (cloudStoreVo != null && cloudStoreVo.getProductCardVoList() != null && !cloudStoreVo.getProductCardVoList().isEmpty()) {
                ((YunCardHolder) holder).bindData(activity, cloudStoreVo, searchResultDataManager.searchResultReporter);
                ((YunCardHolder) holder).itemView.setOnClickListener(v -> {
                    if (cloudStoreVo.getProductCardVoList().size() <= 2) {
                        return;
                    }
                    if (onItemClickListener != null) {
                        onItemClickListener.onYunClick();
                    }
                });
            }
        } else if (holder instanceof NoDataTipsHolder) {
            NoDataTipsHolder noDataTipsHolder = (NoDataTipsHolder) holder;
            noDataTipsHolder.tvTips.setText(R.string.sf_field_search_no_data);
            noDataTipsHolder.tvFeedback.setVisibility(View.VISIBLE);
            noDataTipsHolder.tvFeedback.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.openFeedback();
                    }
                    searchResultDataManager.searchResultReporter.clickFeedBack(searchResultDataManager.searchKeyword);
                }
            });
            searchResultDataManager.searchResultReporter.showFeedBack(searchResultDataManager.searchKeyword);
        } else if (holder instanceof RecommendTitleHolder) {
            RecommendTitleHolder recommendTitleHolder = (RecommendTitleHolder) holder;
            if (showCard) {
                recommendTitleHolder.rlParent.setBackgroundColor(0);
            } else {
                recommendTitleHolder.rlParent.setBackgroundColor(ContextCompat.getColor(activity, R.color.sf_card_white));
            }
        } else if (holder instanceof RelatedHolder) {
            RelatedHolder relatedHolder = (RelatedHolder) holder;
            if (StringUtil.isNotEmpty(searchResultDataManager.searchKeyword)) {
                relatedHolder.llParent.setVisibility(View.VISIBLE);
                relatedHolder.tvKeyWord.setText(searchResultDataManager.searchKeyword);
                relatedHolder.tvKeyWord.setMaxWidth((int) (SPACE_80 * screenWidth));
                int paddingTop = ScreenUtils.dip2px(activity, 18);
                if (showCard) {
                    relatedHolder.llParent.setBackgroundColor(0);
                    relatedHolder.llParent.setPadding(0, paddingTop, 0, paddingTop / 2);
                } else {
                    relatedHolder.llParent.setBackgroundColor(ContextCompat.getColor(activity, R.color.sf_card_white));
                    relatedHolder.llParent.setPadding(0, paddingTop, 0, paddingTop);
                }
                searchResultDataManager.searchResultReporter.showRelated(searchResultDataManager.searchKeyword);
            } else {
                relatedHolder.llParent.setVisibility(View.GONE);
            }
        }
    }

    public synchronized void addDapeigouItem(SkuInfoBean addSkuInfoBean, int position) {
        if (productInfoList == null) {
            return;
        }
        if (position < 0 || productInfoList.size() <= position) {
            return;
        }

        try {
            position = position + 1;
            productInfoList.add(position, addSkuInfoBean);
            int itemCount = productInfoList.size() - position;
            notifyItemRangeChanged(position, itemCount);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return productInfoList == null ? 0 : productInfoList.size();
    }

    @Override
    public int getItemViewType(int position) {
        int viewType = 0;
        SkuInfoBean wareInfo = getItem(position);
        if (wareInfo != null) {
            viewType = wareInfo.getViewType();
            if (showCard) {
                //当前要求展示的是卡片 将单行样式改成卡片样式
                if (viewType == VIEW_TYPE_PRODUCT_LIST) {
                    viewType = VIEW_TYPE_PRODUCT_CARD;
                } else if (viewType == VIEW_TYPE_RECOMMEND_PRODUCT) {
                    viewType = VIEW_TYPE_RECOMMEND_PRODUCT_CARD;
                } else if (viewType == VIEW_TYPE_TRY_SEARCH) {
                    viewType = VIEW_TYPE_TRY_SEARCH_CARD;
                } else if (viewType == VIEW_TYPE_YUN) {
                    viewType = VIEW_TYPE_YUN_CARD;
                }
            } else {
                if (viewType == VIEW_TYPE_PRODUCT_CARD) {
                    viewType = VIEW_TYPE_PRODUCT_LIST;
                } else if (viewType == VIEW_TYPE_RECOMMEND_PRODUCT_CARD) {
                    viewType = VIEW_TYPE_RECOMMEND_PRODUCT;
                } else if (viewType == VIEW_TYPE_TRY_SEARCH_CARD) {
                    viewType = VIEW_TYPE_TRY_SEARCH;
                } else if (viewType == VIEW_TYPE_YUN_CARD) {
                    viewType = VIEW_TYPE_YUN;
                }
            }
        }
        return viewType;
    }

    @Override
    public void onViewRecycled(@NonNull RecyclerView.ViewHolder holder) {
        super.onViewRecycled(holder);
        if (holder instanceof ProductListViewHolder) {// 回收时就显示会divider,并隐藏底部额外信息
            ((ProductListViewHolder) holder).setDividerVisibility(View.VISIBLE);
            ((ProductListViewHolder) holder).hideBottomExtraContainer();
        } else if (holder instanceof ProductCardViewHolder) {// 回收时就隐藏底部额外信息
            ((ProductCardViewHolder) holder).hideBottomExtraContainer();
        }
    }

    public SkuInfoBean getItem(int position) {
        if (productInfoList == null) {
            return null;
        }
        if (position < 0 || productInfoList.size() <= position) {
            return null;
        }
        return productInfoList.get(position);
    }

    public List<SearchRelateWordRecQuery> getRelatedWordRecQueryList() {
        return relatedWordRecQueryList;
    }

    public List<String> getRelatedWords() {
        return relatedWords;
    }

    public List<String> getTrySearchWords() {
        return trySearchWords;
    }

    public List<SkuInfoBean> getCloudStoreSkus() {
        return cloudStoreVo != null ? cloudStoreVo.getProductCardVoList() : null;
    }

    /**
     * 绑定促销提示头数据
     *
     * @param promotionTipHolder
     * @param promotionInfo
     */
    public void bindPromotionTipData(PromotionTipHolder promotionTipHolder, PromotionTypeInfo promotionInfo) {
        if (promotionInfo == null) {
            return;
        }
        if (null != promotionInfo.getShowTexts() && promotionInfo.getShowTexts().size() > 0) {
            promotionTipHolder.llPromotionContainer.removeAllViews();
            if (promotionInfo.isAllRepurchase()) {
                promotionTipHolder.tvPromotionTips.setText(R.string.sf_field_search_all_repurchase_with_colon);
            } else {
                promotionTipHolder.tvPromotionTips.setText(R.string.sf_field_search_promotion_discount_with_colon);
            }
            for (int i = 0; i < promotionInfo.getShowTexts().size(); i++) {
                final PromotionTypeInfo.ShowTextsBean showTextsBean = promotionInfo.getShowTexts().get(i);
                if (showTextsBean == null) {
                    continue;
                }
                View item = LayoutInflater.from(activity).inflate(R.layout.sf_field_search_promotion_item, null);
                TextView tvPromoDesc = item.findViewById(R.id.tv_promotion_desc);
                TextView tvNum = item.findViewById(R.id.tv_num);
                ImageView ivArrow = item.findViewById(R.id.iv_arrow);
                tvPromoDesc.setText(showTextsBean.getShowMsg());
                if (showTextsBean.getNum() != 0) {
                    tvNum.setText("x" + showTextsBean.getNum());
                    tvNum.setVisibility(View.VISIBLE);
                } else {
                    tvNum.setVisibility(View.GONE);
                }
                if ("302".equals(promotionInfo.getPromotionSubType())) {
                    ivArrow.setVisibility(View.VISIBLE);
                } else {
                    ivArrow.setVisibility(View.GONE);
                }
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                if (i != 0) {
                    lp.topMargin = 20;
                }
                promotionTipHolder.llPromotionContainer.addView(item, lp);
                item.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!TextUtils.isEmpty(showTextsBean.getSkuId()) && null != activity) {
                            FloorJumpManager.getInstance().jumpProductDetail(activity, showTextsBean.getSkuId(), "", "");
                        }
                    }
                });
            }
        }
        if (null != promotionInfo && !TextUtils.isEmpty(promotionInfo.getEndTime())) {
            promotionTipHolder.tvPromotionTime.setText(String.format(activity.getString(R.string.sf_field_search_end), promotionInfo.getEndTime()));
        }
    }

    /**
     * 绑定扩招词或者纠错词数据
     *
     * @param wordSearchHolder
     */
    private void bindWordSearchData(WordSearchHolder wordSearchHolder) {
        if (expandQuery != null && expandQuery.isExpandQueryStatus()) {
            String rightWord = expandQuery.getRightWord();
            String errorWord = expandQuery.getErrorWord();
            String searchStr = activity.getString(R.string.sf_field_search_word_expand_query_str, rightWord, errorWord);
            SpannableStringBuilder spannableString = new SpannableStringBuilder(searchStr);
            ForegroundColorSpan colorSpan = new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.sf_field_search_color_1d1f2b));
            ForegroundColorSpan colorSpan2 = new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.sf_field_search_color_1d1f2b));
            spannableString.setSpan(colorSpan, 5, 7 + rightWord.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new StyleSpan(android.graphics.Typeface.BOLD), 5, 7 + rightWord.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(colorSpan2, 16 + rightWord.length(), 18 + rightWord.length() + errorWord.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new StyleSpan(android.graphics.Typeface.BOLD), 16 + rightWord.length(), 18 + rightWord.length() + errorWord.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            wordSearchHolder.tvWordSearch.setText(spannableString);
            wordSearchHolder.tvWordSearch.setOnClickListener(v -> {
                searchResultDataManager.stillSearch();
                searchResultDataManager.searchResultReporter.clickStillSearch();
            });
        } else if (wordSearch != null && wordSearch.isHasWordSearch() && StringUtil.isNotEmpty(wordSearch.getOldWord()) && StringUtil.isNotEmpty(wordSearch.getNewWord())) {
            String oldWorld = wordSearch.getOldWord();
            String newWorld = wordSearch.getNewWord();
            String searchStr = activity.getString(R.string.sf_field_search_word_search_str, oldWorld, newWorld);
            SpannableStringBuilder spannableString = new SpannableStringBuilder(searchStr);
            ForegroundColorSpan colorSpan = new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.sf_field_search_color_1d1f2b));
            ForegroundColorSpan colorSpan2 = new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.sf_field_search_color_1d1f2b));
            spannableString.setSpan(colorSpan, 4, 6 + oldWorld.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new StyleSpan(android.graphics.Typeface.BOLD), 4, 6 + oldWorld.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(colorSpan2, 16 + oldWorld.length(), 18 + oldWorld.length() + newWorld.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new StyleSpan(android.graphics.Typeface.BOLD), 16 + oldWorld.length(), 18 + oldWorld.length() + newWorld.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            wordSearchHolder.tvWordSearch.setText(spannableString);
        }
    }

    @Override
    public void onViewAttachedToWindow(@NonNull RecyclerView.ViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        int itemViewType = holder.getItemViewType();
        //这里要进行设置，有的样式是在卡片模式下也是独占一行的
        int count = 2;
        switch (itemViewType) {
            case VIEW_TYPE_COUPON_TIP:
            case VIEW_TYPE_PROMOTION_TIP:
            case VIEW_TYPE_COUPON_PREPARE:
            case VIEW_TYPE_FOOTER:
            case VIEW_TYPE_PRODUCT_LIST:
            case VIEW_TYPE_NODATA:
            case VIEW_TYPE_NODATA_TRY_SEARCH:
            case VIEW_TYPE_RECOMMEND_TITLE:
            case VIEW_TYPE_RECOMMEND_PRODUCT:
            case VIEW_TYPE_WORD_SEARCH:
            case VIEW_TYPE_TRY_SEARCH:
            case VIEW_TYPE_ERROR_WORD:
            case VIEW_TYPE_YUN:
            case VIEW_TYPE_OTHER_RELATED_RESULT_TITLE:
                count = 1;
                break;
            case VIEW_TYPE_PRODUCT_CARD:
            case VIEW_TYPE_RECOMMEND_PRODUCT_CARD:
            case VIEW_TYPE_TRY_SEARCH_CARD:
            case VIEW_TYPE_YUN_CARD:
                count = 2;
                break;
            default:
        }
        if (count == 1) {
            setFullSpan(holder);
        }
    }

    protected void setFullSpan(RecyclerView.ViewHolder holder) {
        if (holder.itemView.getLayoutParams() instanceof StaggeredGridLayoutManager.LayoutParams) {
            StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) holder
                    .itemView.getLayoutParams();
            params.setFullSpan(true);
        }
    }

    class CouponTipHolder extends RecyclerView.ViewHolder {

        TextView tvCouponTip;

        public CouponTipHolder(@NonNull View itemView) {
            super(itemView);
            tvCouponTip = itemView.findViewById(R.id.tv_coupon_tip);
        }
    }

    class PromotionTipHolder extends RecyclerView.ViewHolder {

        TextView tvPromotionTips;
        LinearLayout llPromotionContainer;
        TextView tvPromotionTime;

        public PromotionTipHolder(@NonNull View itemView) {
            super(itemView);
            tvPromotionTips = itemView.findViewById(R.id.tv_promotion_tips);
            llPromotionContainer = itemView.findViewById(R.id.ll_promotion_container);
            tvPromotionTime = itemView.findViewById(R.id.tv_promotion_time);
        }
    }

    class NoDataTipsHolder extends RecyclerView.ViewHolder {

        TextView tvTips;
        TextView tvFeedback;

        public NoDataTipsHolder(@NonNull View itemView) {
            super(itemView);
            tvTips = itemView.findViewById(R.id.tv_nodata_tips);
            tvFeedback = itemView.findViewById(R.id.tv_nodata_feedback);
        }
    }

    class CouponPrepareHolder extends RecyclerView.ViewHolder {

        TextView tvCouponPrepare;
        TextView tvCouponBeginTime;

        public CouponPrepareHolder(@NonNull View itemView) {
            super(itemView);
            tvCouponPrepare = itemView.findViewById(R.id.tv_coupon_prepare);
            tvCouponBeginTime = itemView.findViewById(R.id.tv_coupon_begin_time);
        }
    }

    class FooterHolder extends RecyclerView.ViewHolder {

        View vDivider;
        TextView tvFooterNomore;
        ImageView ivFooterLogo;
        View root;

        public FooterHolder(@NonNull View itemView) {
            super(itemView);
            root = itemView.findViewById(R.id.root_footer);
            vDivider = itemView.findViewById(R.id.v_divider);
            tvFooterNomore = itemView.findViewById(R.id.tv_footer_nomore);
            ivFooterLogo = itemView.findViewById(R.id.iv_footer_logo);

        }
        public void bindData(boolean isCard) {
            int color = isCard?R.color.sf_field_search_transparent:R.color.sf_field_search_white;
            itemView.setBackgroundResource(color);
        }
    }

    class NoDataTrySearchHolder extends RecyclerView.ViewHolder {

        RecyclerView rvTrySearch;

        public NoDataTrySearchHolder(@NonNull View itemView) {
            super(itemView);
            rvTrySearch = itemView.findViewById(R.id.rv_try_search);
        }

        public void bindData(List<String> wordList) {
            rvTrySearch.setLayoutManager(new GridLayoutManager(activity, 3));
            TrySearchAdapter trySearchAdapter = new TrySearchAdapter(activity, wordList);
            rvTrySearch.setAdapter(trySearchAdapter);
            trySearchAdapter.setOnItemClickListener(position -> {
                searchResultDataManager.searchResultReporter.clickNoDataTrySearch(wordList.get(position));
                if (onItemClickListener != null) {
                    onItemClickListener.secondSearch(wordList.get(position));
                }
            });
        }
    }

    class WordSearchHolder extends RecyclerView.ViewHolder {

        TextView tvWordSearch;

        public WordSearchHolder(@NonNull View itemView) {
            super(itemView);
            tvWordSearch = itemView.findViewById(R.id.tv_word_search);
        }
    }

    class TrySearchHolder extends RecyclerView.ViewHolder {

        RecyclerView rvTrySearch;

        public TrySearchHolder(@NonNull View itemView) {
            super(itemView);
            rvTrySearch = itemView.findViewById(R.id.rv_try_search);
        }

        public void bindData(List<String> wordList) {
            rvTrySearch.setLayoutManager(new GridLayoutManager(activity, 3));
            TrySearchAdapter trySearchAdapter = new TrySearchAdapter(activity, wordList.size() > 6 ? wordList.subList(0, 6) : wordList);
            rvTrySearch.setAdapter(trySearchAdapter);
            trySearchAdapter.setOnItemClickListener(position -> {
                if (onItemClickListener != null) {
                    onItemClickListener.secondSearch(wordList.get(position));
                }
                searchResultDataManager.searchResultReporter.clickTrySearch(wordList.get(position));
            });
        }
    }
    /* 试试搜 带icon，flow排列，listItem样式  */
    class TrySearchFlowHolder extends RecyclerView.ViewHolder {
        private boolean isCard;

        FrameLayout container;
        FlowLayout flowTrySearch;
        View trSearchRoot;
        int flowHeight = 0;

        public TrySearchFlowHolder(@NonNull View itemView,boolean isCard) {
            super(itemView);
            this.isCard = isCard;
            flowTrySearch = itemView.findViewById(R.id.try_search_flow);
            //设置背景图片圆角
            container = itemView.findViewById(R.id.try_search_container);
            trSearchRoot = itemView.findViewById(R.id.try_search_root);
            flowTrySearch.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    if (flowHeight != flowTrySearch.getHeight()) {
                        flowHeight = flowTrySearch.getHeight();

                        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) container.getLayoutParams();
//                        Log.e("onGlobalLayout", "height=" + flowHeight);
                        FrameLayout.LayoutParams paramsFlow = (FrameLayout.LayoutParams) flowTrySearch.getLayoutParams();
                        int containHeight = flowHeight
                                + paramsFlow.topMargin + paramsFlow.bottomMargin
                                + container.getPaddingTop() + container.getPaddingBottom();
//                        Log.e("onGlobalLayout", "praent height=" + containHeight);
                        params.height = containHeight;
                        container.setLayoutParams(params);
                        container.setBackgroundResource(isCard ? R.drawable.sf_field_search_bg_try_search_vertical : R.drawable.sf_field_search_bg_try_search_horizontal);
                        container.setOutlineProvider(new ViewOutlineProvider() {
                            @Override
                            public void getOutline(View view, Outline outline) {
                                outline.setRoundRect(0, 0, container.getWidth(), containHeight, ScreenUtils.dip2px(view.getContext(), 12));
                            }
                        });
                        container.setClipToOutline(true);

//                        ViewGroup.LayoutParams paramsRoot =  try_search_root.getLayoutParams();
//                        paramsRoot.height = ViewGroup.LayoutParams.WRAP_CONTENT;
//                        try_search_root.setLayoutParams(paramsRoot);
                        trSearchRoot.requestLayout();

                        flowTrySearch.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                    }

                }
            });
        }

            public void bindData(List<SearchRelateWordRecQuery> list, Context context) {
            flowTrySearch.removeAllViews();
            flowTrySearch.setAdapter(new TrySearchFlowAdapter(context, list, new TrySearchFlowAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    String word  = list.get(position).getQuery();
                    if (onItemClickListener != null) {
                        onItemClickListener.secondSearch(word);
                    }
                    searchResultDataManager.searchResultReporter.clickTrySearch(word);
                }
            }));
        }
    }

    class TrySearchCardHolder extends RecyclerView.ViewHolder {

        RecyclerView rvTrySearchCard;

        public TrySearchCardHolder(@NonNull View itemView) {
            super(itemView);
            rvTrySearchCard = itemView.findViewById(R.id.rv_try_search_card);
        }

        public void bindData(List<String> wordList) {
            rvTrySearchCard.setLayoutManager(new GridLayoutManager(activity, 2));
            TrySearchAdapter trySearchAdapter = new TrySearchAdapter(activity, wordList.size() > 12 ? wordList.subList(0, 12) : wordList);
            rvTrySearchCard.setAdapter(trySearchAdapter);
            trySearchAdapter.setOnItemClickListener(position -> {
                if (onItemClickListener != null) {
                    onItemClickListener.secondSearch(wordList.get(position));
                }
                searchResultDataManager.searchResultReporter.clickTrySearch(wordList.get(position));
            });
        }
    }

    class RecommendTitleHolder extends RecyclerView.ViewHolder {

        RelativeLayout rlParent;

        public RecommendTitleHolder(@NonNull View itemView) {
            super(itemView);
            rlParent = itemView.findViewById(R.id.rl_recommend_title);
        }
    }

    class RelatedHolder extends RecyclerView.ViewHolder {

        LinearLayout llParent;
        TextView tvKeyWord;

        public RelatedHolder(@NonNull View itemView) {
            super(itemView);
            llParent = itemView.findViewById(R.id.tv_search_related_parent);
            tvKeyWord = itemView.findViewById(R.id.tv_search_related_keyword);
        }
    }

    public static class SimpleViewHolder extends RecyclerView.ViewHolder {

        public SimpleViewHolder(View itemView) {
            super(itemView);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position);

        void onYunClick();

        void secondSearch(String searchKeyword);

        void openFeedback();
    }

    /**
     * 判断是否应该展示搭配购(脑子没包是当不了产品经理的)
     */
    private synchronized boolean shouldShowDaPeiGou(String skuId) {
        // 移动配置的一票否决权!!
        if (!isMPaasShowDaPeiGou) {
            return false;
        }

        if (!FloorInit.getFloorConfig().isLogin()) {
            return false;
        }

        // 当前搜索页已经展示过这个sku的搭配购了,不再重复展示
        if (daPeiGouDisplayedSkuMaps.containsKey(skuId)) {
            return false;
        }

        // 搜索页搭配购开关配置的阈值,表示"不消费搭配购"时,最多推荐ignoreThreshold次搭配购,默认值0表示不展示搭配购
        int ignoreThreshold = PreferenceUtil.getInt("SearchMatchingShopping", 0);
        if (ignoreThreshold < 0) {
            ignoreThreshold = 0;
        }

        // 是否在推荐冷却期
        if (mLastSessionLockTime == 0) {
            // 不在推荐冷却期,正常往下走
        } else {
            // 在推荐冷却期
            long currentTime = System.currentTimeMillis();
            if (currentTime - mLastSessionLockTime > 3600 * 1000) {
                // 检查下是否过了推荐冷却期,如果过了,恢复为可推荐状态
                dapeigouIgnoreCount.set(0);
                mLastSessionLockTime = 0;
            } else {
                return false;// 仍在推荐冷却期，不展示
            }
        }

        // 如果配置为0,或者用户忽略搭配购次数小于阈值ignoreThreshold，才展示搭配购推荐
        if (0 < ignoreThreshold) {
            if (dapeigouIgnoreCount.get() < ignoreThreshold) {
                daPeiGouDisplayedSkuMaps.put(skuId, true);
                return true;
            } else {
                // 忽略次数大于阈值ignoreThreshold了,进入一小时的推荐冷却期,记录冷却期起始时间(只要lastSessionLockTime大于0,就代表在冷却期)
                mLastSessionLockTime = System.currentTimeMillis();
            }
        }
        return false;
    }

    public void setMPaasShowDaPeiGou(boolean isShowDaPeiGou) {
        isMPaasShowDaPeiGou = isShowDaPeiGou;
    }
}
