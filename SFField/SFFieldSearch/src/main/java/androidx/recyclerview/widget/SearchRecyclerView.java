package androidx.recyclerview.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


/**
 * 楼层列表容器
 */
public class SearchRecyclerView extends RecyclerView {

    /**
     * 伪造的系统功能回调
     */
    private FakeCallback fakeCallback;

    public SearchRecyclerView(@NonNull Context context) {
        super(context);
    }

    public SearchRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SearchRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 绕过recyclerView 自身机制
     * 获取到每个item定义的特殊id，并不影响原本的itemId机制
     *
     * @param fakeId
     * @return
     */
    public ViewHolder findViewHolderForFakeItemId(long fakeId) {
        if (mAdapter == null || !mAdapter.hasStableIds()) {
            return null;
        }
        final int childCount = mChildHelper.getUnfilteredChildCount();
        ViewHolder hidden = null;
        for (int i = 0; i < childCount; i++) {
            final ViewHolder holder = getChildViewHolderInt(mChildHelper.getUnfilteredChildAt(i));
            if (fakeCallback != null) {
                if (holder != null && !holder.isRemoved() && fakeCallback.getFakeItemId(holder) == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            } else {
                if (holder != null && !holder.isRemoved() && holder.getItemId() == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            }
        }
        return hidden;
    }

    /**
     * 伪造的系统功能回调
     */
    public interface FakeCallback {
        /**
         * 获取伪造的itemId
         * 由于holder类型不定，将此实现暴露出去实现
         * @param holder  对应的holder
         * @return 当前holder绑定数据对应的itemid
         */
        long getFakeItemId(ViewHolder holder);
    }

    /**
     * 设置回调
     * @param fakeCallback
     */
    public void setFakeCallback(FakeCallback fakeCallback) {
        this.fakeCallback = fakeCallback;
    }
}
