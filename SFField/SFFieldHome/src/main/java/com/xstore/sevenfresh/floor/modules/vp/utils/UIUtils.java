package com.xstore.sevenfresh.floor.modules.vp.utils;

import android.content.res.Resources;
import android.util.DisplayMetrics;

import com.boredream.bdcodehelper.utils.DisplayUtils;

public class UIUtils {
    private static final DisplayMetrics sMetrics = Resources.getSystem().getDisplayMetrics();

    public static int getScreenWidth() {
        return sMetrics != null ? sMetrics.widthPixels : 0;
    }

    public static int getScreenHeight() {
        return sMetrics != null ? sMetrics.heightPixels : 0;
    }

    /**
     * 转换宽度
     *
     * @param designWidth
     * @param designValue
     * @return
     */
    public static int getWidthByDesignValueFitFold(double designWidth, int designValue) {
        if(sMetrics.heightPixels * 1.0f / sMetrics.widthPixels < 1.334) {
            return dp2px( (float) designWidth);
        }
        return (int) ((float) (getWidth() * designWidth) / (float) designValue + 0.5F);
    }

    public static int getWidthByDesignValue(double designWidth, int designValue) {
        return (int) ((float) (getWidth() * designWidth) / (float) designValue + 0.5F);
    }

    public static int getWidth() {
        return sMetrics.widthPixels;
    }
    public static int px2dp(float pxValue) {
        final float scale = sMetrics != null ? sMetrics.density : 1;
        return (int) (pxValue / scale + 0.5f);
    }

    public static int dp2px(float dipValue) {
        final float scale = sMetrics != null ? sMetrics.density : 1;
        return (int) (dipValue * scale + 0.5f);
    }


    public static int px2sp(float pxValue) {
        final float fontScale = sMetrics != null ? sMetrics.scaledDensity : 1;
        return (int) (pxValue / fontScale + 0.5f);
    }

    public static int sp2px(float spValue) {
        final float fontScale = sMetrics != null ? sMetrics.scaledDensity : 1;
        return (int) (spValue * fontScale + 0.5f);
    }

}

