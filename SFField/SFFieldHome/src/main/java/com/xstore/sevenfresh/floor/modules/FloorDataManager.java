package com.xstore.sevenfresh.floor.modules;

import android.content.Context;
import android.util.Log;

import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorMemoryStorageManager;
import com.xstore.sdk.floor.floorcore.HomeRefreshManager;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.utils.TabGroupGoodDataManager;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.interfaces.FloorDataCallback;
import com.xstore.sevenfresh.floor.modules.interfaces.FloorTemplateInterface;
import com.xstore.sevenfresh.floor.modules.interfaces.VPTabDataCallBack;
import com.xstore.sevenfresh.floor.modules.model.RecommendFloorConst;
import com.xstore.sevenfresh.floor.modules.request.FloorNetwork;
import com.xstore.sevenfresh.floor.modules.request.FloorRequestCallback;
import com.xstore.sevenfresh.floor.modules.request.RecommendRequest;
import com.xstore.sevenfresh.floor.modules.vp.ViewPagerRecommendFloor;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.xstore.sevenfresh.addressstore.utils.TenantIdUtils.DEFAULT_STORE_ID;
import static com.xstore.sevenfresh.floor.modules.request.FloorNetwork.SDK_VERSION;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 楼层数据处理类
 */
public abstract class FloorDataManager implements FloorTemplateInterface, FloorRequestCallback {

    protected FloorDataCallback floorDataCallback;

    public static FloorDataManager manager;

    public static FloorDataManager getInstance() {
        return manager;
    }

    JDMaUtils.JdMaPageImp jdMaPageImp;

    public void setJdMaPageImp(JDMaUtils.JdMaPageImp jdMaPageImp) {
        this.jdMaPageImp = jdMaPageImp;
    }

    //首次获取的推荐tab数据
    List<FloorDetailBean> goodsFloor;

    public List<FloorDetailBean> getGoodsFloor() {
        return goodsFloor;
    }

    @Override
    public String getFloatingTemplateCode() {
        //因为浮窗可以为空 所以提供默认实现
        return null;
    }

    @Override
    public String getCeilingTemplateCode() {
        //因为浮窗可以为空 所以提供默认实现
        return null;
    }

    @Override
    public String getSecondFloorTemplateCode() {
        //因为浮窗可以为空 所以提供默认实现
        return null;
    }

    @Override
    public String getRollingFloorTemplateCode() {
        //轮播图也可以为空
        return null;
    }

    @Override
    public String getCarouselLiveFloorTemplateCode() {
        return null;
    }

    @Override
    public String getWaistedLiveFloorTemplateCode() {
        return null;
    }


    @Override
    public String getClubBubbleFloorTemplate() {
        return null;
    }

    @Override
    public String getConfigFloorTemplate() {
        return null;
    }

    @Override
    public String getHomeBrandFloorTemplate() {
        return null;
    }

    @Override
    public String getNewUserPriceFloorTemplate() {
        return null;
    }

    /**
     * 当前兜底分页的页数
     */
    public int curStoreListPage = 1;

    /**
     * 当前请求的时序  不是当前时序的刷新请求和加载更多需要丢弃
     */
    public int requestStep = 0;

    /**
     * 是否正在加载更多
     */
    protected boolean isLoadingMore = false;

    /**
     * 请求刷新数据
     */
    public void refreshData(Context context) {

    }

    // TODO: 2025/5/28 请求tab下数据
    public void refreshTabData(Context context, int tabKey, int nextPage, VPTabDataCallBack callback) {

        RecommendRequest.tabKey = tabKey;
        RecommendRequest.nextPage = nextPage;

        RecommendRequest.dynamicParam = RecommendRequest.dynamicParam.replace("queryIndexRecommendMultiTab", "indexRecommendPaginationQuery");

        RecommendRequest.requestTabGql(context, tabKey, nextPage, callback, RecommendRequest.lastRecommendFloorBean, jdMaPageImp, SDK_VERSION);

    }


    /**
     * 请求更多数据
     *
     * @param hasDataStore 目前要加载数据的门店id，这个目前只是用来区分了是否是兜底门店0，用来区分加载首页和门店列表了，所以暂时不用考虑围栏id
     */
    public void loadMore(Context context, String hasDataStore, JDMaUtils.JdMaPageImp jdMaPageImp) {
        if (isLoadingMore) {
            return;
        }
        isLoadingMore = true;
        if (DEFAULT_STORE_ID.equals(hasDataStore)) {
            //拖底分页
            FloorNetwork.requestMoreStoreList(context, new RecommendRequest.OnLoadMoreResult() {
                @Override
                public void onResult(int state, List<FloorDetailBean> floorDetailBeans) {
                    isLoadingMore = false;
                    floorDataCallback.setMoreData(state, floorDetailBeans);
                }
            });
            return;
        }
        RecommendRequest.requestGql(context, new RecommendRequest.OnLoadMoreResult() {
            @Override
            public void onResult(int state, List<FloorDetailBean> floorDetailBeans) {
                isLoadingMore = false;
                floorDataCallback.setMoreData(state, floorDetailBeans);
            }
        }, RecommendRequest.lastRecommendFloorBean, jdMaPageImp, SDK_VERSION);

    }


    public void useCacheEnter() {
    }


    @Override
    public void callbackFloors(String storeId, String fenceId, List<FloorDetailBean> omnitechPageComponentVoList, boolean isCache) {
        //清除首页记录的数据
        FloorMemoryStorageManager.clearValuesOfField("home");
        //重置沉浸式
        HomeRefreshManager.getInstance().setUseNewRefreshStyle(false);
        //下面的逻辑全部使用 FloorMemoryStorageManager 替代
//        //清空猜你喜欢搭配点击记录
//        MayLikeUtils.getSkuIds().clear();
//        //清空搭配购点击记录
//        CollocationUtils.getSkuIds().clear();
//        //清除插入记录
//        HomeRecommendGoodFloor.getInsertSku().clear();
        //直播间容器默认刷新楼层先隐藏,如果数据返回了就展示
        floorDataCallback.shouldShowLiveContainer(false);

        //楼层数据返回 开始对楼层数据进行加工  数据加工比较复杂 后续可以考虑做子线程处理，做视图预加载+缓存

        if (omnitechPageComponentVoList == null || omnitechPageComponentVoList.size() == 0) {
            //需要重置未默认的ui
            floorDataCallback.setFloors("导航", null, isCache);
            //需要清除二楼
            floorDataCallback.setFloors("二楼", null, isCache);
            //移除浮窗
            floorDataCallback.setFloors("浮窗", null, isCache);
            floorDataCallback.setData(omnitechPageComponentVoList, isCache, false);
            return;
        }

        //设置storeId 用来过滤数据
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean bean = omnitechPageComponentVoList.get(i);
            bean.setStoreId(storeId);
            bean.setFenceId(fenceId);
        }

        //是否存在订单数据
        FloorDetailBean orderBean = null;
        //是否存在公告数据
        FloorDetailBean noticeBean = null;
        //是否存在导航数据
        FloorDetailBean navBean = null;
        //是否存在浮窗
        boolean foundFloating = false;
        //是否存在二楼
        boolean foundSecondFloor = false;
        //是否存在吸顶
        int foundCeilingFloorIndex = -1;
        int foundCeilingFloorIndex2 = -1;
        //找到了轮播图
        boolean foundRolling = false;
        //是否存在弹窗楼层
        boolean popwindowFloor = false;
        //是否有7club气泡
        boolean foundClubBubble = false;
        //是否有配置楼层
        boolean foundConfigFloor = false;
//        //是否需要回收沉浸式直播间，默认是回收的
//        boolean isNeedClearLive = true;
//        //是否需要回收腰部直播间，默认是回收的
//        boolean isNeedSmallClearLive = true;
//        //只渲染第一个返回的沉浸式直播间
//        boolean isFirstLive = true;


        for (int i = omnitechPageComponentVoList.size() - 1; i >= 0; i--) {
            FloorDetailBean bean = omnitechPageComponentVoList.get(i);

            //先过滤掉没有楼层id的空楼层数据
            if (bean.getTemplateCode() == null) {
                omnitechPageComponentVoList.remove(i);
                continue;
            }
            //过滤不合法数据 由于目前不分页 所以全部动态楼层都要过滤
            Object dataObject = FloorManagerUtils.getInstance().filterFloor(bean, isCache);
            if (dataObject == null) {
                // 特殊楼层无需过滤
                if (!getWhiteListFloor().contains(bean.getTemplateCode())) {
                    omnitechPageComponentVoList.remove(i);
                    continue;
                }
            }
            bean.setComponentDataObject(dataObject);

//            //沉浸式直播楼层逻辑
//            if (getCarouselLiveFloorTemplateCode() != null && bean.getTemplateCode() != null &&
//                    (bean.getTemplateCode().equals(getCarouselLiveFloorTemplateCode()))) {
//                CarouseLiveBean liveBean = JDJSON.parseObject(bean.getComponentData(), CarouseLiveBean.class);
//                if (liveBean != null
//                        && liveBean.getQueryLiveInfo() != null
//                        && liveBean.getQueryLiveInfo().getLiveRoomInfo() != null
//                        && SinglePlayView.staticLiveID == liveBean.getQueryLiveInfo().getLiveRoomInfo().getLiveId()) {
//                    //如果ID一致就不进行回收
//                    isNeedClearLive = false;
//                } else {
//                    //回收沉浸式直播间
//                    SinglePlayView.clearLiveView();
//                    //已经回收过了，也设置成false
//                    isNeedClearLive = false;
//                }
//                if (isFirstLive) {
//                    floorDataCallback.setFloors("沉浸式直播", bean, isCache);
//                }
//                isFirstLive = false;
//                floorDataCallback.shouldShowLiveContainer(true);
//            }
//
//            //腰部直播楼层逻辑
//            if (getWaistedLiveFloorTemplateCode() != null && bean.getTemplateCode() != null &&
//                    (bean.getTemplateCode().equals(getWaistedLiveFloorTemplateCode()))) {
//                CarouseLiveBean liveBean = JDJSON.parseObject(bean.getComponentData(), CarouseLiveBean.class);
//                if (liveBean != null
//                        && liveBean.getQueryLiveInfo() != null
//                        && liveBean.getQueryLiveInfo().getLiveRoomInfo() != null
//                        && SingleSmallPlayView.staticSmallLiveID == liveBean.getQueryLiveInfo().getLiveRoomInfo().getLiveId()) {
//                    //如果ID一致就不进行回收
//                    isNeedSmallClearLive = false;
//                } else {
//                    //回收沉浸式直播间
//                    SingleSmallPlayView.clearLiveView();
//                    //已经回收过了，也设置成false
//                    isNeedSmallClearLive = false;
//                }
//            }


            if (getRollingFloorTemplateCode() != null && bean.getTemplateCode() != null &&
                    (bean.getTemplateCode().equals(getRollingFloorTemplateCode()) || bean.getTemplateCode().equals(getStaticRollingFloorTemplateCode()))) {
                //找到了轮播，那过滤完数据后要看下是否满足沉浸式条件
                foundRolling = true;
            }

            //订单进度数据
            if (getOrderProgressTemplateCode() != null && bean.getTemplateCode() != null && bean.getTemplateCode().equals(getOrderProgressTemplateCode())) {
                orderBean = omnitechPageComponentVoList.remove(i);
            }

            //公告楼层数据
            if (getNotifyTemplateCode() != null && bean.getTemplateCode() != null && bean.getTemplateCode().equals(getNotifyTemplateCode())) {
                noticeBean = omnitechPageComponentVoList.get(i);
                try {
                    if (noticeBean != null && noticeBean.getComponentData() != null && (!noticeBean.getComponentData().contains("\"isHomeFullScreen\":") || noticeBean.getComponentData().contains("\"isHomeFullScreen\":0"))) {
                        noticeBean = omnitechPageComponentVoList.remove(i);
                    } else if (noticeBean != null && noticeBean.getComponentData() != null && noticeBean.getComponentData().contains("\"isHomeFullScreen\":1")) {
                        noticeBean = null;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }

            //如果当前支持导航楼层 并且是顶部导航数据 将其从楼层中移除 特殊设置
            if (getNavigationTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getNavigationTemplateCode())) {
                navBean = omnitechPageComponentVoList.remove(i);
                //获取Nav下发颜色
                try {
                    JDJSONObject jsonObject = JDJSONObject.parseObject(navBean.getComponentData());
                    VpTabHelper.navIconStyle = jsonObject.getInteger("navIconStyle");
                    continue;
                } catch (Exception e) {
                    VpTabHelper.navIconStyle = 0;
                    JdCrashReport.postCaughtException(e);
                }
                continue;
            }
            //如果是悬浮窗 将其从楼层中移除 特殊设置
            if (getFloatingTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getFloatingTemplateCode())) {
                omnitechPageComponentVoList.remove(i);
                foundFloating = true;
                floorDataCallback.setFloors("浮窗", bean, isCache);
                continue;
            }
            //如果是二楼
            if (getSecondFloorTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getSecondFloorTemplateCode())) {
                omnitechPageComponentVoList.remove(i);
                foundSecondFloor = true;
                floorDataCallback.setFloors("二楼", bean, isCache);
                continue;
            }
            // 配置楼层
            if (getConfigFloorTemplate() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getConfigFloorTemplate())) {
                omnitechPageComponentVoList.remove(i);
                foundConfigFloor = true;
                floorDataCallback.setFloors("配置楼层", bean, isCache);
                continue;
            }
            //如果是弹窗楼层
            if (getHomePopWindowTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getHomePopWindowTemplateCode())) {
                omnitechPageComponentVoList.remove(i);
                popwindowFloor = true;
                if (!isCache) {
                    floorDataCallback.setFloors("首页弹窗聚合", bean, isCache);
                }
                continue;
            }

            if (getClubBubbleFloorTemplate() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getClubBubbleFloorTemplate())) {
                omnitechPageComponentVoList.remove(i);
                floorDataCallback.setFloors("7clubBubble", bean, isCache);
                foundClubBubble = true;
                continue;
            }

            // m选n楼层，加入 isCache,如果非缓存数据,楼层bind数据后，开始自动滚动
            if (getNewUserPriceFloorTemplate() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getNewUserPriceFloorTemplate())) {
                bean.localParams.put(getNewUserPriceFloorTemplate(),isCache?"0":"1");
            }

        }


//        if(isNeedClearLive){
//            //如果轮播结束了都没有该楼层，也需要回收
//            SinglePlayView.clearLiveView();
//        }
//
//        if(isNeedSmallClearLive){
//            //如果结束了都没有该楼层，也需要回收
//            SingleSmallPlayView.clearLiveView();
//        }

        //数据都过滤完了 并且当前支持轮播组件   这个地方要在导航前面设置，因为导航内要取相关沉浸式状态
        // 逻辑直接写这里了  日后抽离吧
        if (foundRolling) {
            //再次循环找到轮播
//            FloorManagerUtils.getInstance().handleRollingFloorData(omnitechPageComponentVoList);
            handleRollingFloorData(omnitechPageComponentVoList);
            if (!HomeRefreshManager.getInstance().useNewRefreshStyle() && navBean != null) {
                //没有轮播的沉浸式样式，但有导航栏，那就看他支不支持
                handleNavigationFloorData(omnitechPageComponentVoList, navBean);
            }
        } else if (navBean != null) {
            //如果没有轮播  有导航且没有直播  那去看看能否支持沉浸式  更新 不用判断直播了，本期只支持业务这么配置楼层   导航栏  + 搜索 + 功能聚合    或者    导航栏 + 功能聚合  + 搜素
            handleNavigationFloorData(omnitechPageComponentVoList, navBean);
        } else {
            //没有就没有沉浸式
            HomeRefreshManager.getInstance().setUseNewRefreshStyle(false);
        }

        //循环结束 判断是否存在未返回的特殊楼层数据，有过有要设置一下隐藏

        if (!foundSecondFloor) {
            //需要清除二楼
            floorDataCallback.setFloors("二楼", null, isCache);
        }
        if (!foundFloating) {
            //移除浮窗
            floorDataCallback.setFloors("浮窗", null, isCache);
        }
        if (!popwindowFloor) {
            //清除弹窗楼层动作
            floorDataCallback.setFloors("首页弹窗", null, isCache);
        }
        if (!foundClubBubble) {
            floorDataCallback.setFloors("7clubBubble", null, isCache);
        }

        if (!foundConfigFloor) {
            //没有找到配置楼层 那清空他
            floorDataCallback.setFloors("配置楼层", null, isCache);
        }


        // 插入组装好的栏目数据

        // 查找栏目组件第一个的位置
        handleGridData(omnitechPageComponentVoList);
        //恢复默认值
        VpTabHelper.hasSearchFloor = false;
        //保证序号
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean bean = omnitechPageComponentVoList.get(i);
            bean.setRealIndex(i);
            //判断是否有search楼层
            if (bean.getTemplateCode().equals("home_page_search_1")) {
                VpTabHelper.hasSearchFloor = true;
            }
        }

        //吸顶的处理特殊一点 如果是首页刷新，那么之前存在吸顶状态就消失了， 如果刷新后没有吸顶，要将吸顶从容器中移除，去掉吸顶逻辑
        //吸顶逻辑移动到排序之后，避免位置不正确 ！！！！

        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean bean = omnitechPageComponentVoList.get(i);
            if (getCeilingTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getCeilingTemplateCode())) {
                foundCeilingFloorIndex = i;
                //上面已经设置过真实位置了 不需要再次设置
//                bean.setRealIndex(i);
                floorDataCallback.setFloors("吸顶", bean, isCache);
                break;
            }
        }

        boolean hasTabGroupGoodData = handleTabGoodData(omnitechPageComponentVoList);
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean bean = omnitechPageComponentVoList.get(i);
            if (getTabGoodGroupFloorTemplateCode() != null && bean.getTemplateCode() != null &&
                    bean.getTemplateCode().equals(getTabGoodGroupFloorTemplateCode())) {
                foundCeilingFloorIndex2 = i;
                //上面已经设置过真实位置了 不需要再次设置
//                bean.setRealIndex(i);
                floorDataCallback.setFloors("吸顶2", bean, isCache);
                break;
            }
        }

        if (foundCeilingFloorIndex == -1) {
            floorDataCallback.setFloors("吸顶", null, isCache);
        }
        if (foundCeilingFloorIndex2 == -1) {
            floorDataCallback.setFloors("吸顶2", null, isCache);
        }

        //拿到推荐楼层的数据
        FloorDetailBean recommendDetailBean = getViewPagerRecommendRefreshFloorDetailBean(omnitechPageComponentVoList);
        //还原为true
        VpTabHelper.hasVpTab = true;
        RecommendRequest.tabKey = -1;
        boolean hasRecommendData = false;
        RecommendRequest.tabKey = -1;
        if (recommendDetailBean == null) {
            VpTabHelper.hasVpTab = false;
        } else {

            JDJSONObject jdjsonObject = JDJSONObject.parseObject(recommendDetailBean.getComponentData());
            //当queryIndexRecommendMultiTab为空，或者skuInfoLists为空且 queryIndexRecommend 有值skuInfos有值
            //前端降级走原来的逻辑  若服务端异常未返回indexMultiTabsList 静态资源数据，整个TAB推荐都不展示

            if (jdjsonObject.optJSONObject("queryIndexRecommendMultiTab") == null || jdjsonObject.optJSONObject("queryIndexRecommendMultiTab").optJSONArray("skuInfoLists") == null) {
                if (jdjsonObject.optJSONObject("queryIndexRecommend") != null && jdjsonObject.optJSONObject("queryIndexRecommend").optJSONArray("skuInfos") != null) {
                    VpTabHelper.hasVpTab = false;
                }
            } else if (jdjsonObject.optJSONArray("indexMultiTabsList") == null) {
                VpTabHelper.hasVpTab = false;
            } else {
                //获取tabs和商品列表
                goodsFloor = getGoodsFloorAndSetTabs(recommendDetailBean);

                VpTabHelper.isRefreshAll = true; //需要全部刷新推荐楼层

                if (goodsFloor.size() > 0) {
                    hasRecommendData = true;
                }

                if (tabBeans.size() <= 1) {
                    VpTabHelper.hasVpTab = false;
                }
            }


        }

        if (VpTabHelper.hasVpTab) { //有推荐楼层
            //TAB上商品数量小于3，SGM报警  这是第一个tab上的
            if (goodsFloor.size() < 3) {
                //SGM告警
                handleSGM(tabBeans.get(0));
            }

//        //处理推荐轮播数据
            FloorDetailBean recommendRollingFloorBean = handleRecommendRollingData(hasRecommendData, omnitechPageComponentVoList);
//
//        // 处理会员数据
            FloorDetailBean memberFloorBean = handleMemberData(hasRecommendData, omnitechPageComponentVoList);
//
//        // 处理榜单数据
            FloorDetailBean bdFloorBean = handleBdData(hasRecommendData, omnitechPageComponentVoList);


            //前段穿插会员后保证推荐的序号
            int finalRecommendIndex = -1;
            for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
                FloorDetailBean bean = omnitechPageComponentVoList.get(i);
                //先让推荐的index设置成功
                if (bean.isLocalRecommend() && finalRecommendIndex < 0) {
                    finalRecommendIndex = i;
                }
                if (bean.isLocalRecommend()) {
                    bean.setRealIndex(finalRecommendIndex);
                }
            }
            //realIndex是最后添加的楼层的position
            if (VpTabHelper.hasVpTab) {
                finalRecommendIndex = omnitechPageComponentVoList.size();
            }

            if (finalRecommendIndex >= 0 && memberFloorBean != null) {
                memberFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (finalRecommendIndex >= 0 && bdFloorBean != null) {
                bdFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (finalRecommendIndex > 0 && recommendRollingFloorBean != null) {
                recommendRollingFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (RecommendRequest.lastRecommendFloorBean != null) {
                RecommendRequest.lastRecommendFloorBean.setRealIndex(finalRecommendIndex);
            }

            //订单缓存数据不放到导航栏楼层
            if (navBean != null && orderBean != null && !isCache) {
                navBean.localParams.put(getOrderProgressTemplateCode(), orderBean);
            }

            if (navBean != null && noticeBean != null) {
                navBean.localParams.put(getNotifyTemplateCode(), noticeBean);
            }
        } else {//没有推荐楼层


            // 处理底部推荐数据
            hasRecommendData = handleRecommendData(omnitechPageComponentVoList, storeId, fenceId);


//        //处理推荐轮播数据
            FloorDetailBean recommendRollingFloorBean = handleRecommendRollingData(hasRecommendData, omnitechPageComponentVoList);
//
//        // 处理会员数据
            FloorDetailBean memberFloorBean = handleMemberData(hasRecommendData, omnitechPageComponentVoList);
//
//        // 处理榜单数据
            FloorDetailBean bdFloorBean = handleBdData(hasRecommendData, omnitechPageComponentVoList);


            //前段穿插会员后保证推荐的序号
            int finalRecommendIndex = -1;
            for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
                FloorDetailBean bean = omnitechPageComponentVoList.get(i);
                //先让推荐的index设置成功
                if (bean.isLocalRecommend() && finalRecommendIndex < 0) {
                    finalRecommendIndex = i;
                }
                if (bean.isLocalRecommend()) {
                    bean.setRealIndex(finalRecommendIndex);
                }
            }
            if (finalRecommendIndex >= 0 && memberFloorBean != null) {
                memberFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (finalRecommendIndex >= 0 && bdFloorBean != null) {
                bdFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (finalRecommendIndex > 0 && recommendRollingFloorBean != null) {
                recommendRollingFloorBean.setRealIndex(finalRecommendIndex);
            }

            if (RecommendRequest.lastRecommendFloorBean != null) {
                RecommendRequest.lastRecommendFloorBean.setRealIndex(finalRecommendIndex);
            }

            //订单缓存数据不放到导航栏楼层
            if (navBean != null && orderBean != null && !isCache) {
                navBean.localParams.put(getOrderProgressTemplateCode(), orderBean);
            }

            if (navBean != null && noticeBean != null) {
                navBean.localParams.put(getNotifyTemplateCode(), noticeBean);
            }
        }


        //需要重置未默认的ui  导航需要放在最后 因为他需要前面获取的沉浸式
        floorDataCallback.setFloors("导航", navBean, isCache);


        if (VpTabHelper.hasVpTab) {
            //最后插入推荐楼层
            FloorDetailBean vprDetail = new FloorDetailBean();
            vprDetail.setVpRecommendFloorList(goodsFloor);
            vprDetail.setTemplateCode("home_page_view_pager_recommend_1");
            omnitechPageComponentVoList.add(vprDetail);

            boolean enableLoadMore = DEFAULT_STORE_ID.equals(storeId) || hasTabGroupGoodData;
            floorDataCallback.setData(omnitechPageComponentVoList, isCache, enableLoadMore);
            return;
        }

        //  全量数据刷新
        //  如果是兜底页还是可以继续加载数据
        boolean enableLoadMore = DEFAULT_STORE_ID.equals(storeId) || hasRecommendData || hasTabGroupGoodData;
        floorDataCallback.setData(omnitechPageComponentVoList, isCache, enableLoadMore);
    }

    public void handleSGM(VPTabBean vpTabBean) {
        if (vpTabBean == null) {
            return;
        }

        try {
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9502;//固定值,也是默认值,SGM预先创建好的类型,表示自定义的业务异常
            errorLog.errorCode = "首页_推荐流TAB商品数量小于3_TAB名称:" + vpTabBean.getTitle() + "_TAB位置:" + vpTabBean.getPosition() + "_门店名称:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + "_门店ID:" + TenantIdUtils.getStoreId() + "_围栏ID" + TenantIdUtils.getFenceId();
            errorLog.ext1 = "";
            errorLog.location = "首页";
            SFLogCollector.reportBusinessErrorLog(errorLog);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }

    }

    /**
     * 会员数据插入到推荐数据第二个
     *
     * @param hasRecommendData
     * @param omnitechPageComponentVoList
     */
    private FloorDetailBean handleMemberData(boolean hasRecommendData, List<FloorDetailBean> omnitechPageComponentVoList) {

        FloorDetailBean memberBean = null;
        int firstRecommendIndex = -1;
        int memberIndex = -1;
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean floorDetailBean = omnitechPageComponentVoList.get(i);
            if (StringUtil.safeEqualsAndNotNull(floorDetailBean.getTemplateCode(), getMemberFloorTemplate())) {
                memberIndex = i;
                memberBean = floorDetailBean;
                continue;
            }

            if (floorDetailBean.isLocalRecommend() && firstRecommendIndex == -1) {
                firstRecommendIndex = i;
            }
        }

        if (!FloorInit.getFloorConfig().isLogin()) {
            //如果当前没有登录 则移除会员楼层
            omnitechPageComponentVoList.remove(memberBean);
            return memberBean;
        }
        omnitechPageComponentVoList.remove(memberBean);

        //有推荐位firstRecommendIndex一直为-1
        if (VpTabHelper.hasVpTab) {
            //有推荐位、会员在推荐楼层的第二位
            if (memberBean != null) {
                memberBean.setRealIndex(omnitechPageComponentVoList.size() + 1);
                goodsFloor.add(1, memberBean);
            }
        } else if (memberBean != null && firstRecommendIndex != -1 && hasRecommendData) {
            int insertIndex = memberIndex > firstRecommendIndex ? firstRecommendIndex + 1 : firstRecommendIndex;
            memberBean.setRealIndex(firstRecommendIndex);
            omnitechPageComponentVoList.add(insertIndex, memberBean);
        }


        return memberBean;
    }

    /**
     * 榜单数据插入到推荐数据第二个
     *
     * @param hasRecommendData
     * @param omnitechPageComponentVoList
     */
    private FloorDetailBean handleBdData(boolean hasRecommendData, List<FloorDetailBean> omnitechPageComponentVoList) {

        FloorDetailBean memberBean = null;
        int firstRecommendIndex = -1;
        int memberIndex = -1;
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean floorDetailBean = omnitechPageComponentVoList.get(i);
            if (StringUtil.safeEqualsAndNotNull(floorDetailBean.getTemplateCode(), getBdFloorTemplate())) {
                memberIndex = i;
                memberBean = floorDetailBean;
                continue;
            }

            if (floorDetailBean.isLocalRecommend() && firstRecommendIndex == -1) {
                firstRecommendIndex = i;
            }
        }

        omnitechPageComponentVoList.remove(memberBean);

        //有推荐位firstRecommendIndex一直为-1
        if (VpTabHelper.hasVpTab) {
            //有推荐位榜单在推荐楼层的第二位
            if (memberBean != null) {
                memberBean.setRealIndex(omnitechPageComponentVoList.size() + 1);
                goodsFloor.add(1, memberBean);
            }
        } else if (memberBean != null && firstRecommendIndex != -1 && hasRecommendData) {
            int insertIndex = memberIndex > firstRecommendIndex ? firstRecommendIndex + 1 : firstRecommendIndex;
            memberBean.setRealIndex(firstRecommendIndex);
            omnitechPageComponentVoList.add(insertIndex, memberBean);
        }

        return memberBean;
    }

    /**
     * 穿插推荐中的轮播
     * 穿插到推荐的第一个
     *
     * @param hasRecommendData            是否有推荐
     * @param omnitechPageComponentVoList
     */
    private FloorDetailBean handleRecommendRollingData(boolean hasRecommendData, List<FloorDetailBean> omnitechPageComponentVoList) {

        FloorDetailBean rollingFloor = null;
        int firstRecommendIndex = -1;
        int rollingIndex = -1;
        for (int i = 0; i < omnitechPageComponentVoList.size(); i++) {
            FloorDetailBean floorDetailBean = omnitechPageComponentVoList.get(i);
            if (StringUtil.safeEqualsAndNotNull(floorDetailBean.getTemplateCode(), getRecommendRollingTemplateCode())) {
                rollingIndex = i;
                rollingFloor = floorDetailBean;
                continue;
            }

            if (floorDetailBean.isLocalRecommend() && firstRecommendIndex == -1) {
                firstRecommendIndex = i;
            }
        }

        omnitechPageComponentVoList.remove(rollingFloor);

        //有推荐楼层时，firstRecommendIndex一直为-1
        if (VpTabHelper.hasVpTab) {
            //有推荐位、轮播图在最后一位
            if (rollingFloor != null) {
                rollingFloor.setRealIndex(omnitechPageComponentVoList.size());
                goodsFloor.add(0, rollingFloor);
            }
        } else if (rollingFloor != null && firstRecommendIndex != -1 && hasRecommendData) {
            int insertIndex = rollingIndex < firstRecommendIndex ? firstRecommendIndex - 1 : firstRecommendIndex;
            rollingFloor.setRealIndex(firstRecommendIndex);
            omnitechPageComponentVoList.add(insertIndex, rollingFloor);
        }


        return rollingFloor;
    }

    /**
     * 处理栏目楼层数据
     *
     * @param omnitechPageComponentVoList
     */
    private void handleGridData(List<FloorDetailBean> omnitechPageComponentVoList) {

        // 自用
//        public static final String templateCode = "home_grid";
//        // 网关返回-秒杀
//        public static final String templateCode_1 = "home_page_colimnBox_1";
//        // 网关返回-其他
//        public static final String templateCode_2 = "home_page_colimnBox_2";
//        // 网关返回-会员
//        public static final String templateCode_3 = "home_page_colimnBox_vip";

        //detail_belt_1  通栏
        // home_page_worthBuy_1 秒杀

        // 先找出宫格数据
        List<FloorDetailBean> wrapGridData = new ArrayList<>();
        int wrapFirstIndex = -1;
        int wrapIndex = 0;
        for (FloorDetailBean bean : omnitechPageComponentVoList) {
            if (StringUtil.safeEqualsAndNotNull(getGridFloorTemplateCode_2(), bean.getTemplateCode())) {
                wrapGridData.add(bean);
                // 第一个宫格位置
                if (wrapFirstIndex == -1) {
                    wrapFirstIndex = wrapIndex;
                }
            }
            wrapIndex++;
        }
        // 是否开启大促标签
        boolean isOpenSwich = false;

        // 秒杀楼层数据
        FloorDetailBean secKillDetailBean = null;
        if ((wrapFirstIndex - 1) > -1) {
            // 判断上一个数据是秒杀 还是 通栏
            FloorDetailBean floorDetailBean = omnitechPageComponentVoList.get(wrapFirstIndex - 1);
            // 通栏数据
            if (StringUtil.safeEqualsAndNotNull(getBeltTemplateCode(), floorDetailBean.getTemplateCode())) {
                String dataString = floorDetailBean.getComponentData();
                try {
                    JSONObject jsonObject = new JSONObject(dataString);
                    if (jsonObject.has("isColumnAggregation") && jsonObject.getInt("isColumnAggregation") == 1) {
                        isOpenSwich = true;
                    }
                } catch (JSONException e) {
                    JdCrashReport.postCaughtException(e);
                }
            } else if (StringUtil.safeEqualsAndNotNull(getSecKillTemplateCode(), floorDetailBean.getTemplateCode())) {
                if (StringUtil.safeEqualsAndNotNull(getSecKillTemplateCode(), floorDetailBean.getTemplateCode())) {
                    secKillDetailBean = floorDetailBean;
                }
            }
            // 上一个是秒杀 则继续向上查询
            if (secKillDetailBean != null && (wrapFirstIndex - 2) > -1) {
                FloorDetailBean floorDetailBean2 = omnitechPageComponentVoList.get(wrapFirstIndex - 2);
                if (StringUtil.safeEqualsAndNotNull(getBeltTemplateCode(), floorDetailBean2.getTemplateCode())) {
                    String dataString = floorDetailBean2.getComponentData();
                    try {
                        JSONObject jsonObject = new JSONObject(dataString);
                        if (jsonObject.has("isColumnAggregation") && jsonObject.getInt("isColumnAggregation") == 1) {
                            isOpenSwich = true;
                        }
                    } catch (JSONException e) {
                        JdCrashReport.postCaughtException(e);
                    }
                }
            }
        }
        if (isOpenSwich && (wrapGridData.size() < 2)) {
            isOpenSwich = false;
        }
        if (isOpenSwich && secKillDetailBean != null) {
            wrapGridData.add(0, secKillDetailBean);
        }
        // 插入在第一个宫格位置
        insertGridData(wrapFirstIndex, omnitechPageComponentVoList, wrapGridData);

        Iterator<FloorDetailBean> iterator = omnitechPageComponentVoList.iterator();
        while (iterator.hasNext()) {
            FloorDetailBean bean = iterator.next();
            // 栏目组件-秒杀 封装数据
            // home_page_colimnBox_1 不再支持 直接废弃
            if (StringUtil.safeEqualsAndNotNull(getGridFloorTemplateCode_1(), bean.getTemplateCode())) {
                iterator.remove();
                continue;
            }
            // 旧的栏目聚合已废弃
            if (StringUtil.safeEqualsAndNotNull(getGridFloorTemplateCode_2(), bean.getTemplateCode())) {
                iterator.remove();
                continue;
            }
            // 栏目组件-会员 封装数据 不再支持 直接废弃
            if (StringUtil.safeEqualsAndNotNull(getGridFloorTemplateCode_3(), bean.getTemplateCode())) {
                iterator.remove();
                continue;
            }
            // 秒杀 插入宫格 一样移除
            if (isOpenSwich && StringUtil.safeEqualsAndNotNull(getSecKillTemplateCode(), bean.getTemplateCode())) {
                iterator.remove();
            }
            /**
             * 防止无效宫格数据存在
             */
            if ("home_page_colimnBox_miaosha_one".equals(bean.getTemplateCode())) {
                iterator.remove();
            }
            if ("home_page_colimnBox_miaosha_two".equals(bean.getTemplateCode())) {
                iterator.remove();
            }

            if ("home_page_colimnBox_sku_one".equals(bean.getTemplateCode())) {
                iterator.remove();
            }

            if ("home_page_colimnBox_sku_two".equals(bean.getTemplateCode())) {
                iterator.remove();
            }

        }
    }


    /**
     * 处理推荐数据
     *
     * @param omnitechPageComponentVoList
     */
    private boolean handleRecommendData(List<FloorDetailBean> omnitechPageComponentVoList, String storeId, String fenceId) {

        FloorDetailBean floorDetailBean = null;

        // 筛选出推荐楼层数据并得到index
        int recommendIndex = -1;
        Iterator<FloorDetailBean> iterator = omnitechPageComponentVoList.iterator();
        int index = 0;
        while (iterator.hasNext()) {
            floorDetailBean = iterator.next();
            if (StringUtil.safeEqualsAndNotNull(getRecommendFloorTemplateCode(), floorDetailBean.getTemplateCode())) {
                RecommendRequest.dynamicParam = floorDetailBean.getDynamicParam();
                iterator.remove();
                recommendIndex = index;
                break;
            }
            index++;
        }

        if (recommendIndex == -1) {
            //没配楼层 不用告警
            return false;
        }

        boolean dataErr = false;
        if (StringUtil.isEmpty(floorDetailBean.getComponentData())) {
            //配置楼层但数据为空
            dataErr = true;
        }

        JDJSONObject jdjsonObject = JDJSONObject.parseObject(floorDetailBean.getComponentData());
        if (dataErr || jdjsonObject.optJSONObject("queryIndexRecommend") == null || jdjsonObject.optJSONObject("queryIndexRecommend").optJSONArray("skuInfos") == null ||
                jdjsonObject.optJSONObject("queryIndexRecommend").optJSONArray("skuInfos").size() == 0) {
            //配置楼层但数据为空
            dataErr = true;
        }

        if (dataErr) {
            //上报异常
            try {
                SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                errorLog.type = 9502;
                errorLog.errorCode = "首页_推荐流掉楼_" + AddressStoreHelper.getAddressStoreBean().getStoreName() + "_" + TenantIdUtils.getStoreId();
                errorLog.errorMsg = "storeId:" + storeId + " fenceId:" + fenceId;
                errorLog.location = "首页";
                SFLogCollector.reportBusinessErrorLog(errorLog);
            } catch (Exception e) {
                JdCrashReport.postCaughtException(e);
            }
            return false;
        }

        // 重新组装数据 插入楼层data中
        JDJSONArray skuInfos = jdjsonObject.optJSONObject("queryIndexRecommend").optJSONArray("skuInfos");
        RecommendRequest.nextPage = jdjsonObject.optJSONObject("queryIndexRecommend").optInt("nextPage");
        RecommendRequest.lastRecommendFloorBean = floorDetailBean;

        for (int i = 0; i < skuInfos.size(); i++) {
            JDJSONObject skuInfo = skuInfos.getJSONObject(i);
            if (skuInfo == null) {
                continue;
            }
            FloorDetailBean goodFloor = new FloorDetailBean();

            goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                goodFloor.setTemplateCode(getRecommendGoodFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                goodFloor.setTemplateCode(getRecommendCookMenuFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                goodFloor.setTemplateCode(getRecommendAdFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                goodFloor.setTemplateCode(getRecommendVideoFloorTemplate());
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.pormotion) {
                goodFloor.setTemplateCode(getPromotionFloorTemplate());
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.newRank) {
                if (setNewRank(skuInfo, goodFloor)) continue;
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.normal) {
                goodFloor.setJumpType(1);
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.mini) {
                goodFloor.setJumpType(2);
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.promotionSearch) {
                goodFloor.setJumpType(3);
            }
            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.zelatuPage) {
                goodFloor.setJumpType(4);
            }
            goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
            goodFloor.setComponentName(floorDetailBean.getComponentName());
            goodFloor.setComponentCode(floorDetailBean.getComponentCode());
            goodFloor.setRealIndex(recommendIndex);
            goodFloor.setLocalRecommend(true);
            goodFloor.recommendFloorTemplateCode = getRecommendFloorTemplateCode();

            omnitechPageComponentVoList.add(goodFloor);
        }
        return true;
    }


    //获取刷新时推荐楼层的floorDetailBean
    private FloorDetailBean getViewPagerRecommendRefreshFloorDetailBean(List<FloorDetailBean> omnitechPageComponentVoList) {
        FloorDetailBean floorDetailBean = null;
        try {
            // 筛选出推荐楼层数据并得到index
            int recommendIndex = -1;
            Iterator<FloorDetailBean> iterator = omnitechPageComponentVoList.iterator();
            int index = 0;
            while (iterator.hasNext()) {
                floorDetailBean = iterator.next();
                if (StringUtil.safeEqualsAndNotNull(getRecommendFloorTemplateCode(), floorDetailBean.getTemplateCode())) {
                    RecommendRequest.dynamicParam = floorDetailBean.getDynamicParam();
                    recommendIndex = index;
                    break;
                }

                index++;
            }

            if (recommendIndex == -1) {
                //没配楼层 不用告警
                return null;
            }
            floorDetailBean.setRealIndex(recommendIndex);
            RecommendRequest.lastRecommendFloorBean = floorDetailBean;

        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
        }

        return floorDetailBean;


    }

    //推荐tabs数据
    List<VPTabBean> tabBeans = new ArrayList<>();

    public List<VPTabBean> getTabBeans() {
        return tabBeans;
    }

    //拿到推荐tab下的商品列表
    private List<FloorDetailBean> getGoodsFloorAndSetTabs(FloorDetailBean floorDetailBean) {
        //拿到静态资源json
        JDJSONObject jdjsonObject = JDJSONObject.parseObject(floorDetailBean.getComponentData());

        //拿到skuInfoLists
        JDJSONArray skuInfoList = jdjsonObject.optJSONObject("queryIndexRecommendMultiTab").optJSONArray("skuInfoLists");

        //tab下的商品
        List<FloorDetailBean> goodsFloor = new ArrayList<>();


        tabBeans.clear();
        for (int i = 0; i < skuInfoList.size(); i++) {
            JDJSONObject skuInfo = skuInfoList.getJSONObject(i);
            VPTabBean vpTabBean = new VPTabBean();
            vpTabBean.setNextPage(skuInfo.getIntValue("nextPage"));
            vpTabBean.setPage(skuInfo.getIntValue("page"));
            vpTabBean.setTabKey(skuInfo.getIntValue("tabKey"));
            tabBeans.add(vpTabBean);

            JDJSONArray skuInfos = skuInfo.getJSONArray("skuInfos");

            //拿到tabKey==0的商品
            if (vpTabBean.getTabKey() == 0) {
                if (skuInfos != null && skuInfos.size() > 0) {
                    addRecommendGoods(floorDetailBean, skuInfos, goodsFloor);
                }
            }


        }

        //设置tab静态资源
        if (jdjsonObject.containsKey("indexMultiTabsList")) {
            //设置tab静态资源
            JDJSONArray multiTabs = jdjsonObject.getJSONArray("indexMultiTabsList");

            String selectImg = jdjsonObject.optString("selectImg");
            String titleSelectColor = jdjsonObject.optString("titleSelectColor");
            String titleColor = jdjsonObject.optString("titleColor");

            for (int i = 0; i < multiTabs.size(); i++) {
                JDJSONObject object = multiTabs.getJSONObject(i);
                int tabKey = object.optInt("tabKey");
                for (int j = 0; j < tabBeans.size(); j++) {
                    if (tabKey == tabBeans.get(j).getTabKey()) {
                        VPTabBean tabBean = tabBeans.get(j);
                        tabBean.setSelectImg(selectImg);
                        tabBean.setTitleSelectColor(titleSelectColor);
                        tabBean.setTitleColor(titleColor);
                        tabBean.setTitle(object.optString("title"));
                        tabBean.setTitleStyle(object.optInt("titleStyle"));
                        if (object.containsKey("selectTitleImage")) {
                            VPTabBean.TabProp tabProp = new VPTabBean.TabProp();
                            JDJSONObject selectTitleImage = object.getJSONObject("selectTitleImage");
                            tabProp.setImageUrl(selectTitleImage.optString("imageUrl"));
                            tabProp.setWidth(selectTitleImage.optInt("width"));
                            tabProp.setHeight(selectTitleImage.optInt("height"));
                            tabBean.setSelectProp(tabProp);
                        }

                        if (object.containsKey("titleImage")) {
                            VPTabBean.TabProp tabProp = new VPTabBean.TabProp();
                            JDJSONObject selectTitleImage = object.getJSONObject("titleImage");
                            tabProp.setImageUrl(selectTitleImage.optString("imageUrl"));
                            tabProp.setWidth(selectTitleImage.optInt("width"));
                            tabProp.setHeight(selectTitleImage.optInt("height"));
                            tabBean.setNormalProp(tabProp);
                        }

                    }
                }
            }
        }


        return goodsFloor;
    }


    //解析商品添加到goodsFloor里
    private void addRecommendGoods(FloorDetailBean floorDetailBean, JDJSONArray skuInfos, List<FloorDetailBean> goodsFloor) {
        for (int j = 0; j < skuInfos.size(); j++) {
            JDJSONObject skuInfo = skuInfos.getJSONObject(j);
            if (skuInfo == null) {
                continue;
            }
            FloorDetailBean goodFloor = new FloorDetailBean();

            goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                goodFloor.setTemplateCode(getRecommendGoodFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                goodFloor.setTemplateCode(getRecommendCookMenuFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                goodFloor.setTemplateCode(getRecommendAdFloorTemplate());
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                goodFloor.setTemplateCode(getRecommendVideoFloorTemplate());
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.pormotion) {
                goodFloor.setTemplateCode(getPromotionFloorTemplate());
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.newRank) {
                if (setNewRank(skuInfo, goodFloor)) continue;
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.normal) {
                goodFloor.setJumpType(1);
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.mini) {
                goodFloor.setJumpType(2);
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.promotionSearch) {
                goodFloor.setJumpType(3);
            }
            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.zelatuPage) {
                goodFloor.setJumpType(4);
            }
            goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
            goodFloor.setComponentName(floorDetailBean.getComponentName());
            goodFloor.setComponentCode(floorDetailBean.getComponentCode());
            //tab下的RealIndex为1 保证listPageNum和listPageIndex正确
            goodFloor.setRealIndex(1);
            goodFloor.setLocalRecommend(true);
            goodFloor.recommendFloorTemplateCode = getRecommendFloorTemplateCode();

            goodsFloor.add(goodFloor);
        }

    }

    //设置新榜单校验  返回true：不显示  false：显示
    private boolean setNewRank(JDJSONObject skuInfo, FloorDetailBean goodFloor) {
        //新榜单商品等于3个才展示
        JDJSONObject jdObject = JDJSONObject.parseObject(skuInfo.optString("recommendVo"));
        if (jdObject == null) {
            return true;
        }
        JDJSONArray jdjsonArray = jdObject.optJSONArray("productCardVoList");
        if (jdjsonArray == null) {
            return true;
        }

        if (jdjsonArray.size() == 3) {
            goodFloor.setTemplateCode(getNewRankFloorTemplateCode());
            return false;
        } else {
            return true;
        }

    }

    //获取tabs数据
    private void getViewPagerTabs(JDJSONObject tabsObject) {

    }


    /**
     * 处理tab商品组推荐数据
     *
     * @param omnitechPageComponentVoList
     */
    private boolean handleTabGoodData(List<FloorDetailBean> omnitechPageComponentVoList) {

        FloorDetailBean floorDetailBean = null;

        // 筛选出推荐楼层数据并得到index
        int recommendIndex = -1;
        Iterator<FloorDetailBean> iterator = omnitechPageComponentVoList.iterator();
        int index = 0;
        while (iterator.hasNext()) {
            floorDetailBean = iterator.next();
            if (StringUtil.safeEqualsAndNotNull(getTabGoodGroupFloorTemplateCode(), floorDetailBean.getTemplateCode())) {
                RecommendRequest.dynamicParam = floorDetailBean.getDynamicParam();
                recommendIndex = index;
                break;
            }
            index++;
        }

        if (floorDetailBean == null || StringUtil.isEmpty(floorDetailBean.getComponentData())) {
            return false;
        }

        JDJSONObject jdjsonObject = JDJSONObject.parseObject(floorDetailBean.getComponentData());
        if (jdjsonObject.optJSONObject("activityTabMixedGroup") == null
                || jdjsonObject.optJSONObject("activityTabMixedGroup").optJSONArray("tabMixedGroups") == null) {
            return false;
        }

        // 重新组装数据 插入楼层data中
        JDJSONArray tabGroups = jdjsonObject.optJSONObject("activityTabMixedGroup").optJSONArray("tabMixedGroups");

//        RecommendRequest.nextPage = jdjsonObject.optJSONObject("queryIndexRecommend").optInt("nextPage");
//        RecommendRequest.lastRecommendFloorBean = floorDetailBean;
        TabGroupGoodDataManager.getInstance().clearData();
        Integer tabId = null;
        for (int i = 0; i < tabGroups.size(); i++) {
            JDJSONObject tabGroup = tabGroups.getJSONObject(i);
            if (tabGroup == null || tabGroup.optJSONArray("skuInfos") == null) {
                continue;
            }

            if (tabId == null || tabId == 0) {
                tabId = tabGroup.optInt("id");
                TabGroupGoodDataManager.getInstance().setCurrentTabId(tabGroup.optInt("id"));
            }

            JDJSONArray skuInfos = tabGroup.optJSONArray("skuInfos");
            TabGroupGoodDataManager.getInstance().addNextPage(tabGroup.optInt("id"), tabGroup.optInt("nextPage"));
            RecommendRequest.lastRecommendFloorBean = floorDetailBean;

            for (int j = 0; j < skuInfos.size(); j++) {
                JDJSONObject skuInfo = skuInfos.getJSONObject(j);
                if (skuInfo == null) {
                    continue;
                }
                FloorDetailBean goodFloor = new FloorDetailBean();

                goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

                if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                    goodFloor.setTemplateCode(getRecommendGoodFloorTemplate());
                }

                if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                    goodFloor.setTemplateCode(getRecommendCookMenuFloorTemplate());
                }

                if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                    goodFloor.setTemplateCode(getRecommendAdFloorTemplate());
                }

                if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                    goodFloor.setTemplateCode(getRecommendVideoFloorTemplate());
                }

                goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
                goodFloor.setComponentName(floorDetailBean.getComponentName());
                goodFloor.setComponentCode(floorDetailBean.getComponentCode());
                goodFloor.setRealIndex(recommendIndex);
                goodFloor.setLocalRecommend(true);
                goodFloor.recommendFloorTemplateCode = getRecommendFloorTemplateCode();

                TabGroupGoodDataManager.getInstance().addData(tabGroup.optInt("id"), goodFloor);
//                omnitechPageComponentVoList.add(goodFloor);
            }
        }
        if (TabGroupGoodDataManager.getInstance().getData(tabId) != null) {
            omnitechPageComponentVoList.addAll(TabGroupGoodDataManager.getInstance().getData(tabId));
        }
        return !TabGroupGoodDataManager.getInstance().isEmpty();
    }


    /**
     * 插入组装好的数据
     *
     * @param gridIndex
     * @param omnitechPageComponentVoList
     * @param gridData
     */
    private void insertGridData(int gridIndex, List<FloorDetailBean> omnitechPageComponentVoList, List<FloorDetailBean> gridData) {
        if (gridIndex == -1 || gridData == null || gridData.isEmpty() || gridIndex > omnitechPageComponentVoList.size()) {
            return;
        }
        FloorDetailBean floorDetailBean = new FloorDetailBean();
        floorDetailBean.setTemplateCode(getGridFloorTemplateCode());
        floorDetailBean.setDataParseTime(System.currentTimeMillis());
        floorDetailBean.setComponentDataObject(gridData);
        //设置位置
        floorDetailBean.setRealIndex(gridIndex);
        for (FloorDetailBean data : gridData) {
            data.setRealIndex(gridIndex);
        }
        if (gridIndex == omnitechPageComponentVoList.size()) {
            omnitechPageComponentVoList.add(floorDetailBean);
        } else {
            omnitechPageComponentVoList.add(gridIndex, floorDetailBean);
        }
    }

    protected abstract void handleRollingFloorData(List<FloorDetailBean> omnitechPageComponentVoList);

    /**
     * 处理导航栏的轮播效果
     *
     * @param omnitechPageComponentVoList 楼层数据
     */
    protected abstract void handleNavigationFloorData(List<FloorDetailBean> omnitechPageComponentVoList, FloorDetailBean navBean);

    public void bindView(FloorDataCallback floorDataCallback) {
        this.floorDataCallback = floorDataCallback;
    }

}
