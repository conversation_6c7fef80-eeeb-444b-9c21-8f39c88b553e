package com.xstore.sevenfresh.floor.modules.vp.sliding;


import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.viewpager.widget.ViewPager;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.floor.modules.vp.utils.UIUtils;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.image.manager.ImageLoaderUtil;

/**
 * PagerSlidingTabStrip简介
 *
 * <AUTHOR>
 * @date 2019-6-22 11:21:01
 */
public class PagerSlidingVpTabStrip extends HorizontalScrollView {

    /**
     * INDICATOR_MARGIN_BOTTOM 下滑线底部预留边距
     */
    public static final int INDICATOR_MARGIN_BOTTOM = UIUtils.getWidthByDesignValue(5, 375);

    /**
     * ZERO_OFFSET 未移动偏移量
     */
    private static final float ZERO_OFFSET = 0.0f;

    /**
     * ANDROID_ATTRS 参数
     */
    private static final int[] ANDROID_ATTRS = new int[]{
            android.R.attr.textColorPrimary,
            android.R.attr.padding,
            android.R.attr.paddingLeft,
            android.R.attr.paddingRight
    };

    /**
     * tabsContainer tab容器类
     */
    protected LinearLayout tabsContainer;
    /**
     * tabLayoutParams tab布局参数
     */
    protected LayoutParams tabLayoutParams;
    /**
     * adapterObserver 数据变化监听
     */
    private final PagerAdapterObserver adapterObserver;
    /**
     * pageListener viewpager监听
     */
    private final PageListener pageListener;
    /**
     * tabReselectedListener tab选中监听
     */
    private OnTabReselectedListener tabReselectedListener;
    /**
     * pageChangeListener 对外暴露的viewpager监听
     */
    public ViewPager.OnPageChangeListener pageChangeListener;
    /**
     * viewPager 当前绑定的viewpager
     */
    protected ViewPager viewPager;
    /**
     * tabCount tab数量
     */
    protected int tabCount;
    /**
     * currentPosition 当前选中的位置
     */
    private int currentPosition;
    /**
     * currentPositionOffset 当前选中位置的滑动偏移量
     */
    private float currentPositionOffset;
    /**
     * rectPaint 底部下划线类画笔
     */
    private Paint rectPaint;
    /**
     * dividerPaint 分割线类画笔
     */
    private Paint dividerPaint;
    /**
     * selectColor 选中颜色
     */
    private int selectColor;
    /**
     * indicatorColor 底部指示器颜色
     */
    private int indicatorColor;
    /**
     * indicatorHeight 指示器高度
     */
    private int indicatorHeight;
    /**
     * indicatorPadding 指示器左右内间距
     */
    private int indicatorPadding;
    /**
     * underlineHeight 下划线高度
     */
    private int underlineHeight;
    /**
     * underlineColor 下划线颜色
     */
    private int underlineColor;
    /**
     * dividerWidth 分割线宽度
     */
    private int dividerWidth;
    /**
     * dividerPadding 分割线间距
     */
    private int dividerPadding;
    /**
     * dividerColor 分割线颜色
     */
    private int dividerColor;
    /**
     * tabPadding tab内间距
     */
    private int tabPadding;
    /**
     * tabTextSize tab文字大小
     */
    private int tabTextSize;
    /**
     * tabUnSelectTextSize tab未选中文字大大小
     */
    private int tabUnSelectTextSize;
    /**
     * tabTextColor tab文字颜色
     */
    private ColorStateList tabTextColor;
    /**
     * paddingLeft tab栏左间距
     */
    private int paddingLeft;
    /**
     * paddingRight tab栏右间距
     */
    private int paddingRight;
    /**
     * isExpandTabs 可展开展示tab
     */
    private boolean isExpandTabs;
    /**
     * isCustomTabs 是否为"定制"类型
     */
    private boolean isCustomTabs;
    /**
     * isPaddingMiddle ？
     */
    private boolean isPaddingMiddle;
    /**
     * isTabTextAllCaps
     */
    private boolean isTabTextAllCaps;
    /**
     * tabTextTypeface
     */
    private Typeface tabTextTypeface;
    /**
     * tabTextTypefaceStyle
     */
    private int tabTextTypefaceStyle;
    /**
     * scrollOffset
     */
    private int scrollOffset;
    /**
     * lastScrollX
     */
    private int lastScrollX;
    /**
     * tabBackgroundResId
     */
    private int tabBackgroundResId;
    /**
     * rootContainer
     */
    protected FrameLayout rootContainer;
    /**
     * isViewPagerSmoothScroll
     */
    public boolean isViewPagerSmoothScroll;
    /**
     * isEnableUnRegistedObserver
     */
    protected boolean isEnableUnRegistedObserver;
    /**
     * maxScroll
     */
    private int maxScroll;
    /**
     * indicatorColorAlpha 指示器透明度
     */
    private int indicatorColorAlpha;
    /**
     * rect 指示器绘制大小
     */
    private RectF rect = new RectF();

    /**
     * @param context
     */
    public PagerSlidingVpTabStrip(Context context) {
        this(context, null);
    }

    /**
     * @param context
     * @param attrs
     */
    public PagerSlidingVpTabStrip(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * @param context
     * @param attrs
     * @param defStyle
     */
    public PagerSlidingVpTabStrip(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.adapterObserver = new PagerAdapterObserver();
        this.pageListener = new PageListener();
        this.tabReselectedListener = null;
        this.currentPosition = 0;
        this.currentPositionOffset = 0.0F;
        this.indicatorHeight = 2;
        this.indicatorPadding = 0;
        this.underlineHeight = 0;
        this.dividerWidth = 0;
        this.dividerPadding = 0;
        this.tabPadding = 12;
        this.tabTextSize = 12;
        this.tabUnSelectTextSize = 12;
        this.tabTextColor = null;
        this.paddingLeft = 0;
        this.paddingRight = 0;
        this.isExpandTabs = false;
        this.isPaddingMiddle = false;
        this.isTabTextAllCaps = true;
        this.tabTextTypeface = null;
        this.tabTextTypefaceStyle = 1;
        this.lastScrollX = 0;
        this.tabBackgroundResId = 0;
        this.isViewPagerSmoothScroll = true;
        this.isEnableUnRegistedObserver = true;
        this.setFillViewport(true);
        this.setWillNotDraw(false);
        this.rootContainer = new FrameLayout(context);
        this.addView(this.rootContainer);
        this.tabsContainer = new LinearLayout(context);
        this.tabsContainer.setOrientation(LinearLayout.HORIZONTAL);
        this.rootContainer.addView(this.tabsContainer);


        this.rectPaint = new Paint();
        this.rectPaint.setAntiAlias(true);
        this.rectPaint.setStyle(Paint.Style.FILL);
        DisplayMetrics dm = this.getResources().getDisplayMetrics();
        this.scrollOffset = (int) TypedValue.applyDimension(1, (float) this.scrollOffset, dm);
        this.indicatorHeight = (int) TypedValue.applyDimension(1, (float) this.indicatorHeight, dm);
        this.underlineHeight = (int) TypedValue.applyDimension(1, (float) this.underlineHeight, dm);
        this.dividerPadding = (int) TypedValue.applyDimension(1, (float) this.dividerPadding, dm);
        this.tabPadding = (int) TypedValue.applyDimension(1, (float) this.tabPadding, dm);
        this.dividerWidth = (int) TypedValue.applyDimension(1, (float) this.dividerWidth, dm);
        this.tabTextSize = (int) TypedValue.applyDimension(2, (float) this.tabTextSize, dm);
        this.dividerPaint = new Paint();
        this.dividerPaint.setAntiAlias(true);
        this.dividerPaint.setStrokeWidth((float) this.dividerWidth);
        TypedArray a = context.obtainStyledAttributes(attrs, ANDROID_ATTRS);
        int textPrimaryColor = a.getColor(0, ContextCompat.getColor(context, R.color.sf_theme_color_level_1));
        this.underlineColor = textPrimaryColor;
        this.dividerColor = textPrimaryColor;
        this.indicatorColor = textPrimaryColor;
        this.selectColor = textPrimaryColor;
        int padding = a.getDimensionPixelSize(1, 0);
        this.paddingLeft = padding > 0 ? padding : a.getDimensionPixelSize(2, 0);
        this.paddingRight = padding > 0 ? padding : a.getDimensionPixelSize(3, 0);
        a.recycle();
        String tabTextTypefaceName = "sans-serif";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tabTextTypefaceName = "sans-serif-medium";
            this.tabTextTypefaceStyle = 0;
        }

        a = context.obtainStyledAttributes(attrs, R.styleable.RePagerSlidingTabStrip);
        this.indicatorColor = a.getColor(R.styleable.RePagerSlidingTabStrip_re_pstsIndicatorColor, this.indicatorColor);
        this.indicatorHeight = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsIndicatorHeight, this.indicatorHeight);
        this.underlineColor = a.getColor(R.styleable.RePagerSlidingTabStrip_re_pstsUnderlineColor, this.underlineColor);
        this.underlineHeight = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsUnderlineHeight, this.underlineHeight);
        this.dividerColor = a.getColor(R.styleable.RePagerSlidingTabStrip_re_pstsDividerColor, this.dividerColor);
        this.dividerWidth = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsDividerWidth, this.dividerWidth);
        this.dividerPadding = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsDividerPadding, this.dividerPadding);
        this.isExpandTabs = a.getBoolean(R.styleable.RePagerSlidingTabStrip_re_pstsShouldExpand, this.isExpandTabs);
        this.scrollOffset = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsScrollOffset, this.scrollOffset);
        this.isPaddingMiddle = a.getBoolean(R.styleable.RePagerSlidingTabStrip_re_pstsPaddingMiddle, this.isPaddingMiddle);
        this.tabPadding = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsTabPaddingLeftRight, this.tabPadding);
        this.tabBackgroundResId = a.getResourceId(R.styleable.RePagerSlidingTabStrip_re_pstsTabBackground, this.tabBackgroundResId);
        this.tabTextSize = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextSize, this.tabTextSize);
        this.tabUnSelectTextSize = this.tabTextSize;
        this.indicatorPadding = a.getDimensionPixelSize(R.styleable.RePagerSlidingTabStrip_re_pstsIndicatorPadding, this.indicatorPadding);
        this.tabTextColor = a.hasValue(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextColor) ? a.getColorStateList(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextColor) : null;
        this.tabTextTypefaceStyle = a.getInt(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextStyle, this.tabTextTypefaceStyle);
        this.isTabTextAllCaps = a.getBoolean(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextAllCaps, this.isTabTextAllCaps);
        int tabTextAlpha = a.getInt(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextAlpha, 150);
        String fontFamily = a.getString(R.styleable.RePagerSlidingTabStrip_re_pstsTabTextFontFamily);
        a.recycle();
        if (this.tabTextColor == null) {
            this.tabTextColor = this.createColorStateList(this.selectColor, this.selectColor, textPrimaryColor);
        }

        if (fontFamily != null) {
            tabTextTypefaceName = fontFamily;
        }

        this.tabTextTypeface = Typeface.create(tabTextTypefaceName, this.tabTextTypefaceStyle);
        this.setTabsContainerParentViewPaddings();
        //todo  gravity!!!!!
        this.tabLayoutParams = this.isExpandTabs ? new LayoutParams(0, -1, 1) : new LayoutParams(-2, -1);
    }

    /**
     * 设置tab栏内边距 叠加上底部下划线指示器高度
     */
    private void setTabsContainerParentViewPaddings() {
        int bottomMargin = this.indicatorHeight >= this.underlineHeight ? this.indicatorHeight : this.underlineHeight;
        this.setPadding(this.getPaddingLeft(), this.getPaddingTop(), this.getPaddingRight(), bottomMargin);
    }


    ImageView tabIndicatorImg;

    /**
     * 关联viewpager
     *
     * @param pager
     */
    public void setViewPager(ViewPager pager) {
        this.viewPager = pager;
        if (pager.getAdapter() == null) {
            throw new IllegalStateException("ViewPager does not have adapter instance.");
        } else {
            this.isCustomTabs = pager.getAdapter() instanceof CustomTabProvider;
            pager.addOnPageChangeListener(this.pageListener);
            pager.getAdapter().registerDataSetObserver(this.adapterObserver);
            this.adapterObserver.setAttached(true);
            this.notifyDataSetChanged();

            addTabIndicator();

        }
    }


    //添加指示器
    private void addTabIndicator() {
        tabIndicatorImg = new ImageView(getContext());
        tabIndicatorImg.setScaleType(ImageView.ScaleType.FIT_CENTER);
        MarginLayoutParams imlp = new MarginLayoutParams(UIUtils.dp2px(34), UIUtils.dp2px(10));
        imlp.topMargin = UIUtils.dp2px(16);

        tabIndicatorImg.setLayoutParams(imlp);

        this.rootContainer.addView(tabIndicatorImg);

        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int x = tabsContainer.getChildAt(0).getWidth() / 2 - UIUtils.dp2px(18);
                tabIndicatorImg.setX(x);
            }
        });


        VpTabView firstVpTab = ((VpTabView) tabsContainer.getChildAt(0));

        if (StringUtil.isNullByString(firstVpTab.getRecommendTab().getSelectImg())) {
            tabIndicatorImg.setVisibility(View.INVISIBLE);
        } else {
            tabIndicatorImg.setVisibility(View.VISIBLE);
            ImageloadUtils.loadImage(getContext(), tabIndicatorImg, firstVpTab.getRecommendTab().getSelectImg());
        }
        rootContainer.bringChildToFront(tabsContainer);

    }

    /**
     * TAB_SLIDING_PADDING_DESIGN_WITH_375 tab内边距
     */
    public static int TAB_SLIDING_PADDING_DESIGN_WITH_375 = 10;

    public void setVpParam(int size) {
        this.tabsContainer.setPadding(0, 0, 0, 0);
        if (size <= 4) {
            setTabPaddingLeftRight(UIUtils.dp2px(0));
        } else {
            setTabPaddingLeftRight(UIUtils.dp2px(16));
        }

        this.setIndicatorHeight(DisplayUtils.dp2px(getContext(), 0));
        setIndicatorColor(getResources().getColor(R.color.sf_theme_color_level_1));
        setViewPagerSmoothScroll(true);
    }

    /**
     * 刷新tab
     */
    public void notifyDataSetChanged() {
        this.tabsContainer.removeAllViews();
        this.rootContainer.removeAllViews();
        this.addFirstIndexView();
        this.rootContainer.addView(this.tabsContainer);
        this.tabCount = this.viewPager.getAdapter().getCount();

        for (int i = 0; i < this.tabCount; ++i) {
            View tabView;
            if (this.isCustomTabs) {
                tabView = ((CustomTabProvider) this.viewPager.getAdapter()).getCustomTabView(this, i);
            } else {
                tabView = LayoutInflater.from(this.getContext()).inflate(R.layout.psts_tab_vp, this, false);
            }

            CharSequence title = this.viewPager.getAdapter().getPageTitle(i);
            this.initTab(i, title, tabView);
        }

        this.updateTabStyles();
    }


    public void startTabIndicatorAnimal(int newTab, boolean needAnimal) {
        View newView = tabsContainer.getChildAt(newTab);
        float newX = newView.getX() + (float) newView.getWidth() / 2 - (float) tabIndicatorImg.getWidth() / 2;
        if (needAnimal) {
            tabIndicatorImg.animate().x(newX).setDuration(150).start();
        } else {
            tabIndicatorImg.animate().x(newX).setDuration(0).start();
        }

    }

    /**
     *
     */
    protected void addFirstIndexView() {
    }

    /**
     * 初始化tabview
     *
     * @param position
     * @param title
     * @param tabView
     */
    private void initTab(final int position, CharSequence title, View tabView) {
        TextView textView = (TextView) tabView.findViewById(R.id.psts_tab_title);
        if (textView != null && title != null) {
            textView.setText(title);
        }

        tabView.setFocusable(true);
        tabView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                PagerSlidingVpTabStrip.this.onTabClick(position);
            }
        });
        this.addTab(position, tabView);
    }

    /**
     * tab点击
     *
     * @param position
     */
    protected void onTabClick(int position) {
        if (this.tabReselectedListener != null) {
            this.tabReselectedListener.onTabSelected(position);
        }

        if (this.viewPager.getCurrentItem() != position) {
            View tab = this.tabsContainer.getChildAt(this.viewPager.getCurrentItem());
            this.unSelect(tab);

            if (Math.abs(this.viewPager.getCurrentItem() - position) >= 3) {
                this.viewPager.setCurrentItem(position, false);
            } else {
                this.viewPager.setCurrentItem(position, true);
            }
//            if (this.isViewPagerSmoothScroll) {
//                this.viewPager.setCurrentItem(position);
//            } else {
//                this.viewPager.setCurrentItem(position, this.isViewPagerSmoothScroll);
//            }
        } else if (this.tabReselectedListener != null) {
            this.tabReselectedListener.onTabReselected(position);
        }

    }

    /**
     * 添加tab
     *
     * @param position
     * @param tabView
     */
    protected void addTab(int position, View tabView) {
        this.tabsContainer.addView(tabView, position, this.tabLayoutParams);
    }

    /**
     * 设置点击时viewpager是否支持滚动效果
     *
     * @param isSmooth
     */
    public void setViewPagerSmoothScroll(boolean isSmooth) {
        this.isViewPagerSmoothScroll = isSmooth;
    }

    /**
     * 更新文字选中状态
     */
    private void updateTabStyles() {
        for (int i = 0; i < this.tabCount; ++i) {
            View v = this.tabsContainer.getChildAt(i);
            v.setBackgroundResource(this.tabBackgroundResId);
            v.setPadding(this.tabPadding, v.getPaddingTop(), this.tabPadding, v.getPaddingBottom());
            TextView tabTitle = (TextView) v.findViewById(R.id.psts_tab_title);
            if (tabTitle != null) {
                tabTitle.setTextColor(this.tabTextColor);
                tabTitle.setTypeface(this.tabTextTypeface, this.tabTextTypefaceStyle);
                if (tabTitle.isSelected()) {
                    tabTitle.setTextSize(0, (float) this.tabUnSelectTextSize);
                } else {
                    tabTitle.setTextSize(0, (float) this.tabTextSize);
                }

                if (this.isTabTextAllCaps) {
                    if (Build.VERSION.SDK_INT >= 14) {
                        tabTitle.setAllCaps(true);
                    } else {
                        tabTitle.setText(tabTitle.getText().toString().toUpperCase());
                    }
                }
            }
        }

    }

    /**
     * tab栏滑动到指定位置
     *
     * @param position
     * @param offset
     */
    private void scrollToChild(int position, int offset) {
        if (this.tabCount != 0) {
            int newScrollX = this.tabsContainer.getChildAt(position).getLeft() + offset;
            if (position > 0 || offset > 0) {
                newScrollX -= this.scrollOffset;
//                Pair<Float, Float> lines = this.getIndicatorCoordinates();
//                newScrollX = (int)((float)newScrollX + ((Float)lines.second - (Float)lines.first) / 2.0F);
            }

            if (position == 0) {
                newScrollX = 0;
            }
            if (newScrollX != this.lastScrollX) {
                this.lastScrollX = newScrollX;
                this.smoothScrollTo(newScrollX, 0);
            }

        }
    }

    /**
     * 计算指示器位置
     *
     * @return
     */
    public Pair<Float, Float> getIndicatorCoordinates() {
        View currentTab = this.tabsContainer.getChildAt(this.currentPosition);
        float lineLeft = (float) currentTab.getLeft();
        float lineRight = (float) currentTab.getRight();
        if (currentTab instanceof VpTabView) {
            TextView tvCurText = currentTab.findViewById(R.id.recommend_home_tab_b_title);
            lineLeft = (float) lineLeft + tvCurText.getLeft();
            lineRight = (float) lineRight - tvCurText.getLeft();
        }
        if (this.currentPositionOffset > ZERO_OFFSET && this.currentPosition < this.tabCount - 1) {
            View nextTab = this.tabsContainer.getChildAt(this.currentPosition + 1);
            float nextTabLeft = (float) nextTab.getLeft();
            float nextTabRight = (float) nextTab.getRight();
            //如果是跟随文字宽度的样式
            if (nextTab instanceof VpTabView) {
                TextView tvNextText = nextTab.findViewById(R.id.recommend_home_tab_b_title);
                nextTabLeft = (float) nextTabLeft + tvNextText.getLeft();
                nextTabRight = (float) nextTabRight - tvNextText.getLeft();
            }

            lineLeft = this.currentPositionOffset * nextTabLeft + (1.0F - this.currentPositionOffset) * lineLeft;
            lineRight = this.currentPositionOffset * nextTabRight + (1.0F - this.currentPositionOffset) * lineRight;
        }

        return new Pair(lineLeft, lineRight);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        if (this.isPaddingMiddle && this.tabsContainer.getChildCount() > 0) {
            View view = this.tabsContainer.getChildAt(0);
            int halfWidthFirstTab = view.getMeasuredWidth() / 2;
            this.paddingLeft = this.paddingRight = this.getWidth() / 2 - halfWidthFirstTab;
        }

        if (this.isPaddingMiddle || this.paddingLeft > 0 || this.paddingRight > 0) {
            int width;
            if (this.isPaddingMiddle) {
                width = this.getWidth();
            } else {
                width = this.getWidth() - this.paddingLeft - this.paddingRight;
            }

            this.tabsContainer.setMinimumWidth(width);
            this.setClipToPadding(false);
        }

        this.setPadding(this.paddingLeft, this.getPaddingTop(), this.paddingRight, this.getPaddingBottom());
        if (this.scrollOffset == 0) {
            this.scrollOffset = this.getWidth() / 2 - this.paddingLeft;
        }

        if (this.viewPager != null) {
            this.currentPosition = this.viewPager.getCurrentItem();
        }

        this.currentPositionOffset = 0.0F;
        updateSelection(currentPosition);
        scrollToChild(currentPosition, 0);
        super.onLayout(changed, l, t, r, b);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!this.isInEditMode() && this.tabCount != 0) {
            int height = this.getHeight();
            if (this.dividerWidth > 0) {
                this.dividerPaint.setStrokeWidth((float) this.dividerWidth);
                this.dividerPaint.setColor(this.dividerColor);

                for (int i = 0; i < this.tabCount - 1; ++i) {
                    View tab = this.tabsContainer.getChildAt(i);
                    canvas.drawLine((float) tab.getRight(), (float) this.dividerPadding, (float) tab.getRight(), (float) (height - this.dividerPadding), this.dividerPaint);
                }
            }

            if (this.underlineHeight > 0) {
                this.rectPaint.setColor(this.underlineColor);
                canvas.drawRect((float) this.paddingLeft, (float) (height - this.underlineHeight), (float) (this.tabsContainer.getWidth() + this.paddingRight), (float) height, this.rectPaint);
            }

            if (this.indicatorHeight > 0 && !isCustomTabs) {
                this.rectPaint.setColor(this.indicatorColor);
                this.rectPaint.setAlpha(indicatorColorAlpha);
                Pair<Float, Float> lines = this.getIndicatorCoordinates();
                //rect.left = (int) (lines.first + this.indicatorPadding);
                rect.left = (int) (lines.first + (lines.second - lines.first) / 2 - UIUtils.dp2px(13));
                rect.top = (int) (height - this.indicatorHeight - INDICATOR_MARGIN_BOTTOM);
                rect.right = (int) (lines.first + (lines.second - lines.first) / 2 + UIUtils.dp2px(13));
                rect.bottom = height - INDICATOR_MARGIN_BOTTOM;
                canvas.drawRoundRect(rect, height / 2, height / 2, this.rectPaint);
            }

        }
    }

    /**
     * 设置tab重新选中监听
     *
     * @param tabReselectedListener
     */
    public void setOnTabReselectedListener(OnTabReselectedListener tabReselectedListener) {
        this.tabReselectedListener = tabReselectedListener;
    }

    /**
     * 设置viewpager监听
     *
     * @param listener
     */
    public void setOnPageChangeListener(ViewPager.OnPageChangeListener listener) {
        this.pageChangeListener = listener;
    }

    /**
     * 更新选中状态
     *
     * @param position
     */
    private void updateSelection(int position) {
        for (int i = 0; i < this.tabCount; ++i) {
            View tv = this.tabsContainer.getChildAt(i);
            boolean selected = i == position;
            if (selected) {
                this.select(tv);
            } else {
                this.unSelect(tv);
            }
        }
    }

    /**
     * 取消选中状态
     *
     * @param tab
     */
    private void unSelect(View tab) {
        if (tab != null) {
            TextView tabTitle = (TextView) tab.findViewById(R.id.psts_tab_title);
            if (tabTitle != null) {
                tabTitle.setSelected(false);
                this.tabTextTypefaceStyle = 0;
                tabTitle.setTypeface(this.tabTextTypeface, this.tabTextTypefaceStyle);
                tabTitle.setTextSize(0, (float) this.tabUnSelectTextSize);
                TextPaint tp = tabTitle.getPaint();
                if (tp != null) {
                    tp.setFakeBoldText(false);
                }
            }

            if (this.isCustomTabs) {
                ((CustomTabProvider) this.viewPager.getAdapter()).tabUnselected(tab);
            }
        }

    }

    /**
     * 选中tab
     *
     * @param tab
     */
    private void select(View tab) {
        if (tab != null) {
            TextView tabTitle = (TextView) tab.findViewById(R.id.psts_tab_title);
            if (tabTitle != null) {
                tabTitle.setSelected(true);
                this.tabTextTypefaceStyle = 1;
                tabTitle.setTextSize(0, (float) this.tabTextSize);
                TextPaint tp = tabTitle.getPaint();
                if (tp != null) {
                    tp.setFakeBoldText(true);
                }
            }

            if (this.isCustomTabs) {
                ((CustomTabProvider) this.viewPager.getAdapter()).tabSelected(tab);
            }
        }

    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (this.isEnableUnRegistedObserver && this.viewPager != null && !this.adapterObserver.isAttached()) {
            this.viewPager.getAdapter().registerDataSetObserver(this.adapterObserver);
            this.adapterObserver.setAttached(true);
        }

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (this.isEnableUnRegistedObserver && this.viewPager != null && this.adapterObserver.isAttached()) {
            this.viewPager.getAdapter().unregisterDataSetObserver(this.adapterObserver);
            this.adapterObserver.setAttached(false);
        }

    }

    @Override
    public void onRestoreInstanceState(Parcelable state) {
        if (!(state instanceof SavedState)) {
            super.onRestoreInstanceState(state);
            return;
        }
        SavedState savedState = (SavedState) state;
        super.onRestoreInstanceState(savedState.getSuperState());
        this.currentPosition = savedState.currentPosition;
        if (this.currentPosition != 0 && this.tabsContainer.getChildCount() > 0) {
            this.unSelect(this.tabsContainer.getChildAt(0));
            this.select(this.tabsContainer.getChildAt(this.currentPosition));
        }

        this.requestLayout();
    }

    @Override
    public Parcelable onSaveInstanceState() {
        Parcelable superState = super.onSaveInstanceState();
        SavedState savedState = new SavedState(superState);
        savedState.currentPosition = this.currentPosition;
        return savedState;
    }

    /**
     * @return
     */
    public int getIndicatorColor() {
        return this.indicatorColor;
    }

    /**
     * @return
     */
    public int getIndicatorHeight() {
        return this.indicatorHeight;
    }

    /**
     * @return
     */
    public int getUnderlineColor() {
        return this.underlineColor;
    }

    /**
     * @return
     */
    public int getDividerColor() {
        return this.dividerColor;
    }

    /**
     * @return
     */
    public int getDividerWidth() {
        return this.dividerWidth;
    }

    /**
     * @return
     */
    public int getUnderlineHeight() {
        return this.underlineHeight;
    }

    /**
     * @return
     */
    public int getDividerPadding() {
        return this.dividerPadding;
    }

    /**
     * @return
     */
    public int getScrollOffset() {
        return this.scrollOffset;
    }

    /**
     * @return
     */
    public boolean getShouldExpand() {
        return this.isExpandTabs;
    }

    /**
     * @return
     */
    public int getTextSize() {
        return this.tabTextSize;
    }

    /**
     * @return
     */
    public boolean isTextAllCaps() {
        return this.isTabTextAllCaps;
    }

    /**
     * @return
     */
    public ColorStateList getTextColor() {
        return this.tabTextColor;
    }

    /**
     * @return
     */
    public int getTabBackground() {
        return this.tabBackgroundResId;
    }

    /**
     * @return
     */
    public int getTabPaddingLeftRight() {
        return this.tabPadding;
    }

    /**
     * @return
     */
    public LinearLayout getTabsContainer() {
        return this.tabsContainer;
    }

    /**
     * @return
     */
    public int getTabCount() {
        return this.tabCount;
    }

    /**
     * @return
     */
    public int getCurrentPosition() {
        return this.currentPosition;
    }

    /**
     * @return
     */
    public float getCurrentPositionOffset() {
        return this.currentPositionOffset;
    }

    /**
     * @param indicatorColor
     */
    public void setIndicatorColor(int indicatorColor) {
        this.indicatorColor = indicatorColor;
        this.invalidate();
    }

    /**
     * @param alpha
     */
    public void setIndicatorPorgress(float alpha) {
        indicatorColorAlpha = (int) (alpha * 255);
        invalidate();
    }

    /**
     * @param resId
     */
    public void setIndicatorColorResource(int resId) {
        this.indicatorColor = ContextCompat.getColor(this.getContext(), resId);
        this.invalidate();
    }

    /**
     * @param indicatorLineHeightPx
     */
    public void setIndicatorHeight(int indicatorLineHeightPx) {
        this.indicatorHeight = indicatorLineHeightPx;
        this.setTabsContainerParentViewPaddings();
        this.invalidate();
    }

    /**
     * @param underlineColor
     */
    public void setUnderlineColor(int underlineColor) {
        this.underlineColor = underlineColor;
        this.invalidate();
    }

    /**
     * @param resId
     */
    public void setUnderlineColorResource(int resId) {
        this.underlineColor = ContextCompat.getColor(this.getContext(), resId);
        this.invalidate();
    }

    /**
     * @param dividerColor
     */
    public void setDividerColor(int dividerColor) {
        this.dividerColor = dividerColor;
        this.invalidate();
    }

    /**
     * @param resId
     */
    public void setDividerColorResource(int resId) {
        this.dividerColor = ContextCompat.getColor(this.getContext(), resId);
        this.invalidate();
    }

    /**
     * @param dividerWidthPx
     */
    public void setDividerWidth(int dividerWidthPx) {
        this.dividerWidth = dividerWidthPx;
        this.invalidate();
    }

    /**
     * @param underlineHeightPx
     */
    public void setUnderlineHeight(int underlineHeightPx) {
        this.underlineHeight = underlineHeightPx;
        this.invalidate();
    }

    /**
     * @param dividerPaddingPx
     */
    public void setDividerPadding(int dividerPaddingPx) {
        this.dividerPadding = dividerPaddingPx;
        this.invalidate();
    }

    /**
     * @param scrollOffsetPx
     */
    public void setScrollOffset(int scrollOffsetPx) {
        this.scrollOffset = scrollOffsetPx;
        this.invalidate();
    }

    /**
     * @param shouldExpand
     */
    public void setShouldExpand(boolean shouldExpand) {
        this.isExpandTabs = shouldExpand;
        if (this.viewPager != null) {
            this.requestLayout();
        }

    }

    /**
     * @return
     */
    public int getTabUnSelectTextSize() {
        return this.tabUnSelectTextSize;
    }

    /**
     * @param tabUnSelectTextSize
     */
    public void setTabUnSelectTextSize(int tabUnSelectTextSize) {
        this.tabUnSelectTextSize = tabUnSelectTextSize;
        this.updateTabStyles();
    }

    /**
     * @return
     */
    public int getSelectColor() {
        return this.selectColor;
    }

    /**
     * @param selectColor
     */
    public void setSelectColor(int selectColor) {
        this.selectColor = selectColor;
    }

    /**
     * @param textAllCaps
     */
    public void setAllCaps(boolean textAllCaps) {
        this.isTabTextAllCaps = textAllCaps;
    }

    /**
     * @param textSizePx
     */
    public void setTextSize(int textSizePx) {
        this.tabTextSize = textSizePx;
        this.updateTabStyles();
    }

    /**
     * @param resId
     */
    public void setTextColorResource(int resId) {
        this.setTextColor(ContextCompat.getColor(this.getContext(), resId));
    }

    /**
     * @param textColor
     */
    public void setTextColor(int textColor) {
        this.setTextColor(this.createColorStateList(textColor));
    }

    /**
     * @param resId
     */
    public void setTextColorStateListResource(int resId) {
        this.setTextColor(ContextCompat.getColorStateList(this.getContext(), resId));
    }

    /**
     * @param colorStateList
     */
    public void setTextColor(ColorStateList colorStateList) {
        this.tabTextColor = colorStateList;
        this.updateTabStyles();
    }

    /**
     * @param colorStateDefault
     * @return
     */
    private ColorStateList createColorStateList(int colorStateDefault) {
        return new ColorStateList(new int[][]{new int[0]}, new int[]{colorStateDefault});
    }

    /**
     * @param colorStatePressed
     * @param colorStateSelected
     * @param colorStateDefault
     * @return
     */
    private ColorStateList createColorStateList(int colorStatePressed, int colorStateSelected, int colorStateDefault) {
        return new ColorStateList(new int[][]{{16842919}, {16842913}, new int[0]}, new int[]{colorStatePressed, colorStateSelected, colorStateDefault});
    }

    /**
     * @param typeface
     * @param style
     */
    public void setTypeface(Typeface typeface, int style) {
        this.tabTextTypeface = typeface;
        this.tabTextTypefaceStyle = style;
        this.updateTabStyles();
    }

    /**
     * @param resId
     */
    public void setTabBackground(int resId) {
        this.tabBackgroundResId = resId;
    }

    /**
     * @param paddingPx
     */
    public void setTabPaddingLeftRight(int paddingPx) {
        this.tabPadding = paddingPx;
        this.updateTabStyles();
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (l > this.maxScroll) {
            this.maxScroll = l;
        }

    }

    /**
     *
     */
    public void resetScroll() {
        this.maxScroll = this.getScrollX();
    }

    /**
     *
     */
    public void releaseView() {
        this.maxScroll = 0;
        this.scrollTo(0, 0);
    }

    /**
     * @return
     */
    public int getMaxExpoPosition() {
        int totalWidth = this.getMeasuredWidth() + this.maxScroll;
        int position = 0;
        if (this.tabsContainer != null) {
            int width = 0;

            for (int i = 0; i < this.tabsContainer.getChildCount(); ++i) {
                if (this.tabsContainer.getChildAt(0) != null) {
                    width += this.tabsContainer.getChildAt(0).getWidth();
                    if (width >= totalWidth) {
                        position = i;
                        break;
                    }
                }
            }
        }

        return position;
    }

    /**
     * OnTabReselectedListener简介
     *
     * <AUTHOR>
     * @date 2019-6-25 16:34:46
     */
    public interface OnTabReselectedListener {
        /**
         * @param var1 重新选中位置
         */
        void onTabReselected(int var1);

        /**
         * @param var1 选中位置
         */
        void onTabSelected(int var1);
    }

    /**
     * CustomTabProvider简介
     *
     * <AUTHOR>
     * @date 2019-6-25 16:34:54
     */
    public interface CustomTabProvider {
        /**
         * @param var1 父view
         * @param var2 位置
         * @return
         */
        View getCustomTabView(ViewGroup var1, int var2);

        /**
         * @param var1 tabview
         */
        void tabSelected(View var1);

        /**
         * @param var1 tabview
         */
        void tabUnselected(View var1);
    }

    /**
     * SavedState简介
     *
     * <AUTHOR>
     * @date 2019-6-25 16:36:00
     */
    static class SavedState extends BaseSavedState {
        /**
         * currentPosition 当前的位置
         */
        int currentPosition;
        /**
         * CREATOR
         */
        public static final Creator<SavedState> CREATOR = new Creator<SavedState>() {
            ;

            @Override
            public SavedState createFromParcel(Parcel in) {
                return new SavedState(in);
            }

            @Override
            public SavedState[] newArray(int size) {
                return new SavedState[size];
            }
        };

        /**
         * @param superState
         */
        SavedState(Parcelable superState) {
            super(superState);
        }

        /**
         * @param in
         */
        private SavedState(Parcel in) {
            super(in);
            this.currentPosition = in.readInt();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeInt(this.currentPosition);
        }
    }

    /**
     * PagerAdapterObserver简介
     *
     * <AUTHOR>
     * @date 2019-6-25 16:36:03
     */
    private class PagerAdapterObserver extends DataSetObserver {
        /**
         * attached 當前view是否已經綁定在界面
         */
        private boolean attached;

        /**
         *
         */
        private PagerAdapterObserver() {
            this.attached = false;
        }

        @Override
        public void onChanged() {
            PagerSlidingVpTabStrip.this.notifyDataSetChanged();
        }

        void setAttached(boolean attached) {
            this.attached = attached;
        }

        /**
         * @return 是否绑定
         */
        boolean isAttached() {
            return this.attached;
        }
    }

    /**
     * PageListener简介
     *
     * <AUTHOR>
     * @date 2019-6-25 16:36:06
     */
    private class PageListener implements ViewPager.OnPageChangeListener {
        /**
         *
         */
        private PageListener() {
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            PagerSlidingVpTabStrip.this.currentPosition = position;
            PagerSlidingVpTabStrip.this.currentPositionOffset = positionOffset;
            int offset = PagerSlidingVpTabStrip.this.tabCount > 0 ? (int) (positionOffset * (float) PagerSlidingVpTabStrip.this.tabsContainer.getChildAt(position).getWidth()) : 0;
            //PagerSlidingTabStrip.this.scrollToChild(position, offset);
            PagerSlidingVpTabStrip.this.invalidate();
            if (PagerSlidingVpTabStrip.this.pageChangeListener != null) {
                PagerSlidingVpTabStrip.this.pageChangeListener.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            if (state == 0) {
//                PagerSlidingVpTabStrip.this.scrollToChild(PagerSlidingVpTabStrip.this.viewPager.getCurrentItem(), 0);
            }

            if (PagerSlidingVpTabStrip.this.pageChangeListener != null) {
                PagerSlidingVpTabStrip.this.pageChangeListener.onPageScrollStateChanged(state);
            }

        }

        @Override
        public void onPageSelected(int position) {
            PagerSlidingVpTabStrip.this.updateSelection(position);
            View currentTab = PagerSlidingVpTabStrip.this.tabsContainer.getChildAt(position);
            PagerSlidingVpTabStrip.this.select(currentTab);
            View nextTab;
            if (position > 0) {
                nextTab = PagerSlidingVpTabStrip.this.tabsContainer.getChildAt(position - 1);
                PagerSlidingVpTabStrip.this.unSelect(nextTab);
            }

            if (position < PagerSlidingVpTabStrip.this.viewPager.getAdapter().getCount() - 1) {
                nextTab = PagerSlidingVpTabStrip.this.tabsContainer.getChildAt(position + 1);
                PagerSlidingVpTabStrip.this.unSelect(nextTab);
            }

            if (PagerSlidingVpTabStrip.this.pageChangeListener != null) {
                PagerSlidingVpTabStrip.this.pageChangeListener.onPageSelected(position);
            }

        }
    }
}
