package com.xstore.sevenfresh.floor.modules.vp.sliding;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.utils.UIUtils;
import com.xstore.sevenfresh.image.ImageloadUtils;

/**
 * RecommendHomeTabView简介
 * 推荐楼层tabview
 *
 * <AUTHOR>
 * @date 2019-6-25 16:42:33
 */
public class VpTabView extends FrameLayout {
    /**
     * TITLE_TXT_SIZE_SELECT 选中标题文字大小
     */
    public static final int TITLE_TXT_SIZE_SELECT = 14;
    /**
     * TITLE_TXT_SIZE_UNSELECT 未选中标题文字大小
     */
    public static final int TITLE_TXT_SIZE_UNSELECT = 13;
    /**
     * TITLE_MARGIN_TOP 主标题上边距（距顶部）
     */
    public static final int TITLE_MARGIN_TOP = 15;
    /**
     * SUB_TITLE_TXT_SIZE 副标题大小
     */
    public static final int SUB_TITLE_TXT_SIZE = 12;
    /**
     * SUB_TITLE_MARGIN_TOP 副标题上边距（距顶部）
     */
    public static final int SUB_TITLE_MARGIN_TOP = 38;
    /**
     * TBA_PADDING_LR tab左右间距
     */
    public static double TBA_PADDING_LR = 16;
    /**
     * TBA_MARGIN_T tab上间距
     */
    public static double TBA_MARGIN_T = 10;

    /**
     * titleLayout 主标题
     */
    private RelativeLayout titleLayout;

    /**
     * titleTV 主标题
     */
    private TextView titleTV;
    /**
     * titleTV 主标题 图片
     */
    private ImageView ivTitle;
    /**
     * subTitleTV 副标题
     */
    private TextView subTitleTV;
    /**
     * Tab选中指示器
     */
    private ImageView tabIndicator;
    /**
     * isSelected 当前是否选中
     */
    private boolean isSelected;
    /**
     * recommendTab tab数据
     */
    private VPTabBean recommendTab;

    public VPTabBean getRecommendTab() {
        return recommendTab;
    }

    /**
     * hasSubtitle 是否存在子标题
     */
    private boolean hasSubtitle;

    View rootView;
    /**
     * wh 宽高
     */
    private int[] wh;

    /**
     * @param context
     */
    public VpTabView(Context context) {
        this(context, (AttributeSet) null);
    }

    /**
     * @param context
     * @param attrs
     */
    public VpTabView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * @param context
     * @param attrs
     * @param defStyleAttr
     */
    public VpTabView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.hasSubtitle = true;
        this.wh = new int[]{0, 0};
        rootView = LayoutInflater.from(context).inflate(R.layout.recommend_vp_tab_b, (ViewGroup) null, true);
        this.titleLayout = rootView.findViewById(R.id.recommend_home_tab_b);
        this.titleTV = rootView.findViewById(R.id.recommend_home_tab_b_title);
//        this.titleTV.setPadding(0, DPIUtil.getWidthByDesignValue(TITLE_MARGIN_TOP, 375), 0, 0);
        this.ivTitle = rootView.findViewById(R.id.iv_recommend_home_tab_b_title);

        this.subTitleTV = rootView.findViewById(R.id.recommend_home_tab_b_sub_title);
//        this.subTitleTV.setTextSize(0, (float) DPIUtil.getWidthByDesignValue(SUB_TITLE_TXT_SIZE, 375));
//        if (this.subTitleTV.getLayoutParams() != null && this.subTitleTV.getLayoutParams() instanceof RelativeLayout.LayoutParams) {
//            ((RelativeLayout.LayoutParams) this.subTitleTV.getLayoutParams()).topMargin = DPIUtil.getWidthByDesignValue(SUB_TITLE_MARGIN_TOP, 375);
//            subTitleTV.setLayoutParams(subTitleTV.getLayoutParams());
//        }
        tabIndicator = rootView.findViewById(R.id.tab_indicator);
        int padding = UIUtils.getWidthByDesignValueFitFold(TBA_PADDING_LR, 375);
//        rootView.setPadding(padding, 0, padding, 0);
        this.addView(rootView);
    }

    public TextView getTitleTV() {
        return titleTV;
    }

    public TextView getSubTitleTV() {
        return subTitleTV;
    }

    /**
     * 设置选中状态
     *
     * @param select
     */
    public void setTabSelect(boolean select) {
        this.isSelected = select;
        this.updateView();
    }

    //根据tab数据设置tabview宽度  小于等于4个平分屏幕宽度
    public void setTabWidth(int size) {
        if (size <= 4) {
            int width = UIUtils.getScreenWidth() / size;
            ViewGroup.LayoutParams lp = titleLayout.getLayoutParams();
            lp.width = width;
            titleLayout.setLayoutParams(lp);
            rootView.setPadding(0, 0, 0, 0);

        }
    }

    //设置tabIndicator是否可见
    public void setTabIndicatorVisibility() {
//        if (isNullByString(recommendTab.getSelectImg())) {
//            tabIndicator.setVisibility(View.INVISIBLE);
//        } else {
//            tabIndicator.setVisibility(View.VISIBLE);
//        }

    }

    /**
     * 设置Tab数据
     *
     * @param tab
     * @param position
     */
    public void setRecommendTab(VPTabBean tab, int position) {
        this.recommendTab = tab;
        ImageloadUtils.loadImage(getContext(), tabIndicator, recommendTab.getSelectImg());
        if (this.recommendTab != null) {
            if (!isNullByString(tab.getSelectedImgUrl())) {
                //重新计算ivTitle的宽度
                setIvTitleWidthAndImage(tab, isSelected);
                this.ivTitle.setVisibility(VISIBLE);
                this.titleTV.setVisibility(GONE);
            } else {
                this.ivTitle.setVisibility(GONE);
                this.titleTV.setVisibility(VISIBLE);
                if (!isNullByString(tab.getTitle())) {
                    this.titleTV.setText(tab.getTitle());
                }
                if (position == 0) {
                    titleTV.setTextSize(0, (float) UIUtils.getWidthByDesignValueFitFold(TITLE_TXT_SIZE_SELECT, 375));
                } else {
                    titleTV.setTextSize(0, (float) UIUtils.getWidthByDesignValueFitFold(TITLE_TXT_SIZE_UNSELECT, 375));
                }
            }
        }
    }

    /**
     * 更新tab状态
     */
    private void updateView() {
        if (this.isSelected) {
            if (!isNullByString(recommendTab.getSelectedImgUrl())) {
                //重新计算ivTitle的宽度
                setIvTitleWidthAndImage(recommendTab, isSelected);

                this.ivTitle.setVisibility(VISIBLE);
                this.titleTV.setVisibility(GONE);
            } else {
                this.ivTitle.setVisibility(GONE);
                titleTV.setVisibility(VISIBLE);
                titleTV.getPaint().setFakeBoldText(true);//加粗
//                titleTV.setTextColor(ContextCompat.getColor(getContext(), R.color.sf_theme_color_level_1));

                if (isNullByString(recommendTab.getTitleSelectColor())) {
                    titleTV.setTextColor(getResources().getColor(R.color.color_tab_select));
                } else {
                    //颜色值不对时，使用兜底颜色
                    try {
                        titleTV.setTextColor(Color.parseColor(recommendTab.getTitleSelectColor()));
                    } catch (Exception e) {
                        titleTV.setTextColor(getResources().getColor(R.color.color_tab_select));
                    }
                }

                titleTV.setTextSize(0, (float) UIUtils.getWidthByDesignValueFitFold(TITLE_TXT_SIZE_SELECT, 375));
            }
//            subTitleTV.setBackgroundResource(R.drawable.home_recommend_sub_title);
//            this.setTextColor(this.subTitleTV, getContext().getString(R.string.color_str_ffffff));
            setTabIndicatorVisibility();
        } else {
            if (!isNullByString(recommendTab.getNoSelectedImgUrl())) {
                setIvTitleWidthAndImage(recommendTab, isSelected);

                this.ivTitle.setVisibility(VISIBLE);
                this.titleTV.setVisibility(GONE);
            } else {
                this.ivTitle.setVisibility(GONE);
                titleTV.setVisibility(VISIBLE);
                titleTV.getPaint().setFakeBoldText(false);//取消加粗
                //返回颜色是空  默认#1A1A1A
                if (isNullByString(recommendTab.getTitleColor())) {
                    titleTV.setTextColor(ContextCompat.getColor(getContext(), R.color.color_tab_unselect));
                } else {
                    //颜色值不对时，使用兜底颜色
                    try {
                        titleTV.setTextColor(Color.parseColor(recommendTab.getTitleColor()));
                    } catch (Exception e) {
                        titleTV.setTextColor(ContextCompat.getColor(getContext(), R.color.color_tab_unselect));
                    }
                }

                titleTV.setTextSize(0, (float) UIUtils.getWidthByDesignValueFitFold(TITLE_TXT_SIZE_UNSELECT, 375));
            }
//            subTitleTV.setBackgroundResource(0);
//            this.setTextColor(this.subTitleTV, getContext().getString(R.string.color_str_8A8A8A));
            tabIndicator.setVisibility(View.INVISIBLE);
        }
    }

    private void setIvTitleWidthAndImage(VPTabBean recommendTab, boolean isSelected) {
        //计算ivTitle的宽度
        ViewGroup.LayoutParams lp = ivTitle.getLayoutParams();
        VPTabBean.TabProp prop = isSelected ? recommendTab.getSelectProp() : recommendTab.getNormalProp();
        lp.width = prop.getWidth() * UIUtils.dp2px(16) / prop.getHeight();
        ivTitle.setLayoutParams(lp);
        ImageloadUtils.loadImage(getContext(), ivTitle, isSelected ? recommendTab.getSelectedImgUrl() : recommendTab.getNoSelectedImgUrl());
    }

    // 字符串是否为空
    public static boolean isNullByString(String str) {
        return str == null || "".equals(str) || "null".equals(str);
    }

    /**
     * 设置子标题展示状态
     *
     * @param has
     */
    public void setHasSubTitle(boolean has) {
        this.hasSubtitle = has;
//        if (this.hasSubtitle) {
//            this.subTitleTV.setVisibility(INVISIBLE);
//        } else {
//            this.subTitleTV.setVisibility(GONE);
//        }
    }

    /**
     * 设置宽高
     *
     * @param width
     * @param height
     */
    public void setWH(int width, int height) {
        this.wh[0] = width;
        this.wh[1] = height;
    }

    /**
     * @return 获取宽高
     */
    public int[] getWH() {
        return this.wh;
    }

    /**
     * 动画进度
     *
     * @param progress
     */
    public void setChangeProgress(float progress) {
//        if (this.hasSubtitle) {
//            if (this.subTitleTV != null) {
//                this.subTitleTV.setAlpha(progress);
//            }
//        }
    }
}
