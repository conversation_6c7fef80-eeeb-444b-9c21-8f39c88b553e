package com.xstore.sevenfresh.floor.modules;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.constant.SpinnerStyle;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sdk.floor.floorcore.HomeRefreshManager;

import androidx.annotation.NonNull;

/**
 * 首页刷新头部
 *
 * <AUTHOR>
 */
public class HomeRefreshHeaderNew extends LinearLayout implements RefreshHeader {

    /**
     * context
     */
    private Context context;
    /**
     * 刷新中动效
     */
    private ProgressBar refreshProgressBar;
    /**
     * 刷新状态指示器
     */
    private ImageView ivRefreshArrow;
    /**
     * 刷新状态描述
     */
    private TextView tvRefreshStatus;
    /**
     * 是否存在二楼
     */
    private boolean hasSecondFloor;

    /**
     * 构造方法
     *
     * @param context
     */
    public HomeRefreshHeaderNew(Context context) {
        this(context, null);
    }

    /**
     * 构造方法
     *
     * @param context
     * @param attrs
     */
    public HomeRefreshHeaderNew(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        initView();
    }

    /**
     * 初始化view
     */
    public void initView() {
        setGravity(Gravity.BOTTOM);
        View view = LayoutInflater.from(getContext()).inflate(R.layout.sf_floor_base_home_refresh_header_new, this);
        refreshProgressBar = view.findViewById(R.id.pb_refresh_progressBar);
        ivRefreshArrow = view.findViewById(R.id.iv_refresh_arrow);
        tvRefreshStatus = view.findViewById(R.id.tv_refresh_status);
    }

    @NonNull
    @Override
    public View getView() {
        return this;
    }

    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    @Override
    public void setPrimaryColors(int... colors) {
    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {
    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        //延迟500毫秒之后再弹回
        return 500;
    }

    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {
    }

    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    public void updateStyle(boolean hasSecondFloor) {
        this.hasSecondFloor = hasSecondFloor;
        boolean setWhite = HomeRefreshManager.getInstance().useNewRefreshStyle() || hasSecondFloor;

        ivRefreshArrow.setImageResource(setWhite ? R.drawable.sf_floor_base_ic_white_arrow_down : R.drawable.arrow_down_icon);
        tvRefreshStatus.setTextColor(getResources().getColor(setWhite ? R.color.sf_floor_core_white : R.color.font_C_secondary_info_color_black_gray));
        Drawable drawable = getResources().getDrawable(setWhite ? R.drawable.sf_floor_base_home_refresh_progress_style : R.drawable.bdcodehelper_refresh_progress_style);
        Rect bounds = refreshProgressBar.getIndeterminateDrawable().getBounds(); // re-use bounds from current drawable
        refreshProgressBar.setIndeterminateDrawable(drawable); // set new drawable
        refreshProgressBar.getIndeterminateDrawable().setBounds(bounds);
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        if (newState == RefreshState.None) {
            setVisibility(INVISIBLE);
        } else {
            setVisibility(VISIBLE);
        }
        switch (newState) {
            case None:
            case PullDownToRefresh:
                updateStyle(hasSecondFloor);
                tvRefreshStatus.setText(R.string.sf_floor_base_refresh_header_pulling);
                refreshProgressBar.setVisibility(GONE);
                ivRefreshArrow.setVisibility(VISIBLE);
                ivRefreshArrow.animate().rotation(0);
                break;
            case Refreshing:
            case RefreshReleased:
                tvRefreshStatus.setText(R.string.sf_floor_base_refresh_header_refreshing);
                ivRefreshArrow.setVisibility(GONE);
                refreshProgressBar.setVisibility(VISIBLE);
                break;
            case ReleaseToRefresh:
                tvRefreshStatus.setText(R.string.sf_floor_base_refresh_header_release);
                refreshProgressBar.setVisibility(GONE);
                ivRefreshArrow.setVisibility(VISIBLE);
                ivRefreshArrow.animate().rotation(0);
                break;
            case ReleaseToTwoLevel:
                tvRefreshStatus.setText(R.string.sf_floor_base_refresh_header_secondary);
                refreshProgressBar.setVisibility(GONE);
                ivRefreshArrow.setVisibility(VISIBLE);
                ivRefreshArrow.animate().rotation(180);
                break;
            default:
                break;
        }
    }
}
