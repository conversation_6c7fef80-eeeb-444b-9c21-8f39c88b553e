package com.xstore.sevenfresh.floor.modules;

import android.text.TextUtils;
import android.util.Log;


import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.boredream.bdcodehelper.utils.StringUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sdk.floor.floorcore.interfaces.FloorBaseInterface;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

/**
 * 楼层注册管理类，通过包名注册代理类，
 */
public class FloorManagerUtils {
    // 楼层key 楼层className 通过注解 自动注册
    private Map<Integer, String> floorMap = new HashMap<>();
    //首页楼层不允许缓存楼层 这里缓存的是接口，用来调用一些公共逻辑方法 比如出具处理 写法待优化
    private Map<Integer, FloorBaseInterface> floorInterfaces = new HashMap<>();
    private static FloorManagerUtils instance;

    private FloorManagerUtils() {
    }

//    public void loadDesData(Context context) {
//        if (context == null) {
//            return;
//        }
//        BufferedReader readerStream = null;
//        InputStreamReader inputStreamReader = null;
//        try {
//            inputStreamReader = new InputStreamReader(context.getAssets().open("FloorsInfo.json"));
//            readerStream = new BufferedReader(inputStreamReader);
//            String line;
//            StringBuilder result = new StringBuilder();
//            while ((line = readerStream.readLine()) != null) {
//                result.append(line);
//            }
//            floorMap = json2map(result.toString());
//        } catch (Exception e) {
//            floorMap = null;
//        } finally {
//            if (readerStream != null) {
//                try {
//                    readerStream.close();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//            if (inputStreamReader != null) {
//                try {
//                    inputStreamReader.close();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }

//    private Map<Integer, String> json2map(String str_json) {
//        Map<Integer, String> res = null;
//        try {
//            Gson gson = new Gson();
//            res = gson.fromJson(str_json, new TypeToken<Map<Integer, String>>() {
//            }.getType());
//        } catch (JsonSyntaxException e) {
//            LogUtils.logV("JsonSyntaxException ---> " + e.getMessage());
//        }
//        return res;
//    }

    public static FloorManagerUtils getInstance() {
        if (instance == null) {
            instance = new FloorManagerUtils();
        }
        return instance;
    }

    public FloorBaseInterface getFloorClass(int viewType) {
        if (floorMap == null || floorMap.isEmpty()) {
            SFLogCollector.d(FloorContainer.TAG, "floorMap is null, beacause loadDesData method is not call");
            return null;
        }
        FloorBaseInterface floorViewClass = null;
        try {
            if (floorViewClass == null) {
                Class c1 = null;
                String className = floorMap.get(viewType);
                SFLogCollector.d(FloorContainer.TAG, "getFloorClass type:" + viewType + " className:" + className);
                if (!TextUtils.isEmpty(className)) {
                    c1 = Class.forName(floorMap.get(viewType));
                    if (c1 != null) {
                        Object floor = c1.newInstance();
                        if (floor instanceof FloorBaseInterface) {
                            floorInterfaces.put(viewType, (FloorBaseInterface) floor);
                            floorViewClass = (FloorBaseInterface) floor;
                        } else {
                            SFLogCollector.e(FloorContainer.TAG, "getFloorClass type:" + viewType + " not imp FloorBaseInterface");
                        }
                    } else {
                        SFLogCollector.e(FloorContainer.TAG, "getFloorClass type:" + viewType + " newInstance fail");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (floorViewClass == null) {
            SFLogCollector.e(FloorContainer.TAG, "viewType floorViewClass  is null");
        }
        return floorViewClass;
    }


    /**
     *  当前没用注解 先手动注册下
     *
     * @param templateCode 楼层id
     * @param floorCls     楼层类名
     * @return
     */
    public FloorManagerUtils registerFloor(String templateCode, String floorCls) {
        floorMap.put(templateCode.hashCode(), floorCls);
        return this;
    }

    /**
     * 进行无效楼层过滤
     *
     * @param bean 需要进行过滤判断的楼层数据
     * @return 如果是静态楼层 则返回楼层对应的类型
     * 如果是动态楼层 则返回JDJsonObject
     * 或者多个 <code>fieldName-=》 HashMap &lt; String, JDJsonObject &gt;  </code>
     */
    public Object filterFloor(FloorDetailBean bean, boolean isCache) {
        FloorBaseInterface floorInterface = floorInterfaces.get(bean.getTemplateHashCode());
        if (floorInterface == null) {
            floorInterface = getFloorClass(bean.getTemplateHashCode());
        }
        if (floorInterface == null) {
            return null;
        }

        //SGM上报
        if(bean.getDataStatus() != 200 //dataStatus不等于200
                && !StringUtils.isEmpty(bean.getTemplateCode())//判空
                && !bean.getTemplateCode().equals(FloorDataManager.getInstance().getTenantShopInfoFloorTemplate())){//需要过滤拖地门店列表
            try {
                SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                errorLog.type = 9502;
                errorLog.errorCode = "首页_单个楼层数据返回异常"+"_"+bean.getComponentName()+"_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
                if(StringUtil.isNotEmpty(bean.getTemplateCode())) {
                    errorLog.ext1 = "TemplateCode=" + bean.getTemplateCode() + "&&"+ "DataStatus="+bean.getDataStatus();
                }
                errorLog.location = "首页";
                SFLogCollector.reportBusinessErrorLog(errorLog);
            }catch (Exception e){

            }

        }

        Object res = null;
        try {
            res = floorInterface.convertData(bean, isCache);
//            Log.e("FloorManagerUtils","楼层="+bean.getComponentName()+"------templateCode="+bean.getTemplateCode());
        }catch (Exception e){
            e.printStackTrace();
            SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
            errorLog.type = 9502;
            errorLog.errorCode = "首页_单个楼层数据解析异常"+"_"+bean.getComponentName()+"_门店:" +AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
            errorLog.errorMsg = Arrays.toString(e.getStackTrace());
            errorLog.location = "首页";
            SFLogCollector.reportBusinessErrorLog(errorLog);
        }
        return res;
    }

    /**
     * 清除数据
     */
    public void destroy() {
        if (floorInterfaces != null) {
            floorInterfaces.clear();
        }
    }

}
