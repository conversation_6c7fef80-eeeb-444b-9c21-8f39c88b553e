package com.xstore.sevenfresh.floor.modules;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sdk.floor.floorcore.interfaces.FloorBaseInterface;
import com.xstore.sdk.floor.floorcore.interfaces.FloorLifecycle;
import com.xstore.sdk.floor.floorcore.interfaces.FloorScrollInterface;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;


public class ShellRecycleViewAdapter extends RecyclerView.Adapter<FloorBaseViewHolder> implements FloorLifecycle, FloorScrollInterface {
    private final Context context;
    private final FloorContainer floorContainer;
    private List<FloorDetailBean> mListData;
    /**
     * 这里强持有了所有的viewHolder 就算回收也会在触发！！！！
     */
    private HashSet<FloorBaseInterface> allViewHolder = new HashSet<>();

    public HashSet<FloorBaseInterface> getAllViewHolder(){
        return allViewHolder;
    }


    public ShellRecycleViewAdapter(Context context, FloorContainer floorContainer) {
        this.context = context;
        this.floorContainer = floorContainer;
    }

    public void setData(List<FloorDetailBean> listData) {
        if (mListData == null) {
            mListData = new ArrayList<>();
        }
        mListData.clear();
        if (listData != null) {
            mListData.addAll(listData);
        }
        notifyDataSetChanged();
    }

    public void insertData(List<FloorDetailBean> data, int index) {
        if (data == null || data.isEmpty()) {
            return;
        }
        if (mListData == null || mListData.size() == 0) {
            setData(data);
            return;
        }
        if (index > mListData.size()) {
            mListData.addAll(data);
        } else {
            mListData.addAll(index, data);
        }
        notifyItemRangeInserted(index, data.size());
    }

    public void insertDataNotifyAll(List<FloorDetailBean> data, int index) {
        if (data == null || data.isEmpty()) {
            return;
        }
        if (mListData == null || mListData.size() == 0) {
            setData(data);
            return;
        }
        if (index > mListData.size()) {
            mListData.addAll(data);
        } else {
            mListData.addAll(index, data);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public FloorBaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        try {
            FloorBaseInterface viewInterface = FloorManagerUtils.getInstance().getFloorClass(viewType);
            View view = null;
            if (viewInterface == null) {
                // 当前楼层本版本不支持，理论上应该在数据上过滤，不然曝光等楼层位置不正确
                view = new View(context);
            } else {
                view = viewInterface.createView(context, floorContainer);
                allViewHolder.add(viewInterface);
            }
            SFLogCollector.d(FloorContainer.TAG, "onCreateViewHolder viewType:" + viewType + " viewInterface" + viewInterface);
            return new FloorBaseViewHolder(view, viewInterface);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        return new FloorBaseViewHolder(new View(context), null);
    }

    @Override
    public void onBindViewHolder(@NonNull FloorBaseViewHolder holder, int position) {
        try {
            long timeStartBindData = System.currentTimeMillis();
            FloorDetailBean floorDetailBean = mListData.get(position);
            if (holder.floorBaseInterface != null) {
                holder.floorBaseInterface.bindData(context, floorContainer, holder, floorDetailBean, position);
            }
            SFLogCollector.d(FloorContainer.TAG, "onBindViewHolder pos:" + position + " viewInterface" + holder.floorBaseInterface);
        } catch (Exception e) {
            try {
                FloorDetailBean floorDetailBean = mListData.get(position);
                SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                errorLog.type = 9502;
                errorLog.errorCode = "首页_单个楼层数据渲染异常" + "_" + floorDetailBean.getComponentName() + "_门店:" + AddressStoreHelper.getAddressStoreBean().getStoreName() + TenantIdUtils.getStoreId();
                errorLog.errorMsg = Arrays.toString(e.getStackTrace());
                errorLog.location = "首页";
                SFLogCollector.reportBusinessErrorLog(errorLog);
            } catch (Exception e1) {

            }
             e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public int getItemViewType(int position) {
        FloorDetailBean floorDetailBean = getItemData(position);
        if (floorDetailBean != null) {
            //因为模板id是字符串
            return floorDetailBean.getTemplateHashCode();
        }
        return super.getItemViewType(position);
    }

    public FloorDetailBean getItemData(int position) {
        if (position >= 0 && mListData != null && mListData.size() > position) {
            return mListData.get(position);
        }
        return null;
    }

    public void removeItem(int position) {
        mListData.remove(position);
        notifyItemRemoved(position);
    }

    @Override
    public int getItemCount() {
        if (mListData != null) {
            return mListData.size();
        }
        return 0;
    }


    @Override
    public void setHasStableIds(boolean hasStableIds) {
        super.setHasStableIds(hasStableIds);
    }


    @Override
    public void onViewAttachedToWindow(@NonNull FloorBaseViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        try {
            if (holder.floorBaseInterface != null) {
                //分发
                holder.floorBaseInterface.onViewAttachedToWindow();
                if (holder.floorBaseInterface.isFullSpan()) {
                    setFullSpan(holder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull FloorBaseViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        try {
            if (holder.floorBaseInterface != null) {
                holder.floorBaseInterface.onViewDetachedFromWindow();
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public void onViewRecycled(@NonNull FloorBaseViewHolder holder) {
        super.onViewRecycled(holder);
        try {
            if (holder.floorBaseInterface != null) {
                holder.floorBaseInterface.onViewRecycled();
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public boolean onFailedToRecycleView(@NonNull FloorBaseViewHolder holder) {
        try {
            if (holder.floorBaseInterface != null) {
                Boolean needRecycle = holder.floorBaseInterface.onFailedToRecycleView();
                if (needRecycle == null) {
                    return super.onFailedToRecycleView(holder);
                } else {
                    return needRecycle;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        return super.onFailedToRecycleView(holder);
    }

    private void setFullSpan(FloorBaseViewHolder holder) {
        try {
            if (holder.itemView.getLayoutParams() instanceof StaggeredGridLayoutManager.LayoutParams) {
                StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) holder
                        .itemView.getLayoutParams();
                params.setFullSpan(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
    }

    @Override
    public void onResume(boolean hidden) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onResume(hidden);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onPause() {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onPause();
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onHiddenChange(boolean hidden) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onHiddenChange(hidden);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onDestroy() {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onDestroy();
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onActivityResult(requestCode, resultCode, data);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onRequestPermissionsResult(requestCode, permissions, grantResults);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onScroll(int dx, int dy) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onScroll(dx, dy);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    @Override
    public void onHeaderMoving(boolean isDragging, float percent, int offset, int headerHeight, int maxDragHeight) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                holder.onHeaderMoving(isDragging, percent, offset, headerHeight, maxDragHeight);
            }
        }
    }

    /**
     * @param templateCode 使用模板id作为判断时，还要关注存在多个相同楼层时，哪个楼层要接受数据
     * @param json
     */
    public void postEvent(String templateCode, String json) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onFloorUpdateEvent(templateCode, json);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    /**
     * @param floorBaseInterface
     */
    public void addViewHolderInterface(FloorBaseInterface floorBaseInterface) {
        allViewHolder.add(floorBaseInterface);
    }

    @Override
    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onScrollStateChanged(recyclerView, newState);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    public void postFloorEvent(String templateCode, Object event) {
        if (allViewHolder != null) {
            for (FloorBaseInterface holder : allViewHolder) {
                try {
                    holder.onFloorUpdateEvent(templateCode, event);
                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        }
    }

    public List<FloorDetailBean> getData() {
        return mListData;
    }


}
