package com.xstore.sevenfresh.floor.modules;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.boredream.bdcodehelper.utils.NoDoubleClickUtils;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.gyf.barlibrary.ImmersionBar;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.megabox.android.slide.SlideBackActivity;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleMultiListener;
import com.xstore.floorsdk.fieldhome.BuildConfig;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sdk.floor.floorcore.FloorBaseNetwork;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.FloorJumpManager;
import com.xstore.sdk.floor.floorcore.HomeRefreshManager;
import com.xstore.sdk.floor.floorcore.bean.BannerChangeEvent;
import com.xstore.sdk.floor.floorcore.bean.CommonEvent;
import com.xstore.sdk.floor.floorcore.bean.FloorAction;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sdk.floor.floorcore.bean.SecondFloorBean;
import com.xstore.sdk.floor.floorcore.bean.SecondFloorChangeEvent;
import com.xstore.sdk.floor.floorcore.interfaces.FloorBaseInterface;
import com.xstore.sdk.floor.floorcore.interfaces.FloorContainerInterface;
import com.xstore.sdk.floor.floorcore.interfaces.FloorLifecycle;
import com.xstore.sdk.floor.floorcore.interfaces.FloorViewInterface;
import com.xstore.sdk.floor.floorcore.ma.FloorBaseMaEntity;
import com.xstore.sdk.floor.floorcore.utils.RecyclerViewUtils;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.utils.Utils;
import com.xstore.sdk.floor.floorcore.widget.DialogUtils;
import com.xstore.sevenfresh.addressstore.utils.AddressStoreHelper;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.interfaces.ContainerLiveCallBack;
import com.xstore.sevenfresh.floor.modules.interfaces.FloorDataCallback;
import com.xstore.sevenfresh.floor.modules.interfaces.FloorOtherDataCallback;
import com.xstore.sevenfresh.floor.modules.interfaces.OnScrollDistanceListener;
import com.xstore.sevenfresh.floor.modules.interfaces.RefreshStateChangeListener;
import com.xstore.sevenfresh.floor.modules.request.FloorNetwork;
import com.xstore.sevenfresh.floor.modules.request.RecommendRequest;
import com.xstore.sevenfresh.floor.modules.video.HomeFloorPlayHelper;
import com.xstore.sevenfresh.floor.modules.video.ShareAnimationPlayer;
import com.xstore.sevenfresh.floor.modules.vp.CategoryView;
import com.xstore.sevenfresh.floor.modules.vp.ViewPagerRecommendContent;
import com.xstore.sevenfresh.floor.modules.vp.ViewPagerRecommendFloor;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;
import com.xstore.sevenfresh.fresh_network_business.NetUtil;
import com.xstore.sevenfresh.image.ImageloadUtils;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.lbs.location.LocationHelper;
import com.xstore.sevenfresh.permission.PermissionUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.animation.ObjectAnimator;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.FloorRecyclerView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import static com.xstore.sevenfresh.permission.PermissionUtils.getNoGrantedPermission;


/**
 * 楼层容器
 */
public class FloorContainer extends RelativeLayout implements FloorDataCallback, FloorLifecycle, FloorContainerInterface {

    /**
     * 日志tag
     */
    public static final String TAG = "FloorContainer";
    /**
     * 是否要展示二楼引导
     */
    private static boolean SHOW_SECOND_FLOOR_GUIDE = true;

    /**
     * 上下文
     */
    private Context mContext;
    /**
     * 根布局
     */
    private View mRootView;
    /**
     * 适配器
     */
    private ShellRecycleViewAdapter mShellRecycleViewAdapter;


    /**
     * 下拉刷新控件
     */
    private FreshSmartRefreshLayout smartRefreshLayout;
    /**
     * 楼层列表
     */
    private FloorRecyclerView mFloorRecyclerView;
    /**
     * 埋点接口
     */
    private JDMaUtils.JdMaPageImp jdMaPageImp;

    /**
     * 算了先把他放这里了 数据请求处理类 有空改下
     */
    private FloorDataManager floorDataManager;
    /**
     * 下拉刷新的头部
     */
    private SecondFloorHeader refreshHeader;
    /**
     * 顶部吸顶容器
     */
    private ViewGroup topCeilingContainer;

    /**
     * 顶部吸顶容器2
     */
    private ViewGroup topCeilingContainer2;

    /**
     * 浮窗容器
     */
    private ViewGroup floatingViewContainer;
    /**
     * 导航容器
     */
    private NavContainer navigationContainer;
    /**
     * 直播间容器
     */
    private LinearLayout liveContainer;
    /**
     * 新的沉浸式容器
     */
    private FrameLayout flNavTrans;
    /**
     * 导航栏沉浸式背景
     */
    private ImageView ivNavTrans;
    /**
     * 二楼图片
     */
    private ImageView ivSecondFloor;
    /**
     * 二楼跳转
     */
    private FloorAction secondFloorAction;
    /**
     * 刷新头
     */
    private HomeRefreshHeaderNew homeRefreshHeader;

    /**
     * 当前下拉header下拉的百分比
     */
    private float headerMovingPercent;
    /**
     * 下拉头部当前是否在拖拽下拉的状态
     */
    private boolean isHeaderDragging;
    /**
     * 是否有二楼
     */
    private boolean hasSecondFloor = false;
    /**
     * 首页视频播放帮助类
     */
    private HomeFloorPlayHelper recyclerviewPlayHelper;
    /**
     * 空白页
     */
    private View noDataLayout;
    /**
     * 空白页文字描述
     */
    private TextView noDataDesc;
    /**
     * 空白页按钮
     */
    private TextView tvNoDataBtn;

    private LinearLayout llNotData;
    private LinearLayout llNotNetWork;

    /**
     * 网络异常按钮
     */
    private TextView tvNotNetworkBtn;
    /**
     * 当前首页展示的数据是否为缓存数据
     */
    private boolean isCache = false;
    /**
     * 当前是否可以曝光二楼下拉
     */
    private boolean canExposureSecondFloor = true;
    /**
     * 当前首页所展示的数据对应的门店
     */
    private String hasDataStore;
    /**
     * 首页布局管理器
     */
    private StaggeredGridLayoutManager layoutManager;
    /**
     * 吸顶动画
     */
    private ValueAnimator alphaAnimation;
    /**
     * 记录当前插入猜你喜欢卡片的位置  用于计算曝光的真实位置
     */
    private final HashSet<Integer> mayLikePosition = new HashSet<>();
    /**
     * 向外分发首页的特殊楼层
     */
    private FloorOtherDataCallback floorOtherDataCallback;
    /**
     * 需要依赖的activity
     * 目前由于三方sdk原因，需要使用activity
     */
    private Activity activity;
    /**
     * 首页领域配置楼层
     */
    private FloorDetailBean configFloorBean;
    /**
     * ItemDecoration
     */
    private FloorContainerItemDecoration floorContainerItemDecoration;
    private RefreshStateChangeListener refreshStateChangeListener;

//    private View sfFloorBaseAddressTipView;

    private ContainerLiveCallBack containerLiveCallBack;

    /**
     * 未开启定位权限提示view
     */
    private LinearLayout sfFloorBaseLocationTipView;

    //推荐多tab楼层Content
    ViewPagerRecommendContent viewPagerRecommendContent;

    /**
     * 未开启定位 提示文案
     */
    private TextView tvLocationTiTitle;

    boolean statusBarIsDark = false;

    public boolean isStatusBarIsDark() {
        return statusBarIsDark;
    }

    public void setStatusBarIsDark(boolean statusBarIsDark) {
        this.statusBarIsDark = statusBarIsDark;
    }

    /**
     * 当前权限申请的code
     */
    private int permissionRequestCode = 0;


    public void setContainerLiveCallBack(ContainerLiveCallBack containerLiveCallBack) {
        this.containerLiveCallBack = containerLiveCallBack;
    }


    public FloorContainer(Context context) {
        super(context);
        initView(context);
    }

    public FloorContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public FloorContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化视图
     *
     * @param context
     */
    private void initView(Context context) {
        FloorBaseNetwork.INNER_SDK_VERSION = FloorNetwork.SDK_VERSION;
        mContext = context;
        mRootView = LayoutInflater.from(context).inflate(R.layout.sf_floor_base_container, this, true);
        mRootView.setBackgroundColor(mContext.getResources().getColor(R.color.sf_floor_base_container_bg));
        smartRefreshLayout = findViewById(R.id.refreshLayout);
        initNoData();
//        initAddressTipView();
        initRefresh();
        initRecycleView();
        initNavigationBar();
        initCeiling();
        initFloatingView();
        initLocationTipView();
        addTopStatusBarBg();
    }

    private void initLocationTipView() {
        sfFloorBaseLocationTipView = findViewById(R.id.sf_floor_base_location_tip_view);
        tvLocationTiTitle = findViewById(R.id.tv_location_tip_title);
        sfFloorBaseLocationTipView.setVisibility(View.GONE);
        sfFloorBaseLocationTipView.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View view) {
                if (NoDoubleClickUtils.isDoubleClick()) {
                    return;
                }
                getGPSAndLocationPermissionState();

            }
        });
    }

    View statusBarBg;

    public View getStatusBarBg() {
        return statusBarBg;
    }

    //吸顶的时候，添加遮盖层
    private void addTopStatusBarBg() {
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                getViewTreeObserver().removeOnGlobalLayoutListener(this);
                statusBarBg = new View(getContext());
                statusBarBg.setLayoutParams(new LinearLayout.LayoutParams(-1, ImmersionBar.getStatusBarHeight(getActivity())));
                statusBarBg.setBackgroundColor(getResources().getColor(R.color.sf_floor_base_container_bg));
                statusBarBg.setVisibility(View.GONE);
                addView(statusBarBg);
            }
        });


    }

    /**
     * 请求gps和定位权限状态
     */
    private void getGPSAndLocationPermissionState() {
        if (getActivity() == null) {
            return;
        }
        if (!LocationHelper.isGrantLocationPermission(getActivity()) && !LocationHelper.isOpenLocation(getActivity())) {
            requestGPSAndLocationPermission();
        } else if (!LocationHelper.isGrantLocationPermission(getActivity())) {
            requestGPSAndLocationPermission();
        } else {
            openLocationSetting();
        }
    }

    /**
     * 打开系统设置定位
     */
    private void openLocationSetting() {
        if (getActivity() == null) {
            return;
        }
        Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        getActivity().startActivityForResult(intent, 0);
    }


    private void requestGPSAndLocationPermission() {
        if (getActivity() == null) {
            return;
        }

//        Log.e("FloorContainer","隐私弹窗状态---"+FloorInit.getFloorConfig().hasAgreePolicy());
        String[] data = PermissionUtils.queryForTipAndCodeLocation(getActivity(), PermissionUtils.LOCATION_PERMISSION_GROUP, true, FloorInit.getFloorConfig().hasAgreePolicy(), 0);
        permissionRequestCode = Integer.parseInt(data[1]) + 1000;

        DialogUtilCreateHelper.createForLocation(getActivity(), PermissionUtils.LOCATION_PERMISSION_GROUP, FloorInit.getFloorConfig().hasAgreePolicy(), PreferenceUtil.getBoolean(PermissionUtils.KEY_PERMISSION_LOCATION_FIRST, true), 0, permissionRequestCode, new DialogUtilCreateHelper.CancelRequestListener() {

            @Override
            public void cancelRequestPermission() {
            }
        });
    }


    public static class DialogUtilCreateHelper {


        public interface CancelRequestListener {
            void cancelRequestPermission();
        }

        /**
         * 请求定位权限创建的试图
         */
        public static Boolean createForLocation(final Activity activity, final String[] permissions, boolean hasAgreePrivacy, final boolean isFirst, int type, Integer requestCode, CancelRequestListener cancelRequestListener) {
            String[] data = PermissionUtils.queryForTipAndCodeLocation(activity, permissions, true, hasAgreePrivacy, type);
            String message = data[0];
            final int finalRequestCode = requestCode == null ? Integer.parseInt(data[1]) : requestCode;
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M || activity == null) {
                return false;
            }
            if (permissions == null || permissions.length <= 0) {
                return false;
            }
            // 过滤传入的权限列表,筛除已经获取的权限
            String[] noGrantPermission = getNoGrantedPermission(activity, permissions);
            if (noGrantPermission == null || noGrantPermission.length <= 0) {
                return false;
            }
            boolean shouldShowRequestPermissionRationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, noGrantPermission[0]);
            if (isFirst || shouldShowRequestPermissionRationale || !hasAgreePrivacy) {
                // 向用户解释为什么需要此权限，第一次拒绝请求权限之后和永久拒绝之前，shouldShowRequestPermissionRationale都会返回true
                try {
                    DialogUtils.showDialog(activity).setCancelable(false).setStyle(R.style.sf_floor_core_alert).setTitle("权限收集提醒").setTitleSize(16).setMessage(message).setPositiveButton(R.string.sf_floor_base_go_to_permission, new DialogInterface.OnClickListener() {

                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                            if (FloorInit.getFloorConfig().hasAgreePolicy()) {
                                PermissionUtils.reQuestPermission(activity, permissions, finalRequestCode);
                                return;
                            }
                            FloorInit.getFloorConfig().showSecretDialog(activity, new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    PermissionUtils.reQuestPermission(activity, permissions, finalRequestCode);
                                    FloorInit.getFloorConfig().agreePolicy();
                                    dialog.dismiss();
                                }
                            }, new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                    if (cancelRequestListener != null) {
                                        cancelRequestListener.cancelRequestPermission();
                                    }
                                }
                            });
                        }
                    }, activity.getResources().getColor(R.color.sf_theme_color_level_1)).setNegativeButton(R.string.sf_floor_base_not_agree, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                            if (cancelRequestListener != null) {
                                cancelRequestListener.cancelRequestPermission();
                            }
                        }
                    }).build().show();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                // 请求权限
                ActivityCompat.requestPermissions(activity, noGrantPermission, finalRequestCode);
            }
            return true;
        }


    }


//    private void initAddressTipView() {
//        sfFloorBaseAddressTipView = findViewById(R.id.sf_floor_base_address_tip_view);
//        sfFloorBaseAddressTipView.setVisibility(View.GONE);
//        sfFloorBaseAddressTipView.setClickable(false);
//        sfFloorBaseAddressTipView.findViewById(R.id.sf_floor_base_address_tip_view).setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (NoDoubleClickUtils.isDoubleClick()) {
//                    return;
//                }
//                FloorJumpManager.getInstance().createAddress(activity);
//            }
//        });
//
//
//    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //屏幕发生变更的时候触发刷新  原因为很多布局是计算的，所以需要触发再次计算
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 初始化空白页
     */
    private void initNoData() {
        llNotNetWork = findViewById(R.id.ll_not_network);
        llNotData = findViewById(R.id.ll_not_data);
        noDataLayout = findViewById(R.id.nodata);
        noDataDesc = findViewById(R.id.tv_nodata_desc);
        tvNoDataBtn = findViewById(R.id.search_other);
        tvNotNetworkBtn = findViewById(R.id.not_network_search_other);

    }


    private int getSearchBarHeight() {


        return 0;
    }

    /**
     * @param noData 是否展示空白页
     */
    private void showNoData(boolean noData) {
        if (noData) {
            noDataLayout.setVisibility(VISIBLE);
            if (!NetUtil.isNetworkAvailable(mContext)) {
                llNotNetWork.setVisibility(View.VISIBLE);
                llNotData.setVisibility(View.GONE);
            } else {
                llNotNetWork.setVisibility(View.GONE);
                llNotData.setVisibility(View.VISIBLE);
            }

            tvNoDataBtn.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    refreshData(true, false);
                }
            });
            tvNotNetworkBtn.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(Settings.ACTION_AIRPLANE_MODE_SETTINGS);
                    mContext.startActivity(intent);
                }
            });
            smartRefreshLayout.setEnableLoadMore(false);
        } else {
            noDataLayout.setVisibility(GONE);
            smartRefreshLayout.setEnableLoadMore(true);
        }
    }

    /**
     * 初始化悬浮窗
     */
    private void initFloatingView() {
        floatingViewContainer = findViewById(R.id.ll_floating_container);
    }

    /**
     * 初始化吸顶楼层容器
     */
    private void initCeiling() {
        topCeilingContainer = findViewById(R.id.ll_top_ceiling_container);
        topCeilingContainer.setBackgroundResource(R.color.sf_floor_base_container_bg);
        topCeilingContainer.setVisibility(GONE);

        topCeilingContainer2 = findViewById(R.id.ll_top_ceiling_container2);
        topCeilingContainer2.setBackgroundResource(R.color.sf_floor_base_container_bg);
        topCeilingContainer2.setVisibility(GONE);

    }

    /**
     * 初始化顶部导航ui
     */
    private void initNavigationBar() {
        navigationContainer = findViewById(R.id.ll_navigation_container);
    }

    /**
     * 设置下来刷新控件
     */
    private void initRefresh() {
        //todo 日后需要扩展自定义下拉刷新图标及header功能  本次暂时不封装
        // 默认设置二楼header 并根据 楼层数据决定二楼数据的展示
        refreshHeader = findViewById(R.id.header);
        ivSecondFloor = findViewById(R.id.iv_second_floor);
        ivSecondFloor.setBackgroundResource(R.color.sf_floor_base_container_bg);//todo 验证一下是不是这里影响了背景色
        ivSecondFloor.setAlpha(0.0f);
        homeRefreshHeader = findViewById(R.id.hrh_header);
        smartRefreshLayout.setDisableContentWhenRefresh(true);
        smartRefreshLayout.setDisableContentWhenLoading(true);
        smartRefreshLayout.setRefreshHeader(refreshHeader);
        smartRefreshLayout.setDragRate(1);
        smartRefreshLayout.setEnableOverScrollBounce(false);


        if (VpTabHelper.hasVpTab) {
            smartRefreshLayout.setEnableRefresh(false);
        } else {
            ClassicsFooter footer = findViewById(R.id.main_recycle_footer);
            footer.setBackgroundResource(R.color.sf_floor_base_container_bg);
            smartRefreshLayout.setRefreshFooter(footer);
        }
        smartRefreshLayout.setOnMultiListener(new SimpleMultiListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                //兼容已经设置了不能获取更多，但是接口依然回调的问题
                if (smartRefreshLayout.isEnableLoadMore()) {
                    floorDataManager.loadMore(mContext, hasDataStore, jdMaPageImp);
                }
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                //下拉刷新肯定已经滚动到了顶部
                SFLogCollector.d(TAG, "callback onRefresh");
                refreshData(false, false);
            }

            private boolean skipMovingValue = true;

            @Override
            public void onHeaderMoving(RefreshHeader header, boolean isDragging, float percent, int offset, int headerHeight, int maxDragHeight) {
                //注意！！！！！这里在动画缩放里关闭了动画的话，回调异常，逻辑处理上需要注意
                //目前识别到第一次有问题，丢弃第一次的值
                if (skipMovingValue) {
                    skipMovingValue = false;
                    return;
                }

                if (liveContainer != null && liveContainer.getVisibility() == VISIBLE) {
                    if (offset == 0) {
                        int[] firstViewPos = layoutManager.findFirstVisibleItemPositions(null);
                        if (firstViewPos[0] == 0) {
                            for (int i = 0; i < 5; i++) {
                                View viewByPosition = layoutManager.findViewByPosition(i);
                                if (viewByPosition != null && viewByPosition.getBottom() > ScreenUtils.dip2px(mContext, 330)) {
                                    //这个330不要随意更改，控制的沉浸式直播容器高度
                                    flagFloorPos = i;
                                    distance = viewByPosition.getBottom();
                                    break;
                                }
                            }
                        }
                    }
                }

                //直播楼层滑动
                if (liveContainer != null && liveContainer.getVisibility() == VISIBLE) {
                    View liveReferPos = layoutManager.findViewByPosition(flagFloorPos);
                    if (flagFloorPos != 0 && distance != 0 && liveReferPos != null) {
                        liveContainer.setTranslationY(offset);
                    }
                }

                if (flNavTrans != null && flNavTrans.getVisibility() == VISIBLE) {
                    try {
                        int lastTransFloorIndex = HomeRefreshManager.getInstance().getImmersiveFloorBeans().size() - 1;
                        View lastTransFloor = layoutManager.findViewByPosition(lastTransFloorIndex);
                        if (lastTransFloor != null) {
                            flNavTrans.setTranslationY(-(getBottom() - lastTransFloor.getBottom()) + offset);
                        } else {
                            flNavTrans.setTranslationY(-getBottom());
                        }
                    } catch (Exception e) {
                        JdCrashReport.postCaughtException(e);
                        e.printStackTrace();
                    }
                }

                if (mShellRecycleViewAdapter != null) {
                    mShellRecycleViewAdapter.onHeaderMoving(isDragging, percent, offset, headerHeight, maxDragHeight);
                }

                if (navigationContainer != null) {
//                    float p = percent > 1 ? 1 : percent;
                    navigationContainer.setAlpha(1 - Math.min(percent * 3, 1));
                    SFLogCollector.d("setAlpha", " " + navigationContainer.getAlpha() + " p:" + percent);
                }
                if (alphaAnimation != null && alphaAnimation.isRunning()) {
                    alphaAnimation.cancel();
                }

                headerMovingPercent = percent;
                isHeaderDragging = isDragging;
                updateCeiling("moving");
                updateCeiling2();

                if (hasSecondFloor && canExposureSecondFloor && isDragging) {
                    canExposureSecondFloor = false;
                    exposureSecondFloor();
                }
                if (percent <= 0.000001 && !isDragging) {
                    canExposureSecondFloor = true;
                }
                ivSecondFloor.setTranslationY(Math.min(offset - ivSecondFloor.getHeight() + navigationContainer.getHeight(), smartRefreshLayout.getLayout().getHeight() - ivSecondFloor.getHeight()));
                ivSecondFloor.setAlpha(1f);
                if (percent > 0) {
                    refreshHeader.setAlpha(1f);
                } else {
                    refreshHeader.setAlpha(0f);
                }
            }

            @Override
            public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
                if (oldState == RefreshState.None && newState == RefreshState.PullDownToRefresh) {
                    if (refreshStateChangeListener != null) {
                        refreshStateChangeListener.startRefreshDrag();
                    }
                } else if (oldState == RefreshState.ReleaseToTwoLevel && newState == RefreshState.TwoLevelReleased) {
                    postFloorEvent("", new SecondFloorChangeEvent(true));
                    postDelayed(() -> {
                        Bundle bundle = new Bundle();
                        FloorBaseMaEntity entity = new FloorBaseMaEntity(null);

                        if (secondFloorAction != null) {
                            bundle.putString(FloorJumpManager.TO_URL, secondFloorAction.getToUrl());
                            bundle.putInt(FloorJumpManager.URL_TYPE, secondFloorAction.getUrlType());
                            entity.url = secondFloorAction.getToUrl();
                        }
                        FloorJumpManager.getInstance().jumpAction(activity, bundle);
                        JDMaUtils.save7FClick("frontPage_theSecondFloor_floorClick", jdMaPageImp, entity);
                    }, 400);
                } else if (oldState == RefreshState.TwoLevelReleased && newState == RefreshState.TwoLevel) {
                    if (refreshHeader != null) {
                        refreshHeader.finishTwoLevel();
                        postFloorEvent("", new SecondFloorChangeEvent(false));
                    }
                } else if (oldState == RefreshState.RefreshFinish && newState == RefreshState.None) {
                    if (hasSecondFloor && SHOW_SECOND_FLOOR_GUIDE) {
                        smartRefreshLayout.guideTwoLevel();
                        if (homeRefreshHeader != null) {
                            homeRefreshHeader.onStateChanged(smartRefreshLayout, RefreshState.None, RefreshState.ReleaseToTwoLevel);
                        }
                        exposureSecondFloor();
                        SHOW_SECOND_FLOOR_GUIDE = false;
                    }
                }
            }
        });
    }

    /**
     * 二楼曝光方法
     */
    private void exposureSecondFloor() {
        FloorBaseMaEntity entity = new FloorBaseMaEntity(null);
        if (secondFloorAction != null) {
            entity.url = secondFloorAction.getToUrl();
            entity.urlType = String.valueOf(secondFloorAction.getUrlType());
        }
        JDMaUtils.save7FExposure("frontPage_theSecondFloor_floorExpose", null, entity, null, jdMaPageImp);
    }


    /**
     * 判断mainRecyclerView 是否滑动到了顶部
     *
     * @return
     */
    private boolean isReachTop() {

        if (mFloorRecyclerView == null || mFloorRecyclerView.getLayoutManager() == null) {
            return false;
        }
        if (getData() == null || getData().size() == 0) {
            return true;
        }
        StaggeredGridLayoutManager linearLayoutManager = (StaggeredGridLayoutManager) mFloorRecyclerView.getLayoutManager();
        int[] firstVisiblePos = linearLayoutManager.findFirstCompletelyVisibleItemPositions(null);
        if (firstVisiblePos != null && firstVisiblePos.length > 0) {
            for (int i : firstVisiblePos) {
                if (i == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean isRealReachTop() {
        return isReachTop() && navigationContainer.getTranslationY() == 0;
    }

    @Override
    public void showCreateAddressTip() {
//        showCreateLocationTipState();
    }

    @Override
    public RecyclerView getParentRcv() {
        return mFloorRecyclerView;
    }

    @Override
    public RelativeLayout getFlContainer() {
        return this;
    }


    public void showCreateLocationTipState() {
        if (getActivity() == null) {
            return;
        }

        if (sfFloorBaseLocationTipView != null && tvLocationTiTitle != null) {
            if (!LocationHelper.isGrantLocationPermission(getActivity()) && !LocationHelper.isOpenLocation(getActivity())) {
                tvLocationTiTitle.setText("定位和GPS未开启，授权后可查找附近门店");
                sfFloorBaseLocationTipView.setVisibility(View.VISIBLE);
            } else if (!LocationHelper.isOpenLocation(getActivity())) {
                tvLocationTiTitle.setText("GPS权限未开启，授权后可提高定位精准度");
                sfFloorBaseLocationTipView.setVisibility(View.VISIBLE);
            } else if (!LocationHelper.isGrantLocationPermission(getActivity())) {
                tvLocationTiTitle.setText("定位权限未开启，授权后可提高定位精准度");
                sfFloorBaseLocationTipView.setVisibility(View.VISIBLE);
            } else {
                sfFloorBaseLocationTipView.setVisibility(View.GONE);
            }
        }
    }

    public void hiddenCreateLocationTip() {
        if (sfFloorBaseLocationTipView != null) {
            sfFloorBaseLocationTipView.setVisibility(View.GONE);
        }
    }

    /**
     * GRID_LINE_ITEM_COUNT 一行两个
     */
    protected static int GRID_LINE_ITEM_COUNT = 2;

    /**
     * 直播相关
     */
    private int flagFloorPos = 0;

    private int distance = 0;

    /**
     * 初始化列表
     */
    private void initRecycleView() {
        mFloorRecyclerView = mRootView.findViewById(R.id.main_recycle);
        mFloorRecyclerView.initScroll();
        mFloorRecyclerView.setFloorContainer(this);
        //直播间容器
        liveContainer = mRootView.findViewById(R.id.live_container);
        flNavTrans = mRootView.findViewById(R.id.fl_nav_trans);
        ivNavTrans = mRootView.findViewById(R.id.iv_nav_trans);
        mFloorRecyclerView.setFakeCallback(new FloorRecyclerView.FakeCallback() {
            @Override
            public long getFakeItemId(RecyclerView.ViewHolder holder) {
                if (holder instanceof FloorBaseViewHolder) {
                    return ((FloorBaseViewHolder) holder).getFakeItemId();
                }
                return holder.getItemId();
            }
        });
        floorContainerItemDecoration = new FloorContainerItemDecoration(mContext);
        mFloorRecyclerView.addItemDecoration(floorContainerItemDecoration);

        mShellRecycleViewAdapter = new ShellRecycleViewAdapter(mContext, this);
        layoutManager = mFloorRecyclerView.refreshLayoutManager();
        mFloorRecyclerView.setLayoutManager(layoutManager);
//        layoutManager.setGapStrategy(StaggeredGridLayoutManager.GAP_HANDLING_NONE);
        mFloorRecyclerView.setLiveContainer(liveContainer);
        mFloorRecyclerView.setAdapter(mShellRecycleViewAdapter);

        //禁用动画,设置动画时长为0
        setLayoutTransition(null);
        RecyclerView.ItemAnimator itemAnimator = mFloorRecyclerView.getItemAnimator();
        if (null != itemAnimator) {
            itemAnimator.setAddDuration(0);
            itemAnimator.setChangeDuration(0);
            itemAnimator.setMoveDuration(250);
            itemAnimator.setRemoveDuration(0);
            if (itemAnimator instanceof SimpleItemAnimator) {
                ((SimpleItemAnimator) itemAnimator).setSupportsChangeAnimations(false);
            }
        }

        recyclerviewPlayHelper = new HomeFloorPlayHelper();
        recyclerviewPlayHelper.init(mFloorRecyclerView, ShareAnimationPlayer.PlayType.HOME_LIST);

        List<FloorDetailBean> listData = new ArrayList<>();
        mShellRecycleViewAdapter.setData(listData);
        mFloorRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (mShellRecycleViewAdapter != null) {
                    mShellRecycleViewAdapter.onScrollStateChanged(recyclerView, newState);
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                try {

                    if (liveContainer != null && liveContainer.getVisibility() == VISIBLE) {
                        View liveReferPos = layoutManager.findViewByPosition(flagFloorPos);
                        if (flagFloorPos != 0 && distance != 0 && liveReferPos != null) {
                            liveContainer.setTranslationY(liveReferPos.getBottom() - distance);
                        }
                    }

                    if (flNavTrans != null && flNavTrans.getVisibility() == VISIBLE) {
                        try {
                            int lastTransFloorIndex = HomeRefreshManager.getInstance().getImmersiveFloorBeans().size() - 1;
                            View lastTransFloor = layoutManager.findViewByPosition(lastTransFloorIndex);
                            if (lastTransFloor != null) {
                                flNavTrans.setTranslationY(-(getBottom() - lastTransFloor.getBottom()));
                            } else {
                                flNavTrans.setTranslationY(-getBottom());
                            }
                        } catch (Exception e) {
                            JdCrashReport.postCaughtException(e);
                            e.printStackTrace();
                        }
                    }

                    //注意由于间距变化等引起的滚动！！！！！
                    recyclerviewPlayHelper.setCellingHeight(topCeilingContainer.getHeight());
                    recyclerviewPlayHelper.setCellingHeight(topCeilingContainer2.getHeight());

                    if (mShellRecycleViewAdapter != null) {
                        mShellRecycleViewAdapter.onScroll(dx, dy);
                    }
                    if (scrollDistanceListener != null) {
                        scrollDistanceListener.scrollDistance(dy, isReachTop());
                    }

                    if (mShellRecycleViewAdapter.getItemCount() == 0) {
                        navigationContainer.setTranslationY(0);
                        SFLogCollector.d("lllsssppp", "a");

                    } else {
                        View firstItemView = layoutManager.findViewByPosition(0);

                        if (firstItemView != null) {
                            float aimTransY = firstItemView.getTop() - navigationContainer.getMeasuredHeight();

                            SFLogCollector.d("lllsssppp", "aimTransY :" + aimTransY + " top" + firstItemView.getTop() + " " + navigationContainer.getMeasuredHeight());

                            if (aimTransY < -navigationContainer.getMeasuredHeight()) {
                                aimTransY = -navigationContainer.getMeasuredHeight();
                            }
                            if (aimTransY > 0) {
                                aimTransY = 0;
                            }
                            navigationContainer.setTranslationY(aimTransY);
                        } else {
                            navigationContainer.setTranslationY(-navigationContainer.getMeasuredHeight());
                            SFLogCollector.d("lllsssppp", "c");
                        }
                    }

                    if (dy > 0 && !VpTabHelper.hasVpTab) {
                        //判断一下当前是否需要提前加载更多
                        checkLoadMore();
                    }

                    //更新吸顶效果
                    updateCeiling("scroll");
                    updateCeiling2();
                    exposure();

                    //推荐tab内容曝光
                    if (viewPagerRecommendContent == null && VpTabHelper.hasVpTab) {
                        for (FloorBaseInterface floorBaseInterface : mShellRecycleViewAdapter.getAllViewHolder()) {
                            if (floorBaseInterface instanceof ViewPagerRecommendFloor) {
                                viewPagerRecommendContent = ((ViewPagerRecommendFloor) floorBaseInterface).getViewPagerRecommendContent();
                            }
                        }
                    }
                    //父Rcv触发ChildRcv的曝光
                    if (viewPagerRecommendContent != null && viewPagerRecommendContent.getCurrentChildRcv() != null) {
                        viewPagerRecommendContent.getCurrentChildRcv().parentRcvScrollExposure();
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                    JdCrashReport.postCaughtException(e);
                }
            }
        });
    }


    /**
     *
     */
    private void checkLoadMore() {
        if (mFloorRecyclerView == null || mFloorRecyclerView.getLayoutManager() == null) {
            return;
        }

        if (mShellRecycleViewAdapter == null || mShellRecycleViewAdapter.getItemCount() == 0) {
            return;
        }

        StaggeredGridLayoutManager linearLayoutManager = (StaggeredGridLayoutManager) mFloorRecyclerView.getLayoutManager();
        int[] lastVisiblePos = linearLayoutManager.findLastVisibleItemPositions(null);
        if (lastVisiblePos != null && lastVisiblePos.length > 0) {
            int last = Utils.getMaxValue(lastVisiblePos, lastVisiblePos);
            //如果下面只有8个了（提前加载一页），并且当前是可以加载更多的 尝试加载更多
            if (last + 8 > mShellRecycleViewAdapter.getItemCount() && smartRefreshLayout.isEnableLoadMore()) {
                floorDataManager.loadMore(mContext, hasDataStore, jdMaPageImp);
            }
        }
        return;
    }


    /**
     * 更新吸顶效果
     */
    private void updateCeiling(String type) {
        boolean needCelling = false;
        //如果当前有需要吸顶的楼层  并且  当前不处于下拉刷新的状态 （规避下拉刷新过程中设置padding导致的个别机型返回第一个可见楼层位置不正确）
        if (firstCellingPos >= 0 && headerMovingPercent < 0.01) {

            //吸顶动画
            int[] firstVis = layoutManager.findFirstVisibleItemPositions(null);

            View cellingView = layoutManager.findViewByPosition(firstCellingPos);
            int statusBarHeight = safeGetStatusBarHeight(activity);
            if (cellingView != null && cellingView.getTop() < statusBarHeight) {
                needCelling = true;
            } else if (firstVis != null && firstVis.length > 0) {
                needCelling = true;
                for (int i = 0; i < firstVis.length; i++) {
                    if (firstVis[i] <= firstCellingPos) {
                        needCelling = false;
                        break;
                    }
                }
            }
        }

        if (needCelling) {
            if (topCeilingContainer.getVisibility() == GONE) {
                if (alphaAnimation != null && alphaAnimation.isRunning()) {
                    alphaAnimation.cancel();
                }
                alphaAnimation = ValueAnimator.ofFloat(0, 1);
                alphaAnimation.setDuration(150);
                alphaAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        topCeilingContainer.setVisibility(VISIBLE);
                        if (VpTabHelper.hasSearchFloor && VpTabHelper.navIconStyle != 0) {
                            setStatusBarIsWhite();
                        }
                        topCeilingContainer.setAlpha((float) animation.getAnimatedValue());
                    }
                });
                alphaAnimation.start();
            }
        } else {
            if (alphaAnimation != null && alphaAnimation.isRunning()) {
                alphaAnimation.cancel();
            }
            if (topCeilingContainer.getVisibility() == VISIBLE && topCeilingContainer.getAlpha() == 1) {
                topCeilingContainer.setAlpha(0.999f);
                alphaAnimation = ValueAnimator.ofFloat(0.999f, 0);
                alphaAnimation.setDuration(150);
                alphaAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        topCeilingContainer.setAlpha((float) animation.getAnimatedValue());
                    }
                });
                alphaAnimation.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        topCeilingContainer.setVisibility(GONE);
                        if (VpTabHelper.hasSearchFloor && VpTabHelper.navIconStyle != 0) {
                            setStatusBarDark();
                        }

                    }
                });
                alphaAnimation.start();
            } else {
                topCeilingContainer.setVisibility(GONE);
                if (VpTabHelper.hasSearchFloor && VpTabHelper.navIconStyle != 0) {
                    setStatusBarDark();
                }

            }
        }
    }

    //设置状态栏文字颜色黑色
    public void setStatusBarIsWhite() {
        if (statusBarIsDark) {
            statusBarIsDark = false;
            setWindowBackgroundRes(R.color.sf_floor_base_trans);
            setMainIsDarkStatusbar(true);
        }
    }

    //设置状态栏文字颜色白色
    public void setStatusBarDark() {
        if (!statusBarIsDark) {
            setWindowBackgroundRes(R.color.sf_floor_base_trans);
            setIsDarkStatusbar(true);
            statusBarIsDark = true;
        }
    }

    public void setIsDarkStatusbar(boolean isDarkStatusbar) {
        if (getContext() instanceof SlideBackActivity) {
            ((SlideBackActivity) getContext()).setIsDarkStatusbar(isDarkStatusbar);
        }
    }


    public void setMainIsDarkStatusbar(boolean isDarkStatusbar) {
        if (getContext() instanceof SlideBackActivity) {
            ((SlideBackActivity) getContext()).setMainIsDarkStatusbar(isDarkStatusbar);
        }

    }

    private void setWindowBackgroundRes(int r) {
        Window w = ((Activity) getContext()).getWindow();
        if (w != null) {
            w.setBackgroundDrawableResource(r);
        }
    }

    /**
     * 更新吸顶效果
     * todo  这个需要改成全新的判定逻辑  处理吸顶
     */
    private void updateCeiling2() {
        boolean needCelling = false;
        if (topCeilingContainer.getVisibility() == GONE) {
            int statusBarHeight = safeGetStatusBarHeight(activity);
            topCeilingContainer2.setPadding(0, statusBarHeight, 0, 0);
            if (secondCellingPos >= 0) {
                //吸顶动画
                int[] firstVis = layoutManager.findFirstVisibleItemPositions(null);

                View cellingView = layoutManager.findViewByPosition(secondCellingPos);
                if (cellingView != null && cellingView.getTop() < statusBarHeight) {
                    needCelling = true;
                } else if (firstVis != null && firstVis.length > 0) {
                    needCelling = true;
                    for (int i = 0; i < firstVis.length; i++) {
                        if (firstVis[i] <= secondCellingPos) {
                            needCelling = false;
                            break;
                        }
                    }
                }
            }
        } else {
            topCeilingContainer2.setPadding(0, 0, 0, 0);

            int[] location2 = new int[2];
            if (layoutManager.findViewByPosition(secondCellingPos) != null) {
                layoutManager.findViewByPosition(secondCellingPos).getLocationInWindow(location2);
                if (location2[1] < 0 || location2[1] < topCeilingContainer.getHeight()) {
                    needCelling = true;
                }
            } else {
                if (secondCellingPos >= 0) {
                    //吸顶动画
                    int[] firstVis = layoutManager.findFirstVisibleItemPositions(null);
                    if (firstVis != null && firstVis.length > 0) {
                        needCelling = true;
                        for (int i = 0; i < firstVis.length; i++) {
                            if (firstVis[i] <= secondCellingPos) {
                                needCelling = false;
                                break;
                            }
                        }
                    }
                }
            }
        }


        if (needCelling) {
            if (topCeilingContainer2.getVisibility() == GONE) {
                topCeilingContainer2.setVisibility(VISIBLE);
            }
        } else {
            if (topCeilingContainer2.getVisibility() == VISIBLE && topCeilingContainer2.getAlpha() == 1) {
                topCeilingContainer2.setVisibility(GONE);
            }
        }
    }

    /**
     * 向首页楼层发送事件
     *
     * @param templateCode 楼层id
     * @param json         参数
     */
    public void postEvent(String templateCode, String json) {
        //todo 事件传递不应该这写
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.postEvent(templateCode, json);
        }
    }

    /**
     *
     */
    private final Handler handler = new Handler();


    @Override
    public void shouldShowLiveContainer(boolean shouldShow) {
        //直播间容器刷新展示逻辑处理
        liveContainer.setVisibility(shouldShow ? VISIBLE : GONE);
    }

    /**
     * 设置首页楼层数据
     *
     * @param listData 楼层数据
     * @param isCache  是否是缓存数据 缓存数据不能上拉加载更多
     */
    @Override
    public void setData(List<FloorDetailBean> listData, boolean isCache, boolean enableLoadMore) {
//        if (sfFloorBaseAddressTipView != null) {
//            sfFloorBaseAddressTipView.setVisibility(View.GONE);
//        }
        //设置状态栏
        if (VpTabHelper.navIconStyle != 0) {
            statusBarIsDark = false;
            setStatusBarDark();
        } else {
            statusBarIsDark = true;
            setStatusBarIsWhite();
        }

        this.isCache = isCache;
        //清除曝光
        sparseBooleanArray.clear();
        //清除猜你喜欢
        mayLikePosition.clear();

        //缓存不能下拉更多
        if (listData == null || listData.isEmpty()) {
            showNoData(true);
            hasDataStore = null;
            smartRefreshLayout.setEnableLoadMore(false);
        } else {
            showNoData(false);
            hasDataStore = listData.get(0).getStoreId();
            smartRefreshLayout.setEnableLoadMore(!isCache);
        }
        showCreateLocationTipState();


        if (smartRefreshLayout != null) {
            smartRefreshLayout.finishRefresh();
        }
        //过滤楼层数据
        mShellRecycleViewAdapter.setData(listData);


        //设置是否还能继续加载数据
        if (listData != null && listData.size() > 0 && !enableLoadMore) {
            setMoreData(RecommendRequest.OnLoadMoreResult.NO_MORE_DATA, null);
        }

        //设置沉浸式
        if (HomeRefreshManager.getInstance().getStyle() == HomeRefreshManager.TRAN_BACKGROUND) {
            flNavTrans.setVisibility(VISIBLE);
            flNavTrans.setBackgroundColor(StringUtil.getSetColor("#00000000", HomeRefreshManager.getInstance().getAtmosphereBackgroundColor()));
//            ImageloadUtils.loadImageAsBitmap(mContext, HomeRefreshManager.getInstance().getAtmosphereBackgroundImage(), R.drawable.sf_theme_image_placeholder_square,
//                    R.drawable.sf_theme_image_placeholder_square, new SimpleTarget<Bitmap>() {
//
//                        @Override
//                        public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
//                            ivNavTrans.setImageBitmap(resource);
//                        }
//
//                        @Override
//                        public void onLoadFailed(Exception e, Drawable errorDrawable) {
//                            super.onLoadFailed(e, errorDrawable);
//                            //todo
//                        }
//                    }
//            );
            try {
                FrameLayout.LayoutParams ivLp = (FrameLayout.LayoutParams) ivNavTrans.getLayoutParams();
                int width = ScreenUtils.getScreenWidth(activity);
                ivLp.height = (int) (width * 1.0f / HomeRefreshManager.getInstance().getAtmosphereBackgroundImageWidth() * HomeRefreshManager.getInstance().getAtmosphereBackgroundImageHeight());
                ivNavTrans.setLayoutParams(ivLp);
                ImageloadUtils.loadImage(activity, ivNavTrans, HomeRefreshManager.getInstance().getAtmosphereBackgroundImage(), 0, 0);
            } catch (Exception e) {
                e.printStackTrace();
                JdCrashReport.postCaughtException(e);
            }
            ivSecondFloor.setBackgroundResource(R.color.sf_floor_base_trans);
            homeRefreshHeader.updateStyle(true);
        } else {
            flNavTrans.setVisibility(GONE);
            ivNavTrans.setBackgroundDrawable(null);
            ivNavTrans.setImageDrawable(null);
        }

        if (containerLiveCallBack != null) {
            containerLiveCallBack.loadComplateData(isCache);
        }
        if (BuildConfig.DEBUG) {
            //
            SFLogCollector.d("FloorContainer", "======================setData===============");
            if (listData == null || listData.size() == 0) {
                SFLogCollector.d("FloorContainer", "no floor");
            } else {
                for (FloorDetailBean bean : listData) {
                    SFLogCollector.d("FloorContainer", bean.getTemplateCode());
                }
            }
            SFLogCollector.d("FloorContainer", "======================setData end===============");
        }

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                //触发曝光
                exposure();
            }
        }, 100);


        postDelayAutoPlay();
    }


    @Override
    public void setMoreData(int state, List<FloorDetailBean> floorDetailBeans) {
        if (state == RecommendRequest.OnLoadMoreResult.FAIL) {
            try {
                SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                errorLog.type = 9502;
                errorLog.errorCode = "首页_兜底页_" + AddressStoreHelper.getAddressStoreBean().getStoreName() + "_" + TenantIdUtils.getStoreId();
                errorLog.errorMsg = "首页接口异常";
                errorLog.location = "首页";
                SFLogCollector.reportBusinessErrorLog(errorLog);
            } catch (Exception e) {
            }
            smartRefreshLayout.finishLoadMore(false);
        } else if (state == RecommendRequest.OnLoadMoreResult.SUCCESS) {
            if (floorDetailBeans != null) {
                smartRefreshLayout.finishLoadMore(true);
                insertData(floorDetailBeans, mShellRecycleViewAdapter.getItemCount());
            }
        } else if (state == RecommendRequest.OnLoadMoreResult.NO_MORE_DATA) {
            if (!VpTabHelper.hasVpTab) {
                FloorDetailBean floorDetailBean = new FloorDetailBean();
                floorDetailBean.setTemplateCode("home_page_recommend_footer");
                ArrayList<FloorDetailBean> list = new ArrayList<>();
                list.add(floorDetailBean);
                insertData(list, mShellRecycleViewAdapter.getItemCount());
                smartRefreshLayout.finishLoadMore(true);
                smartRefreshLayout.setEnableLoadMore(false);
            } else {
                smartRefreshLayout.setEnableLoadMore(false);
            }

        }
    }

    /**
     * @param storeId
     * @return 是否当前展示的数据是指定门店的数据 如果存在数据的话，会停止当前的刷新状态，避免重复刷新
     */
    @Override
    public boolean hasThisStoreData(String storeId) {
        boolean hasData = (storeId != null && storeId.equals(hasDataStore));
        if (hasData) {
            //结束刷新
            if (smartRefreshLayout != null) {
                smartRefreshLayout.finishRefresh();
                smartRefreshLayout.setEnableLoadMore(false);
            }
        }
        return hasData;
    }

    /**
     * 第一个吸顶控件的位置
     */
    private int firstCellingPos = -1;

    /**
     * 第二个吸顶控件的位置
     */
    private int secondCellingPos = -1;

    /**
     * 设置特殊楼层数据
     *
     * @param floor 模板id
     * @param bean  楼层数据
     */
    @Override
    public void setFloors(String floor, FloorDetailBean bean, boolean isCache) {
        switch (floor) {
            case "导航":
            case "浮窗":
            case "首页弹窗聚合":
            case "吸顶":
                FloorViewInterface navInterface = floorViewInterfaceHashMap.get(floor);
                if (navInterface != null) {
                    navInterface.setCellingFloor(true);

                    navInterface.bindData(mContext, this, null, bean, -1);
                }
                if ("吸顶".equals(floor)) {
                    if (bean == null) {
                        topCeilingContainer.setVisibility(GONE);
                        firstCellingPos = -1;
                    } else {
                        firstCellingPos = bean.getRealIndex();
                    }
                }
                break;
            case "吸顶2":
                FloorViewInterface navInterface2 = floorViewInterfaceHashMap.get(floor);
                if (navInterface2 != null) {
                    navInterface2.setCellingFloor(true);
                    navInterface2.bindData(mContext, this, null, bean, -1);
                }
                if ("吸顶2".equals(floor)) {
                    if (bean == null) {
                        topCeilingContainer2.setVisibility(GONE);
                        secondCellingPos = -1;
                    } else {
                        secondCellingPos = bean.getRealIndex();
                    }
                }
                break;
            case "二楼":
                //todo  不想抽取了 日后加油
                secondFloorAction = null;
                hasSecondFloor = false;
                homeRefreshHeader.updateStyle(hasSecondFloor);
                if (bean != null && bean.getComponentDataObject() instanceof SecondFloorBean) {
                    SecondFloorBean secondFloorBean = (SecondFloorBean) bean.getComponentDataObject();
                    if (secondFloorBean != null && secondFloorBean.getImageSrc() != null && secondFloorBean.getActionVo() != null && !StringUtil.isNullByString(secondFloorBean.getActionVo().getToUrl())) {
                        SFLogCollector.i(TAG, "设置二楼：" + secondFloorBean.getImageSrc());
                        ImageloadUtils.loadImageAsBitmap(mContext, secondFloorBean.getImageSrc(), R.drawable.sf_theme_image_placeholder_square, R.drawable.sf_theme_image_placeholder_square, new SimpleTarget<Bitmap>() {
                            @Override
                            public void onResourceReady(Bitmap resource, GlideAnimation glideAnimation) {
                                if (TenantIdUtils.getStoreId().equals(bean.getStoreId()) && TextUtils.equals(TenantIdUtils.getFenceId(), bean.getFenceId())) {
                                    ivSecondFloor.setImageBitmap(resource);
                                    refreshHeader.setEnableTwoLevel(true);
                                    hasSecondFloor = true;
                                    homeRefreshHeader.updateStyle(hasSecondFloor);
                                    SFLogCollector.i(TAG, "设置二楼成功：" + secondFloorBean.getImageSrc());
                                } else {
                                    ivSecondFloor.setImageDrawable(null);
                                    refreshHeader.setEnableTwoLevel(false);
                                    hasSecondFloor = false;
                                    SFLogCollector.w(TAG, "设置二楼失败 门店不一致");
                                }
                            }

                            @Override
                            public void onLoadFailed(Exception e, Drawable errorDrawable) {
                                super.onLoadFailed(e, errorDrawable);
                                ivSecondFloor.setImageDrawable(null);
                                refreshHeader.setEnableTwoLevel(false);
                                hasSecondFloor = false;
                                SFLogCollector.i(TAG, "设置二楼失败：" + secondFloorBean.getImageSrc());
                            }
                        });
                        secondFloorAction = secondFloorBean.getActionVo();
                        return;
                    }
                }
                SFLogCollector.i(TAG, "设置二楼没有数据");

                //没有命中二楼 隐藏
                ivSecondFloor.setImageDrawable(null);
                //设置背景色为默认楼层颜色
                if (HomeRefreshManager.getInstance().getStyle() == HomeRefreshManager.TRAN_BACKGROUND) {
                    ivSecondFloor.setBackgroundResource(R.color.sf_floor_base_trans);
                } else {
                    ivSecondFloor.setBackgroundResource(R.color.sf_floor_base_container_bg);
                }
                homeRefreshHeader.updateStyle(hasSecondFloor);
                refreshHeader.setEnableTwoLevel(false);
                break;
            case "配置楼层":
                configFloorBean = bean;
                break;
            case "沉浸式直播":
                FloorViewInterface floorData = floorViewInterfaceHashMap.get(floor);
                if (floorData != null) {
                    floorData.convertData(bean, isCache);
                    if (!isCache) {
                        //不是缓存数据的时候才进行加载
                        floorData.bindData(mContext, this, null, bean, -1);
                    }
                }
                break;
            default:
                if (floorOtherDataCallback != null) {
                    floorOtherDataCallback.setFloors(floor, bean);
                }
                break;
        }
    }

    /**
     * 持有目前首页所有的特殊楼层接口，用于调用和设置
     */
    private final HashMap<String, FloorViewInterface> floorViewInterfaceHashMap = new HashMap<>();

    /**
     * 添加特殊楼层视图
     *
     * @param floor 特殊楼层
     */
    public void addSpecialFloor(String floor, FloorBaseInterface floorViewInterface) {
        switch (floor) {
            case "导航":
                if (navigationContainer.getChildCount() == 0) {
                    floorViewInterfaceHashMap.put(floor, floorViewInterface);
                    mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                    View view = floorViewInterface.createView(mContext, this);
                    navigationContainer.addView(view);
                    //这里约定 楼层高度等于导航栏高度
                    //注意这里设置高度后并没有刷新，因为后续肯定会设置楼层数据，能够正常触发变更！！！！！！！！
                    //!!!!!!!!
                    floorContainerItemDecoration.setNavBarHeight(floorViewInterface.getFloorHeight());
                }
                break;
            case "浮窗":
                if (floatingViewContainer.getChildCount() == 0) {
                    floorViewInterfaceHashMap.put(floor, floorViewInterface);
                    mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                    View view = floorViewInterface.createView(mContext, this);
                    floatingViewContainer.addView(view);
                }
                break;
            case "首页弹窗聚合":
                floorViewInterfaceHashMap.put(floor, floorViewInterface);
                mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                break;
            case "吸顶":
                if (topCeilingContainer.getChildCount() == 0) {
                    floorViewInterfaceHashMap.put(floor, floorViewInterface);
                    mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                    View view = floorViewInterface.createView(mContext, this);
                    topCeilingContainer.addView(view);
                }
                break;
            case "吸顶2":
                if (topCeilingContainer2.getChildCount() == 0) {
                    floorViewInterfaceHashMap.put(floor, floorViewInterface);
                    mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                    View view = floorViewInterface.createView(mContext, this);
                    topCeilingContainer2.addView(view);
                }
                break;
            case "沉浸式直播":
                floorViewInterfaceHashMap.put(floor, floorViewInterface);
                mShellRecycleViewAdapter.addViewHolderInterface(floorViewInterface);
                View view = floorViewInterface.createView(mContext, this);
                liveContainer.addView(view);
            default:
                break;
        }
    }


    //////////////////////////////////////////////
    /////////////  数据请求处理区域开始   ////////////
    //////////////////////////////////////////////


    /**
     * @param floorDataManager 设置数据处理方法
     */
    public void setFloorDataManager(FloorDataManager floorDataManager) {
        this.floorDataManager = floorDataManager;
        if (floorDataManager != null) {
            floorDataManager.bindView(this);
        }
    }

    /**
     * 手动触发数据刷新加载
     *
     * @param scrollToTop     是否在刷新的同时滚动到顶部
     * @param needAutoRefresh 是否要触发下拉刷新动画
     */
    public void refreshData(boolean scrollToTop, boolean needAutoRefresh) {
        isCache = false;
        if (floorDataManager == null) {
            SFLogCollector.e(TAG, "floorDataManager is null");
            return;
        }
        if (smartRefreshLayout.isLoading()) {
            smartRefreshLayout.closeFooter();
        }
        if (scrollToTop && mFloorRecyclerView != null) {
            mFloorRecyclerView.scrollToPosition(0);
            if (navigationContainer != null) {
                navigationContainer.setTranslationY(0);
            }
        }
        if (needAutoRefresh) {
            smartRefreshLayout.setEnableRefresh(true);
        }
        if (needAutoRefresh && smartRefreshLayout.autoRefresh(0, 150, 1.0f, false)) {
            SFLogCollector.d(TAG, "autoRefresh while smartRefreshLayout is not Refreshing and twoLevel");
            if (homeRefreshHeader != null) {
                homeRefreshHeader.onStateChanged(smartRefreshLayout, RefreshState.None, RefreshState.Refreshing);
            }
            //超时自动关闭
            handler.removeCallbacks(closeRefresh);
            handler.postDelayed(closeRefresh, 10000);
        } else {
            SFLogCollector.d(TAG, "refreshData by manager");
            floorDataManager.refreshData(mContext);
            smartRefreshLayout.setEnableLoadMore(true);
            if (ocrListener != null) {
                ocrListener.exeRefresh();
            }
        }

    }

    /**
     * 停止刷新动画
     */
    private final Runnable closeRefresh = new Runnable() {
        @Override
        public void run() {
            if (smartRefreshLayout.isRefreshing()) {
                smartRefreshLayout.finishRefresh();
            }
        }
    };


    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////   生命周期回调区域 开始  /////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////


    /**
     * 生命周期-onResume，需要外部手动调用下
     *
     * @param hidden 当前是否是隐藏状态
     */
    @Override
    public void onResume(boolean hidden) {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onResume(hidden);
        }
        ShareAnimationPlayer.get(mContext, ShareAnimationPlayer.PlayType.HOME_LIST).onResume(hidden);


//        if(!hidden){
        //GPS权限和定位权限都开启才会关闭弹窗
        if (LocationHelper.isLocationPermissionOn(getActivity())) {
            hiddenCreateLocationTip();
        } else {
            showCreateLocationTipState();
        }

//        }
    }

    /**
     * 生命周期-onPause，需要外部手动调用下
     */
    @Override
    public void onPause() {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onPause();
        }
        ShareAnimationPlayer.get(mContext, ShareAnimationPlayer.PlayType.HOME_LIST).onPause();
    }

    /**
     * 生命周期-onHiddenChange，需要外部手动调用下
     *
     * @param hidden 当前是否是隐藏状态
     */
    @Override
    public void onHiddenChange(boolean hidden) {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onHiddenChange(hidden);
        }
        ShareAnimationPlayer.get(mContext, ShareAnimationPlayer.PlayType.HOME_LIST).onHiddenChanged(hidden);
    }

    /**
     * 生命周期-onDestroy，需要外部手动调用下
     */
    @Override
    public void onDestroy() {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onDestroy();
        }
        ShareAnimationPlayer.get(mContext, ShareAnimationPlayer.PlayType.HOME_LIST).destroy();
        activity = null;
        if (floorViewInterfaceHashMap != null) {
            floorViewInterfaceHashMap.clear();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (getActivity() != null) {

            //闭环定位权限请求逻辑 记录是否请求过
            if (permissions != null && permissions.length > 0) {
                List<String> permissionList = Arrays.asList(permissions);
                if (permissionList.contains(PermissionUtils.PERMISSION_ACCESS_COARSE_LOCATION) || permissionList.contains(PermissionUtils.PERMISSION_ACCESS_FINE_LOCATION)) {
                    PreferenceUtil.saveBoolean(PermissionUtils.KEY_PERMISSION_LOCATION_FIRST, false);
                    if (LocationHelper.isGrantLocationPermission(getActivity()) && !LocationHelper.isOpenLocation(getActivity())) {
                        openLocationSetting();
                    }
                }
            }

            if (requestCode == permissionRequestCode && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && permissions != null && permissions.length > 0 && !PermissionUtils.hasPermission(getActivity(), permissions)) {
                //以防万一还是重置掉  理论上权限申请过程中，view不可能复用
                permissionRequestCode = 0;
            }

        }


        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }

    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////   数据更新通知区域 开始  /////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * 通知指定的楼层发生了变化需要刷新
     *
     * @param floorIndex
     */
    public void notifyItemChange(int floorIndex) {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.notifyItemChanged(floorIndex);
        }
    }

    @Override
    public void notifyDataChange() {
        notifyDataSetChanged();
    }

    @Override
    public void scrollToPos(int pos) {
        if (mFloorRecyclerView != null) {
            layoutManager.scrollToPositionWithOffset(pos, topCeilingContainer.getHeight());
        }
    }

    /**
     * 通知刷新全部楼层
     */
    public void notifyDataSetChanged() {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.notifyDataSetChanged();
        }
    }


    /**
     * 插入楼层数据 ，并局部刷新
     *
     * @param data  插入的数据
     * @param index 插入的位置
     */
    public void insertData(List<FloorDetailBean> data, int index) {
        if (mShellRecycleViewAdapter == null) {
            return;
        }
        mShellRecycleViewAdapter.insertData(data, index);
    }

    /**
     * 插入楼层数据并且触发全部楼层刷新
     *
     * @param data  插入的数据
     * @param index 插入的位置
     */
    public void insertDataNotifyAll(List<FloorDetailBean> data, int index) {
        if (mShellRecycleViewAdapter == null) {
            return;
        }
        mShellRecycleViewAdapter.insertDataNotifyAll(data, index);
    }


    /**
     * 向楼层发送数据
     *
     * @param templateCode 楼层id
     * @param event        参数对象
     *                     如果参数对修昂是 {@link BannerChangeEvent} 则同时是在通知首页的沉浸式效果
     */
    public void postFloorEvent(String templateCode, Object event) {
        if (mShellRecycleViewAdapter != null) {
            mShellRecycleViewAdapter.postFloorEvent(templateCode, event);
        }
        if (event instanceof BannerChangeEvent) {
            homeRefreshHeader.updateStyle(hasSecondFloor);
            if (!hasSecondFloor) {
                SFLogCollector.i(TAG, "设置二楼沉浸式");
                ivSecondFloor.setBackgroundColor(StringUtil.getSetColor(mContext.getString(R.string.sf_floor_base_container_bg), ((BannerChangeEvent) event).getBackgroundMainColor()));
            }
        }

        if (templateCode.equals("home_page_order_progress") && (event instanceof CommonEvent)) {
            if (!TextUtils.isEmpty(((CommonEvent) event).eventJsonValue)) {
                JDJSONObject jsonObject = JDJSONObject.parseObject(((CommonEvent) event).eventJsonValue);
                if (jsonObject.containsKey("index")) {
                    int index = jsonObject.getIntValue("index");
                    if (mShellRecycleViewAdapter.getItemData(index) == null) {
                        return;
                    }
                    if ("home_page_order_progress".equals(mShellRecycleViewAdapter.getItemData(index).getTemplateCode())) {
                        mShellRecycleViewAdapter.removeItem(index);
                        HomeRefreshManager.getInstance().removeItem("home_page_order_progress");
                    } else {
                        return;
                    }
                }
                if (flNavTrans == null || flNavTrans.getVisibility() != VISIBLE) {
                    return;
                }
                if (jsonObject.containsKey("itemHeight")) {
                    int itemHeigh = jsonObject.getIntValue("itemHeight");
                    float transY = flNavTrans.getTranslationY();
                    // 创建ObjectAnimator对象
                    ObjectAnimator animator = ObjectAnimator.ofFloat(flNavTrans, "translationY", transY, transY - itemHeigh);
                    // 设置动画持续时间为250毫秒
                    animator.setDuration(300);
                    animator.addListener(new androidx.core.animation.AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(@NonNull androidx.core.animation.Animator animation) {
                            super.onAnimationEnd(animation);
                            if (flNavTrans != null) {
                                flNavTrans.setTranslationY(transY - itemHeigh);
                            }
                        }
                    });
                    // 开始动画
                    animator.setStartDelay(600);
                    animator.start();
                }
            }
        }
    }


    /// ///////////////////////////////////////////
    /// //////////  数据请求处理区域结束   ////////////
    /// ///////////////////////////////////////////

    private onContainerRefreshListener ocrListener;

    public void setOnContainerRefreshListener(onContainerRefreshListener listener) {
        ocrListener = listener;
    }

    /**
     * 滚动到首页顶部
     */
    public void scrollToTop() {
        if (mFloorRecyclerView != null) {
            mFloorRecyclerView.scrollToPosition(0);
        }
    }

    public boolean isUseCache() {
        return isCache;
    }

    public void insertMayLike(int floorIndex) {
        mayLikePosition.add(floorIndex);
    }

    /**
     * @param pos 当前的pos
     * @return 当前pos前面插入了几个推荐
     */
    public int getMayLikeCountBeforePos(int pos) {
        int count = 0;
        for (Integer likePos : mayLikePosition) {
            if (likePos != null && likePos < pos) {
                count++;
            }
        }
        return count;
    }

    public void useCacheEnter() {
        if (smartRefreshLayout.isLoading()) {
            smartRefreshLayout.closeFooter();
        }
        if (navigationContainer != null) {
            navigationContainer.setTranslationY(0);
        }
        floorDataManager.useCacheEnter();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                layoutManager.scrollToPosition(0);
            }
        }, 300);
    }

    /**
     * 首页刷新回调
     */
    public interface onContainerRefreshListener {
        /**
         * 首页触发了刷新
         */
        void exeRefresh();
    }

    /**
     * 滑动距离监听
     */
    private OnScrollDistanceListener scrollDistanceListener;

    /**
     * @param scrollDistanceListener 设置滚动距离监听
     */
    public void setOnScrollDistanceListener(OnScrollDistanceListener scrollDistanceListener) {
        this.scrollDistanceListener = scrollDistanceListener;
    }

    /**
     * 首页曝光数组
     */
    private final SparseBooleanArray sparseBooleanArray = new SparseBooleanArray();

    /**
     * 曝光埋点方法
     */
    private void exposure() {
        if (layoutManager == null) {
            return;
        }
        int[] firstItems = null;
        int[] lastItems = null;
        firstItems = layoutManager.findFirstVisibleItemPositions(null);
        lastItems = layoutManager.findLastVisibleItemPositions(null);
        int first = RecyclerViewUtils.getMinValue(firstItems, lastItems);
        int last = RecyclerViewUtils.getMaxValue(firstItems, lastItems);
        if (mShellRecycleViewAdapter == null || first == -1) {
            return;
        }

        for (int i = first; i <= last; i++) {
            // 剔除已曝光的数据
            if (sparseBooleanArray.get(i)) {
                continue;
            }
            sparseBooleanArray.put(i, true);
            RecyclerView.ViewHolder holder = mFloorRecyclerView.findViewHolderForAdapterPosition(i);
            if (holder instanceof FloorBaseViewHolder) {
                ((FloorBaseViewHolder) holder).onExposureFloor();
            } else {
                SFLogCollector.e(TAG, "exposure not baseViewHolder");
            }
        }
    }

    /**
     * @return 获取首页的adapter容器
     */
    public ShellRecycleViewAdapter getmShellRecycleViewAdapter() {
        return mShellRecycleViewAdapter;
    }

    /**
     * @param floorOtherDataCallback 设置首页透传楼层回调
     */
    public void setFloorOtherDataCallback(FloorOtherDataCallback floorOtherDataCallback) {
        this.floorOtherDataCallback = floorOtherDataCallback;
    }


    /**
     * @param activity 设置首页依赖的activity
     */
    public void setActivity(Activity activity) {
        this.activity = activity;
        if (topCeilingContainer != null) {
            int statusBarHeight = safeGetStatusBarHeight(activity);
            topCeilingContainer.setPadding(0, statusBarHeight, 0, 0);
        }
    }

    /**
     * @return 获取首页的activity
     */
    @Nullable
    public Activity getActivity() {
        return activity;
    }

    /**
     * @return 获取首页的埋点接口
     */
    @Nullable
    public JDMaUtils.JdMaPageImp getJdMaPageImp() {
        return jdMaPageImp;
    }

    /**
     * @param jdMaPageImp 设置首页的埋点接口
     */
    public void setJdMaPageImp(JDMaUtils.JdMaPageImp jdMaPageImp) {
        this.jdMaPageImp = jdMaPageImp;
        floorDataManager.setJdMaPageImp(this.jdMaPageImp);
    }


    @Override
    public List<FloorDetailBean> getData() {
        if (mShellRecycleViewAdapter == null) {
            return null;
        }
        return mShellRecycleViewAdapter.getData();
    }

    @Override
    public View getCellingView2() {
        return topCeilingContainer2;
    }

    @Override
    public void enableLoadMore() {
        smartRefreshLayout.setEnableLoadMore(true);
    }

    /**
     * @return 获取当前首页的配置楼层
     */
    @Nullable
    public FloorDetailBean getConfigFloorBean() {
        return configFloorBean;
    }

    /**
     * 缓存一下高
     */
    private int statusBarHeight = 0;

    /**
     * 目前出现了不能出的错误，堆栈无法对应 手动catch一下
     *
     * @param activity
     * @return
     */
    private int safeGetStatusBarHeight(Activity activity) {
        try {
            statusBarHeight = ImmersionBar.getStatusBarHeight(activity);
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        return statusBarHeight;
    }

    public void setRefreshStateChangeListener(RefreshStateChangeListener refreshStateChangeListener) {
        this.refreshStateChangeListener = refreshStateChangeListener;
    }

    // 列表内容刚刚填充或刷新时，手动触发可视区域第一个高光视频并播放
    private Runnable autoPlayRunnable = new Runnable() {
        @Override
        public void run() {
            if (recyclerviewPlayHelper != null) {
                recyclerviewPlayHelper.autoPlay();
            }
        }
    };

    /**
     * 先移除，在增加，延迟1秒，确保1秒内不会被多次执行高光时刻视频的自动播放检测
     */
    private void postDelayAutoPlay() {
        handler.removeCallbacks(autoPlayRunnable);
        handler.postDelayed(autoPlayRunnable, 2000);
    }
}
