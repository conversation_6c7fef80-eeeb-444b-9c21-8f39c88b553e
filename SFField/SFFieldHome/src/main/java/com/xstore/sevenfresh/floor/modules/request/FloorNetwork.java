package com.xstore.sevenfresh.floor.modules.request;


import android.content.Context;
import android.text.TextUtils;
import android.util.Pair;

import com.jd.TypeReference;
import com.jd.framework.json.JDJSON;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sdk.floor.floorcore.FloorBaseNetwork;
import com.xstore.sdk.floor.floorcore.bean.ResponseData;
import com.xstore.sevenfresh.addressstore.bean.TenantShopInfo;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.datareport.entity.BaseMaEntity;
import com.xstore.sevenfresh.floor.modules.FloorContainer;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.sevenfresh.floor.modules.model.HomeFloorsBean;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.floor.modules.model.HomeTenantShopFloorBean;
import com.xstore.sevenfresh.floor.modules.model.QueryIndexPlatformShopList;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.CacheConfig;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.sflog.SFLogProxyInterface;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 楼层基本数据请求
 */
public class FloorNetwork extends FloorBaseNetwork {

    /**
     * 楼层数据缓存key前缀
     */
    private static final String CACHE_PREFIX = "FLOOR_SDK_CACHE";
    /**
     * 缓存的场景key
     */
    private static final String HOME_INDEX_7FRESH_GRAPHQL_QUERY = "Home_index_7fresh_graphql_query";

    public static final String SDK_VERSION = "1.0.0";

    /**
     * 数据请求时使用的屏幕高度
     * 目前还不止分屏，所以还是写死高度
     */
    public static final int MAX_HEIGHT = 9999;

    private static Pair<String, HomeFloorsBean> preloadCache = null;

    /**
     * 请求楼层数据
     *
     * @param context   上下文
     * @param varsExtra 首页请求的动态数据,可能是null
     * @param callback  数据请求回调
     */
    public static void requestPost(Context context, JDJSONObject varsExtra, FloorRequestCallback callback, CacheConfig cacheConfig) {
        String storeId = TenantIdUtils.getStoreId();
        String fenceId = TenantIdUtils.getFenceId();
        SFLogCollector.i(FloorContainer.TAG, "requestPost cur: storeId：" + storeId + " fenceId：" + fenceId);
        List<String> fieldName = new ArrayList<>();
        fieldName.add("pageFloors");
        fieldName.add("baseParam_MemberInfo");
        if (varsExtra != null) {
            varsExtra.put("exposureSkus", PreferenceUtil.getStringMax100("exposureSkus"));
            varsExtra.put("carSkus", PreferenceUtil.getStringMax100("carSkus"));
            varsExtra.put("clkSkus", PreferenceUtil.getStringMax100("clkSkus"));
        }

        //预加载不计算请求次数
        if (cacheConfig.isNeedRequest()) {
            FloorDataManager.getInstance().requestStep++;
        }

        requestGqlByCache(context, FreshHttpSetting.NO_EFFECT, null, 60, fieldName, varsExtra,
                !cacheConfig.isNeedRequest() ? 0 : FloorDataManager.getInstance().requestStep, SDK_VERSION, MAX_HEIGHT, cacheConfig, new BaseFreshResultCallback<String, ResponseData<HomeFloorsBean>>() {

                    @Override
                    public ResponseData<HomeFloorsBean> onData(String data, FreshHttpSetting httpSetting) {
                        //特殊解析
                        return JDJSON.parseObject(data, new TypeReference<ResponseData<HomeFloorsBean>>() {
                        }.getType());
                    }

                    @Override
                    public void onEnd(ResponseData<HomeFloorsBean> response, FreshHttpSetting httpSetting) {
                        //使用缓存加载数据
                        if (httpSetting.isFromCache()) {
                            if (response != null && response.getData() != null) {
                                if (preloadCache == null) {
                                    //冷启动、预加载数据
                                    //todo 之后要区分围栏
                                    preloadCache = new Pair<>(TenantIdUtils.getStoreId(), response.getData());
                                } else {
                                    //网络异常、直接取缓存
                                    callback.callbackFloors(storeId, fenceId, response.getData().getOmnitechPageComponentVoList(), true);
                                }
                            } else {
                                if(callback != null) {
                                    //网络异常、无缓存数据时
                                    callback.callbackFloors(storeId, fenceId,null, false);
                                }
                            }
                            return;
                        }

                        String requestStoreId = null;
                        String requestFenceId = null;
                        if (httpSetting.getCustomVariables() != null) {
                            requestStoreId = (String) httpSetting.getCustomVariables().get("localStoreId");
                            requestFenceId = (String) httpSetting.getCustomVariables().get(FloorBaseNetwork.LOCAL_FENCE_ID);
                            if (!TextUtils.equals(TenantIdUtils.getStoreId(), requestStoreId)) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                                return;
                            } else if (!TextUtils.equals(TenantIdUtils.getFenceId(), requestFenceId)) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- fenceId");
                                return;
                            }
                            int step = (int) httpSetting.getCustomVariables().get("requestStep");
                            if (step != FloorDataManager.getInstance().requestStep) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                                return;
                            }
                        }
                        if (response == null || response.getData() == null) {
                            //判断一下是否有缓存数据可以加载
                            loadCache(callback, storeId, fenceId);
                            return;
                        }
                        //判断当前刷新的门店数据和当前门店是否一致
                        callback.callbackFloors(storeId, fenceId, response.getData().getOmnitechPageComponentVoList(), false);

                        try {
                            if (response.getData().getCode() != 200) {
                                SFLogProxyInterface.BusinessErrorLog errorLog = new SFLogProxyInterface.BusinessErrorLog();
                                errorLog.type = 9502;
                                errorLog.errorCode = "首页_首页接口异常";
                                errorLog.errorMsg = "params:" + httpSetting.getMapParams() + "res:" + response;
                                errorLog.location = "首页";
                                SFLogCollector.reportBusinessErrorLog(errorLog);
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(FreshHttpException error) {
                        if (error != null && FreshHttpException.ERR_TYPE_COLOR_RETRY == error.getErrorType()) {
                            return;
                        }
                        String requestStoreId = null;
                        String requestFenceId = null;
                        if (error != null && error.getHttpSetting() != null && error.getHttpSetting().getCustomVariables() != null) {
                            requestStoreId = (String) error.getHttpSetting().getCustomVariables().get("localStoreId");
                            requestFenceId = (String) error.getHttpSetting().getCustomVariables().get(FloorBaseNetwork.LOCAL_FENCE_ID);
                            if (!TextUtils.equals(TenantIdUtils.getStoreId(), requestStoreId)) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                                return;
                            } else if (!TextUtils.equals(TenantIdUtils.getFenceId(), requestFenceId)) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- fenceId");
                                return;
                            }
                            int step = (int) error.getHttpSetting().getCustomVariables().get("requestStep");
                            if (step != FloorDataManager.getInstance().requestStep) {
                                SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                                return;
                            }
                        }
                        //代码问题引起的onError不走缓存
                        if(error != null && error.getErrorType() != 6) {
                            loadCache(callback, storeId, fenceId);
                        }
                    }
                });
    }

    /**
     * 兜底页楼层分页请求
     *
     * @param context          上下文
     * @param onLoadMoreResult 分页请求回调
     */
    public static void requestMoreStoreList(Context context, RecommendRequest.OnLoadMoreResult onLoadMoreResult) {
        if (onLoadMoreResult == null) {
            return;
        }
        List<String> localFieldName = new ArrayList<>();
        localFieldName.add("queryIndexPlatformShopList");

        JDJSONObject varsExtra = new JDJSONObject();
//        FloorDataManager.getInstance().curStoreListPage += 1;
        varsExtra.put("page", (FloorDataManager.getInstance().curStoreListPage + 1));
        varsExtra.put("pageSize", 10);

        FloorNetwork.requestGql(context, FreshHttpSetting.NO_EFFECT, null, 0,
                localFieldName, varsExtra, FloorDataManager.getInstance().requestStep, SDK_VERSION, MAX_HEIGHT,
                new BaseFreshResultCallback<String, HomeFloorsBean>() {

                    @Override
                    public HomeFloorsBean onData(String data, FreshHttpSetting httpSetting) {
                        ResponseData<HomeTenantShopFloorBean> response = JDJSON.parseObject(data, new TypeReference<ResponseData<HomeTenantShopFloorBean>>() {
                        }.getType());


                        if (response != null && response.getData() != null &&
                                response.getData().getQueryIndexPlatformShopList() != null &&
                                response.getData().getQueryIndexPlatformShopList().isSuccess()) {
                            //数据请求成功了 那么写一下缓存
                            HomeFloorsBean bean = convertStoreList(response.getData().getQueryIndexPlatformShopList(), response.getData().getQueryIndexPlatformShopList().getPage());
                            return bean;
                        }
                        return null;
                    }

                    @Override
                    public void onEnd(HomeFloorsBean object, FreshHttpSetting httpSetting) {

                        //如果当前的门店id已经不是0了，也没有必要加载兜底页了
                        if (!TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                            onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
                            return;
                        }
                        int step = (int) httpSetting.getCustomVariables().get("requestStep");
                        if (step != FloorDataManager.getInstance().requestStep) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                            onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
                            return;
                        }

                        //记录一下当前的页数
                        FloorDataManager.getInstance().curStoreListPage = object.getPage();
                        if (object != null) {
                            //判断当前刷新的门店数据和当前门店是否一致
                            if (object.getOmnitechPageComponentVoList() == null || object.getOmnitechPageComponentVoList().size() == 0) {
                                onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.NO_MORE_DATA, null);
                                return;
                            }
                            onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.SUCCESS, object.getOmnitechPageComponentVoList());
                        } else {
                            onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
                        }
                    }

                    @Override
                    public void onError(FreshHttpException error) {
                        if (error != null && FreshHttpException.ERR_TYPE_COLOR_RETRY == error.getErrorType()) {
                            return;
                        }
//                //如果当前的门店id已经不是0了，也没有必要加载兜底页了
//                if (!TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
//                    SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
//                    onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
//                    return;
//                }
//                int step = (int) error.getHttpSetting().getCustomVariables().get("requestStep");
//                if (step != FloorDataManager.getInstance().requestStep) {
//                    SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
//                    onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
//                    return;
//                }
                        onLoadMoreResult.onResult(RecommendRequest.OnLoadMoreResult.FAIL, null);
                    }
                });
    }

    /**
     * 获取兜底门店列表
     *
     * @param context  上下文
     * @param page     当前请求的页数
     * @param pageSize 当前请求的分页大小
     * @param callback 请求回调
     */
    public static void requestStoreList(Context context, int page, int pageSize, FloorRequestCallback callback, CacheConfig config) {
        List<String> localFieldName = new ArrayList<>();
        localFieldName.add("queryIndexPlatformShopList");

        JDJSONObject varsExtra = new JDJSONObject();
        varsExtra.put("page", page);
        varsExtra.put("pageSize", pageSize);

        if (config.isNeedRequest()) {
            FloorDataManager.getInstance().requestStep++;
        }
        FloorNetwork.requestGqlByCache(context, FreshHttpSetting.NO_EFFECT, null, 0,
                localFieldName, varsExtra, !config.isNeedRequest() ? 0 : FloorDataManager.getInstance().requestStep, SDK_VERSION, MAX_HEIGHT, page == 1 ? config : null,
                new BaseFreshResultCallback<String, HomeFloorsBean>() {

                    @Override
                    public HomeFloorsBean onData(String data, FreshHttpSetting httpSetting) {

                        ResponseData<HomeTenantShopFloorBean> response = JDJSON.parseObject(data, new TypeReference<ResponseData<HomeTenantShopFloorBean>>() {
                        }.getType());


                        if (response != null && response.getData() != null &&
                                response.getData().getQueryIndexPlatformShopList() != null &&
                                response.getData().getQueryIndexPlatformShopList().isSuccess() &&
                                response.getData().getQueryIndexPlatformShopList().getShopInfoList() != null &&
                                response.getData().getQueryIndexPlatformShopList().getShopInfoList().size() > 0) {

                            return convertStoreList(response.getData().getQueryIndexPlatformShopList(), response.getData().getQueryIndexPlatformShopList().getPage());
                        }
                        return null;
                    }

                    @Override
                    public void onEnd(HomeFloorsBean object, FreshHttpSetting httpSetting) {
                        //使用缓存加载数据
                        if (httpSetting.isFromCache()) {
                            if (object != null) {
                                if (preloadCache == null) {
                                    //冷启动、预加载数据
                                    preloadCache = new Pair<>(TenantIdUtils.DEFAULT_STORE_ID, object);
                                } else {
                                    //网络异常、直接取缓存
                                    if(callback != null) {
                                        callback.callbackFloors(TenantIdUtils.DEFAULT_STORE_ID, null, object.getOmnitechPageComponentVoList(), true);
                                    }
                                }

                            } else {
                                if(callback != null) {
                                    callback.callbackFloors(TenantIdUtils.DEFAULT_STORE_ID, null, null, true);
                                }
                            }
                            //网络异常、无缓存数据时
                            return;
                        }

                        //如果当前的门店id已经不是0了，也没有必要加载兜底页了
                        if (!TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                            return;
                        }
                        //默认门店不存在围栏，那么就没有必要判断了
                        int step = (int) httpSetting.getCustomVariables().get("requestStep");
                        if (step != FloorDataManager.getInstance().requestStep) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                            return;
                        }

                        if (object != null) {
                            //判断当前刷新的门店数据和当前门店是否一致
                            callback.callbackFloors(TenantIdUtils.DEFAULT_STORE_ID, null, object.getOmnitechPageComponentVoList(), false);
                        } else {
                            loadStoreListCache(callback);
                        }
                    }

                    @Override
                    public void onError(FreshHttpException error) {
                        if (error != null && FreshHttpException.ERR_TYPE_COLOR_RETRY == error.getErrorType()) {
                            return;
                        }

                        //如果当前的门店id已经不是0了，也没有必要加载兜底页了
                        if (!TenantIdUtils.DEFAULT_STORE_ID.equals(TenantIdUtils.getStoreId())) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                            return;
                        }
                        int step = (int) error.getHttpSetting().getCustomVariables().get("requestStep");
                        if (step != FloorDataManager.getInstance().requestStep) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                            return;
                        }
                        //代码问题引起的error不走缓存了
                        if(error.getErrorType() != 6) {
                            loadStoreListCache(callback);
                        }
                    }
                });
    }

    /**
     * 将门店列表数据转成楼层数据，方便统一处理
     * 咱们全部都是使用楼层数据来进行处理
     *
     * @param list 门店列表
     * @param page
     * @return 返回转换后的楼层数据
     */
    private static HomeFloorsBean convertStoreList(QueryIndexPlatformShopList list, int page) {
        HomeFloorsBean bean = new HomeFloorsBean();
        bean.setPage(page);
        ArrayList<FloorDetailBean> floorDetailBeanArrayList = new ArrayList<>();
        bean.setOmnitechPageComponentVoList(floorDetailBeanArrayList);
        if (list != null && list.getShopInfoList() != null) {
            for (TenantShopInfo tenantShopInfo : list.getShopInfoList()) {
                FloorDetailBean floorDetailBean = new FloorDetailBean();
                floorDetailBean.setTemplateCode(FloorDataManager.getInstance().getTenantShopInfoFloorTemplate());
                floorDetailBean.setComponentDataObject(tenantShopInfo);
                floorDetailBeanArrayList.add(floorDetailBean);
            }
        }
        return bean;
    }

    /**
     * 加载兜底楼层缓存数据
     *
     * @param callback 数据回调
     */
    private static void loadStoreListCache(FloorRequestCallback callback) {
        if (callback == null) {
            return;
        }
        //判断当前首页是否是这个门店的
        if (callback.hasThisStoreData(TenantIdUtils.DEFAULT_STORE_ID)) {
            SFLogCollector.i(FloorContainer.TAG, "hasThisStoreData cur:0");
            return;
        }

        try {
            if (preloadCache != null && TextUtils.equals(preloadCache.first, TenantIdUtils.DEFAULT_STORE_ID) && preloadCache.second != null) {
                ArrayList copyData = preloadCache.second.getOmnitechPageComponentVoList() == null ? null : new ArrayList(preloadCache.second.getOmnitechPageComponentVoList());
                callback.callbackFloors(TenantIdUtils.DEFAULT_STORE_ID, null, copyData, true);
                return;
            }
            //预加载线程没有回来就用缓存
            FloorNetwork.requestStoreList(null, 1, 10, callback,storeListUseLocalCache());
            return;
        } catch (Exception e) {
            e.printStackTrace();
            JdCrashReport.postCaughtException(e);
        }
        callback.callbackFloors(TenantIdUtils.DEFAULT_STORE_ID, null, null, false);
    }

    /**
     * 加载首页缓缓存数据
     *
     * @param callback
     * @param storeId
     */
    private static void loadCache(FloorRequestCallback callback, String storeId, String fenceId) {
        SFLogCollector.i(FloorContainer.TAG, "loadCache cur:" + storeId);
        if (callback == null) {
            return;
        }
        //给一个默认值
        if (storeId == null) {
            storeId = TenantIdUtils.DEFAULT_STORE_ID;
        }
        //判断当前首页是否是这个门店的
        if (callback.hasThisStoreData(storeId)) {
            SFLogCollector.i(FloorContainer.TAG, "hasThisStoreData cur:" + storeId);
            return;
        }
        try {
            //预加载数据存在则直接使用
            if (preloadCache != null && TextUtils.equals(preloadCache.first, storeId) && preloadCache.second != null) {
                ArrayList copyData = preloadCache.second.getOmnitechPageComponentVoList() == null ? null : new ArrayList(preloadCache.second.getOmnitechPageComponentVoList());
                callback.callbackFloors(storeId, fenceId, copyData, true);
                return;
            }
            //预加载线程没有回来就用缓存
            FloorNetwork.requestPost(null, null, callback, homeUseLocalCache());
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
            e.printStackTrace();
        }
    }


    public static void useCacheEnter(FloorRequestCallback callback) {
        if (TenantIdUtils.getStoreId().equals(TenantIdUtils.DEFAULT_STORE_ID)) {
            loadStoreListCache(callback);
        } else {
            loadCache(callback, TenantIdUtils.getStoreId(), TenantIdUtils.getFenceId());
        }
    }

    /**
     * 首页容器预加载缓存数据
     */
    public static void preloadCache() {
        try {
            if (TenantIdUtils.getStoreId().equals(TenantIdUtils.DEFAULT_STORE_ID)) {
                FloorNetwork.requestStoreList(null, 1, 10, null,storeListUseLocalCache());
            } else {
                FloorNetwork.requestPost(null, null, null,homeUseLocalCache());
            }
        } catch (Exception e) {
            JdCrashReport.postCaughtException(e);
            e.printStackTrace();
        }
    }

    /**
     * 首页使用本地缓存
     * @return  CacheConfig
     */
    public static CacheConfig homeUseLocalCache() {
        return new CacheConfig()
                .setIgnoreCacheSwitch(true)
                .setNeedCache(true)
                .setNeedRequest(false)
                .setSceneKey(HOME_INDEX_7FRESH_GRAPHQL_QUERY)
                .setDimenKey(CACHE_PREFIX + "-" + TenantIdUtils.getStoreId());
    }

    /**
     * 兜底门店页使用本地缓存
     * @return  CacheConfig
     */
    public static CacheConfig storeListUseLocalCache() {
        return new CacheConfig()
                .setIgnoreCacheSwitch(true)
                .setNeedCache(true)
                .setNeedRequest(false)
                .setSceneKey(HOME_INDEX_7FRESH_GRAPHQL_QUERY)
                .setDimenKey(CACHE_PREFIX + "-" + TenantIdUtils.DEFAULT_STORE_ID);
    }
}
