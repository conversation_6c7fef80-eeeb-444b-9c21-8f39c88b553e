package com.xstore.sevenfresh.floor.modules.vp.helper;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.jingdong.sdk.baseinfo.BaseInfo;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.kk.taurus.playerbase.VideoInitHelper;
import com.kk.taurus.playerbase.entity.DataSource;
import com.kk.taurus.playerbase.event.OnErrorEventListener;
import com.kk.taurus.playerbase.event.OnPlayerEventListener;
import com.xstore.floorsdk.fieldhome.BuildConfig;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.interfaces.VideoHolderInterface;

import androidx.recyclerview.widget.ChildRecyclerView;

import com.xstore.sevenfresh.floor.modules.video.ShareAnimationPlayer;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

/**
 * 列表播放帮助类
 */
public class VpFloorPlayHelper {

    /**
     * 当前正在播放的位置
     */
    private long currentPlayId = -1;

    /**
     * 当前正在
     */
    private RecyclerView.ViewHolder currentPlayViewHolder;

    /**
     * recyclerview 的顶部位置
     */
    private int verticalRecyclerStart;

    private RecyclerView parentRecyclerView;

    /**
     * 首页列表
     */
    private ChildRecyclerView recyclerView;

    /**
     * 播放的类型
     */
    private ShareAnimationPlayer.PlayType playType;
    /**
     * 吸顶区域高度，自动播放时要让出这部分的高度判定
     */
    private int cellingHeight;


    /**
     * 初始化
     *
     * @param recyclerView 列表容器
     * @param playType     播放类型
     */
    public void init(final ChildRecyclerView recyclerView, RecyclerView parentRecyclerView, ShareAnimationPlayer.PlayType playType) {
        this.recyclerView = recyclerView;
        this.playType = playType;
        this.parentRecyclerView = parentRecyclerView;
        recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {

            @Override
            public void onGlobalLayout() {
                int[] location = new int[2];
                recyclerView.getLocationOnScreen(location);
                verticalRecyclerStart = location[1];
                recyclerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });

        addScrollListener();
        addParentRecyclerViewScrollListener();
    }


    private void addParentRecyclerViewScrollListener() {
        if (parentRecyclerView == null) {
            return;
        }
        parentRecyclerView.addOnScrollListener(parentScrollListener);
    }

    public RecyclerView.OnScrollListener getParentScrollListener() {
        return parentScrollListener;
    }

    RecyclerView.OnScrollListener parentScrollListener=new RecyclerView.OnScrollListener() {

        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                //自动播放
                autoPlay();
            }
        }

        @Override
        public void onScrolled(@NonNull RecyclerView rv, int dx, int dy) {
            super.onScrolled(rv, dx, dy);
            if (parentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                //当前滑动不是用户手动触发的 不用暂停视频播放
            } else {
                ShareAnimationPlayer.get(parentRecyclerView.getContext(),playType).pause();
            }
        }
    };

    /**
     * 设置滚动监听
     */
    private void addScrollListener() {

        recyclerView.addOnScrollListener(scrollListener);
    }

    public RecyclerView.OnScrollListener getScrollListener() {
        return scrollListener;
    }

    RecyclerView.OnScrollListener scrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                //自动播放
                autoPlay();
            }
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            if (recyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                //当前滑动不是用户手动触发的 不用暂停视频播放
            } else {
                //ShareAnimationPlayer.get(recyclerView.getContext(), playType).pause();
            }
        }
    };

    /**
     * 获取Item中渲染视图的可见高度
     *
     * @param position
     * @return
     */
    private int getItemVisibleRectHeight(int position, View rectView) {
        RecyclerView.ViewHolder holder = recyclerView.findViewHolderForAdapterPosition(position);
        if (!(holder instanceof VideoHolderInterface)) {
            return 0;
        }
        if (((VideoHolderInterface) holder).getVideoContainer() == null) {
            return 0;
        }
        int[] location = new int[2];
        ((VideoHolderInterface) holder).getVideoContainer().getLocationOnScreen(location);
        int height = ((VideoHolderInterface) holder).getVideoContainer().getHeight();
        //更新一下位置，首页布局会滚动
        int[] recyclerViewLocation = new int[2];
        rectView.getLocationOnScreen(recyclerViewLocation);
        verticalRecyclerStart = recyclerViewLocation[1] + cellingHeight;

        int visibleRect;
        if (location[1] <= verticalRecyclerStart) {
            visibleRect = location[1] - verticalRecyclerStart + height;
        } else {
            if (location[1] + height >= verticalRecyclerStart + rectView.getHeight()) {
                visibleRect = verticalRecyclerStart + rectView.getHeight() - location[1];
            } else {
                visibleRect = height;
            }
        }
        return visibleRect;
    }

    /**
     * 获取第一个可见item的position
     *
     * @return
     */
    public static int getFirstVisibleItem(RecyclerView recyclerView) {
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (null != layoutManager && layoutManager instanceof StaggeredGridLayoutManager) {
            int[] into = new int[2];
            ((StaggeredGridLayoutManager) layoutManager).findFirstVisibleItemPositions(into);
            return into[0] > into[1] ? into[1] : into[0];
        } else if (layoutManager instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
        } else {
            return null != layoutManager && layoutManager instanceof GridLayoutManager ? ((GridLayoutManager) layoutManager).findFirstVisibleItemPosition() : -1;
        }
    }

    /**
     * 获取最后一个可见的item 位置
     *
     * @return
     */
    public int getLastVisibleItem(RecyclerView recyclerView) {
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (null != layoutManager && layoutManager instanceof StaggeredGridLayoutManager) {
            int[] into = new int[2];
            ((StaggeredGridLayoutManager) layoutManager).findLastVisibleItemPositions(into);
            return into[0] > into[1] ? into[0] : into[1];
        }
        if (layoutManager instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition();
        } else {
            return null != layoutManager && layoutManager instanceof LinearLayoutManager ? ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition() : -1;
        }
    }


    /**
     * @return 找到当前正在播放的holder
     */
    private RecyclerView.ViewHolder findCurrentPlayViewHolder() {
        RecyclerView.ViewHolder h2 = recyclerView.findViewHolderForFakeItemId(currentPlayId);
        if (h2 instanceof VideoHolderInterface) {
            return h2;
        } else if (currentPlayViewHolder instanceof VideoHolderInterface) {
            return currentPlayViewHolder;
        }
        return null;
    }

    public static boolean isMobileNet(Context context) {
        String type = BaseInfo.getNetworkType();
        if (BaseInfo.NETWORK_TYPE_WIFI.equals(type)) {
            return false;
        }
        return true;
    }

    public void autoPlay() {
        //v4.8.4 产品要求不再判断网络类型
//        if (isMobileNet(recyclerView.getContext())) {
//            SFLogCollector.d("RecyclerviewPlayHelper", "not wifi");
//            //将之前的视频恢复
//            RecyclerView.ViewHolder h2 = findCurrentPlayViewHolder();
//            if (h2 instanceof VideoHolderInterface) {
//                ((VideoHolderInterface) h2).setPlayUI(false, false);
//            }
//            ShareAnimationPlayer.get(recyclerView.getContext(), playType).stop();
//            currentPlayId = -1;
//            currentPlayViewHolder = null;
//            return;
//        }
        ShareAnimationPlayer.get(recyclerView.getContext(), playType).setSingleErrorEventListeners(new OnErrorEventListener() {

            @Override
            public void onErrorEvent(int i, Bundle bundle) {
                //
                if (BuildConfig.DEBUG) {
                    FloorInit.getFloorConfig().showToast("error" + i);
                } else {
                    JdCrashReport.postCaughtException(new Exception("play error :" + i));
                }
            }
        });

        ShareAnimationPlayer.get(recyclerView.getContext(), playType).setSinglePlayerEventListener(new OnPlayerEventListener() {
            @Override
            public void onPlayerEvent(int i, Bundle bundle) {
                //
                if (i != PLAYER_EVENT_ON_VIDEO_RENDER_START) {
                    return;
                }
                RecyclerView.ViewHolder holder = findCurrentPlayViewHolder();
                if (holder instanceof VideoHolderInterface && !TextUtils.isEmpty(((VideoHolderInterface) holder).getVideoPath()) && ((VideoHolderInterface) holder).getVideoContainer() != null) {
                    ((VideoHolderInterface) holder).setPlayUI(true, true);
                }
            }
        });
        //自动播放
        int first = getFirstVisibleItem(recyclerView);
        int last = getLastVisibleItem(recyclerView);
        //找到第一个视频可视区域大于视频ui一半的条目进行视频播放
        for (int i = first; i <= last; i++) {
            RecyclerView.ViewHolder holder = recyclerView.findViewHolderForAdapterPosition(i);
            if (holder instanceof VideoHolderInterface && !TextUtils.isEmpty(((VideoHolderInterface) holder).getVideoPath()) && ((VideoHolderInterface) holder).getVideoContainer() != null) {
//
                int videoHeight = ((VideoHolderInterface) holder).getVideoContainer().getHeight();
                int itemVisibleRectHeight = getItemVisibleRectHeight(i, recyclerView);

                long holderId = ((VideoHolderInterface) holder).getFakeItemId();

                if (itemVisibleRectHeight < videoHeight / 2) {
                    //判断一下这个如果是之前正在播放的视频，那么需要将他还原回去
                    //将之前的视频恢复
                    if (currentPlayId == holderId) {
                        RecyclerView.ViewHolder h2 = findCurrentPlayViewHolder();
                        if (h2 instanceof VideoHolderInterface) {
                            ((VideoHolderInterface) h2).setPlayUI(false, false);
                        }
                    }
                    continue;
                }
//
                if (currentPlayId == holderId) {
//                    ShareAnimationPlayer.get(playType).resume();
                    ((VideoHolderInterface) holder).setPlayUI(true, true);
                    ShareAnimationPlayer.get(recyclerView.getContext(), playType).makeSureAttach(((VideoHolderInterface) holder).getVideoContainer()); //VideoInitHelper.getVideoCacheProxy().getProxyUrl(((VideoHolderInterface) holder).getVideoPath())
                    ShareAnimationPlayer.get(recyclerView.getContext(), playType).resume();
                    break;
                }

                //停止播放防止错位播放
                ShareAnimationPlayer.get(recyclerView.getContext(), playType).stop();

                //将之前的视频恢复
                RecyclerView.ViewHolder h2 = findCurrentPlayViewHolder();
                if (h2 instanceof VideoHolderInterface) {
                    ((VideoHolderInterface) h2).setPlayUI(false, false);
                }

                currentPlayId = holderId;
                currentPlayViewHolder = holder;


                ((VideoHolderInterface) holder).setPlayUI(true, false);
                ShareAnimationPlayer.get(recyclerView.getContext(), playType).play(((VideoHolderInterface) holder).getVideoContainer(),
                        new DataSource(VideoInitHelper.getProxyUrl(recyclerView.getContext(), ((VideoHolderInterface) holder).getVideoPath())), playType);
                break;
            }
        }
    }


    public void resetPlayPosition() {
        ShareAnimationPlayer.get(recyclerView.getContext(), playType).stop();
        currentPlayId = -1;
        currentPlayViewHolder = null;
    }

    public void onTabSelected() {

        //切换tab后要停止之前的其他tab的视频
        resetPlayPosition();


        recyclerView.post(new Runnable() {
            @Override
            public void run() {
                SFLogCollector.d("setRecommendTab", "layout1 count =" + getLastVisibleItem(recyclerView));
                autoPlay();
            }
        });
    }


    public void setCellingHeight(int cellingHeight) {
        this.cellingHeight = cellingHeight;
    }
}
