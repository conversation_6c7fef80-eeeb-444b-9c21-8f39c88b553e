package com.xstore.sevenfresh.floor.modules.interfaces;

import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

import java.util.List;

/**
 * 楼层数据设置回调接口，楼层实现该接口用于接受楼层数据
 */
public interface FloorDataCallback {
    /**
     * 设置首页楼层数据
     *
     * @param list    楼层数据
     * @param isCache 是否是缓存数据 缓存数据不能上拉加载更多
     * @@param enableLoadMore 是否可以继续加载更多数据 没有底部推荐楼层或者tab商品组楼层的时候，并且不是兜底页，那么是不允许加载更多的
     */
    void setData(List<FloorDetailBean> list, boolean isCache, boolean enableLoadMore);

    /**
     * 设置特殊楼层数据
     *
     * @param templateCode 模板id
     * @param bean         楼层数据
     */
    void setFloors(String templateCode, FloorDetailBean bean, boolean isCache);

    /**
     * @param storeId
     * @return 是否当前展示的数据是指定门店的数据 如果存在数据的话，会停止当前的刷新状态，避免重复刷新
     */
    boolean hasThisStoreData(String storeId);

    /**
     * @param state            -1 失败 0 成功  1 加载更多
     * @param floorDetailBeans
     */
    void setMoreData(int state, List<FloorDetailBean> floorDetailBeans);



    void shouldShowLiveContainer(boolean shouldShow);
}
