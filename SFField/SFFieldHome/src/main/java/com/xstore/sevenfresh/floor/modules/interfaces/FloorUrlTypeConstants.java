package com.xstore.sevenfresh.floor.modules.interfaces;

/**
 * 跳转枚举定义
 */
public interface FloorUrlTypeConstants {

    /**
     * 无跳转
     */
    int NO_LINK = 0;

    /**
     * 商详
     */
    int SKU_DETAIL = 1;

    /**
     * m页
     */
    int H5 = 3;

    /**
     * 分类
     */
    int CATEGORY = 5;

    /**
     * 扫码
     */
    int SCAN_CODE = 207;

    /**
     * 付款码
     */
    int PAY_CODE = 208;

    /**
     * 菜谱
     */
    int MENU = 220;

    /**
     * 加车
     */
//    int ADD_CART = 223;

    /**
     * 小程序
     */
    int MINI_PROGRAM = 224;

    /**
     * 视频
     */
    int VIDEO = 226;

    /**
     * 堂食
     */
    int DINE_IN = 230;

    /**
     * 话题
     */
    int TOPIC = 231;

    /**
     * 直播
     */
    int JD_LIVE = 233;




    ///===== 自己定义的一些
    int CHANGE_STORE = 10000;



}
