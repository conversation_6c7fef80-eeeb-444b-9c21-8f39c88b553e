package com.xstore.sevenfresh.floor.modules.vp.bean;

import android.util.Log;

import java.io.Serializable;

public class VPTabBean implements Serializable {
    private int tabKey;

    private int page;

    private int position;

    private int nextPage;

    private String selectImg;
    private String titleSelectColor;

    private String titleColor;


    private int titleStyle;

    private String title;

    private TabProp normalProp;

    private TabProp selectProp;

    public String getNoSelectedImgUrl() {
        if (normalProp != null) {
            return normalProp.getImageUrl();
        }
        return null;
    }

    public String getSelectedImgUrl() {
        if (selectProp != null) {
            return selectProp.getImageUrl();
        }
        return null;
    }


    public int getTabKey() {
        return tabKey;
    }

    public void setTabKey(int tabKey) {
        this.tabKey = tabKey;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getNextPage() {
        return nextPage;
    }

    public void setNextPage(int nextPage) {
        this.nextPage = nextPage;
    }

    public int getTitleStyle() {
        return titleStyle;
    }

    public void setTitleStyle(int titleStyle) {
        this.titleStyle = titleStyle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSelectImg() {
        return selectImg;
    }

    public void setSelectImg(String selectImg) {
        this.selectImg = selectImg;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public String getTitleSelectColor() {
        return titleSelectColor;
    }

    public void setTitleSelectColor(String titleSelectColor) {
        this.titleSelectColor = titleSelectColor;
    }

    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    //获取埋点tabPos
    public String getTabPos() {
        return position + "";
    }

    public TabProp getNormalProp() {
        return normalProp;
    }

    public void setNormalProp(TabProp normalProp) {
        this.normalProp = normalProp;
    }

    public TabProp getSelectProp() {
        return selectProp;
    }

    public void setSelectProp(TabProp selectProp) {
        this.selectProp = selectProp;
    }

    public static class TabProp implements Serializable {

        private String selectTitleImage;
        private String imageUrl;

        private int width;

        private int height;

        public String getSelectTitleImage() {
            return selectTitleImage;
        }

        public void setSelectTitleImage(String selectTitleImage) {
            this.selectTitleImage = selectTitleImage;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }
    }

}


