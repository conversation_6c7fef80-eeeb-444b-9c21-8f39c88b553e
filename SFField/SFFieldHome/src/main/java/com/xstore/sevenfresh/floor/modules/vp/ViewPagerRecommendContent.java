package com.xstore.sevenfresh.floor.modules.vp;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ChildRecyclerView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;


import com.xstore.floorsdk.fieldhome.BuildConfig;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sdk.floor.floorcore.interfaces.FloorContainerInterface;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.FloorContainer;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sevenfresh.floor.modules.video.ShareAnimationPlayer;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.bean.VpMaEntry;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpFloorPlayHelper;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;
import com.xstore.sevenfresh.floor.modules.vp.sliding.PagerSlidingVpTabStrip;
import com.xstore.sevenfresh.floor.modules.vp.sliding.VpTabView;
import com.xstore.sevenfresh.floor.modules.vp.utils.UIUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.List;


//推荐的楼层
public class ViewPagerRecommendContent extends LinearLayout implements ViewPager.OnPageChangeListener {

    private OnRecommendChangeListener onPageChangeListener;


    protected ArrayList<CategoryView> childViews = new ArrayList<CategoryView>();
    private ViewPager viewPager;

    private FloorContainerInterface floorContainerInterface;

    /**
     * 安全滑动距离
     */
    private static final int SAFE_TOTAL_Y = 4;
    /**
     * onScrollListener 滑动监听
     */
    public RecyclerView.OnScrollListener onScrollListener;


    private PagerSlidingVpTabStrip slidingVpTabStrip;

    CategoryView currentChildRcv;

    public CategoryView getCurrentChildRcv() {
        return currentChildRcv;
    }

    VpFloorPlayHelper vpFloorPlayHelper;


    private int currentPageId = 0;

    /**
     * maxVisitPosition
     */
    private int maxVisitPosition;

    LinearLayout llContent;

    private RelativeLayout floorContainer;

    private FloorDetailBean floorDetailBean;

    protected CategoryPagerAdapter pageAdapter;

    RecyclerView parentRecyclerView;

    FloorDataManager floorDataManager;


    /**
     * 设置pager监听
     *
     * @param listener
     */
    public void setOnPageChangeListener(OnRecommendChangeListener listener) {
        this.onPageChangeListener = listener;
    }

    public ViewPagerRecommendContent(Context context) {
        super(context);
    }

    public ViewPagerRecommendContent(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ViewPagerRecommendContent(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setFloorDetailBean(FloorDetailBean floorDetailBean) {
        this.floorDetailBean = floorDetailBean;
    }

    public FloorDetailBean getFloorDetailBean() {
        return floorDetailBean;
    }

    public void setFloorContainer(RelativeLayout floorContainer) {
        this.floorContainer = floorContainer;
    }

    public void setParentRecyclerView(RecyclerView parentRecyclerView) {
        this.parentRecyclerView = parentRecyclerView;
    }

    public RecyclerView getParentRecyclerView() {
        return parentRecyclerView;
    }

    public void init() {
        initViews();
    }


    @SuppressLint("ResourceAsColor")
    private void initViews() {

        inflate(getContext(), R.layout.layout_floor_vp, this);

        llContent = findViewById(R.id.ll_content);
        //添加TabLayout
        slidingVpTabStrip = findViewById(R.id.tabs);
        floorDataManager = FloorDataManager.getInstance();
        //添加ViewPager
        viewPager = findViewById(R.id.viewPager);
        viewPager.setLayoutParams(new LayoutParams(-1, -1));
        createViewPagerAdapter();
        initStrip();
        viewPager.setAdapter(pageAdapter);
        slidingVpTabStrip.setViewPager(viewPager);


        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {
            }

            @Override
            public void onPageSelected(int position) {
                int maxPosition = childViews.size();
                if (maxPosition >= 1) {
                    position = position > maxPosition ? maxPosition : position;
                    currentPageId = position;

                    if (onPageChangeListener != null) {
                        onPageChangeListener.onPageSelected(position);
                        if (currentChildRcv != null) {
                            onPageChangeListener.onPageSelected(currentChildRcv);
                        }
                    }


                    if (currentChildRcv != childViews.get(position)) {
                        //之前的位置
                        int oldPosition = currentChildRcv.getRecommendTab().getPosition();

                        //移除之前的监听
                        currentChildRcv.removeOnScrollListener(vpFloorPlayHelper.getScrollListener());
                        parentRecyclerView.removeOnScrollListener(vpFloorPlayHelper.getParentScrollListener());
                        currentChildRcv = childViews.get(position);
                        //重新添加监听
                        vpFloorPlayHelper.init(currentChildRcv, parentRecyclerView, ShareAnimationPlayer.PlayType.HOME_LIST);
                        //停止其他tab的视频播放
                        vpFloorPlayHelper.onTabSelected();
                        //TAB前后差值大于3不执行动画
                        int finalPosition = position;
                        int finalPosition1 = position;
                        slidingVpTabStrip.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                slidingVpTabStrip.startTabIndicatorAnimal(finalPosition, Math.abs(finalPosition1 - oldPosition) <= 3);
                            }
                        }, 100);

                        addTabMaData();

                        if (currentChildRcv != null) {
                            currentChildRcv.switchTabToRefreshSelf();
                        }
                    }


                    if (position > maxVisitPosition) {
                        maxVisitPosition = position;
                    }

                }
            }

            @Override
            public void onPageScrollStateChanged(int i) {
            }
        });


        viewPager.setCurrentItem(0);
        currentChildRcv = childViews.get(0);
        // 缓存埋点数据
        addTabMaData();

        vpFloorPlayHelper = new VpFloorPlayHelper();
        vpFloorPlayHelper.init(currentChildRcv, parentRecyclerView, ShareAnimationPlayer.PlayType.HOME_LIST);
    }


    //添加选中项埋点
    private void addTabMaData() {
        VpMaEntry vpMaEntry = new VpMaEntry(floorDetailBean);
        vpMaEntry.tab_name = currentChildRcv.getRecommendTab().getTitle();
        vpMaEntry.tab_pos = currentChildRcv.getRecommendTab().getTabPos();


        PreferenceUtil.saveString(VpTabHelper.KEY_VP_TAB_MA_TITLE, currentChildRcv.getRecommendTab().getTitle());
        PreferenceUtil.saveString(VpTabHelper.KEY_VP_TAB_MA_POS, currentChildRcv.getRecommendTab().getTabPos());

        JDMaUtils.save7FClick(VpMaEntry.Constants.FRONTPAGE_FEEDSTAB_CLICK, ((FloorContainer) floorContainer).getJdMaPageImp(), vpMaEntry);
    }

    /**
     * 推荐位的滚动事件，通知首页
     **/
    private void notifyScroll(RecyclerView recyclerView) {
    }


    public void initStrip() {
        if (childViews.size() <= 4) {
            slidingVpTabStrip.setTabPaddingLeftRight(0);
        }
        slidingVpTabStrip.setTextSize(UIUtils.dp2px(14));
        slidingVpTabStrip.setTabUnSelectTextSize(UIUtils.dp2px(13));
        slidingVpTabStrip.setVpParam(childViews.size());
        slidingVpTabStrip.setOnPageChangeListener(this);

    }


    public void fling(int velocityX, int velocityY) {
        if (null != this.currentChildRcv) {
            this.currentChildRcv.fling(velocityX, velocityY);
        }

    }


    /**
     * 是否存在tab
     *
     * @return
     */
    public boolean hasTab() {
        if (this.slidingVpTabStrip != null) {
            return this.indexOfChild(this.slidingVpTabStrip) > -1;
        } else {
            return false;
        }
    }

    /**
     * 滑动到顶部
     */
    public void viewToTop() {
        this.allChildToTop();
    }

    /**
     * 子列表全部滑动到顶部
     */
    public void allChildToTop() {
        try {
            for (int i = 0; i < this.childViews.size(); ++i) {
                if (this.childViews.get(i) != null) {
                    ((CategoryView) this.childViews.get(i)).scrollToTop();
                }
            }
        } catch (Exception var2) {
            if (BuildConfig.DEBUG) {
                var2.printStackTrace();
            }
        }

    }


    /**
     * 设置滚动监听
     *
     * @param onScrollListener
     */
    public void setOnScrollListener(RecyclerView.OnScrollListener onScrollListener) {
        this.onScrollListener = onScrollListener;
    }


    protected void createViewPagerAdapter() {
        List<VPTabBean> tabBeans = FloorDataManager.getInstance().getTabBeans();
        Log.i("VP5", "tabBeans.size():" + tabBeans.size());
        childViews.clear();
        for (int i = 0; i < tabBeans.size(); i++) {
            VPTabBean tabBean = tabBeans.get(i);
            tabBean.setPosition(i);
            CategoryView childView = new CategoryView(getContext());
            childView.setViewPagerRecommendContent(this);
            childView.setNextPage(tabBean.getNextPage());
            childView.setRecommendTab(tabBean);
            childView.setFloorContainer((FloorContainer) floorContainer);
            childView.setTabKey(tabBean.getTabKey());
            childView.setAdapter();
            childView.setData();
            childViews.add(childView);
        }


        this.pageAdapter = new CategoryPagerAdapter(tabBeans, childViews);
        this.pageAdapter.setRecommendTabs(tabBeans);
    }

    /**
     * 滑动
     *
     * @param dy
     */
    public void onScroll(int dy) {
        if (null != this.currentChildRcv) {
            this.currentChildRcv.scrollBy(0, dy);
        }

    }

    /**
     * @param scrollState
     */
    public void onScrollChanged(int scrollState) {
        if (null != this.currentChildRcv) {
            this.currentChildRcv.onScrollChanged(scrollState);
        }


    }

    /**
     * 当前是否在顶部
     *
     * @return
     */
    public boolean isTop() {
        return this.indexOfChild(this.llContent) == -1 || null != this.currentChildRcv && this.currentChildRcv.isTop();
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * TITLEBARLAYOUTHEIGHT 顶部标题高度 目前没有
     */
    private final int MINITEMWIDTH = UIUtils.getWidthByDesignValueFitFold(40, 375);
    private final int PICWIDTH = UIUtils.getWidthByDesignValueFitFold(62, 375);

    /**
     * TBA_PADDING_LR tab左右间距
     */
    public static double TBA_PADDING_LR = 15;

    public interface OnRecommendChangeListener extends ViewPager.OnPageChangeListener {
        /**
         * @param var1 當前選中的子列表
         */
        void onPageSelected(CategoryView var1);
    }


    class CategoryPagerAdapter extends PagerAdapter implements PagerSlidingVpTabStrip.CustomTabProvider {
        private List<VPTabBean> titleList;

        private ArrayList<CategoryView> viewList;


        /**
         * recommendTabs 推荐内容
         */
        protected List<VPTabBean> recommendTabs;

        public void setRecommendTabs(List<VPTabBean> recommendTabs) {
            this.recommendTabs = recommendTabs;
        }

        /**
         * hasSubtitle 是否存在子标题
         */
        public boolean hasSubtitle = false;
        /**
         * tabItemWidth 计算出来的tab宽度
         */
        private int tabItemWidth;

        public CategoryPagerAdapter(List<VPTabBean> titleList, ArrayList<CategoryView> viewList) {
            this.titleList = titleList;
            this.viewList = viewList;
        }

        @Override
        public int getCount() {
            return viewList.size();
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            ChildRecyclerView rcrv = viewList.get(position);
            if (rcrv != null && rcrv.getParent() instanceof ViewGroup) {
                ((ViewGroup) rcrv.getParent()).removeView(rcrv);
            }
            container.addView(rcrv);
            return rcrv;
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
            return view == o;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            if (object instanceof View) {
                container.removeView((View) object);
            }

        }

        @Override
        public CharSequence getPageTitle(int position) {
            return titleList.get(position).getTitle();
        }

        @Override
        public View getCustomTabView(ViewGroup parent, int position) {
            try {
                boolean foundTabB = position >= 0 && position < viewList.size() &&
                        viewList.get(position) != null;

                if (foundTabB) {
                    return this.getBTabView(position);
                }
            } catch (Exception var4) {
                if (BuildConfig.DEBUG) {
                    var4.printStackTrace();
                }
            }

            return new View(viewList.get(0).getContext());
        }

        @Override
        public void tabSelected(View tab) {
            if (tab instanceof VpTabView) {
                ((VpTabView) tab).setTabSelect(true);
            }

        }

        @Override
        public void tabUnselected(View tab) {
            if (tab instanceof VpTabView) {
                ((VpTabView) tab).setTabSelect(false);
            }
        }


        /**
         * 创建tab view
         *
         * @param position
         * @return
         */
        private View getBTabView(int position) {
            try {
                VpTabView newTabView = new VpTabView(ViewPagerRecommendContent.this.getContext());
                newTabView.setRecommendTab(ViewPagerRecommendContent.this.childViews.get(position).getRecommendTab(), position);
                newTabView.setTabWidth(childViews.size());
                //hasSubtitle = true;
                newTabView.setHasSubTitle(hasSubtitle);

                int height = UIUtils.getWidthByDesignValueFitFold(this.hasSubtitle ? 70 : 45, 375);
                if (recommendTabs != null) {
                    if (tabItemWidth != 0) {
                        if (tabItemWidth < MINITEMWIDTH) {
                            newTabView.setWH(MINITEMWIDTH, height);
                        } else {
                            newTabView.setWH(tabItemWidth, height);
                        }
                    } else {
                        TextPaint paint = newTabView.getTitleTV().getPaint();
                        TextPaint subPainit = newTabView.getSubTitleTV().getPaint();

                        //主标题文字宽度
                        int itemFactorWidth = 0;
                        int minItemWidth = 0;
                        //主标题是否是图片
                        boolean hasPic = false;
                        if (StringUtil.isNullByString(recommendTabs.get(position).getTitle())) {
                            minItemWidth = MINITEMWIDTH + (UIUtils.getWidthByDesignValueFitFold(TBA_PADDING_LR * 2, 375));
                            float textWidth = paint.measureText(recommendTabs.get(position).getTitle());
                            itemFactorWidth = (int) textWidth + (UIUtils.getWidthByDesignValueFitFold(TBA_PADDING_LR * 2, 375));
                        } else {
                            minItemWidth = PICWIDTH + (UIUtils.getWidthByDesignValueFitFold(TBA_PADDING_LR * 2, 375));
                            hasPic = true;
                        }
                        SFLogCollector.d("ClubTabView", recommendTabs.get(position).getTitle() + "hasPic===" + hasPic);
                        newTabView.setWH(minItemWidth, height);
                    }
                }

                return newTabView;
            } catch (Exception var6) {
                return new View(ViewPagerRecommendContent.this.getContext());
            }
        }
    }

}
