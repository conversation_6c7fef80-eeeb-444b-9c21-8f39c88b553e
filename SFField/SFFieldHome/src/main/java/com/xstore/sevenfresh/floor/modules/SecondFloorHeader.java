package com.xstore.sevenfresh.floor.modules;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.scwang.smart.refresh.header.TwoLevelHeader;

public class SecondFloorHeader extends TwoLevelHeader {
    public SecondFloorHeader(Context context) {
        super(context);
    }

    public SecondFloorHeader(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public TwoLevelHeader finishTwoLevel() {
        super.finishTwoLevel();
        return this;
    }
}
