package com.xstore.sevenfresh.floor.modules.model;

import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

import java.io.Serializable;
import java.util.List;

/**
 * 首页完整楼层数据对象
 */
public class HomeFloorsBean implements Serializable {

    private int code;

    private String message;

    private String tenantId;

    private String platformId;

    private String pageId;

    private int page;

    /**
     * 首页楼层完整框架数据
     */
    private List<FloorDetailBean> omnitechPageComponentVoList;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public List<FloorDetailBean> getOmnitechPageComponentVoList() {
        return omnitechPageComponentVoList;
    }

    public void setOmnitechPageComponentVoList(List<FloorDetailBean> omnitechPageComponentVoList) {
        this.omnitechPageComponentVoList = omnitechPageComponentVoList;
    }
}
