package com.xstore.sevenfresh.floor.modules.model;

/**
 * 推荐楼层常量定义
 */
public class RecommendFloorConst {

    /**
     * 推荐类型
     */
    public interface RecommendType {
        /**
         * 商品卡片
         */
        int good = 1;
        /**
         * 菜谱卡片
         */
        int cookMenu = 2;
        /**
         * 广告卡片
         */
        int ad = 3;
        /**
         * 视频卡片
         */
        int video = 4;

        /**
         * 促销卡片
         * 买一赠一 or 第二份半价
         */
        int pormotion = 5;

        /**
         * 新榜单排行榜
         */
        int newRank=10;
    }

    /**
     * 跳转类型
     */
    public interface JumpRecommendType {
        /**
         * 普通卡片
         */
        int normal = 1;
        /**
         * 迷你卡片
         */
        int mini = 2;

        /**
         * 促销搜索页面
         */
        int promotionSearch = 3;

        /**
         * 泽拉图商品列表
         */
        int zelatuPage = 4;
    }
}
