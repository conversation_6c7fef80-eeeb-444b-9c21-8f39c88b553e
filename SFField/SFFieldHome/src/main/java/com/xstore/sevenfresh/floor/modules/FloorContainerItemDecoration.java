package com.xstore.sevenfresh.floor.modules;

import static com.xstore.sevenfresh.floor.modules.FloorContainer.GRID_LINE_ITEM_COUNT;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.boredream.bdcodehelper.utils.DisplayUtils;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

/**
 * 新首页的边框
 */
public class FloorContainerItemDecoration extends RecyclerView.ItemDecoration {

    private final Paint paint;
    private int recyclerviewItemDistance;
    private int recyclerviewPaddingLR;

    private int navBarHeight;

    public FloorContainerItemDecoration(Context context) {
        this.recyclerviewItemDistance = DisplayUtils.dp2px(context, 8);
        this.recyclerviewPaddingLR = DisplayUtils.dp2px(context, 12);
        paint = new Paint();
        paint.setColor(context.getResources().getColor(R.color.sf_floor_base_container_bg));
        paint.setStyle(Paint.Style.FILL);
        paint.setAntiAlias(true);
    }

    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(c, parent, state);

        final int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            final View child = parent.getChildAt(i);
        }

    }

    @Override
    public void onDraw(@NonNull Canvas c, RecyclerView parent) {

//        final int childCount = parent.getChildCount();
//        int center = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
//        int lr = recyclerviewPaddingLR;
//
//        for (int i = 0; i < childCount; i++) {
//            final View child = parent.getChildAt(i);
//            final StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) child.getLayoutParams();
//            int childPosition = params.getSpanIndex();
//
//            if (params.isFullSpan()) {
//                //独占一行不用绘制
//                continue;
//            }
//
//            c.save();
////            if (childPosition % GRID_LINE_ITEM_COUNT == 0) {
////                //绘制左侧item的背景
////                c.drawRect(child.getLeft() - lr, child.getTop(), child.getRight() + lr, child.getBottom(), paint);
////            } else {
//////                outRect.left = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
//////                outRect.right = recyclerviewPaddingLR;
////                c.drawRect(child.getLeft() - lr, child.getTop(), child.getRight() + lr, child.getBottom(), paint);
////            }
////
////
////            //第一行顶部间距9
////            if (childPosition / GRID_LINE_ITEM_COUNT == 0) {
////                c.drawRect(child.getLeft() - lr, child.getTop() - recyclerviewItemDistance, child.getRight() + lr, child.getTop(), paint);
////            }
////
////            c.drawRect(child.getLeft() - lr, child.getBottom(), child.getRight() + lr, child.getBottom() + recyclerviewItemDistance, paint);
//
//            //有过度绘制
//            c.drawRect(parent.getLeft(), child.getTop(), parent.getRight(), child.getBottom(), paint);
//            //第一行顶部间距9
//            if (childPosition / GRID_LINE_ITEM_COUNT == 0) {
//                c.drawRect(parent.getLeft(), child.getTop() - recyclerviewItemDistance, parent.getRight(), child.getTop(), paint);
//            }
//
//            c.drawRect(parent.getLeft(), child.getBottom(), parent.getRight(), child.getBottom() + recyclerviewItemDistance, paint);
//            c.restore();
//        }
    }


    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        //int childPosition = parent.getChildAdapterPosition(view);
        StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) view.getLayoutParams();
        // 获取item在span中的下标
        int childPosition = params.getSpanIndex();

        if (params.isFullSpan()) {
            outRect.right = 0;
            outRect.left = 0;
            outRect.bottom = 0;
            if(parent.getChildAdapterPosition(view) == 0) {
                outRect.top = navBarHeight;
            }
            return;
        }

        if (childPosition % GRID_LINE_ITEM_COUNT == 0) {
            outRect.right = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
            outRect.left = recyclerviewPaddingLR;
        } else {
            outRect.left = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
            outRect.right = recyclerviewPaddingLR;
        }

        outRect.top = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
        outRect.bottom = recyclerviewItemDistance / GRID_LINE_ITEM_COUNT;
    }


    public void setNavBarHeight(int navBarHeight) {
        this.navBarHeight = navBarHeight;
    }
}
