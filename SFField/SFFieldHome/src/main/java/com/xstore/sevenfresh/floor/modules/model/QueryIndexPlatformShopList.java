package com.xstore.sevenfresh.floor.modules.model;

import com.xstore.sevenfresh.addressstore.bean.TenantShopInfo;

import java.io.Serializable;
import java.util.List;

public class QueryIndexPlatformShopList implements Serializable {

    private int page;

    private int pageSize;

    private boolean success;

    private List<TenantShopInfo> shopInfoList;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<TenantShopInfo> getShopInfoList() {
        return shopInfoList;
    }

    public void setShopInfoList(List<TenantShopInfo> shopInfoList) {
        this.shopInfoList = shopInfoList;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
