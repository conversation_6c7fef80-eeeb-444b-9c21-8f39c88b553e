package com.xstore.sevenfresh.floor.modules.interfaces;

import java.util.List;

/**
 * 楼层模板数据
 */
public interface FloorTemplateInterface {

    /**
     * @return 当前导航楼层的id 为null 代表没有 走默认
     */
    String getNavigationTemplateCode();

    /**
     * @return 获取悬浮窗控件楼层
     */
    String getFloatingTemplateCode();

    /**
     * @return 获取吸顶楼层id
     */
    String getCeilingTemplateCode();

    /**
     * @return 获取二楼id
     */
    String getSecondFloorTemplateCode();

    /**
     * @return 轮播图
     */
    String getRollingFloorTemplateCode();

    /**
     * @return 新榜单楼层
     */
    String getNewRankFloorTemplateCode();

    /**
     * @return 沉浸式直播间
     */
    String getCarouselLiveFloorTemplateCode();

    /**
     * @return 腰部直播间
     */
    String getWaistedLiveFloorTemplateCode();

    /**
     * @return 轮播图
     */
    String getStaticRollingFloorTemplateCode();

    /**
     * 栏目组件
     *
     * @return
     */
    String getGridFloorTemplateCode();

    /**
     * @return 栏目组件-秒杀楼层
     */
    String getGridFloorTemplateCode_1();

    /**
     * 栏目组件-其他楼层
     *
     * @return
     */
    String getGridFloorTemplateCode_2();

    /**
     * 栏目组件-会员楼层
     *
     * @return
     */
    String getGridFloorTemplateCode_3();

    /**
     * @return 获取首页弹窗楼层id
     */
    String getHomePopWindowTemplateCode();

    /**
     * 栏目组件-其他楼层
     *
     * @return
     */
    String getRecommendFloorTemplateCode();

    /**
     * tab商品组
     *
     * @return
     */
    String getTabGoodGroupFloorTemplateCode();

    /**
     * 白名单特殊楼层-无需过滤掉的
     */
    List<String> getWhiteListFloor();


    /**
     * 获取推荐数据楼层id
     */
    String getRecommendGoodFloorTemplate();

    /**
     * 获取推荐数据楼层id
     */
    String getRecommendCookMenuFloorTemplate();

    /**
     * 获取推荐数据楼层id
     */
    String getRecommendAdFloorTemplate();

    /**
     * 获取推荐数据楼层id
     */
    String getRecommendVideoFloorTemplate();

    /**
     * 获取 促销楼层 templateCode
     * @return
     */
    String getPromotionFloorTemplate();

    /**
     * 获取推荐数据楼层id
     */
    String getMemberFloorTemplate();

    /**
     * 获取推荐数据楼层id
     * @return
     */
    String getBdFloorTemplate();

    /**
     * @return 气泡楼层
     */
    String getClubBubbleFloorTemplate();

    /**
     * @return 获取首页兜底租户楼层的id
     */
    String getTenantShopInfoFloorTemplate();

    /**
     * @return 获取当前首页配置楼层
     */
    String getConfigFloorTemplate();

    /**
     * @return 获取新人三单礼大组件（三单礼活动+专享价商品）
     */
    String getNewUserGiftTemplateCode();

    /**
     * @return 获取新人三单礼任务进度小组件
     */
    String getNewUserGiftProgressTemplateCode();

    /**
     * @return 返回推荐穿插的轮播
     */
    String getRecommendRollingTemplateCode();

    /**
     * 公告楼层数据
     */
    String getNotifyTemplateCode();

    /**
     * 订单进度楼层数据
     */
    String getOrderProgressTemplateCode();

    /**
     * 通栏的TemplateCode
     * @return
     */
    String getBeltTemplateCode();

    /**
     * 获取秒杀TemplateCode
     * @return
     */
    String getSecKillTemplateCode();


    /**
     * 品牌楼层数据
     */
    String getHomeBrandFloorTemplate();

    /**
     * 新人优惠楼层数据
     */
    String getNewUserPriceFloorTemplate();
}
