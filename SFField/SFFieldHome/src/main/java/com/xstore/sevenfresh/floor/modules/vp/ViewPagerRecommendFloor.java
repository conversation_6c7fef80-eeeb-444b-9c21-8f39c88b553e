package com.xstore.sevenfresh.floor.modules.vp;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.gyf.barlibrary.ImmersionBar;
import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sdk.floor.floorcore.interfaces.AbsBaseFloor;
import com.xstore.sdk.floor.floorcore.interfaces.FloorContainerInterface;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.bean.VpMaEntry;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;
import com.xstore.sevenfresh.floor.modules.vp.utils.UIUtils;

import java.util.List;

public class ViewPagerRecommendFloor extends AbsBaseFloor {

    public static final String templateCode = "home_page_view_pager_recommend_1";

    public ViewPagerRecommendContent viewPagerRecommendContent;


    private FloorContainerInterface floorContainer;


    private FloorDetailBean floorDetailBean;


    private int floorIndex;


    public ViewPagerRecommendContent getViewPagerRecommendContent() {
        return viewPagerRecommendContent;
    }

    @Override
    public boolean onExposureFloor() {
        List<VPTabBean> tabBeans = FloorDataManager.getInstance().getTabBeans();
        VpMaEntry vpMaEntry = new VpMaEntry(floorDetailBean);
        if (tabBeans.size() > 0 && getViewPagerRecommendContent() != null && getViewPagerRecommendContent().currentChildRcv != null) {
            vpMaEntry.tab_name = getViewPagerRecommendContent().currentChildRcv.getRecommendTab().getTitle();
            vpMaEntry.tab_pos = getViewPagerRecommendContent().currentChildRcv.getRecommendTab().getTabPos();
        }
        JDMaUtils.save7FExposure(VpMaEntry.Constants.FRONTPAGE_FEEDSTAB_EXPOSE, null, vpMaEntry, null, floorContainer.getJdMaPageImp());
        return true;
    }

    @Override
    public void bindData(Context context, FloorContainerInterface floorContainer, @Nullable FloorBaseViewHolder floorBaseViewHolder, @Nullable FloorDetailBean floorDetailBean, int floorIndex) {
        this.floorContainer = floorContainer;
        if (floorDetailBean == null) {
            viewPagerRecommendContent.setVisibility(View.GONE);
            return;
        }

        this.floorDetailBean = floorDetailBean;
        if (viewPagerRecommendContent != null) {
            viewPagerRecommendContent.setFloorDetailBean(floorDetailBean);
        }
        this.floorIndex = floorIndex;
        if (VpTabHelper.isRefreshAll) {
            VpTabHelper.isRefreshAll = false;
            viewPagerRecommendContent.init();
        }

        setContentHeight((Activity) context, floorContainer);

    }

    @Override
    public View createView(Context context, FloorContainerInterface floorContainerInterface) {
        LinearLayout view = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.layout_floor_root, null, false);
        viewPagerRecommendContent = (ViewPagerRecommendContent) LayoutInflater.from(context).inflate(R.layout.sf_floor_vp_recommend_content, null, false);
        viewPagerRecommendContent.setFloorContainer(floorContainerInterface.getFlContainer());
        viewPagerRecommendContent.setParentRecyclerView(floorContainerInterface.getParentRcv());
        viewPagerRecommendContent.setFloorDetailBean(floorDetailBean);

        setContentHeight((Activity) context, floorContainerInterface);
        view.addView(viewPagerRecommendContent);
        return view;
    }

    private void setContentHeight(Activity context, FloorContainerInterface floorContainerInterface) {
        int height;
        //计算推荐楼层高度
        if (VpTabHelper.hasSearchFloor) {
            height = floorContainerInterface.getParentRcv().getHeight() - UIUtils.dp2px(38) - ImmersionBar.getStatusBarHeight(context);
        } else {
            height = floorContainerInterface.getParentRcv().getHeight() - ImmersionBar.getStatusBarHeight(context);
        }

        viewPagerRecommendContent.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height));
    }


    @Override
    public boolean isFullSpan() {
        return true;
    }


    @Override
    public void onResume(boolean hidden) {
        super.onResume(hidden);
    }


    @Override
    public Object convertData(FloorDetailBean floorDetailBean, boolean b) {


        return null;
    }
}
