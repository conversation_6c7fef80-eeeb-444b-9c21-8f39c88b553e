package com.xstore.sevenfresh.floor.modules.vp.helper;

import android.content.Context;
import android.view.ViewConfiguration;

/**
 * FlingHelperUtil简介
 * copy from jd
 *
 * <AUTHOR>
 * @date 2019-6-11 15:33:34
 */
public class FlingHelperUtil {
    /**
     * INFLEXION
     */
    private static final float INFLEXION = 0.35F;
    /**
     * mFlingFriction
     */
    private static float mFlingFriction = ViewConfiguration.getScrollFriction();
    /**
     * mPhysicalCoeff
     */
    private static float mPhysicalCoeff;
    /**
     * DECELERATION_RATE
     */
    private static float DECELERATION_RATE = (float) (Math.log(0.78D) / Math.log(0.9D));

    /**
     *
     * @param context
     */
    public FlingHelperUtil(Context context) {
        float ppi = context.getResources().getDisplayMetrics().density * 160.0F;
        mPhysicalCoeff = 386.0878F * ppi * 0.84F;
    }

    /**
     *
     * @param velocity
     * @return
     */
    private double getSplineDeceleration(int velocity) {
        return Math.log((double)(0.35F * (float)Math.abs(velocity) / (mFlingFriction * mPhysicalCoeff)));
    }

    /**
     *
     * @param distance
     * @return
     */
    private double getSplineDecelerationByDistance(double distance) {
        double decelMinusOne = (double)DECELERATION_RATE - 1.0D;
        return decelMinusOne * Math.log(distance / (double)(mFlingFriction * mPhysicalCoeff)) / (double)DECELERATION_RATE;
    }

    /**
     *
     * @param velocity
     * @return
     */
    public double getSplineFlingDistance(int velocity) {
        double l = this.getSplineDeceleration(velocity);
        double decelMinusOne = (double)DECELERATION_RATE - 1.0D;
        return (double)(mFlingFriction * mPhysicalCoeff) * Math.exp((double)DECELERATION_RATE / decelMinusOne * l);
    }

    /**
     *
     * @param distance
     * @return
     */
    public int getVelocityByDistance(double distance) {
        double l = this.getSplineDecelerationByDistance(distance);
        int velocity = (int)(Math.exp(l) * (double)mFlingFriction * (double)mPhysicalCoeff / INFLEXION);
        return Math.abs(velocity);
    }

    /**
     *
     * @param velocity
     * @return
     */
    public int getSplineFlingDuration(int velocity) {
        double l = this.getSplineDeceleration(velocity);
        double decelMinusOne = (double)DECELERATION_RATE - 1.0D;
        return (int)(1000.0D * Math.exp(l / decelMinusOne));
    }
}

