package com.xstore.sevenfresh.floor.modules.request;

import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;

import java.util.List;

/**
 * 楼层数据回调接口
 */
public interface FloorRequestCallback {

    /**
     * 返回当前请求的楼层数据
     * @param storeId 当前请求数据对应的门店
     * @param fenceId 当前加载的温岚id
     * @param omnitechPageComponentVoList 楼层数据
     * @param isCache 数据是否为缓存数据
     */
    void callbackFloors(String storeId, String fenceId, List<FloorDetailBean> omnitechPageComponentVoList, boolean isCache);

    /**
     * 判断当前首页是否展示了指定门店的数据
     * todo  本期由于缓存不改 所以这个判断逻辑可以不动先，下期缓存方案肯定要改了
     * @param storeId 门店id
     * @return true-当前首页所显示的数据为指定门店的数据
     */
    boolean hasThisStoreData(String storeId);
}
