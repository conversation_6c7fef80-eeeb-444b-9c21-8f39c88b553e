package com.xstore.sevenfresh.floor.modules.vp;


import android.content.Context;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseBooleanArray;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ChildRecyclerView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.xstore.sdk.floor.floorcore.adapter.loadmore.LoadMoreView;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sdk.floor.floorcore.interfaces.FloorBaseInterface;
import com.xstore.sdk.floor.floorcore.utils.RecyclerViewUtils;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.FloorContainer;
import com.xstore.sevenfresh.floor.modules.FloorContainerItemDecoration;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.sevenfresh.floor.modules.ShellRecycleViewAdapter;
import com.xstore.sevenfresh.floor.modules.interfaces.VPTabDataCallBack;
import com.xstore.sevenfresh.floor.modules.request.RecommendRequest;
import com.xstore.sevenfresh.floor.modules.video.HomeFloorPlayHelper;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.bean.VpMaEntry;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;
import com.xstore.sevenfresh.floor.modules.vp.holder.VpTabFooterFloor;
import com.xstore.sevenfresh.floor.modules.vp.utils.UIUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;

import java.util.ArrayList;
import java.util.List;


public class CategoryView extends ChildRecyclerView {


    FloorContainer floorContainer;


    /**
     * recommendTab 当前展示的tab信息
     */
    private VPTabBean recommendTab;

    ViewPagerRecommendContent viewPagerRecommendContent;

    public void setViewPagerRecommendContent(ViewPagerRecommendContent viewPagerRecommendContent) {
        this.viewPagerRecommendContent = viewPagerRecommendContent;
    }

    public void setRecommendTab(VPTabBean recommendTab) {
        this.recommendTab = recommendTab;
    }

    public VPTabBean getRecommendTab() {
        return recommendTab;
    }

    ShellRecycleViewAdapter shellRecycleViewAdapter;

    public void setFloorContainer(FloorContainer floorContainer) {
        this.floorContainer = floorContainer;
        parentRecyclerView = floorContainer.getParentRcv();
    }

    int tabKey = -1;


    int nextPage = 1;


    public int getNextPage() {
        return nextPage;
    }

    public void setNextPage(int nextPage) {
        this.nextPage = nextPage;
    }

    public void setTabKey(int tabKey) {
        this.tabKey = tabKey;
    }


    public void setFloorScrollListener(OnScrollListener scrollListener) {
        addOnScrollListener(scrollListener);
    }


    FloorDetailBean floorDetailBean;

    public void setFloorDetailBean(FloorDetailBean floorDetailBean) {
        this.floorDetailBean = floorDetailBean;
    }

    //底部加载更多
    FloorDetailBean footerFloor = new FloorDetailBean();

    FloorDataManager floorDataManager;
    private List mDataList = new ArrayList<Object>();

    public CategoryView(Context context) {
        this(context, null);
        init();
    }

    public CategoryView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
        init();
    }

    public CategoryView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    StaggeredGridLayoutManager staggeredGridLayoutManager;

    //正在加载更多
    boolean isLoadMore = false;

    private FloorContainerItemDecoration floorContainerItemDecoration;

    boolean hasAddDecoration = false;

    private void init() {
        footerFloor.setTemplateCode(VpTabFooterFloor.templateCode);
        footerFloor.setDataStatus(LoadMoreView.STATUS_LOADING);
        floorDataManager = FloorDataManager.getInstance();

        floorContainerItemDecoration = new FloorContainerItemDecoration(getContext());
        if (!hasAddDecoration) {
            hasAddDecoration = true;
            addItemDecoration(floorContainerItemDecoration);
        }


        // 设置布局管理器
        staggeredGridLayoutManager = refreshLayoutManager();
        setLayoutManager(staggeredGridLayoutManager);
        addOnScrollListener(new OnScrollListener() {


            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (!isTop()) {
                    exposure();
                }

            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (recyclerView.getLayoutManager() instanceof StaggeredGridLayoutManager) {
                    boolean needLoadMore = getLastVisibleItem(recyclerView) >= getTotalItemCount((ChildRecyclerView) recyclerView) - 4;
                    //加载更多
                    if (needLoadMore) {
                        loadMore();
                    }
                }
            }
        });
    }


    //加载跟多
    private void loadMore() {
        {
            //没有更多数据  正在刷新  正在加载更多
            if (nextPage == 0 || isRefresh || isLoadMore) {
                return;
            }
            //显示的无数据
            if (shellRecycleViewAdapter.getData() != null && shellRecycleViewAdapter.getData().size() == 1
                    && shellRecycleViewAdapter.getData().get(0).getDataStatus() == LoadMoreView.STATUS_FAIL) {
                return;
            }

            isLoadMore = true;

            //显示加载更多
            if (shellRecycleViewAdapter.getData() != null) {
                footerFloor.setDataStatus(LoadMoreView.STATUS_LOADING);
                shellRecycleViewAdapter.getData().add(footerFloor);
                shellRecycleViewAdapter.notifyItemInserted(shellRecycleViewAdapter.getItemCount());
                postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        scrollBy(0, 100);
                    }
                }, 100);
            }


            floorDataManager.refreshTabData(getContext(), tabKey, nextPage, new VPTabDataCallBack() {
                @Override
                public void setTabData(int state, int tabKey, int nextPage, List<FloorDetailBean> floorDetailBeans) {
                    Log.i("VP", "setTabData" + state);

                    isLoadMore = false;

                    //删除底部加载更多
                    if (shellRecycleViewAdapter.getData() != null) {
                        shellRecycleViewAdapter.getData().remove(footerFloor);
                        shellRecycleViewAdapter.notifyItemRemoved(shellRecycleViewAdapter.getItemCount());
                    }

                    if (state == RecommendRequest.OnLoadMoreResult.SUCCESS) {
                        setNextPage(nextPage);
                        shellRecycleViewAdapter.insertData(floorDetailBeans, shellRecycleViewAdapter.getItemCount());
                        if (floorDetailBeans.size() < 10) {
                            setNextPage(0);
                            addEndViewHolder();
                        }
                    }
                    if (state == RecommendRequest.OnLoadMoreResult.NO_MORE_DATA) {
                        setNextPage(nextPage);
                        // 添加底部结束符
                        addEndViewHolder();
                    }
                    if (state == RecommendRequest.OnLoadMoreResult.FAIL) {
                        SFLogCollector.e("loadMore fail");
                    }

                }
            });
        }
    }

    //添加结束符
    private void addEndViewHolder() {
        // 添加底部结束
        FloorDetailBean floorDetailBean = new FloorDetailBean();
        floorDetailBean.setTemplateCode("home_page_recommend_footer");
        ArrayList<FloorDetailBean> list = new ArrayList<>();
        list.add(floorDetailBean);
        insertData(list, shellRecycleViewAdapter.getItemCount());
        postDelayed(new Runnable() {
            @Override
            public void run() {
                scrollBy(0, 100);
            }
        }, 100);

        // 添加滑动到底埋点
        if (viewPagerRecommendContent != null && viewPagerRecommendContent.getFloorDetailBean() != null
                && viewPagerRecommendContent.currentChildRcv != null) {
            VpMaEntry vpMaEntry = new VpMaEntry(viewPagerRecommendContent.getFloorDetailBean());
            vpMaEntry.tab_name = viewPagerRecommendContent.currentChildRcv.getRecommendTab().getTitle();
            vpMaEntry.tab_pos = viewPagerRecommendContent.currentChildRcv.getRecommendTab().getTabPos();
            JDMaUtils.save7FExposure(VpMaEntry.Constants.FRONTPAGE_FEEDS_END, null, vpMaEntry, null, floorContainer.getJdMaPageImp());
        }

    }


    /**
     * 插入楼层数据 ，并局部刷新
     *
     * @param data  插入的数据
     * @param index 插入的位置
     */
    public void insertData(List<FloorDetailBean> data, int index) {
        if (shellRecycleViewAdapter == null) {
            return;
        }
        shellRecycleViewAdapter.insertData(data, index);
    }


    boolean isRefresh = false;

    //切换tab后自己刷新
    public void switchTabToRefreshSelf() {
        //没数据时先刷新
        if (shellRecycleViewAdapter.getData() == null ||
                shellRecycleViewAdapter.getData().size() == 1 &&
                        shellRecycleViewAdapter.getData().get(0) != null &&
                        shellRecycleViewAdapter.getData().get(0).getTemplateCode().equals(VpTabFooterFloor.templateCode)) {
            isRefresh = true;


            footerFloor.setDataStatus(VpTabHelper.REFRESHING);
            if (shellRecycleViewAdapter.getData() == null) {
                List<FloorDetailBean> list = new ArrayList<>();
                list.add(footerFloor);
                shellRecycleViewAdapter.setData(list);
            } else {
                shellRecycleViewAdapter.notifyDataSetChanged();
            }

            floorDataManager.refreshTabData(getContext(), tabKey, nextPage, new VPTabDataCallBack() {
                @Override
                public void setTabData(int state, int tabKey, int nextPage, List<FloorDetailBean> floorDetailBeans) {


                    if (floorDetailBeans == null || floorDetailBeans.size() == 0) {
                        showNoDataHolder();
                        isRefresh = false;
                        return;
                    }
                    //显示没有数据
                    if (state == RecommendRequest.OnLoadMoreResult.FAIL) {
                        showNoDataHolder();
                    }

                    if (state == RecommendRequest.OnLoadMoreResult.SUCCESS) {
                        //刷新成功删除网络错误holder
                        if (shellRecycleViewAdapter.getData() != null && shellRecycleViewAdapter.getData().size() == 1) {
                            shellRecycleViewAdapter.getData().remove(footerFloor);
                        }

                        setNextPage(nextPage);

                        shellRecycleViewAdapter.setData(floorDetailBeans);


                        if (floorDetailBeans.size() < 3) {
                            floorDataManager.handleSGM(getRecommendTab());
                        }

                        if (floorDetailBeans.size() < 10) {
                            setNextPage(0);
                            addEndViewHolder();
                        }

                        delayExposure();

                    }
                    isRefresh = false;

                }
            });
        }


    }

    //延迟曝光
    private void delayExposure() {
        postDelayed(new Runnable() {
            @Override
            public void run() {
                parentRcvScrollExposure();
            }
        }, 300);
    }


    public void setAdapter() {
        shellRecycleViewAdapter = new ShellRecycleViewAdapter(getContext(), floorContainer);
        setAdapter(shellRecycleViewAdapter);
    }

    //显示无数据页面
    public void showNoDataHolder() {
        if (shellRecycleViewAdapter.getData().size() == 1) {
            footerFloor.setDataStatus(LoadMoreView.STATUS_FAIL);
            //添加刷新按钮click
            getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    for (FloorBaseInterface floorBaseInterface : shellRecycleViewAdapter.getAllViewHolder()) {
                        if (floorBaseInterface instanceof VpTabFooterFloor) {
                            ((VpTabFooterFloor) floorBaseInterface).setOnClickListener(new OnClickListener() {
                                @Override
                                public void onClick(View view) {
                                    switchTabToRefreshSelf();
                                }
                            });
                        }
                    }
                }
            });
            shellRecycleViewAdapter.notifyDataSetChanged();

        }
    }

    public void setData() {
        isLoadMore = false;
        //tabKey为0时，是第一页推荐先加载
        if (tabKey == 0) {
            List<FloorDetailBean> goodsFloor = floorDataManager.getGoodsFloor();
            if (goodsFloor != null && goodsFloor.size() > 0) {
                shellRecycleViewAdapter.setData(goodsFloor);
            } else {
                setNextPage(1);
                switchTabToRefreshSelf();
            }
        }


    }


    private int getLastVisibleItem(RecyclerView childRecyclerView) {
        LayoutManager layoutManager = childRecyclerView.getLayoutManager();
        if (layoutManager != null && layoutManager instanceof StaggeredGridLayoutManager) {
            int[] iArr = new int[2];
            ((StaggeredGridLayoutManager) layoutManager).findLastVisibleItemPositions(iArr);
            if (iArr[0] > iArr[1])
                return iArr[0];
            else
                return iArr[1];
        } else {
            return -1;
        }
    }

    private int getTotalItemCount(ChildRecyclerView childRecyclerView) {
        Adapter adapter = childRecyclerView.getAdapter();
        if (adapter != null) {
            return adapter.getItemCount();
        }
        return -1;
    }


    public StaggeredGridLayoutManager refreshLayoutManager() {

        if (staggeredGridLayoutManager != null) {
            return staggeredGridLayoutManager;
        }
        Parcelable parcelable = null;
        if (null != staggeredGridLayoutManager) {
            parcelable = staggeredGridLayoutManager.onSaveInstanceState();
        }
        staggeredGridLayoutManager = new StaggeredGridLayoutManager(2, GridLayoutManager.VERTICAL) {
            @Override
            public int scrollVerticallyBy(int dy, Recycler recycler, State state) {
                try {
                    return super.scrollVerticallyBy(dy, recycler, state);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }

            @Override
            public void onLayoutChildren(Recycler recycler, State state) {
                try {
                    super.onLayoutChildren(recycler, state);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void addDisappearingView(View child) {
                try {
                    super.addDisappearingView(child);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void addView(View child, int index) {
                try {
                    super.addView(child, index);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public boolean canScrollVertically() {
                return super.canScrollVertically();
            }

            @Override
            public boolean supportsPredictiveItemAnimations() {
                return false;
            }
        };
        if (null != parcelable) {
            staggeredGridLayoutManager.onRestoreInstanceState(parcelable);
        }
//        layoutManager.setItemPrefetchEnabled(false);
        staggeredGridLayoutManager.setAutoMeasureEnabled(false);
        staggeredGridLayoutManager.setOrientation(GridLayoutManager.VERTICAL);
        return staggeredGridLayoutManager;
    }

    private final SparseBooleanArray sparseBooleanArray = new SparseBooleanArray();
    public static final String TAG = "ChildRecyclerView";

    //外层Rcv滚动触发子Rcv的曝光
    public void parentRcvScrollExposure() {
        if (staggeredGridLayoutManager == null) {
            return;
        }

        int first = HomeFloorPlayHelper.getFirstVisibleItem(this);
        int last = getLastVisibleItemWithRcv(this);

        if (shellRecycleViewAdapter == null || first == -1) {
            return;
        }

        for (int i = first; i <= last; i++) {

            // 剔除已曝光的数据
            if (sparseBooleanArray.get(i)) {
                continue;
            }

            int height = getVisibleHeight(i);
            if (height > 0) {
                sparseBooleanArray.put(i, true);
            } else {
                return;
            }

            RecyclerView.ViewHolder holder = findViewHolderForAdapterPosition(i);
            if (holder instanceof FloorBaseViewHolder) {
                ((FloorBaseViewHolder) holder).onExposureFloor();
            } else {
                SFLogCollector.e(TAG, "exposure not baseViewHolder");
            }
        }
    }

    //自己滚动触发的曝光
    private void exposure() {
        if (staggeredGridLayoutManager == null) {
            return;
        }
        int[] firstItems = null;
        int[] lastItems = null;
        firstItems = staggeredGridLayoutManager.findFirstVisibleItemPositions(null);
        lastItems = staggeredGridLayoutManager.findLastVisibleItemPositions(null);
        int first = RecyclerViewUtils.getMinValue(firstItems, lastItems);
        int last = RecyclerViewUtils.getMaxValue(firstItems, lastItems);
        if (shellRecycleViewAdapter == null || first == -1) {
            return;
        }

        for (int i = first; i <= last; i++) {
            // 剔除已曝光的数据
            if (sparseBooleanArray.get(i)) {
                continue;
            }
            sparseBooleanArray.put(i, true);
            RecyclerView.ViewHolder holder = findViewHolderForAdapterPosition(i);
            if (holder instanceof FloorBaseViewHolder) {
                ((FloorBaseViewHolder) holder).onExposureFloor();
            } else {
                SFLogCollector.e(TAG, "exposure not baseViewHolder");
            }
        }
    }

    /**
     * 获取最后一个可见的item 位置
     *
     * @return
     */
    public int getLastVisibleItemWithRcv(RecyclerView recyclerView) {
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (null != layoutManager && layoutManager instanceof StaggeredGridLayoutManager) {
            int[] into = new int[2];
            ((StaggeredGridLayoutManager) layoutManager).findLastVisibleItemPositions(into);
            return into[0] > into[1] ? into[0] : into[1];
        }
        if (null != layoutManager && layoutManager instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition();
        } else {
            return -1;
        }
    }


    // 计算视图在屏幕中的可见高度
    private int getVisibleHeight(int position) {
        RecyclerView.ViewHolder holder = findViewHolderForAdapterPosition(position);
        View view = ((FloorBaseViewHolder) holder).getConvertView();
        int[] location = new int[2];
        view.getLocationOnScreen(location);

        // 获取父 RecyclerView 在屏幕中的位置

        if (parentRecyclerView == null) return 0;

        int[] parentLocation = new int[2];
        parentRecyclerView.getLocationOnScreen(parentLocation);

        int parentTop = parentLocation[1];
        int parentBottom = parentTop + parentRecyclerView.getHeight();

        int viewTop = location[1];
        int viewBottom = viewTop + view.getHeight();

        // 计算可见区域
        int visibleTop = Math.max(viewTop, parentTop);
        int visibleBottom = Math.min(viewBottom, parentBottom);

        return Math.max(0, visibleBottom - visibleTop);
    }

}