package com.xstore.sevenfresh.floor.modules.request;

import static com.xstore.sdk.floor.floorcore.FloorBaseNetwork.INNER_SDK_VERSION;
import static com.xstore.sevenfresh.fresh_network_business.FreshHttpException.ERR_TYPE_COLOR_RETRY;

import android.content.Context;
import android.text.TextUtils;

import com.jd.framework.json.JDJSONArray;
import com.jd.framework.json.JDJSONObject;
import com.jingdong.sdk.jdcrashreport.JdCrashReport;
import com.xstore.sdk.floor.floorcore.FloorInit;
import com.xstore.sdk.floor.floorcore.utils.StringUtil;
import com.xstore.sdk.floor.floorcore.utils.TabGroupGoodDataManager;
import com.xstore.sevenfresh.datareport.JDMaUtils;
import com.xstore.sevenfresh.floor.modules.FloorContainer;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.sdk.floor.floorcore.ma.FloorBaseMaEntity;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.floor.modules.interfaces.VPTabDataCallBack;
import com.xstore.sevenfresh.floor.modules.model.RecommendFloorConst;
import com.xstore.sevenfresh.fresh_network_business.BaseFreshResultCallback;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpException;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpGroupUtils;
import com.xstore.sevenfresh.fresh_network_business.FreshHttpSetting;
import com.xstore.sevenfresh.addressstore.utils.TenantIdUtils;
import com.xstore.sevenfresh.service.sflog.SFLogCollector;
import com.xstore.sevenfresh.service.storage.kvstorage.PreferenceUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 首页推荐楼层数据请求类
 */
public class RecommendRequest {

    /**
     * 当前分页所使用的的动态参数
     */
    public static String dynamicParam = "";
    /**
     * 当前要请求的页码
     */
    public static int nextPage = 2;
    /**
     * 最近一次请求的推荐数据
     */
    public static FloorDetailBean lastRecommendFloorBean = null;


    public static int tabKey = -1;


    //请求tab分页数据
    public static void requestTabGql(Context context, int tabKey, int nextPage, VPTabDataCallBack callBack, FloorDetailBean floorDetailBean, JDMaUtils.JdMaPageImp jdMaPageImp, String sdkVersion) {
        if (callBack == null) {
            return;
        }
        FreshHttpSetting setting = new FreshHttpSetting();
        setting.setFunctionId(FloorInit.getFloorConfig().getFunctionId());
        //刷新显示 加载更多不显示
        setting.setEffect(FreshHttpSetting.NO_EFFECT);
        setting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        setting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        JDJSONObject jsonObject = new JDJSONObject();
        jsonObject.put("pageType", 0);
        if (TextUtils.isEmpty(sdkVersion)) {
            jsonObject.put("sdkVersion", INNER_SDK_VERSION);
        } else {
            jsonObject.put("sdkVersion", sdkVersion);
        }
        //        jsonObject.put("pin", PinUtils.getPin());
        String fieldName = null;
        if (StringUtil.safeEqualsAndNotNull(FloorInit.getFloorConfig().getBizCode(), "jingxin")) {
            nextPage = TabGroupGoodDataManager.getInstance().getNextPage();
            fieldName = "activityTabMixedGroup";
            if (nextPage == 0) {
                //那就没有更多了
                callBack.setTabData(OnLoadMoreResult.NO_MORE_DATA, tabKey, nextPage, null);
                return;
            }

        } else {
            fieldName = "queryIndexRecommend";
        }


        //分页请求
        if (tabKey >= 0) {
            if (tabKey == 0) {
                fieldName = "queryIndexRecommend";
            } else {
                fieldName = "indexRecommendPaginationQuery";
            }
            jsonObject.put("tabKey", tabKey);
        }

        jsonObject.put("exposureSkus", PreferenceUtil.getStringMax100("exposureSkus"));
        jsonObject.put("carSkus", PreferenceUtil.getStringMax100("carSkus"));
        jsonObject.put("clkSkus", PreferenceUtil.getStringMax100("clkSkus"));
        jsonObject.put("version518", true);
        jsonObject.put("page", nextPage);
        jsonObject.put("chooseTabIndex", TabGroupGoodDataManager.getInstance().getCurrentTabId());
        jsonObject.put("pageSize", 10);
        jsonObject.put("sdkChannel", "android");
        jsonObject.put("screenHigh", 9999); //暂时获取所有的数据，9999应该可以一次拉取到所有的首页数据
        jsonObject.put("terminalType", 3);
        jsonObject.put("dynamicParam", dynamicParam);
        setting.putJsonParam("bizCode", FloorInit.getFloorConfig().getBizCode());
        setting.putJsonParam("variables", jsonObject);
        setting.putJsonParam("fieldName", Arrays.asList(fieldName));
        setting.setBackString(fieldName);
//        String storeId = TenantIdUtils.getStoreId();
        setting.setResultCallback(new BaseFreshResultCallback<String, String>() {


            @Override
            public String onData(String data, FreshHttpSetting httpSetting) {
                return data;
            }

            @Override
            public void onEnd(String response, FreshHttpSetting httpSetting) {
                String requestStoreId = null;
                String requestFenceId = null;
                if (httpSetting.getCustomVariables() != null) {
                    requestStoreId = (String) httpSetting.getCustomVariables().get("localStoreId");
                    requestFenceId = (String) httpSetting.getCustomVariables().get("localFenceId");
                    if (!TextUtils.equals(TenantIdUtils.getStoreId(), requestStoreId)) {
                        SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                        return;
                    } else if (!TextUtils.equals(TenantIdUtils.getFenceId(), requestFenceId)) {
                        SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- fenceId");
                        return;
                    }
                    int step = (int) httpSetting.getCustomVariables().get("requestStep");
                    if (step != FloorDataManager.getInstance().requestStep) {
                        SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                        return;
                    }
                }

                JDJSONObject jdjsonObject = JDJSONObject.parseObject(response);

                JDJSONObject indexRecommendJson;
                //解析tab分页数据
                if (tabKey == 0) {
                    indexRecommendJson = jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend");
                } else {
                    indexRecommendJson = jdjsonObject.optJSONObject("data").optJSONObject("indexRecommendPaginationQuery");
                }

                if (indexRecommendJson != null) {

                    int nextPage1 = indexRecommendJson.optInt("nextPage");
                    JDJSONArray skuInfos = indexRecommendJson.optJSONArray("skuInfos");

                    if (nextPage1 <= 0) {
                        callBack.setTabData(OnLoadMoreResult.NO_MORE_DATA, tabKey, nextPage1, null);
                        return;
                    }

                    if (skuInfos != null && skuInfos.size() > 0) {

                        List<FloorDetailBean> newData = new ArrayList<>();

                        for (int i = 0; i < skuInfos.size(); i++) {
                            JDJSONObject skuInfo = skuInfos.getJSONObject(i);
                            if (skuInfo == null) {
                                continue;
                            }

                            FloorDetailBean goodFloor = new FloorDetailBean();
                            goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                                goodFloor.setTemplateCode("home_page_recommend_good");
                            }

                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                                goodFloor.setTemplateCode("home_page_recommend_menu");
                            }

                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                                goodFloor.setTemplateCode("home_page_recommend_ad");
                            }

                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                                goodFloor.setTemplateCode("home_page_recommend_video");
                            }
                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.pormotion) {
                                goodFloor.setTemplateCode("home_page_promotion_card");
                            }

                            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.newRank) {
                                goodFloor.setTemplateCode("new_rank_floor");
                            }

                            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.normal) {
                                goodFloor.setJumpType(1);
                            }

                            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.mini) {
                                goodFloor.setJumpType(2);
                            }
                            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.promotionSearch) {
                                goodFloor.setJumpType(3);
                            }
                            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.zelatuPage) {
                                goodFloor.setJumpType(4);
                            }

                            if (floorDetailBean != null) {
                                goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
                                goodFloor.setComponentName(floorDetailBean.getComponentName());
                                goodFloor.setComponentCode(floorDetailBean.getComponentCode());
                                goodFloor.setRealIndex(1);
                                goodFloor.setLocalRecommend(true);
                            }

                            newData.add(goodFloor);

                        }
                        callBack.setTabData(OnLoadMoreResult.SUCCESS, tabKey, nextPage1, newData);

                    }

                } else {
                    callBack.setTabData(OnLoadMoreResult.FAIL, tabKey, 0, null);
                }


            }

            @Override
            public void onError(FreshHttpException error) {
                if (error != null && error.getErrorType() == ERR_TYPE_COLOR_RETRY) {
                    return;
                }
                callBack.setTabData(OnLoadMoreResult.FAIL, tabKey, -1, null);

            }
        });
        HashMap<String, Object> var = new HashMap<>();
        var.put("localStoreId", TenantIdUtils.getStoreId());
        var.put("localFenceId", TenantIdUtils.getFenceId());
        var.put("requestStep", FloorDataManager.getInstance().requestStep);
        setting.setCustomVariables(var);
        FreshHttpGroupUtils.getHttpGroup().add(context, setting);
    }


    /**
     * 推荐数据通用请求方法
     *
     * @param context          上下文
     * @param onLoadMoreResult 分页回调
     * @param floorDetailBean  当前推荐楼层的数据，用于分别赋值给每个推荐卡片楼层
     * @param jdMaPageImp      埋点接口
     */
    public static void requestGql(Context context, OnLoadMoreResult onLoadMoreResult, FloorDetailBean floorDetailBean, JDMaUtils.JdMaPageImp jdMaPageImp, String sdkVersion) {
        if (onLoadMoreResult == null) {
            return;
        }
        FreshHttpSetting setting = new FreshHttpSetting();
        setting.setFunctionId(FloorInit.getFloorConfig().getFunctionId());
        setting.setEffect(FreshHttpSetting.NO_EFFECT);
        setting.setToastType(FreshHttpSetting.ToastType.NO_TIME);
        setting.setShowNetErr(FreshHttpSetting.NetErrType.NO_ERR);
        JDJSONObject jsonObject = new JDJSONObject();
        jsonObject.put("pageType", 0);
        if (TextUtils.isEmpty(sdkVersion)) {
            jsonObject.put("sdkVersion", INNER_SDK_VERSION);
        } else {
            jsonObject.put("sdkVersion", sdkVersion);
        }
        //        jsonObject.put("pin", PinUtils.getPin());
        String fieldName = null;
        if (StringUtil.safeEqualsAndNotNull(FloorInit.getFloorConfig().getBizCode(), "jingxin")) {
            nextPage = TabGroupGoodDataManager.getInstance().getNextPage();
            fieldName = "activityTabMixedGroup";
            if (nextPage == 0) {
                //那就没有更多了
                onLoadMoreResult.onResult(OnLoadMoreResult.NO_MORE_DATA, null);
                return;
            }

        } else {
            fieldName = "queryIndexRecommend";
        }

        jsonObject.put("exposureSkus", PreferenceUtil.getStringMax100("exposureSkus"));
        jsonObject.put("carSkus", PreferenceUtil.getStringMax100("carSkus"));
        jsonObject.put("clkSkus", PreferenceUtil.getStringMax100("clkSkus"));
        jsonObject.put("page", nextPage);
        jsonObject.put("chooseTabIndex", TabGroupGoodDataManager.getInstance().getCurrentTabId());
        jsonObject.put("pageSize", 10);
        jsonObject.put("sdkChannel", "android");
        jsonObject.put("screenHigh", 9999); //暂时获取所有的数据，9999应该可以一次拉取到所有的首页数据
        jsonObject.put("terminalType", 3);
        jsonObject.put("dynamicParam", dynamicParam);
        setting.putJsonParam("bizCode", FloorInit.getFloorConfig().getBizCode());
        setting.putJsonParam("variables", jsonObject);
        setting.putJsonParam("fieldName", Arrays.asList(fieldName));
        setting.setBackString(fieldName);
//        String storeId = TenantIdUtils.getStoreId();
        setting.setResultCallback(new BaseFreshResultCallback<String, String>() {


            @Override
            public String onData(String data, FreshHttpSetting httpSetting) {
                return data;
            }

            @Override
            public void onEnd(String response, FreshHttpSetting httpSetting) {
                try {
                    String requestStoreId = null;
                    String requestFenceId = null;
                    if (httpSetting.getCustomVariables() != null) {
                        requestStoreId = (String) httpSetting.getCustomVariables().get("localStoreId");
                        requestFenceId = (String) httpSetting.getCustomVariables().get("localFenceId");
                        if (!TextUtils.equals(TenantIdUtils.getStoreId(), requestStoreId)) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- storeId");
                            return;
                        } else if (!TextUtils.equals(TenantIdUtils.getFenceId(), requestFenceId)) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- fenceId");
                            return;
                        }
                        int step = (int) httpSetting.getCustomVariables().get("requestStep");
                        if (step != FloorDataManager.getInstance().requestStep) {
                            SFLogCollector.e(FloorContainer.TAG, "丢弃无效数据 -- step");
                            return;
                        }
                    }

                    if (StringUtil.safeEqualsAndNotNull(FloorInit.getFloorConfig().getBizCode(), "jingxin")) {
                        parseData2(context, response, onLoadMoreResult, floorDetailBean, jdMaPageImp);
                    } else {
                        parseData(context, response, onLoadMoreResult, floorDetailBean, jdMaPageImp);
                    }
                } catch (Exception e) {
                    JdCrashReport.postCaughtException(e);
                }

            }

            @Override
            public void onError(FreshHttpException error) {
                if (error != null && error.getErrorType() == ERR_TYPE_COLOR_RETRY) {
                    return;
                }
                onLoadMoreResult.onResult(OnLoadMoreResult.FAIL, null);

            }
        });
        HashMap<String, Object> var = new HashMap<>();
        var.put("localStoreId", TenantIdUtils.getStoreId());
        var.put("localFenceId", TenantIdUtils.getFenceId());
        var.put("requestStep", FloorDataManager.getInstance().requestStep);
        setting.setCustomVariables(var);
        FreshHttpGroupUtils.getHttpGroup().add(context, setting);
    }

    /**
     * 解析加工推荐楼层数据 将推荐卡片转换为独立的推荐楼层
     *
     * @param context          上下文
     * @param response         返回的json
     * @param onLoadMoreResult 分页回调
     * @param floorDetailBean  推荐楼层
     * @param jdMaPageImp      埋点接口
     */
    private static void parseData(Context context, String response, OnLoadMoreResult onLoadMoreResult, FloorDetailBean floorDetailBean, JDMaUtils.JdMaPageImp jdMaPageImp) {

        // 重新组装数据 插入楼层data中
        JDJSONObject jdjsonObject = JDJSONObject.parseObject(response);

        if (jdjsonObject == null
                || jdjsonObject.optJSONObject("data") == null
                || jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend") == null
                || !jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend").optBoolean("success")) {
            onLoadMoreResult.onResult(OnLoadMoreResult.FAIL, null);
            return;
        }


        JDJSONArray skuInfos = jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend").optJSONArray("skuInfos");
        RecommendRequest.nextPage = jdjsonObject.optJSONObject("data").optJSONObject("queryIndexRecommend").optInt("nextPage");


        if (RecommendRequest.nextPage <= 0) {
            onLoadMoreResult.onResult(OnLoadMoreResult.NO_MORE_DATA, null);
            return;
        }

        //
        FloorBaseMaEntity entity = new FloorBaseMaEntity(floorDetailBean);
        entity.page = RecommendRequest.nextPage - 1;
        JDMaUtils.save7FExposure("orangeComponent_newFeeds_screenExpose", null, entity, null, jdMaPageImp);

        for (int i = 0; i < skuInfos.size(); i++) {
            JDJSONObject skuInfo = skuInfos.getJSONObject(i);
            if (skuInfo == null) {
                continue;
            }
            List<FloorDetailBean> newData = new ArrayList<>();
            FloorDetailBean goodFloor = new FloorDetailBean();
            goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                goodFloor.setTemplateCode("home_page_recommend_good");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                goodFloor.setTemplateCode("home_page_recommend_menu");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                goodFloor.setTemplateCode("home_page_recommend_ad");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                goodFloor.setTemplateCode("home_page_recommend_video");
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.pormotion) {
                goodFloor.setTemplateCode("home_page_promotion_card");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.newRank) {
                goodFloor.setTemplateCode("new_rank_floor");
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.normal) {
                goodFloor.setJumpType(1);
            }

            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.mini) {
                goodFloor.setJumpType(2);
            }
            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.promotionSearch) {
                goodFloor.setJumpType(3);
            }
            if (skuInfo.optInt("jumpType") == RecommendFloorConst.JumpRecommendType.zelatuPage) {
                goodFloor.setJumpType(4);
            }

            if (floorDetailBean != null) {
                goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
                goodFloor.setComponentName(floorDetailBean.getComponentName());
                goodFloor.setComponentCode(floorDetailBean.getComponentCode());
                goodFloor.setRealIndex(floorDetailBean.getRealIndex());
                goodFloor.setLocalRecommend(true);
            }

            newData.add(goodFloor);
            onLoadMoreResult.onResult(OnLoadMoreResult.SUCCESS, newData);

        }
    }


    /**
     * 解析加工推荐楼层数据 将推荐卡片转换为独立的推荐楼层
     *
     * @param context          上下文
     * @param response         返回的json
     * @param onLoadMoreResult 分页回调
     * @param floorDetailBean  推荐楼层
     * @param jdMaPageImp      埋点接口
     */
    private static void parseData2(Context context, String response, OnLoadMoreResult onLoadMoreResult, FloorDetailBean floorDetailBean, JDMaUtils.JdMaPageImp jdMaPageImp) {


        // 重新组装数据 插入楼层data中
        JDJSONObject jdjsonObject = JDJSONObject.parseObject(response);
        if (jdjsonObject == null
                || jdjsonObject.optJSONObject("data") == null
                || jdjsonObject.optJSONObject("data").optJSONObject("activityTabMixedGroup") == null
                || !jdjsonObject.optJSONObject("data").optJSONObject("activityTabMixedGroup").optBoolean("success")
                || !(jdjsonObject.optJSONObject("data").optJSONObject("activityTabMixedGroup").optJSONArray("tabMixedGroups") == null
                || !(jdjsonObject.optJSONObject("data").optJSONObject("activityTabMixedGroup").optJSONArray("tabMixedGroups").optJSONObject(0) == null))) {
            onLoadMoreResult.onResult(OnLoadMoreResult.FAIL, null);
            return;
        }

        JDJSONObject data = jdjsonObject.optJSONObject("data").optJSONObject("activityTabMixedGroup").optJSONArray("tabMixedGroups").optJSONObject(0);

        JDJSONArray skuInfos = data.optJSONArray("skuInfos");
        TabGroupGoodDataManager.getInstance().addNextPage(data.optInt("id"), data.optInt("nextPage"));
        //
        FloorBaseMaEntity entity = new FloorBaseMaEntity(floorDetailBean);
        entity.page = RecommendRequest.nextPage - 1;
        JDMaUtils.save7FExposure("orangeComponent_newFeeds_screenExpose", null, entity, null, jdMaPageImp);

        for (int i = 0; i < skuInfos.size(); i++) {
            JDJSONObject skuInfo = skuInfos.getJSONObject(i);
            if (skuInfo == null) {
                continue;
            }
            List<FloorDetailBean> newData = new ArrayList<>();
            FloorDetailBean goodFloor = new FloorDetailBean();
            goodFloor.setComponentDataObject(skuInfo.optString("recommendVo"));

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.good) {
                goodFloor.setTemplateCode("home_page_recommend_good");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.cookMenu) {
                goodFloor.setTemplateCode("home_page_recommend_menu");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.ad) {
                goodFloor.setTemplateCode("home_page_recommend_ad");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.video) {
                goodFloor.setTemplateCode("home_page_recommend_video");
            }
            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.pormotion) {
                goodFloor.setTemplateCode("home_page_promotion_card");
            }

            if (skuInfo.optInt("style") == RecommendFloorConst.RecommendType.newRank) {
                goodFloor.setTemplateCode("new_rank_floor");
            }

            if (floorDetailBean != null) {
                goodFloor.setComponentUuid(floorDetailBean.getComponentUuid());
                goodFloor.setComponentName(floorDetailBean.getComponentName());
                goodFloor.setComponentCode(floorDetailBean.getComponentCode());
                goodFloor.setRealIndex(floorDetailBean.getRealIndex());
                goodFloor.setLocalRecommend(true);
            }

            newData.add(goodFloor);
            TabGroupGoodDataManager.getInstance().addData(data.optInt("id"), newData);
            onLoadMoreResult.onResult(OnLoadMoreResult.SUCCESS, newData);

        }

        if (data.optInt("nextPage") <= 0) {
            onLoadMoreResult.onResult(OnLoadMoreResult.NO_MORE_DATA, null);
            return;
        }
    }

    /**
     * 分页回调接口
     */
    public interface OnLoadMoreResult {

        /**
         * 分页请求成功
         */
        int SUCCESS = 0;
        /**
         * 分页结束
         */
        int NO_MORE_DATA = 1;
        /**
         * 分页获取失败
         */
        int FAIL = -1;

        /**
         * 结果回调
         *
         * @param state            请求的状态
         * @param floorDetailBeans 推荐楼层数据
         */
        void onResult(int state, List<FloorDetailBean> floorDetailBeans);
    }
}
