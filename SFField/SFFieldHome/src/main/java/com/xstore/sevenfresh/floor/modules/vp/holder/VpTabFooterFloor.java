package com.xstore.sevenfresh.floor.modules.vp.holder;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;


import com.xstore.floorsdk.fieldhome.R;
import com.xstore.sdk.floor.floorcore.adapter.loadmore.LoadMoreView;
import com.xstore.sdk.floor.floorcore.interfaces.FloorContainerInterface;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.interfaces.AbsBaseFloor;
import com.xstore.sdk.floor.floorcore.bean.FloorDetailBean;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;

import androidx.annotation.Nullable;

/**
 * 首页底部slogan楼层
 */
public class VpTabFooterFloor extends AbsBaseFloor {

    public static final String templateCode = "home_page_recommend_vp_tab_footer";
    private View root;

    View.OnClickListener onClickListener;

    public void setOnClickListener(View.OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
        if (refreshBtn != null) {
            refreshBtn.setOnClickListener(onClickListener);
        }
    }

    @Override
    public boolean onExposureFloor() {
        return false;
    }

    View refreshBtn;

    @Override
    public void bindData(Context context, FloorContainerInterface floorContainer, @Nullable FloorBaseViewHolder holder, @Nullable FloorDetailBean indexDetail, int floorIndex) {
        root.findViewById(R.id.loading_viewstub).setVisibility(View.GONE);
        root.findViewById(R.id.ll_end_view).setVisibility(View.GONE);
        root.findViewById(R.id.ll_not_data).setVisibility(View.GONE);
        root.findViewById(R.id.ll_loading).setVisibility(View.GONE);
        switch (indexDetail.getDataStatus()) {
            case LoadMoreView.STATUS_LOADING:
                root.findViewById(R.id.loading_viewstub).setVisibility(View.VISIBLE);
                break;
            case LoadMoreView.STATUS_END:
                root.findViewById(R.id.ll_end_view).setVisibility(View.VISIBLE);
                break;
            case LoadMoreView.STATUS_FAIL:
                root.findViewById(R.id.ll_not_data).setVisibility(View.VISIBLE);
                refreshBtn = root.findViewById(R.id.search_other);
                break;
            case VpTabHelper.REFRESHING:
                root.findViewById(R.id.ll_loading).setVisibility(View.VISIBLE);
                break;
        }

    }

    @Override
    public View createView(Context context, FloorContainerInterface floorContainer) {
        root = LayoutInflater.from(context).inflate(R.layout.layout_vp_tab_footer, null, false);
        return root;
    }

    @Override
    public boolean isFullSpan() {
        return true;
    }

    @Override
    public Object convertData(FloorDetailBean bean, boolean isCache) {
        return null;
    }
}
