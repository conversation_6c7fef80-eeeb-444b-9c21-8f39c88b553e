package com.xstore.sevenfresh.floor.modules;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.LinearLayout;

public class NavContainer extends LinearLayout {
    public NavContainer(Context context) {
        super(context);
    }

    public NavContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NavContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public NavContainer(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public void setTranslationY(float translationY) {
        super.setTranslationY(translationY);
    }
}
