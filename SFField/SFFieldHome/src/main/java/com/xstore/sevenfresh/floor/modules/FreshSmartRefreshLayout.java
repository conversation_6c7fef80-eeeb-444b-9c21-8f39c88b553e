package com.xstore.sevenfresh.floor.modules;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;

public class FreshSmartRefreshLayout extends SmartRefreshLayout {

    /**
     * onTouchListener
     */
    private OnTouchListener onTouchListener;

    public FreshSmartRefreshLayout(Context context) {
        super(context);
    }

    public FreshSmartRefreshLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent e) {

        if (onTouchListener != null) {
            onTouchListener.onTouch(this, e);
        }

        return super.dispatchTouchEvent(e);
    }


    public void setDispatchTouchListener(OnTouchListener onTouchListener) {
        this.onTouchListener = onTouchListener;
    }

    public OnTouchListener getOnTouchListener() {
        return onTouchListener;
    }

    /**
     * 引导用户打开二楼， 包含展示和收起
     */
    public void guideTwoLevel() {
        AnimatorListenerAdapter listener = new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (animation != null && animation.getDuration() == 0) {
                    return;//0 表示被取消
                }
                mKernel.setState(RefreshState.ReleaseToTwoLevel);
//                mHandler.postDelayed(() -> mKernel.setState(RefreshState.None), 500);
                ValueAnimator valueAnimator = animSpinner(0, 500, mReboundInterpolator, mReboundDuration);
                if (valueAnimator != null && valueAnimator == reboundAnimator) {
                    valueAnimator.addListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            if (animation != null && animation.getDuration() == 0) {
                                return;//0 表示被取消
                            }
                            //此处需要重置状态，不然之后无法触发自动刷新
                            notifyStateChanged(RefreshState.None);
                        }
                    });
                }
            }
        };
        ValueAnimator animator = animSpinner((int) (1.9f * mHeaderHeight), 0, mReboundInterpolator, mReboundDuration);
        if (animator != null && animator == reboundAnimator) {
            animator.setDuration(mFloorDuration);
            animator.addListener(listener);
        } else {
            listener.onAnimationEnd(null);
        }
    }

    /**
     * 关闭Footer
     */
    public void closeFooter() {
        mFooterLocked = false;
        //直接关闭 Header/Footer 或者 Header/Footer 到原始状态
        mKernel.moveSpinner(0, false);
        mKernel.setState(RefreshState.None);
    }

    /**
     * @return 是否可以加载更多
     */
    public boolean isEnableLoadMore() {
        return mEnableLoadMore;
    }
}
