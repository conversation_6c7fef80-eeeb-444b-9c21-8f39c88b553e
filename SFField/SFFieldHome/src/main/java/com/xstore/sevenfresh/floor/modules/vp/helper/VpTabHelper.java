package com.xstore.sevenfresh.floor.modules.vp.helper;

public class VpTabHelper {

    //是否需要全部刷新推荐楼层
    public static boolean isRefreshAll = false;

    //是否有推荐楼层  默认有 当返回一个时不展示推荐楼层  按原来的方式展示
    public static boolean hasVpTab = true;

    public static boolean hasSearchFloor = false;

    //Nav下发颜色  默认黑色 0:黑色  1：白色
    public static int navIconStyle = 0;

    public static final int REFRESHING = 5;

    public static final String KEY_VP_TAB_MA_TITLE = "key_vp_tab_ma_title";

    public static final String KEY_VP_TAB_MA_POS = "key_vp_tab_ma_pos";

}
