package androidx.recyclerview.widget;

import android.content.Context;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.NestedScrollingParent;

import com.gyf.barlibrary.ImmersionBar;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;
import com.xstore.sevenfresh.floor.modules.FloorContainer;
import com.xstore.sevenfresh.floor.modules.FloorDataManager;
import com.xstore.sevenfresh.floor.modules.helper.FlingHelperUtil;
import com.xstore.sevenfresh.floor.modules.vp.ViewPagerRecommendContent;
import com.xstore.sevenfresh.floor.modules.vp.bean.VPTabBean;
import com.xstore.sevenfresh.floor.modules.vp.helper.VpTabHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;


/**
 * 楼层列表容器
 */
public class FloorRecyclerView extends RecyclerView implements NestedScrollingParent {

    /**
     * 伪造的系统功能回调
     */
    private FakeCallback fakeCallback;

    private LinearLayout liveContainer;

    private FloorContainer floorContainer;

    /**
     * preLastCompleteVisible 上一次最后一个item是否完全可见
     */
    private boolean preLastCompleteVisible;

    public void setFloorContainer(FloorContainer floorContainer) {
        this.floorContainer = floorContainer;
    }

    /**
     * flingHelperUtil 滑动帮助类
     */
    private FlingHelperUtil flingHelperUtil;
    /**
     * startFling 是否开始惯性滑动
     */
    private boolean startFling;
    /**
     * velocityY y轴滑动速度
     */
    private int velocityY;
    /**
     * totalDy y轴滑动距离
     */
    private int totalDy;

    private int stateDy;

    /**
     * 是否由嵌套滚动nestScroll触发的滑动
     */
    private boolean isNestScroll;
    /**
     * lastY 上次y轴位置
     */
    private float lastY;

    private boolean isInTop;
    /**
     * maxDistance 最大滑动距离
     */
    private int maxDistance;
    /**
     * canScrollVertically 是否可以纵向滑动
     */
    public AtomicBoolean canScrollVertically = new AtomicBoolean(true);
    /**
     * interceptVerticalScroll 是否需要拦截纵向滑动
     */
    private AtomicBoolean interceptVerticalScroll = new AtomicBoolean(false);

    public void setLiveContainer(LinearLayout liveContainer) {
        this.liveContainer = liveContainer;
    }

    public FloorRecyclerView(@NonNull Context context) {
        super(context);
    }

    public FloorRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public FloorRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void initScroll() {
        flingHelperUtil = new FlingHelperUtil(getContext());
        maxDistance = flingHelperUtil.getVelocityByDistance(DPIUtil.getHeight(getContext()) * 4);
        addOnScrollListener(new OnScrollListener() {

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                ViewPagerRecommendContent recommendChild = getRecommendChild();
                if (null != recommendChild && recommendChild.hasTab()) {
                    boolean lastComplete = lastCompletelyVisible();
                    if (preLastCompleteVisible & !lastComplete) {
                        recommendChild.allChildToTop();
                    }

                    //设置tab状态，收缩或展开
                    if (isNestScroll) {
                        if (lastComplete) {
                            isInTop = true;
//                            recommendChild.spreadSlidingTab(true, true);
                        }
                    } else {
                        isInTop = lastComplete;
//                        recommendChild.spreadSlidingTab(!lastComplete, true);
                    }

                    preLastCompleteVisible = lastComplete;
                }

                if (startFling) {
                    totalDy = 0;
                    startFling = false;
                }
                totalDy += dy;
                stateDy += dy;
            }

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    dispatchChildFling();
                    onChildScrollChanged(RecyclerView.SCROLL_STATE_IDLE);
                }
            }
        });
    }

    /**
     * 分发子view进行惯性滑动
     */
    private void dispatchChildFling() {
        if (isScrollEnd() && lastCompletelyVisible() && velocityY != 0) {
            double distance = flingHelperUtil.getSplineFlingDistance(velocityY);
            if (distance > totalDy) {
                int childVelocityY = flingHelperUtil.getVelocityByDistance(distance - totalDy);
                childFling(childVelocityY);
            }
        }
        totalDy = 0;
        velocityY = 0;
    }

    /**
     * 子view进行惯性滑动
     *
     * @param velocityY
     */
    public void childFling(int velocityY) {
        ViewPagerRecommendContent recommendContent = getRecommendChild();
        if (null != recommendContent) {
            recommendContent.fling(0, velocityY);
        }
        // 更新状态栏背景
        if (!VpTabHelper.hasSearchFloor) {
            postDelayed(new Runnable() {
                @Override
                public void run() {
                    floorContainer.getStatusBarBg().setVisibility(View.VISIBLE);
                }
            }, 100);
        }

    }

    /**
     * 更新子view滑动状态
     *
     * @param newState
     */
    public void onChildScrollChanged(int newState) {
        ViewPagerRecommendContent recommendContent = getRecommendChild();
        if (null != recommendContent) {
            recommendContent.onScrollChanged(newState);
        }
    }


    /**
     * 获取最后一个view
     *
     * @return
     */
    public View getLastView() {
        return getChildAt(getChildCount() - 1);
    }


    public boolean lastCompletelyVisible() {
        View view = getLastView();
        if (view instanceof LinearLayout) {
            ViewPagerRecommendContent recommendWidget = getRecommendChild();
            if (null != recommendWidget) {
                return findLastCompletelyVisibleItemPosition() >= getTotalItemCount() - 1;
            }
        }
        return false;
    }


    public List<VPTabBean> getTabList() {
        return FloorDataManager.getInstance().getTabBeans();
    }

    /**
     * @return 条目总数
     */
    public int getTotalItemCount() {
        return layoutManager.getItemCount();
    }

    /**
     * 找到最后一位的position
     **/
    public int findLastCompletelyVisibleItemPosition() {

        int[] lastCompletelyVisiblePositions = layoutManager.findLastCompletelyVisibleItemPositions(null);

        // 取所有列中的最大值（最底部的项）
        int maxPosition = -1;
        for (int pos : lastCompletelyVisiblePositions) {
            if (pos > maxPosition) {
                maxPosition = pos;
            }
        }
        return maxPosition;
    }

    /**
     * 滑动到推荐顶部
     */
    public void scrollToChildTop() {
        ViewPagerRecommendContent recommendContent = getRecommendContent();
        if (null != recommendContent) {
            recommendContent.viewToTop();
        }
    }

    /**
     * 从adapter中获取推荐view
     *
     * @return
     */
    private ViewPagerRecommendContent getRecommendContent() {
        Adapter adapter = getAdapter();
//        if (!(adapter instanceof ClubFloorAdapter)) {
//            return null;
//        }
//        return ((ClubFloorAdapter) adapter).getRecommendWidget();
        return null;
    }

    /**
     * 是否滑动到了底部
     *
     * @return
     */
    public boolean isScrollEnd() {
        return !canScrollVertically(1);
    }

    @Override
    public boolean fling(int velocityX, int velocityY) {
        int absY = Math.abs(velocityY);
        velocityY = (maxDistance > 8888 && absY > maxDistance) ? maxDistance * absY / velocityY : velocityY;
        boolean flag = super.fling(velocityX, velocityY);
        if (flag && velocityY > 0) {
            startFling = true;
            this.velocityY = velocityY;
        } else {
            this.velocityY = 0;
        }
        return flag;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (null != ev && ev.getAction() == MotionEvent.ACTION_DOWN) {
            velocityY = 0;
            stopScroll();
        }
        if (null != ev && ev.getActionMasked() != MotionEvent.ACTION_MOVE) {
            lastY = 0;
            canScrollVertically.set(!lastCompletelyVisible());
        }
        try {
            return super.dispatchTouchEvent(ev);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        if (lastY == 0) {
            lastY = e.getY();
        }
        //吸顶并且parentRcv滑动到底部
        if (isScrollEnd() && lastCompletelyVisible()) {
            ViewPagerRecommendContent recommendContent = getRecommendChild();
            if (null != recommendContent) {
                canScrollVertically.set(false);
                int dy = (int) (lastY - e.getY());
                recommendContent.onScroll(dy);

            }
        }
        if (e.getActionMasked() == MotionEvent.ACTION_UP) {
            canScrollVertically.set(true);
        }
        lastY = e.getY();
        isNestScroll = false;
        try {
            return super.onTouchEvent(e);
        } catch (Exception exc) {
            exc.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean dispatchNestedPreFling(float velocityX, float velocityY) {
        ViewPagerRecommendContent child = getRecommendChild();
        boolean childFlingEnable = isScrollEnd() && lastCompletelyVisible() && null != child && !child.isTop();
        if (childFlingEnable) {
            childFling((int) velocityY);
            return true;
        }

        return super.dispatchNestedPreFling(velocityX, velocityY);
    }


    private StaggeredGridLayoutManager layoutManager;

    @Nullable
    @Override
    public StaggeredGridLayoutManager getLayoutManager() {
        return layoutManager;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        try {
            super.onSizeChanged(w, h, oldw, oldh);
        } catch (Exception e) {
        }
    }

    @Override
    public void addChildrenForAccessibility(ArrayList<View> outChildren) {
        try {
            super.addChildrenForAccessibility(outChildren);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void requestChildFocus(View child, View focused) {
        //Item获取焦点时滑动会跳转到焦点位置
    }

    @Override
    protected void onMeasure(int widthSpec, int heightSpec) {
        try {
            super.onMeasure(widthSpec, heightSpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        try {
            super.onLayout(changed, l, t, r, b);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @return 第一个显示的条目位置
     */
    public int getFirstVisibleItem() {

        int[] firstVisiblePositions = layoutManager.findFirstVisibleItemPositions(null);

        // 取所有列中的最小值（最顶部的项）
        int minPosition = Integer.MAX_VALUE;
        for (int pos : firstVisiblePositions) {
            if (pos < minPosition) {
                minPosition = pos;
            }
        }
        return minPosition == Integer.MAX_VALUE ? 0 : minPosition;
    }

    /**
     * 更新布局管理器的设置
     */
    public StaggeredGridLayoutManager refreshLayoutManager() {

        if (layoutManager != null) {
            return layoutManager;
        }
        Parcelable parcelable = null;
        if (null != layoutManager) {
            parcelable = layoutManager.onSaveInstanceState();
        }
        layoutManager = new StaggeredGridLayoutManager(2, GridLayoutManager.VERTICAL) {
            @Override
            public int scrollVerticallyBy(int dy, Recycler recycler, State state) {
                try {
                    return super.scrollVerticallyBy(dy, recycler, state);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }

            @Override
            public void onLayoutChildren(Recycler recycler, State state) {
                try {
                    super.onLayoutChildren(recycler, state);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void addDisappearingView(View child) {
                try {
                    super.addDisappearingView(child);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void addView(View child, int index) {
                try {
                    super.addView(child, index);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public boolean canScrollVertically() {
                return canScroll();
            }

            @Override
            public boolean supportsPredictiveItemAnimations() {
                return false;
            }
        };
        if (null != parcelable) {
            layoutManager.onRestoreInstanceState(parcelable);
        }
//        layoutManager.setItemPrefetchEnabled(false);
        layoutManager.setAutoMeasureEnabled(false);
        layoutManager.setOrientation(GridLayoutManager.VERTICAL);
        setLayoutManager(layoutManager);
        return layoutManager;
    }

    @Override
    public void setLayoutManager(LayoutManager layout) {
        if (layout == layoutManager) {
            super.setLayoutManager(layout);
        }
    }


    ViewPagerRecommendContent child = null;

    /**
     * 从当前滑动列表中获取最后一个推荐楼层
     *
     * @return
     */
    public ViewPagerRecommendContent getRecommendChild() {
        View view = getLastView();
        if (view instanceof LinearLayout && ((LinearLayout) view).getChildCount() == 1 && ((LinearLayout) view).getChildAt(0) instanceof ViewPagerRecommendContent) {
            return (ViewPagerRecommendContent) ((LinearLayout) view).getChildAt(0);
        }
        return null;
    }


    boolean darkStatusbar = false;

    /**
     * 判断是否满足滑动条件
     *
     * @return
     */
    private boolean canScroll() {
        if (interceptVerticalScroll.get()) {
            return false;
        }
        ViewPagerRecommendContent child = getRecommendChild();
        if (null != child && !child.isTop() && lastCompletelyVisible()) {
            if (!VpTabHelper.hasSearchFloor) {
                if (floorContainer.getStatusBarBg().getVisibility() == View.GONE) {
                    floorContainer.getStatusBarBg().setVisibility(View.VISIBLE);
                    stateDy = 0;
                }
            }
            //上滑到吸顶
            if (darkStatusbar) {
                darkStatusbar = !darkStatusbar;
                if (!VpTabHelper.hasSearchFloor && VpTabHelper.navIconStyle != 0) {
                    floorContainer.setStatusBarIsDark(true);
                    floorContainer.setStatusBarIsWhite();
                }
            }

            return false;
        }
        boolean canScroll = canScrollVertically.get() || null == child || child.isTop();
        if (canScroll) {
            //吸顶时下滑超过20让状态栏背景消失
            if (Math.abs(stateDy) > 20) {
                //吸顶到下滑
                if (!darkStatusbar) {
                    floorContainer.getStatusBarBg().setVisibility(View.GONE);
                    darkStatusbar = !darkStatusbar;
                    //下发黑色时状态栏颜色不变
                    if (!VpTabHelper.hasSearchFloor && VpTabHelper.navIconStyle != 0) {
                        floorContainer.setStatusBarIsDark(false);
                        floorContainer.setStatusBarDark();
                    }
                }

            }
        }
        return canScroll;
    }


    /**
     * 绕过recyclerView 自身机制
     * 获取到每个item定义的特殊id，并不影响原本的itemId机制
     *
     * @param fakeId
     * @return
     */
    public ViewHolder findViewHolderForFakeItemId(long fakeId) {
        if (mAdapter == null || !mAdapter.hasStableIds()) {
            return null;
        }
        final int childCount = mChildHelper.getUnfilteredChildCount();
        ViewHolder hidden = null;
        for (int i = 0; i < childCount; i++) {
            final ViewHolder holder = getChildViewHolderInt(mChildHelper.getUnfilteredChildAt(i));
            if (fakeCallback != null) {
                if (holder != null && !holder.isRemoved() && fakeCallback.getFakeItemId(holder) == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            } else {
                if (holder != null && !holder.isRemoved() && holder.getItemId() == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            }
        }
        return hidden;
    }


    /**
     * 伪造的系统功能回调
     */
    public interface FakeCallback {
        /**
         * 获取伪造的itemId
         * 由于holder类型不定，将此实现暴露出去实现
         *
         * @param holder 对应的holder
         * @return 当前holder绑定数据对应的itemid
         */
        long getFakeItemId(ViewHolder holder);
    }

    /**
     * 设置回调
     *
     * @param fakeCallback
     */
    public void setFakeCallback(FakeCallback fakeCallback) {
        this.fakeCallback = fakeCallback;
    }


    @Override
    public void setNestedScrollingEnabled(boolean enabled) {
        super.setNestedScrollingEnabled(enabled);
    }

    @Override
    public boolean onStartNestedScroll(View child, View target, int nestedScrollAxes) {

        return target instanceof ChildRecyclerView;
    }


    @Override
    public void onNestedPreScroll(View target, int dx, int dy, int[] consumed) {

        ViewPagerRecommendContent viewPagerRecommendContent = getRecommendContent();

        boolean hiddenTop = dy > 0 && !isScrollEnd();
        boolean showTop = dy < 0 && null != viewPagerRecommendContent && viewPagerRecommendContent.isTop();
        if (hiddenTop || showTop) {
            isNestScroll = true;
            boolean enable = showTop && isInTop && isScrollEnd();
            if (enable) {
                consumed[1] = dy;
            } else {
                scrollBy(0, dy);
                consumed[1] = dy;
            }
        }
    }

    @Override
    public boolean onNestedFling(View target, float velocityX, float velocityY, boolean consumed) {
        return true;
    }

    @Override
    public boolean onNestedPreFling(View target, float velocityX, float velocityY) {
        ViewPagerRecommendContent viewPagerRecommendContent = getRecommendContent();
        boolean hiddenTop = velocityY > 0 && !isScrollEnd();
        boolean showTop = velocityY < 0 && null != viewPagerRecommendContent && viewPagerRecommendContent.isTop();
        if (hiddenTop || showTop) {
            isNestScroll = false;
            fling(0, (int) velocityY);
            return true;
        }
        return false;
    }

    @Override
    public int getNestedScrollAxes() {
        return 0;
    }


    /**
     *
     */
    public void onResume() {
        onChildScrollChanged(RecyclerView.SCROLL_STATE_IDLE);
    }

}
