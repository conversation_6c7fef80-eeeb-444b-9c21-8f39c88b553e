package androidx.recyclerview.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.xstore.floorsdk.fieldhome.BuildConfig;
import com.xstore.sdk.floor.floorcore.bean.FloorBaseViewHolder;
import com.xstore.sdk.floor.floorcore.utils.DPIUtil;
import com.xstore.sdk.floor.floorcore.utils.ScreenUtils;
import com.xstore.sevenfresh.floor.modules.vp.bean.VpBean;
import com.xstore.sevenfresh.floor.modules.vp.helper.FlingHelperUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.concurrent.atomic.AtomicBoolean;

public class ChildRecyclerView extends RecyclerView {
    private FlingHelperUtil mFlingHelper;


    private AtomicBoolean isScrollTop = new AtomicBoolean(false);

    /**
     * velocityY y轴上的速度
     */
    private int velocityY;

    /**
     * startFling 开始y轴惯性滑动
     */
    private boolean startFling;


    /**
     * 是否滑动到parentRecyclerView的顶部
     */
    boolean isFlingToTop = false;
    /**
     * parentRecyclerView 父级的recyclerview
     */
    public RecyclerView parentRecyclerView;

    /**
     * maxDistance 最大距离
     */
    private int maxDistance;


    int totalDy = 0;

    /**
     * 滑动距离
     */
    private int scrollDistance = 0;

    private int screenHeight6 = 0;

    private boolean hasSendEvent = false;

    public void setParentRecyclerView(RecyclerView parentRecyclerView) {
        this.parentRecyclerView = parentRecyclerView;
    }

    public ChildRecyclerView(@NonNull Context context) {
        super(context);
        init(context);
    }

    public ChildRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ChildRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mFlingHelper = new FlingHelperUtil(context);
        setFakeId();
        setOverScrollMode(RecyclerView.OVER_SCROLL_NEVER);
        getItemAnimator().setAddDuration(0);
        getItemAnimator().setChangeDuration(0);
        getItemAnimator().setMoveDuration(0);
        getItemAnimator().setRemoveDuration(0);
        if (getItemAnimator() instanceof SimpleItemAnimator) {
            ((SimpleItemAnimator) getItemAnimator()).setSupportsChangeAnimations(false);
        }

        setNestedScrollingEnabled(true);
        this.maxDistance = mFlingHelper.getVelocityByDistance((double) (DPIUtil.getHeight(getContext()) * 4));
        initScrollListener();
        screenHeight6 = ScreenUtils.getScreenHeight(getContext()) * 6;
    }


    /**
     * 绕过recyclerView 自身机制
     * 获取到每个item定义的特殊id，并不影响原本的itemId机制
     *
     * @param fakeId
     * @return
     */
    public ViewHolder findViewHolderForFakeItemId(long fakeId) {
        if (mAdapter == null || !mAdapter.hasStableIds()) {
            return null;
        }
        final int childCount = mChildHelper.getUnfilteredChildCount();
        ViewHolder hidden = null;
        for (int i = 0; i < childCount; i++) {
            final ViewHolder holder = getChildViewHolderInt(mChildHelper.getUnfilteredChildAt(i));
            if (fakeCallback != null) {
                if (holder != null && !holder.isRemoved() && fakeCallback.getFakeItemId(holder) == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            } else {
                if (holder != null && !holder.isRemoved() && holder.getItemId() == fakeId) {
                    if (mChildHelper.isHidden(holder.itemView)) {
                        hidden = holder;
                    } else {
                        return holder;
                    }
                }
            }
        }
        return hidden;
    }

    /**
     * 是否位于顶部 通过判断是否可以滑动来看
     *
     * @return
     */
    public boolean isTop() {

        return !this.canScrollVertically(-1);
    }

    private void initScrollListener() {

        this.addOnScrollListener(new OnScrollListener() {

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int scrollState) {

                if (getFirstVisibleItem() == 0) {
                    notifyLayoutChange();
                }

                if (SCROLL_STATE_IDLE == scrollState) {
                    dispatchParentFling();
                }
                onScrollChanged(scrollState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                if (startFling) {
                    totalDy = 0;
                    startFling = false;
                }

                totalDy = totalDy + dy;

                scrollDistance += dy;
                if (scrollDistance > screenHeight6 && !hasSendEvent){
                    hasSendEvent = true;
                    EventBus.getDefault().post(new VpBean());
                }

            }
        });
    }


    /**
     * 获取第一个可见item的position
     *
     * @return
     */
    public int getFirstVisibleItem() {
        LayoutManager layoutManager = this.getLayoutManager();
        if (null != layoutManager && layoutManager instanceof StaggeredGridLayoutManager) {
            int[] into = new int[2];
            ((StaggeredGridLayoutManager) layoutManager).findFirstVisibleItemPositions(into);
            return into[0] > into[1] ? into[1] : into[0];
        } else {
            return null != layoutManager && layoutManager instanceof GridLayoutManager ? ((GridLayoutManager) layoutManager).findFirstVisibleItemPosition() : -1;
        }
    }

    /**
     * 刷新布局 避免错乱
     */
    private void notifyLayoutChange() {
        LayoutManager layoutManager = this.getLayoutManager();
        if (null != layoutManager && layoutManager instanceof StaggeredGridLayoutManager) {
            //主站为了解决瀑布流错乱的问题？？？？？？？？
            ((StaggeredGridLayoutManager) layoutManager).invalidateSpanAssignments();
        }

    }

    @Override
    public boolean fling(int velocityX, int velocityY) {
        if (!this.isAttachedToWindow()) {
            return false;
        } else {
            int absY = Math.abs(velocityY);
            velocityY = this.maxDistance > 8888 && absY > this.maxDistance ? this.maxDistance * absY / velocityY : velocityY;
            boolean flag = super.fling(velocityX, velocityY);
            if (flag && velocityY < 0) {
                this.startFling = true;
                this.velocityY = velocityY;
            } else {
                this.velocityY = 0;
            }

            return flag;
        }
    }


    /**
     * 滑动到parentRecyclerView的顶部
     */
    public void flingToTop() {
        isFlingToTop = true;
        smoothScrollToPosition(0);
    }

    /**
     * 向 父Recyclerview 传递fling
     */
    private void dispatchParentFling() {
        if (this.parentRecyclerView != null) {
            //如果当前已经滑动到了顶端 并且y向上还存在速度

            if (isFlingToTop && isTop()) {
                this.parentRecyclerView.smoothScrollToPosition(0);
                isFlingToTop = false;
                return;
            }

            if (this.isTop() && this.velocityY != 0) {
                double distance = this.mFlingHelper.getSplineFlingDistance(this.velocityY);
                if (distance > (double) Math.abs(this.totalDy)) {
                    int childVelocityY = this.mFlingHelper.getVelocityByDistance(distance + (double) this.totalDy);
                    this.parentRecyclerView.fling(0, -childVelocityY);
                }
            }

            this.totalDy = 0;
            this.velocityY = 0;
        }

    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {

        if (null != event && event.getAction() == 0) {
            this.velocityY = 0;
        }

        return super.dispatchTouchEvent(event);
    }

    /**
     * 滑动状态变化回调
     *
     * @param scrollState
     */
    public void onScrollChanged(int scrollState) {

    }

    /**
     * 滑动到顶端
     */
    public void scrollToTop() {
        this.isScrollTop.set(true);
        if (!this.isTop()) {
            try {
                this.scrollToPosition(0);
            } catch (Exception var2) {
                if (BuildConfig.DEBUG) {
                    var2.printStackTrace();
                }
            }
        }
    }


    /**
     * 伪造的系统功能回调
     */
    private FloorRecyclerView.FakeCallback fakeCallback;

    private void setFakeId() {
        fakeCallback = new FloorRecyclerView.FakeCallback() {
            @Override
            public long getFakeItemId(RecyclerView.ViewHolder holder) {
                if (holder instanceof FloorBaseViewHolder) {
                    return ((FloorBaseViewHolder) holder).getFakeItemId();
                }
                return holder.getItemId();
            }
        };
    }


}
