<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="RePagerSlidingTabStrip">
        <attr name="re_pstsIndicatorColor" format="color" />
        <attr name="re_pstsIndicatorPadding" format="dimension" />
        <attr name="re_pstsIndicatorHeight" format="dimension" />
        <attr name="re_pstsUnderlineColor" format="color" />
        <attr name="re_pstsUnderlineHeight" format="dimension" />
        <attr name="re_pstsDividerColor" format="color" />
        <attr name="re_pstsDividerWidth" format="dimension" />
        <attr name="re_pstsDividerPadding" format="dimension" />
        <attr name="re_pstsScrollOffset" format="dimension" />
        <attr name="re_pstsShouldExpand" format="boolean" />
        <attr name="re_pstsPaddingMiddle" format="boolean" />
        <attr name="re_pstsTabPaddingLeftRight" format="dimension" />
        <attr name="re_pstsTabBackground" format="reference" />
        <attr name="re_pstsTabTextSize" format="dimension" />
        <attr name="re_pstsTabTextColor" format="reference" />
        <attr name="re_pstsTabTextStyle" format="reference">
            <flag name="normal" value="0x0" />
            <flag name="bold" value="0x1" />
            <flag name="italic" value="0x2" />
        </attr>
        <attr name="re_pstsTabTextAllCaps" format="boolean" />
        <attr name="re_pstsTabTextAlpha" format="integer" />
        <attr name="re_pstsTabTextFontFamily" format="string" />
    </declare-styleable>
</resources>