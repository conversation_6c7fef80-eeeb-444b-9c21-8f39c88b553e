<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/ll_not_network"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:visibility="gone"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_not_network_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:drawableTop="@drawable/sf_theme_image_no_net"
            android:drawablePadding="20dp"
            android:gravity="center_horizontal"
            android:text="无网络权限"
            android:textColor="#1A1A1A"
            android:textStyle="bold"
            android:textSize="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请进入系统：设置 - 七鲜 - 无线数据\n允许七鲜使用网络服务"
            android:textColor="#FF505259"
            android:layout_marginTop="12dp"
            android:textSize="12sp"
            android:gravity="center"/>

        <TextView
            android:id="@+id/not_network_search_other"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@drawable/sf_floor_base_bg_net_request_error"
            android:gravity="center"
            android:text="@string/sf_floor_base_retry"
            android:paddingTop="9dp"
            android:paddingBottom="9dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:textColor="@color/new_white"
            android:textStyle="bold"
            android:textSize="16dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_not_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_nodata_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:drawableTop="@drawable/sf_theme_image_no_net"
            android:drawablePadding="15dp"
            android:gravity="center_horizontal"
            android:text="网络异常,请检查您的网络设置"
            android:textColor="@color/sf_floor_base_color_95969f"
            android:textSize="13dp" />

        <TextView
            android:id="@+id/search_other"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/sf_floor_base_bg_net_request_error_1"
            android:gravity="center"
            android:text="刷新试试"
            android:paddingTop="9dp"
            android:paddingBottom="9dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:textColor="@color/sf_floor_base_color_95969f"
            android:textSize="13dp" />
    </LinearLayout>
</LinearLayout>