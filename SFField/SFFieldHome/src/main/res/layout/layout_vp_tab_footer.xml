<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    tools:layout_height="wrap_content">


    <include
        android:id="@+id/loading_viewstub"
        layout="@layout/layout_tab_footer_loading"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:visibility="gone" />

    <include
        layout="@layout/layout_tab_footer_loading"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_end_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/tab_20dp"
            android:paddingBottom="@dimen/tab_20dp"
            android:text="@string/sf_no_more_tip"
            android:textColor="#C2C3C2"
            android:textSize="12sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_not_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_nodata_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="120dp"
            android:drawableTop="@drawable/sf_theme_image_no_data"
            android:drawablePadding="15dp"
            android:gravity="center_horizontal"
            android:text="暂无数据，刷新试试"
            android:textColor="#505259"
            android:textSize="13dp" />

        <TextView
            android:id="@+id/search_other"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/sf_floor_base_home_vp_no_data_btn"
            android:gravity="center"
            android:paddingLeft="30dp"
            android:paddingTop="9dp"
            android:paddingRight="30dp"
            android:paddingBottom="9dp"
            android:text="刷新"
            android:textColor="@color/new_white"
            android:textSize="13dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:gravity="center"
        android:visibility="gone">


        <ProgressBar
            android:id="@+id/header_progressbar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:indeterminate="false"
            android:indeterminateDrawable="@drawable/refresh_progress" />
    </LinearLayout>


</LinearLayout>
