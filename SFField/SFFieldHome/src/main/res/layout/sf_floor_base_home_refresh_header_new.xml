<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal|bottom"
    android:paddingTop="19dp"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/fl_refresh_indicator"
        android:layout_width="wrap_content"
        android:layout_height="24dp">

        <ProgressBar
            android:id="@+id/pb_refresh_progressBar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="bottom|center_horizontal"
            android:indeterminate="false"
            android:indeterminateDrawable="@drawable/sf_floor_base_home_refresh_progress_style"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_refresh_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:src="@drawable/sf_floor_base_ic_white_arrow_down" />

    </FrameLayout>

    <TextView
        android:id="@+id/tv_refresh_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="@string/sf_floor_base_refresh_header_pulling"
        android:textColor="@color/sf_floor_core_white"
        android:textSize="14dp" />

</LinearLayout>