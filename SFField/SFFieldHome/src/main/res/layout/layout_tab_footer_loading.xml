<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical"
    android:padding="@dimen/footer_padding">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true">

        <ProgressBar
            android:id="@+id/footer_progressbar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:indeterminate="false"
            android:indeterminateDrawable="@drawable/bdcodehelper_refresh_progress_style"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/footer_hint_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@id/footer_progressbar"
            android:text="@string/footer_hint_load_ready"
            android:textColor="@color/title_color"
            android:visibility="visible"/>

    </RelativeLayout>
</RelativeLayout>