<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/sf_floor_base_trans">


    <RelativeLayout
        android:id="@+id/recommend_home_tab_b"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:background="@color/sf_floor_base_trans">

        <ImageView
            android:id="@+id/tab_indicator"
            android:layout_width="@dimen/common_34dp"
            android:layout_height="@dimen/common_10dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="4dp"
            android:src="@drawable/club_tab_selected_indicator"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/recommend_home_tab_b_title"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="0dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:textColor="@color/color_99000000"
            android:textSize="@dimen/main_tv_14_size" />

        <ImageView
            android:id="@+id/iv_recommend_home_tab_b_title"
            android:layout_width="62dp"
            android:layout_height="16dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="0dp"
            android:gravity="center_horizontal"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/recommend_home_tab_b_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:includeFontPadding="false"
            android:paddingLeft="4dp"
            android:paddingTop="1.5dp"
            android:paddingRight="4dp"
            android:paddingBottom="1.5dp"
            android:singleLine="true"
            android:textSize="11dp"
            android:visibility="gone" />


    </RelativeLayout>


</RelativeLayout>