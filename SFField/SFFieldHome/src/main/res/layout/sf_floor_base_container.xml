<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="android.widget.RelativeLayout">

    <!--  新的沉浸式背景  -->
    <FrameLayout
        android:id="@+id/fl_nav_trans"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom">

        <ImageView
            android:id="@+id/iv_nav_trans"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:adjustViewBounds="true"
            android:scaleType="fitXY" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/live_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal" />

    <!--  下拉刷新内容区域  -->
    <com.xstore.sevenfresh.floor.modules.FreshSmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <com.xstore.sevenfresh.floor.modules.SecondFloorHeader
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_second_floor"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" />

            <com.xstore.sevenfresh.floor.modules.HomeRefreshHeaderNew
                android:id="@+id/hrh_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp" />

        </com.xstore.sevenfresh.floor.modules.SecondFloorHeader>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:descendantFocusability="blocksDescendants">

            <androidx.recyclerview.widget.FloorRecyclerView
                android:id="@+id/main_recycle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none"
                android:overScrollMode="never"
                android:scrollbars="none"
                android:visibility="visible" />


            <include
                android:id="@+id/nodata"
                layout="@layout/sf_floor_base_nodata_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:id="@+id/main_recycle_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:srlClassicsSpinnerStyle="Translate"
            app:srlDrawableProgress="@drawable/bdcodehelper_refresh_progress_style"
            app:srlFinishDuration="0"
            android:visibility="gone"
            app:srlTextFinish=""
            app:srlTextLoading="加载中..." />

    </com.xstore.sevenfresh.floor.modules.FreshSmartRefreshLayout>

    <!--  顶部导航栏容器  -->
    <com.xstore.sevenfresh.floor.modules.NavContainer
        android:id="@+id/ll_navigation_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="@color/sf_floor_core_black"
        tools:layout_height="48dp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="@color/sf_floor_core_black">
        <!--  顶部吸顶控件容器-->
        <LinearLayout
            android:id="@+id/ll_top_ceiling_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            tools:background="@color/sf_floor_core_black"
            tools:layout_height="48dp" />

        <!--  顶部吸顶控件容器-->
        <LinearLayout
            android:id="@+id/ll_top_ceiling_container2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            tools:background="@color/sf_floor_core_black"
            tools:layout_height="48dp" />
    </LinearLayout>


    <!--  悬浮窗容器  -->
    <LinearLayout
        android:id="@+id/ll_floating_container"
        android:layout_width="84dp"
        android:layout_height="84dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="0dp"
        android:layout_marginBottom="80dp"
        android:orientation="vertical" />


    <LinearLayout
        android:id="@+id/sf_floor_base_address_tip_view"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/sf_floor_base_address_tip"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="hi~ 新朋友，请先创建收货地址噢~"
            android:textColor="#2A2A2A"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/sf_floor_base_address_tip_btn"
            android:layout_width="90dp"
            android:layout_height="24dp"
            android:background="@drawable/sf_floor_base_address_btn"
            android:gravity="center"
            android:text="新建收货地址"
            android:textColor="#FFFFFF"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sf_floor_base_location_tip_view"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/sf_floor_base_location_tip_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_location_tip_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:layout_weight="1"
            android:text="开启定位权限，为您匹配更精准的地址噢~"
            android:textColor="#2A2A2A"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/sf_floor_base_location_tip_btn"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:background="@drawable/sf_floor_base_location_btn"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:text="去开启"
            android:textColor="#FFFFFF"
            android:textSize="12sp" />

    </LinearLayout>
</merge>