## 模块说明
首页领域。


## 组件更新日志

e.g.
1. 需求/bugfix  简介
改动内容
影响范围
回归范围


#3.1.4
1. 支持新的沉浸式

# 3.1.1
1. 首页商卡支持外卖品

# 3.1.0 
1. 首页支持围栏维度

# 3.0.0
1. 首页架构升级


#  2.0.8
1.feat: 分类sgm上报业务异常
【影响】一级分类、二级分类展示正常

#  2.0.7
1.feat: 暴露下拉刷新动作给业务侧
【改动】下拉时回调业务侧
【影响】无影响
2. 跳转商详 新增榜单排名字段

#  2.0.2-2.0.6
1.新增直播容器，支持跟随滚动

#  2.0.1 
1.fix  catch获取状态栏高度的崩溃


###  2.0.0 因为core发生了变化
1. 【修复/新增】 核心逻辑增加try catch 
【改动】 搜索楼层、新人楼层、tab商品组楼层，给Color.parse增加try-catch, 避免异常颜色数据
【影响】 搜索楼层、新人楼层、tab商品组颜色设置，以及沉浸式颜色设置

2. feat: 七鲜价功能升级
改动：
影响：

3. 【新增】全部toast都替换成公共方法, 使用外部实现
【改动】替换智能优惠券领取文案
【影响】智能优惠券


4. 【新增】容器吸顶逻辑调整
【改动】重构吸顶逻辑实现方式
【影响】吸顶效果展示

5.【删除】容器气泡逻辑删除
【改动】移除气泡逻辑，收归到导航楼层当中
【影响】影响气泡的展示

### 1.0.25
1. 推荐商品新增天天低价埋点
2. 增加获取新人三单礼大组件和进度小组件的方法

### 1.0.22
1. 修改target适配裁剪（有问题）

### 1.0.21
1. plus视觉升级

### 1.0.20
1. 商品卡片更换

### 1.0.19
1. 兼容首页设置导航时padding变化引起的页面滚动
2. 首页更改缓存请求方式

### 1.0.18
1.首页轮播图过度动画
2.首页通栏Gif图加载优化
3.图片加载完成渐现动画

###1.0.17
1.首页头部优化
2.地址门店服务包路径修改，取数逻辑修改

###1.0.16
1.兼容图片宽高为0的情况

###1.0.15

### 1.0.14
1.优化过度绘制，向上混动有看到header的可能，提前进行隐藏

### com.xstore.floorsdk:SFFieldHome:1.0.13
1.修改首页容器过度绘制问题
2.首页缓存数据支持预解析功能

### com.xstore.floorsdk:SFFieldHome:1.0.0-1.0.12
1. 创建仓库。
